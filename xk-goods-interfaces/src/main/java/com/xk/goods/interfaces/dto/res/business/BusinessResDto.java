package com.xk.goods.interfaces.dto.res.business;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessResDto implements Serializable {
    /**
     * 业务id
     */
    private Integer resId;

    /**
     * 资源地址
     */
    private String addr;

    /**
     * 资源映射类型
     */
    private Integer businessResType;

    private Long businessId;
}

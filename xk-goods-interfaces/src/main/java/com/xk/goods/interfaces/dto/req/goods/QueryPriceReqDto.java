package com.xk.goods.interfaces.dto.req.goods;

import java.io.Serializable;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import jakarta.validation.constraints.NotNull;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QueryPriceReqDto extends AbstractSession implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 总金额
     */
    @NotNull(message = "总金额不能为空")
    private Long amount;

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空")
    private Long goodsId;

    /**
     * 购买数量
     */
    private Integer buyCount;

    /**
     * 优惠券领取id
     */
    private Long couponUserId;
}

package com.xk.goods.interfaces.dto.res.goods;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商城商品
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MallDetailResDto {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称 分词
     */
    private String goodsName;

    /**
     * 上下架状态
     */
    private Integer listingStatus;

    /**
     * 上架时间
     */
    private Date actualUpTime;

    /**
     * 显示状态
     */
    private Integer showStatus;

    /**
     * 实际下架时间
     */
    private Date actualDownTime;

    /**
     * 商品描述 分词
     */
    private String goodsDescribe;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 类目ID
     */
    private List<GoodsCategoryDto> goodsCategoryDtoList;

    /**
     * 价格
     */
    private Long amount;

    /**
     * 成本价格
     */
    private Long costAmount;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 库存数量
     */
    private Long stockAmount;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 销量
     */
    private Long orderNum;

    /**
     * 资源图片
     */
    private List<BusinessResDto> productPicList;

    @Data
    @Builder
    public static class GoodsCategoryDto implements Serializable {

        private Long nodeId;

        private String categoryName;

        private Boolean isRoot;

        private Long goodsCategoryId;
    }
}

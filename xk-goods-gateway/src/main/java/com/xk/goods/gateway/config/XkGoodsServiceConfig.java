package com.xk.goods.gateway.config;

import com.xk.search.interfaces.query.goods.*;
import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.config.interfaces.query.res.SysResourceQueryService;
import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;
import com.xk.promotion.interfaces.query.coupon.CouponQueryInnerService;

/**
 * @author: killer
 **/
public class XkGoodsServiceConfig {

    @Bean
    public UserObjectQueryService userObjectQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
    }

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

    @Bean
    public SysResourceQueryService sysResourceQueryService(
            HttpServiceProxyFactory xkConfigHttpServiceProxyFactory) {
        return xkConfigHttpServiceProxyFactory.createClient(SysResourceQueryService.class);
    }

    @Bean
    public CollectibleQueryService collectibleQueryService(
            HttpServiceProxyFactory xkSearchHttpServiceProxyFactory) {
        return xkSearchHttpServiceProxyFactory.createClient(CollectibleQueryService.class);
    }

    @Bean
    public MaterialsQueryService materialsQueryService(
            HttpServiceProxyFactory xkSearchHttpServiceProxyFactory) {
        return xkSearchHttpServiceProxyFactory.createClient(MaterialsQueryService.class);
    }

    @Bean
    public MallQueryService mallQueryService(
            HttpServiceProxyFactory xkSearchHttpServiceProxyFactory) {
        return xkSearchHttpServiceProxyFactory.createClient(MallQueryService.class);
    }

    @Bean
    public MallAppQueryService mallAppQueryService(
            HttpServiceProxyFactory xkSearchHttpServiceProxyFactory) {
        return xkSearchHttpServiceProxyFactory.createClient(MallAppQueryService.class);
    }

    @Bean
    public MerchantQueryService merchantQueryService(
            HttpServiceProxyFactory xkSearchHttpServiceProxyFactory) {
        return xkSearchHttpServiceProxyFactory.createClient(MerchantQueryService.class);
    }

    @Bean
    public CorpQueryService corpQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpQueryService.class);
    }

    @Bean
    public CouponQueryInnerService couponQueryInnerService(
            HttpServiceProxyFactory promotionHttpServiceProxyFactory) {
        return promotionHttpServiceProxyFactory.createClient(CouponQueryInnerService.class);
    }
}

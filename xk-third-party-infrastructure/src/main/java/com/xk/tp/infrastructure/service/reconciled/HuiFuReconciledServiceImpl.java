package com.xk.tp.infrastructure.service.reconciled;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.huifu.bspay.sdk.opps.core.BasePay;
import com.huifu.bspay.sdk.opps.core.config.MerConfig;
import com.huifu.bspay.sdk.opps.core.net.BasePayRequest;
import com.huifu.bspay.sdk.opps.core.sign.JsonUtils;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.RsaUtils;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.infrastructure.util.ExcelUtil;
import com.xk.tp.domain.commons.MoneyHelper;
import com.xk.tp.domain.event.reconciled.CreateFilesReconciledEvent;
import com.xk.tp.domain.event.reconciled.CreateFinancialTransactionEvent;
import com.xk.tp.domain.event.sms.SendSmsEvent;
import com.xk.tp.domain.model.reconciled.ReconciledPayPlatformTypeEntity;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;
import com.xk.tp.domain.service.reconciled.ReconciledService;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import com.xk.tp.enums.reconciled.BillTypeEnum;
import com.xk.tp.infrastructure.commons.entity.ApiConfig;
import com.xk.tp.infrastructure.commons.util.BeanUtil;
import com.xk.tp.infrastructure.dto.rsp.HuiFuReconciledFileDetailsRsp;
import com.xk.tp.infrastructure.dto.rsp.HuiFuReconciledFileExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> date 2024/07/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HuiFuReconciledServiceImpl implements ReconciledService {

    public static final String CONFIG_URI = "/v2/merchant/busi/bill/config";
    public static final String QUERY_URI = "v2/trade/check/filequery";

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Integer getPayPlatformType() {
        return PayPlatformTypeEnum.HUIFU.getValue();
    }

    @Override
    public Mono<Void> selectReconciled(ReconciledRoot root) {
        try {
            // 1. 数据初始化，填入对应的商户配置
            ReconciledPayPlatformTypeEntity platformTypeEntity =
                    root.getReconciledPayPlatformTypeEntity();
            ApiConfig hfPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), ApiConfig.class);
            Map<String, Object> requestBody = getSelectReconciledReqBody(root, hfPay);

            // 3. 发起API调用
            Map<String, Object> response =
                    BasePayRequest.requestBasePay(QUERY_URI, requestBody, null, false);
            Object fileDetails = response.get("file_details");
            if (fileDetails == null) {
                return Mono.empty();
            }
            List<HuiFuReconciledFileDetailsRsp> huiFuReconciledFileDetailsRspList = JSONArray
                    .parseArray(fileDetails.toString(), HuiFuReconciledFileDetailsRsp.class);
            return Flux.fromIterable(huiFuReconciledFileDetailsRspList)
                    .flatMap(huiFuReconciledFileDetailsRsp -> processZipFilesFromUrl(
                            huiFuReconciledFileDetailsRsp.getDownloadUrl()))
                    .then();
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException("Failed to execute huifu reconciled operation", e);
        }
    }

    public Mono<Void> processZipFilesFromUrl(String fileUrl) {
        try {
            List<Path> extractedFiles = new ArrayList<>();
            URL url = new URL(fileUrl);

            try (InputStream inputStream = url.openStream();
                    ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {

                byte[] buffer = new byte[8192]; // 使用更大的缓冲区提高IO性能
                ZipEntry entry;

                while ((entry = zipInputStream.getNextEntry()) != null) {
                    if (isCsvEntry(entry)) {
                        Path outputFile = Paths.get("./" + getFileName(entry));
                        extractZipEntry(zipInputStream, buffer, outputFile);
                        extractedFiles.add(outputFile);
                    }
                }
            }

            if (extractedFiles.isEmpty()) {
                log.warn("No CSV files found in ZIP from URL: {}", fileUrl);
                return Mono.empty();
            }

            return readExcelFiles(extractedFiles);
        } catch (IOException e) {
            log.error("Failed to process ZIP from URL: {}", fileUrl, e);
            return Mono.error(new RuntimeException("Failed to process ZIP file", e));
        }
    }

    private boolean isCsvEntry(ZipEntry entry) {
        return !entry.isDirectory() && entry.getName().toLowerCase().endsWith(".csv");
    }

    private String getFileName(ZipEntry entry) {
        return Paths.get(entry.getName()).getFileName().toString();
    }

    private void extractZipEntry(ZipInputStream zipStream, byte[] buffer, Path targetFile)
            throws IOException {
        // 确保父目录存在
        Files.createDirectories(targetFile.getParent());

        try (OutputStream outputStream = Files.newOutputStream(targetFile)) {
            int bytesRead;
            while ((bytesRead = zipStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    private Mono<Void> readExcelFiles(List<Path> files) {
        return Flux.fromIterable(files).flatMap(file -> {
            try (InputStream inputStream = Files.newInputStream(file)) {
                List<HuiFuReconciledFileExcel> huiFuReconciledFileExcels =
                        EasyExcel.read(inputStream).head(HuiFuReconciledFileExcel.class)
                                .charset(Charset.forName("GB18030")) // 设置检测到的编码
                                .excelType(ExcelTypeEnum.CSV) // 指定为CSV格式
                                .sheet().doReadSync();
                return Flux.fromIterable(huiFuReconciledFileExcels)
                        .flatMap(huiFuReconciledFileExcel -> {
                            EventRoot event = EventRoot.builder()
                                    .domainEvent(CreateFinancialTransactionEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    CreateFinancialTransactionEvent.class))
                                            .financialTransactionId(huiFuReconciledFileExcel
                                                    .getFinancialTransactionId())
                                            .payPlatformType(PayPlatformTypeEnum.HUIFU.getValue())
                                            .financialDate(
                                                    huiFuReconciledFileExcel.getFinancialDate())
                                            .payNo(huiFuReconciledFileExcel.getPayNo())
                                            .payAmount(MoneyHelper.multiply(new BigDecimal(
                                                    huiFuReconciledFileExcel.getPayAmount())))
                                            .build())
                                    .build();
                            return eventRootService.publisheByMono(event);
                        }).then().thenReturn(file);
            } catch (IOException e) {
                log.error("Failed to read Excel file: {}", file, e);
            }
            return Mono.empty();
        }).flatMap(file -> {
            try {
                Files.deleteIfExists(file);
            } catch (IOException e) {
                log.warn("Failed to delete temp file: {}", file, e);
            }
            return Mono.empty();
        }).then();
    }


    @Override
    public Mono<Void> huiFuMerchantConfig(ReconciledRoot root) {
        try {
            // 1. 数据初始化，填入对应的商户配置
            ReconciledPayPlatformTypeEntity platformTypeEntity =
                    root.getReconciledPayPlatformTypeEntity();
            ApiConfig hfPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), ApiConfig.class);
            Map<String, Object> requestBody = getConfigReqBody(root, hfPay);

            // 3. 发起API调用
            Map<String, Object> response =
                    BasePayRequest.requestBasePay(CONFIG_URI, requestBody, null, false);
            log.info(JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Mono.empty();
    }

    private Map<String, Object> getSelectReconciledReqBody(ReconciledRoot root, ApiConfig hfPay)
            throws Exception {
        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(hfPay.getProductId());
        merConfig.setSysId(hfPay.getSysId());
        merConfig.setRsaPrivateKey(hfPay.getAppKey());
        merConfig.setRsaPublicKey(hfPay.getPublicKey());

        BasePay.initWithMerConfig(merConfig);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 2.组装请求参数
        Map<String, Object> paramsInfo = new HashMap<>();
        // 请求日期
        paramsInfo.put("req_date", LocalDate.now().format(formatter));
        // 请求流水号
        paramsInfo.put("req_seq_id",
                "dayReconciled" + DateTools.getCurrentDateTimeYYYYMMDDHHMMSSSSS());
        // 汇付客户Id
        paramsInfo.put("huifu_id", hfPay.getCusId());
        // 文件生成日期
        paramsInfo.put("file_date", root.getReconciledEntity().getTradeDate().format(formatter));
        // 对账单类型
        paramsInfo.put("bill_type", BillTypeEnum.TRADE_BILL.getValue());

        String reqData = JSONObject.toJSONString(paramsInfo);
        String sortedData = JsonUtils.sort4JsonString(reqData, 0);
        String requestSign = RsaUtils.sign(sortedData, hfPay.getAppKey());
        // 签名
        paramsInfo.put("sign", requestSign);

        return paramsInfo;
    }

    private Map<String, Object> getConfigReqBody(ReconciledRoot root, ApiConfig hfPay)
            throws Exception {
        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(hfPay.getProductId());
        merConfig.setSysId(hfPay.getSysId());
        merConfig.setRsaPrivateKey(hfPay.getAppKey());
        merConfig.setRsaPublicKey(hfPay.getPublicKey());

        BasePay.initWithMerConfig(merConfig);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 2.组装请求参数
        Map<String, Object> paramsInfo = new HashMap<>();
        // 请求日期
        paramsInfo.put("req_date", LocalDate.now().format(formatter));
        // 请求流水号
        paramsInfo.put("req_seq_id",
                "dayReconciled" + DateTools.getCurrentDateTimeYYYYMMDDHHMMSSSSS());
        // 汇付客户Id
        paramsInfo.put("huifu_id", hfPay.getCusId());
        // 文件生成日期
        paramsInfo.put("file_date", root.getReconciledEntity().getTradeDate().format(formatter));
        // 对账单类型
        paramsInfo.put("file_type", "1");

        paramsInfo.put("recon_send_flag", "Y");

        String reqData = JSONObject.toJSONString(paramsInfo);
        String sortedData = JsonUtils.sort4JsonString(reqData, 0);
        String requestSign = RsaUtils.sign(sortedData, hfPay.getAppKey());
        // 签名
        paramsInfo.put("sign", requestSign);

        return paramsInfo;
    }
}

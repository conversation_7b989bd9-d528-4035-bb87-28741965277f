package com.xk.tp.infrastructure.dto.rsp;

import com.alibaba.excel.annotation.ExcelProperty;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.xk.tp.infrastructure.convertor.reconciled.HuiFuDateConvert;
import com.xk.tp.infrastructure.convertor.reconciled.HuiFuStringConvert;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HuiFuReconciledFileExcel {

    @ExcelProperty(value = "汇付订单号", index = 5, converter = HuiFuStringConvert.class)
    private String financialTransactionId;

    @ExcelProperty(value = "外部订单号", index = 6, converter = HuiFuStringConvert.class)
    private String payNo;

    @ExcelProperty(value = "交易金额", index = 9, converter = HuiFuStringConvert.class)
    private String payAmount;

    @ExcelProperty(value = "交易状态", index = 12, converter = HuiFuStringConvert.class)
    private String cardTypeNo;

    @ExcelProperty(value = "交易类型", index = 7, converter = HuiFuStringConvert.class)
    private String payDirection;

    @ExcelProperty(value = "交易方式", index = 8, converter = HuiFuStringConvert.class)
    private String payType;

    @ExcelProperty(value = "交易时间", index = 13, converter = HuiFuDateConvert.class)
    @DateTimeFormat("yyy-MM-dd HH:mm:ss")
    private Date payCreateTime;

    @ExcelProperty(value = "交易日期", index = 14, converter = HuiFuDateConvert.class)
    @DateTimeFormat("yyyMMdd")
    private Date financialDate;

    @ExcelProperty(value = "账户号", index = 44, converter = HuiFuStringConvert.class)
    private String payAccount;

    @ExcelProperty(value = "入账客户号", index = 50, converter = HuiFuStringConvert.class)
    private String receiveAccount;

}

package com.xk.goods.application.handler.query.business;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import com.xk.goods.enums.business.BusinessResTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.goods.application.action.query.business.BusinessResBusinessQueryMany;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Optimized handler for batch querying business resources
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessResBusinessManyQueryHandler
        implements IActionQueryManyHandler<BusinessResBusinessQueryMany, BusinessResDto> {

    private final BusinessResRootQueryRepository queryRepository;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Flux<BusinessResDto> execute(Mono<BusinessResBusinessQueryMany> mono) {
        return mono.flatMapMany(query -> {
            if (CollectionUtils.isEmpty(query.getBusinessIds())) {
                log.debug("No business IDs provided, returning empty result");
                return Flux.empty();
            }


            log.debug("Batch querying business resources for {} business IDs", query.getBusinessIds().size());

            // Use optimized batch query instead of individual queries
            return queryRepository
                    .batchSearchByBusinessIds(query.getBusinessIds(), query.getBusinessGroupTypeEnum())
                    .filter(entity -> isBusinessResTypeMatched(entity, query.getBusinessResTypeList()))
                    .collectList()
                    .flatMapMany(this::convertToDto);
        });
    }

    /**
     * Check if business resource type matches the filter criteria
     */
    public boolean isBusinessResTypeMatched(BusinessResEntity entity, List<BusinessResTypeEnum> filterTypes) {
        return CollectionUtils.isEmpty(filterTypes) || filterTypes.contains(entity.getBusinessResType());
    }

    /**
     * Convert BusinessResEntity to BusinessResDto with resource address lookup
     */
    public Flux<BusinessResDto> convertToDto(List<BusinessResEntity> businessResEntity) {
        Function<List<BusinessResEntity>, Flux<BusinessResDto>> createDtoWithAddress = businessResEntities -> {
            return Flux.fromIterable(businessResEntities).flatMap(entity -> {
                return sysResourceRootQueryRepository
                        .findById(IntegerIdentifier.builder().id(entity.getResId()).build())
                        .map(sysResourceEntity -> BusinessResDto.builder()
                                .resId(entity.getResId())
                                .addr(sysResourceEntity.getAddr())
                                .businessResType(entity.getBusinessResType().getCode())
                                .businessId(entity.getBusinessId())
                                .build())
                        .switchIfEmpty(Mono.just(BusinessResDto.builder()
                                .resId(entity.getResId())
                                .businessResType(entity.getBusinessResType().getCode())
                                .businessId(entity.getBusinessId())
                                .build()));
            });
        };

        return createDtoWithAddress.apply(businessResEntity);
    }
}

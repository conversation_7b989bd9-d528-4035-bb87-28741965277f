package com.xk.goods.application.service.random;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.domain.model.random.RandomDistributionRoot;
import com.xk.goods.domain.model.random.id.DistributionItemIdentifier;
import com.xk.goods.domain.model.random.id.RandomDistributionIdentifier;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialTeamItemEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.model.serialitem.entity.SerialItemEntity;
import com.xk.goods.domain.service.random.RandomDistributionDomainService;
import com.xk.goods.domain.service.serial.SerialGroupRootService;
import com.xk.goods.domain.service.serial.SerialItemRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.enums.serial.GroupTypeEnum;
import com.xk.goods.enums.serial.SerialItemTypeEnum;
import com.xk.goods.infrastructure.convertor.serial.SerialGroupIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.serial.SerialItemIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.specification.SpecificationIdentifierConvertor;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionCreateReq;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionIdReq;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionItemReq;
import com.xk.goods.interfaces.dto.req.random.SurplusRandomDistributionReq;
import com.xk.goods.interfaces.dto.res.random.RandomDistributionItemRes;
import com.xk.goods.interfaces.dto.res.random.RandomDistributionRes;
import com.xk.goods.interfaces.service.random.RandomDistributionService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 随机分发服务实现
 * 
 * @author: system
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RandomDistributionServiceImpl implements RandomDistributionService {

    private final RandomDistributionDomainService randomDistributionDomainService;
    private final SpecificationRootService specificationRootService;
    private final SerialItemRootService serialItemRootService;
    private final SerialGroupRootService serialGroupRootService;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<RandomDistributionRes> create(Mono<RandomDistributionCreateReq> mono) {
        return mono.flatMap(req -> {
            if (log.isInfoEnabled()) {
                log.info("开始创建随机分发，bizType: {}, bizIdentifier: {}, totalCount: {}",
                        req.getBizType(), req.getBizIdentifier(), req.getTotalCount());
            }
            // 转换为领域对象
            RandomDistributionRoot root = converter.convert(req, RandomDistributionRoot.class);
            return randomDistributionDomainService.createRandomDistribute(Mono.just(root))
                    .map(result -> converter.convert(result, RandomDistributionRes.class))
                    .doOnSuccess(result -> {
                    }).doOnError(error -> log.error("随机分发创建失败，bizType: {}, bizIdentifier: {}",
                            req.getBizType(), req.getBizIdentifier(), error));
        });
    }

    @BusiCode
    @Override
    public Mono<RandomDistributionRes> createSurplusRandom(
            Mono<SurplusRandomDistributionReq> mono) {
        return mono.flatMap(req -> {
            if (log.isInfoEnabled()) {
                log.info("开始剩余随机分发，identifiers: {}", req.getIdentifiers());
            }
            List<RandomDistributionIdentifier> identifiers = req.getIdentifiers().stream()
                    .map(id -> RandomDistributionIdentifier.builder().distributionId(id).build())
                    .toList();
            return randomDistributionDomainService
                    .createSurplusRandom(Flux.fromIterable(identifiers))
                    .map(result -> converter.convert(result, RandomDistributionRes.class))
                    .doOnSuccess(result -> {
                    }).doOnError(error -> log.error("剩余随机分发失败，identifiers: {}",
                            req.getIdentifiers(), error));
        });
    }

    @BusiCode
    @Override
    public Mono<List<RandomDistributionItemRes>> updateDistributionItem(
            Mono<RandomDistributionItemReq> mono) {
        return mono.flatMap(req -> {
            if (log.isDebugEnabled()) {
                log.debug("获取随机分发项，distributionId: {}", req.getDistributionId());
            }
            RandomDistributionIdentifier identifier = RandomDistributionIdentifier.builder()
                    .distributionId(req.getDistributionId()).build();

            Function<List<RandomDistributionItemRes>, Mono<SerialGroupEntity>> getSpecRoot =
                    itemResList -> {
                        if (CollectionUtils.isEmpty(itemResList)) {
                            return Mono.empty();
                        }

                        return specificationRootService
                                .getRoot(SpecificationIdentifierConvertor
                                        .map(Long.valueOf(itemResList.getFirst().getGroupId())))
                                .map(v -> SerialGroupIdentifierConvertor
                                        .map(v.getSpecificationGiftValObj().getBusinessGroupId()))
                                .flatMap(serialGroupRootService::findById);
                    };

            Function<RandomDistributionItemRes, Mono<RandomDistributionItemRes>> getLessSerialData =
                    itemRes -> {
                        if (req.getBuyCount() >= 100) {
                            return Mono.just(itemRes);
                        }
                        return serialItemRootService.getRoot(SerialItemIdentifierConvertor
                                .map(Long.valueOf(itemRes.getItemId()))).flatMap(root -> {
                                    SerialItemEntity serialItemEntity = root.getSerialItemEntity();
                                    itemRes.setSerialGroupId(serialItemEntity.getSerialGroupId());
                                    itemRes.setSerialItemType(serialItemEntity.getSerialItemType());
                                    if (Objects.equals(serialItemEntity.getSerialItemType(),
                                            SerialItemTypeEnum.ORIGINAL.getCode())) {
                                        SerialOriginalItemEntity serialOriginalItemEntity =
                                                root.getSerialOriginalItemEntity();
                                        itemRes.setCategoryName(
                                                serialOriginalItemEntity.getTeamName());
                                        itemRes.setSerialName(serialOriginalItemEntity.getName());
                                        itemRes.setSerialShowAddr(
                                                serialOriginalItemEntity.getMemberPicAddr());
                                        itemRes.setSerialTeamAddr(
                                                serialOriginalItemEntity.getMemberPicAddr());
                                        itemRes.setSerialAddr(
                                                serialOriginalItemEntity.getMemberAvatarAddr());
                                        itemRes.setColorName(serialOriginalItemEntity.getColor());
                                        itemRes.setLimitEdition(
                                                serialOriginalItemEntity.getLimitEdition());
                                    }

                                    if (Objects.equals(serialItemEntity.getSerialItemType(),
                                            SerialItemTypeEnum.SPECIAL.getCode())) {
                                        SerialSpecialItemEntity serialSpecialItemEntity =
                                                root.getSerialSpecialItemEntity();
                                        itemRes.setCategoryName(
                                                serialSpecialItemEntity.getSerialName());
                                        itemRes.setSerialName(
                                                serialSpecialItemEntity.getSerialName());
                                        itemRes.setSerialAddr(
                                                serialSpecialItemEntity.getOrdinaryPicAddr());
                                        itemRes.setSerialShowAddr(
                                                serialSpecialItemEntity.getSpecialPicAddr());
                                    }

                                    if (Objects.equals(serialItemEntity.getSerialItemType(),
                                            SerialItemTypeEnum.TEAM.getCode())) {
                                        SerialTeamItemEntity serialTeamEntity =
                                                root.getSerialTeamItemEntity();
                                        itemRes.setCategoryName(serialTeamEntity.getTeamName());
                                        itemRes.setSerialName(serialTeamEntity.getTeamName());
                                        itemRes.setColorName(serialTeamEntity.getColor());
                                        itemRes.setSerialShowAddr(
                                                serialTeamEntity.getMemberPicAddr());
                                        itemRes.setSerialAddr(
                                                serialTeamEntity.getMemberAvatarAddr());
                                        itemRes.setSerialTeamAddr(
                                                serialTeamEntity.getMemberAvatarAddr());
                                    }

                                    return Mono.just(itemRes);
                                }).switchIfEmpty(Mono.just(itemRes));
                    };

            BiFunction<List<RandomDistributionItemRes>, SerialGroupEntity, Mono<List<RandomDistributionItemRes>>> getMoreSerialData =
                    (itemResList, serialGroupEntity) -> {
                        if (CollectionUtils.isEmpty(itemResList)) {
                            return Mono.just(itemResList);
                        }
                        if (req.getBuyCount() < 100) {
                            return Mono.just(itemResList);
                        }

                        if (GroupTypeEnum.SPECIAL.getCode()
                                .equals(serialGroupEntity.getGroupType())) {
                            return Mono.just(itemResList);
                        }

                        SerialGroupIdentifier groupIdentifier = serialGroupEntity.getIdentifier();
                        return Mono.zip(
                                serialItemRootService.searchOriginalBySerialGroupId(groupIdentifier)
                                        .collectList().defaultIfEmpty(new ArrayList<>()),
                                serialItemRootService.searchTeamBySerialGroupId(groupIdentifier)
                                        .collectList().defaultIfEmpty(new ArrayList<>()))
                                .flatMap(tuple -> {
                                    Map<Long, SerialOriginalItemEntity> originalItemMap = tuple
                                            .getT1().stream()
                                            .collect(Collectors.toMap(
                                                    SerialOriginalItemEntity::getSerialItemId,
                                                    v -> v));
                                    Map<Long, SerialTeamItemEntity> teamItemMap =
                                            tuple.getT2().stream().collect(Collectors.toMap(
                                                    SerialTeamItemEntity::getSerialItemId, v -> v));

                                    return Flux.fromIterable(itemResList).map(itemRes -> {
                                        Long key = Long.valueOf(itemRes.getItemId());
                                        SerialOriginalItemEntity originalItemEntity =
                                                originalItemMap.get(key);
                                        SerialTeamItemEntity teamItemEntity = teamItemMap.get(key);
                                        if (originalItemEntity != null) {
                                            itemRes.setSerialItemType(
                                                    SerialItemTypeEnum.ORIGINAL.getCode());
                                            itemRes.setSerialGroupId(
                                                    originalItemEntity.getSerialGroupId());
                                            itemRes.setCategoryName(
                                                    originalItemEntity.getTeamName());
                                            itemRes.setSerialName(originalItemEntity.getName());
                                            itemRes.setSerialShowAddr(
                                                    originalItemEntity.getMemberPicAddr());
                                            itemRes.setSerialTeamAddr(
                                                    originalItemEntity.getMemberPicAddr());
                                            itemRes.setSerialAddr(
                                                    originalItemEntity.getMemberAvatarAddr());
                                            itemRes.setColorName(originalItemEntity.getColor());
                                            itemRes.setLimitEdition(
                                                    originalItemEntity.getLimitEdition());
                                        }

                                        if (teamItemEntity != null) {
                                            itemRes.setSerialItemType(
                                                    SerialItemTypeEnum.TEAM.getCode());
                                            itemRes.setSerialGroupId(
                                                    teamItemEntity.getSerialGroupId());
                                            itemRes.setCategoryName(teamItemEntity.getTeamName());
                                            itemRes.setSerialName(teamItemEntity.getTeamName());
                                            itemRes.setColorName(teamItemEntity.getColor());
                                            itemRes.setSerialShowAddr(
                                                    teamItemEntity.getMemberPicAddr());
                                            itemRes.setSerialAddr(
                                                    teamItemEntity.getMemberAvatarAddr());
                                            itemRes.setSerialTeamAddr(
                                                    teamItemEntity.getMemberAvatarAddr());
                                        }
                                        return itemRes;
                                    }).collectList();
                                });
                    };

            return randomDistributionDomainService
                    .getDistributionItem(Mono.just(identifier), req.getBuyCount())
                    .map(item -> converter.convert(item, RandomDistributionItemRes.class))
                    .flatMap(getLessSerialData).collectList()
                    .defaultIfEmpty(Collections.emptyList())
                    .flatMap(list -> getSpecRoot.apply(list)
                            .flatMap(v -> getMoreSerialData.apply(list, v)))
                    .doOnError(error -> log.error("获取随机分发项失败，distributionId: {}",
                            req.getDistributionId(), error));
        });
    }

    @BusiCode
    @Override
    public Mono<Void> updateCallbackDistributionItem(Mono<RandomDistributionIdReq> mono) {
        return mono.flatMap(dto -> Flux.fromIterable(dto.getItemIdList())
                .flatMap(itemId -> randomDistributionDomainService.callbackDistributionItem(
                        Mono.just(DistributionItemIdentifier.builder().id(itemId).build()),
                        itemIdentifierMono -> Mono.just(true)))
                .then());
    }

}

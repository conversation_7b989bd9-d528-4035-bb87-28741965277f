package com.xk.goods.application.service.serial;

import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.function.Function;
import java.util.function.LongFunction;
import java.util.stream.Collectors;

import com.xk.goods.application.dto.serial.SaveSerialOriginalItemDto;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.goods.application.action.command.serial.*;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.dto.serial.SerialGroupOriginalItemTemplate;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.domain.repository.series.SeriesCategoryRootQueryRepository;
import com.xk.goods.domain.service.serial.SerialGroupRootService;
import com.xk.goods.domain.service.serial.SerialItemRootService;
import com.xk.goods.interfaces.dto.req.serial.*;
import com.xk.goods.interfaces.service.serial.SerialGroupService;
import com.xk.infrastructure.util.ExcelUtil;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SerialGroupServiceImpl implements SerialGroupService {

    private static final String TEAM_COLOR = "TEAM";
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final SerialGroupRootService serialGroupRootService;
    private final SerialItemRootService serialItemRootService;
    private final SeriesCategoryRootQueryRepository seriesCategoryRootQueryRepository;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Void> saveOriginal(Mono<SerialGroupSaveOriginalReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            // 解码 Base64 字符串
            byte[] excelBytes = Base64.getDecoder().decode(dto.getExcelBase64());
            ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
            List<SerialGroupOriginalItemTemplate> originalItemDataDTOs =
                    ExcelUtil.readExcel(inputStream, SerialGroupOriginalItemTemplate.class);
            // 过滤出卡密数据
            List<SerialGroupOriginalItemTemplate> itemDataDTOs = originalItemDataDTOs.stream()
                    .filter(item -> !TEAM_COLOR.equals(item.getColor())).toList();
            // 过滤出队伍数据
            List<SerialGroupOriginalItemTemplate> teamDataDTOs = originalItemDataDTOs.stream()
                    .filter(item -> TEAM_COLOR.equals(item.getColor())).toList();

            String checkResult = this.checkList(originalItemDataDTOs);
            if (StringUtils.isNotBlank(checkResult)) {
                return Mono.error(new XkGoodsApplicationException(
                        XkGoodsApplicationErrorEnum.SERIALS_TEMPLATE_EMPTY_ERROR, checkResult));
            }

            // 1. 保存卡密组并获取serialGroupId
            Mono<Long> saveSerialGroupMono = serialGroupRootService.generateId()
                    .flatMap(id -> commandDispatcher.executeCommand(Mono.just(dto),
                            SaveSerialGroupCommand.class, command -> {
                                command.setUserId(userId);
                                command.setSerialGroupId(id);
                                command.setSerialItemNum(originalItemDataDTOs.size());
                                return command;
                            }, Long.class).thenReturn(id) // 确保返回生成的ID
            );

            // 2. 保存卡密条目（接收serialGroupId作为参数）
            LongFunction<Mono<SaveSerialOriginalItemCommand>> saveSerialOriginalItem =
                    serialGroupId -> Flux.fromIterable(itemDataDTOs)
                            .flatMap(originalItem -> seriesCategoryRootQueryRepository
                                    .searchSeriesCategoryByName(originalItem.getTeamName())
                                    .switchIfEmpty(Mono.just(SeriesCategoryEntity.builder()
                                            .seriesCategoryId(0L).build()))
                                    .flatMap(seriesCategory -> serialItemRootService.generateId()
                                            .map(itemId -> {
                                                SaveSerialOriginalItemDto command =
                                                        converter.convert(originalItem,
                                                                SaveSerialOriginalItemDto.class);
                                                command.setTeamType(dto.getTeamType());
                                                command.setUserId(userId);
                                                command.setSerialItemId(itemId);
                                                command.setSerialGroupId(serialGroupId);
                                                command.setSeriesCategoryId(
                                                        seriesCategory.getSeriesCategoryId());
                                                command.setCategoryName(
                                                        seriesCategory.getCategoryName());
                                                if (command.getLimitEdition() == null) {
                                                    command.setLimitEdition("无限编");
                                                }
                                                return command;
                                            })))
                            .collectList() // 收集所有命令
                            .flatMap(saveSerialOriginalItemDtos -> {
                                // 批量执行命令
                                SaveSerialOriginalItemCommand command = SaveSerialOriginalItemCommand.builder()
                                        .itemDtoList(saveSerialOriginalItemDtos).build();
                                return  commandDispatcher
                                                .executeCommand(Mono.just(command), SaveSerialOriginalItemCommand.class)
                                        .then(Mono.just(command));
                            });

            // 3、保存球队信息
            Function<SaveSerialOriginalItemCommand, Mono<Void>> saveSerialGroupTeam =
                    itemCommand -> {
                        // 先按teamName分组
                        Map<String, List<SaveSerialOriginalItemDto>> grouped =
                                itemCommand.getItemDtoList().stream().filter(item -> item.getCategoryName() != null)
                                        .collect(Collectors.groupingBy(
                                                SaveSerialOriginalItemDto::getCategoryName));

                        // 处理每个group
                        return Flux.fromIterable(grouped.entrySet()).flatMap(entry -> {
                            SerialGroupTeamReqDto reqDto = converter.convert(
                                    entry.getValue().getFirst(), SerialGroupTeamReqDto.class);
                            reqDto.setSerialItemNum(entry.getValue().size());

                            return commandDispatcher.executeCommand(Mono.just(reqDto),
                                    SaveSerialGroupTeamCommand.class, cmd -> cmd);
                        }).then();
                    };

            // 4、保存卡密队伍信息
            LongFunction<Mono<Integer>> saveSerialTeamFunc = serialGroupId -> Flux
                    .fromIterable(teamDataDTOs)
                    .flatMap(template -> commandDispatcher.executeCommand(Mono.just(template),
                            SaveSerialTeamCommand.class, command -> {
                                command.setTeamName(template.getMemberCnName());
                                command.setColor(TEAM_COLOR);
                                command.setSerialGroupId(serialGroupId);
                                command.setTeamType(dto.getTeamType());
                                return command;
                            }))
                    .then().thenReturn(1);

            // 链式调用：先保存卡密组，然后并行保存队伍信息和卡密条目，最后保存球队信息
            return saveSerialGroupMono.flatMap(serialGroupId -> {
                // 并行执行保存队伍信息和保存卡密条目
                Mono<Integer> saveTeamMono = saveSerialTeamFunc.apply(serialGroupId);
                Mono<SaveSerialOriginalItemCommand> saveItemMono =
                        saveSerialOriginalItem.apply(serialGroupId);

                // 等待两个操作都完成，然后继续保存球队信息
                return Mono.zip(saveTeamMono, saveItemMono)
                        .flatMap(tuple -> saveSerialGroupTeam.apply(tuple.getT2()));
            });
        }));
    }

    private String checkList(List<SerialGroupOriginalItemTemplate> templateList) {
        if (templateList == null || templateList.isEmpty()) {
            return "模板列表不能为空";
        }

        Set<String> itemHashSet = new HashSet<>();
        Integer index = 1;

        for (SerialGroupOriginalItemTemplate template : templateList) {
            // 数据索引
            index++;

            // 如果是队伍，则跳过
//            if (TEAM_COLOR.equals(template.getColor())) {
//                continue;
//            }


            // 空值校验
//            boolean isEmpty = StringUtils.isEmpty(template.getMemberCnName())
//                    || StringUtils.isEmpty(template.getMemberEnName())
//                    || StringUtils.isEmpty(template.getCardTypeNo())
//                    || StringUtils.isEmpty(template.getCardType());
//            if (isEmpty) {
//                return String.format("第%d行，存在空字段，请修改后上传", index);
//            }

            boolean isEmpty = StringUtils.isEmpty(template.getMemberCnName())
                    || (StringUtils.isEmpty(template.getMemberEnName()) && !TEAM_COLOR.equals(template.getColor()));
            if (isEmpty) {
                return String.format("第%d行，存在空字段，请修改后上传", index);
            }

            // 重复数据校验
            String itemKey = template.getMemberCnName().trim();
            if (template.getMemberEnName() != null) {
                itemKey += template.getMemberEnName().trim();
            }
            if (template.getCardTypeNo() != null) {
                itemKey += template.getCardTypeNo().trim();
            }
            if (template.getCardType() != null) {
                itemKey += template.getCardType().trim();
            }
            if (template.getTeamName() != null) {
                itemKey += template.getTeamName().trim();
            }
            if (template.getLimitEdition() != null) {
                itemKey += template.getLimitEdition().trim();
            }

            String itemHashKey = DigestUtils.md5Hex(itemKey);
            if (itemHashSet.contains(itemHashKey)) {
                return String.format("第%d行，存在重复数据，请修改后上传", index);
            }
            itemHashSet.add(itemHashKey);
        }
        return null; // 验证通过
    }

    @BusiCode
    @Override
    public Mono<Void> updateOriginal(Mono<SerialGroupUpdateOriginalReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            // 1. 更新卡密组
            return commandDispatcher.executeCommand(Mono.just(dto), UpdateSerialGroupCommand.class,
                    command -> {
                        command.setUserId(userId);
                        return command;
                    });

        }));
    }

    @BusiCode
    @Override
    public Mono<Void> updateForbidden(Mono<SerialGroupForbiddenReqDto> mono) {
        return mono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> commandDispatcher.executeCommand(mono,
                        UpdateSerialGroupForbiddenCommand.class,
                        command -> command.setUserId(userId))));
    }

    @BusiCode
    @Override
    public Mono<Void> saveSpecial(Mono<SerialGroupSaveSpecialReqDto> mono) {
        return mono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono().flatMap(
                userId -> serialGroupRootService.generateId().flatMap(id -> commandDispatcher
                        .executeCommand(mono, SaveSerialGroupCommand.class, command -> {
                            command.setSerialGroupId(id);
                            command.setUserId(userId);
                            return command;
                        }))));
    }

    @BusiCode
    @Override
    public Mono<Void> updateSpecial(Mono<SerialGroupUpdateSpecialReqDto> mono) {
        return mono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> commandDispatcher.executeCommand(mono,
                        UpdateSerialGroupCommand.class, command -> command.setUserId(userId))));
    }
}

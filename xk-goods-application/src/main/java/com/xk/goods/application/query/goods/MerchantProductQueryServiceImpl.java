package com.xk.goods.application.query.goods;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.application.action.query.goods.MerchantDetailQuery;
import com.xk.goods.application.action.query.goods.MerchantPagerQuery;
import com.xk.goods.application.dto.goods.QueryPriceAppDto;
import com.xk.goods.application.dto.merchant.StatusCountDto;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityPayValObj;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityShowValObj;
import com.xk.goods.domain.model.merchant.entity.MerchantProductEntity;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialTeamItemEntity;
import com.xk.goods.domain.model.serialitem.id.SerialItemIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.model.specification.id.SpecificationIdentifier;
import com.xk.goods.domain.service.activity.GoodsActivityRootService;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.domain.service.serial.SerialItemRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.enums.serial.SerialItemTypeEnum;
import com.xk.goods.infrastructure.convertor.activity.GoodsActivityIdentifierConvertor;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdInnerReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.QueryPriceReqDto;
import com.xk.goods.interfaces.dto.req.goods.SpecificationIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantSearchPagerBaseDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantSearchPagerReqDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.StatusCountReqDto;
import com.xk.goods.interfaces.dto.res.goods.*;
import com.xk.goods.interfaces.query.goods.MerchantProductQueryService;
import com.xk.search.interfaces.dto.req.goods.SearchGoodsMerchantReqDto;
import com.xk.search.interfaces.query.goods.MerchantQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MerchantProductQueryServiceImpl implements MerchantProductQueryService {

    private final MerchantQueryService merchantQueryService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final MerchantProductRootService merchantProductRootService;
    private final GoodsActivityRootService goodsActivityRootService;
    private final SelectorRootService selectorRootService;
    private final SpecificationRootService specificationRootService;
    private final SerialItemRootService serialItemRootService;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<MerchantSearchPagerReqDto> mono) {
        return mono.flatMap(dto -> {
            SearchGoodsMerchantReqDto req = SearchGoodsMerchantReqDto.builder()
                    .goodsId(dto.getGoodsId()).goodsName(dto.getGoodsName())
                    .collectibleCardId(dto.getCollectibleCardId())
                    .actualUpStartTime(dto.getActualUpStartTime())
                    .actualUpEndTime(dto.getActualUpEndTime()).listingStatus(dto.getListingStatus())
                    .productType(dto.getProductType()).auditStatus(dto.getAuditStatus())
                    .finishStatus(dto.getFinishStatus()).soldOutStatus(dto.getSoldOutStatus())
                    .notSoldOutOrRefund(dto.getNotSoldOutOrRefund())
                    .refundStatus(dto.getRefundStatus()).publicityStatus(dto.getPublicityStatus())
                    .reportStatus(dto.getReportStatus()).recycleStatus(dto.getRecycleStatus())
                    .groupStatus(dto.getGroupStatus()).corpId(dto.getCorpId())
                    .soldStatus(dto.getSoldStatus()).build();
            req.setSessionId(dto.getSessionId());
            req.setSort(dto.getSort());
            req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
            req.setLimit(dto.getLimit());
            req.setOffset(dto.getOffset());
            req.setCurrentPage(dto.getCurrentPage());
            return merchantQueryService.searchGoodsByIds(Mono.just(req))
                    .flatMap(result -> Flux.fromIterable(result.getRecords())
                            .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                            .flatMap(resDto -> actionQueryDispatcher.executeQuery(resDto,
                                    MerchantPagerQuery.class, MerchantPagerResDto.class))
                            .collectList().doOnNext(result::setRecords).thenReturn(result));
        });
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchCorpPager(Mono<MerchantSearchPagerBaseDto> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(user -> mono.flatMap(dto -> {
                    Long corpId = user.getUserDataObjectEntity().getCorpId();
                    if (corpId == null) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    SearchGoodsMerchantReqDto req = SearchGoodsMerchantReqDto.builder()
                            .goodsId(dto.getGoodsId()).goodsName(dto.getGoodsName())
                            .collectibleCardId(dto.getCollectibleCardId())
                            .actualUpStartTime(dto.getActualUpStartTime())
                            .actualUpEndTime(dto.getActualUpEndTime())
                            .listingStatus(dto.getListingStatus()).productType(dto.getProductType())
                            .auditStatus(dto.getAuditStatus()).finishStatus(dto.getFinishStatus())
                            .soldOutStatus(dto.getSoldOutStatus())
                            .notSoldOutOrRefund(dto.getNotSoldOutOrRefund())
                            .refundStatus(dto.getRefundStatus())
                            .publicityStatus(dto.getPublicityStatus())
                            .reportStatus(dto.getReportStatus()).soldStatus(dto.getSoldStatus())
                            .recycleStatus(dto.getRecycleStatus()).groupStatus(dto.getGroupStatus())
                            .corpId(corpId).build();
                    req.setSessionId(dto.getSessionId());
                    req.setSort(dto.getSort());
                    req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
                    req.setLimit(dto.getLimit());
                    req.setOffset(dto.getOffset());
                    req.setCurrentPage(dto.getCurrentPage());
                    return merchantQueryService.searchGoodsByIds(Mono.just(req))
                            .flatMap(result -> Flux.fromIterable(result.getRecords())
                                    .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                                    .flatMap(resDto -> actionQueryDispatcher.executeQuery(resDto,
                                            MerchantPagerQuery.class, MerchantPagerResDto.class))
                                    .collectList().doOnNext(result::setRecords).thenReturn(result));
                }));
    }

    @BusiCode
    @Override
    public Mono<MerchantDetailResDto> detail(Mono<GoodsIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, MerchantDetailQuery.class,
                MerchantDetailResDto.class);
    }

    @BusiCode
    @Override
    public Mono<MerchantDetailResDto> corpDetail(Mono<GoodsIdReqDto> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> mono.flatMap(req -> {
                    Long corpId = userObjectRoot.getUserDataObjectEntity().getCorpId();
                    if (corpId == null) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    return selectorRootService.getGoodsObject(req.getGoodsId())
                            .filter(v -> Objects.equals(v.getGoodsInfo().getGoodsType(),
                                    GoodsTypeEnum.MERCHANT_PRODUCT.name()))
                            .filter(v -> Objects.equals(corpId,
                                    v.getGoodsInfo().getCardGoods().getCorpId()))
                            .switchIfEmpty(Mono.error(new SystemWrapperThrowable(
                                    SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
                })).then(actionQueryDispatcher.executeQuery(mono, MerchantDetailQuery.class,
                        MerchantDetailResDto.class));
    }

    @BusiCode
    @Override
    public Mono<MerchantDetailResDto> innerDetail(Mono<GoodsIdInnerReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, MerchantDetailQuery.class,
                MerchantDetailResDto.class);
    }

    @BusiCode
    @Override
    public Mono<MerchantStatusCountResDto> statusCount(Mono<StatusCountReqDto> mono) {
        return mono.flatMap(dto -> merchantProductRootService
                .statusCount(MerchantProductEntity.builder()
                        .productType(ProductTypeEnum.getByCode(dto.getProductType())).build())
                .map(v -> converter.convert(converter.convert(v, StatusCountDto.class),
                        MerchantStatusCountResDto.class)));
    }

    @BusiCode
    @Override
    public Mono<MerchantStatusCountResDto> statusCorpCount(Mono<StatusCountReqDto> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    Long corpId = userObject.getUserDataObjectEntity().getCorpId();
                    if (corpId == null) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    return merchantProductRootService
                            .statusCount(MerchantProductEntity.builder()
                                    .productType(ProductTypeEnum.getByCode(dto.getProductType()))
                                    .corpInfoId(corpId).build())
                            .map(v -> converter.convert(converter.convert(v, StatusCountDto.class),
                                    MerchantStatusCountResDto.class));
                }));
    }

    @BusiCode
    @Override
    public Mono<Integer> corpCount(Mono<MerchantIdReqDto> merchantIdReqDtoMono) {
        return merchantIdReqDtoMono.flatMap(merchantIdReqDto -> merchantProductRootService
                .corpCount(merchantIdReqDto.getCorpId()));
    }

    @BusiCode
    @Override
    public Mono<PayPriceResDto> queryPayPrice(Mono<QueryPriceReqDto> mono) {
        return mono.flatMap(dto -> {
            GoodsActivityPayValObj valObj = new GoodsActivityPayValObj();
            valObj.setUserId(dto.getUserId() == null ? null
                    : LongIdentifier.builder().id(dto.getUserId()).build());
            valObj.setTotalAmount(dto.getAmount());
            valObj.setPayAmount(dto.getAmount());
            valObj.setIdentifier(GoodsActivityIdentifierConvertor.map(dto.getGoodsId()));
            valObj.setBuyCount(dto.getBuyCount());
            if (dto.getCouponUserId() != null) {
                valObj.setCouponStatus(CommonStatusEnum.ENABLE);
                valObj.setCouponUserId(LongIdentifier.builder().id(dto.getCouponUserId()).build());
            }
            return goodsActivityRootService.getPayPrice(valObj).map(v -> converter
                    .convert(converter.convert(v, QueryPriceAppDto.class), PayPriceResDto.class));
        });
    }

    @BusiCode
    @Override
    public Mono<ShowPriceResDto> queryShowPrice(Mono<QueryPriceReqDto> mono) {
        return mono.flatMap(dto -> {
            GoodsActivityShowValObj valObj = new GoodsActivityShowValObj();
            valObj.setShowAmount(dto.getAmount());
            valObj.setIdentifier(GoodsActivityIdentifierConvertor.map(dto.getGoodsId()));
            valObj.setUserId(dto.getUserId() == null ? null
                    : LongIdentifier.builder().id(dto.getUserId()).build());
            return goodsActivityRootService.getShowPrice(valObj).map(v -> converter
                    .convert(converter.convert(v, QueryPriceAppDto.class), ShowPriceResDto.class));
        });
    }

    @BusiCode
    @Override
    public Mono<List<SpecificationSerialItemDetailResDto>> innerSpecSerialDetail(
            Mono<SpecificationIdReqDto> mono) {
        return mono.flatMap(req -> {
            Mono<SpecificationRoot> getSpecRoot =
                    specificationRootService.getRoot(SpecificationIdentifier.builder()
                            .specificationId(req.getSpecificationId()).build());
            Function<SpecificationRoot, Flux<Long>> getItemFlux = specRoot -> Flux
                    .fromIterable(specRoot.getSpecificationGiftValObj().getGiftBusinessIdList());

            Function<Long, Mono<SpecificationSerialItemDetailResDto>> getResDto =
                    itemId -> serialItemRootService
                            .getRoot(SerialItemIdentifier.builder().serialItemId(itemId).build())
                            .flatMap(root -> {
                                SpecificationSerialItemDetailResDto build =
                                        SpecificationSerialItemDetailResDto.builder()
                                                .serialItemId(root.getSerialItemEntity()
                                                        .getSerialItemId())
                                                .serialGroupId(root.getSerialItemEntity()
                                                        .getSerialGroupId())
                                                .build();
                                build.setSerialItemType(
                                        root.getSerialItemEntity().getSerialItemType());
                                if (Objects.equals(root.getSerialItemEntity().getSerialItemType(),
                                        SerialItemTypeEnum.ORIGINAL.getCode())) {
                                    SerialOriginalItemEntity entity =
                                            root.getSerialOriginalItemEntity();
                                    build.setSerialName(entity.getName());
                                    build.setSerialShowAddr(entity.getMemberPicAddr());
                                    build.setSerialTeamAddr(entity.getMemberPicAddr());
                                    build.setSerialAddr(entity.getMemberAvatarAddr());
                                    build.setColorName(entity.getColor());
                                    build.setLimitEdition(entity.getLimitEdition());
                                }

                                if (Objects.equals(root.getSerialItemEntity().getSerialItemType(),
                                        SerialItemTypeEnum.TEAM.getCode())) {
                                    SerialTeamItemEntity entity = root.getSerialTeamItemEntity();
                                    build.setSerialName(entity.getTeamName());
                                    build.setSerialShowAddr(entity.getMemberPicAddr());
                                    build.setSerialTeamAddr(entity.getMemberPicAddr());
                                    build.setSerialAddr(entity.getMemberAvatarAddr());
                                    build.setColorName(entity.getColor());
                                    build.setLimitEdition("");
                                }

                                if (Objects.equals(root.getSerialItemEntity().getSerialItemType(),
                                        SerialItemTypeEnum.SPECIAL.getCode())) {
                                    SerialSpecialItemEntity entity =
                                            root.getSerialSpecialItemEntity();
                                    build.setSerialName(entity.getSerialName());
                                    build.setSerialShowAddr(entity.getSpecialPicAddr());
                                    build.setSerialTeamAddr(entity.getSpecialPicAddr());
                                    build.setSerialAddr(entity.getOrdinaryPicAddr());
                                    build.setColorName("");
                                    build.setLimitEdition("");
                                }

                                return Mono.just(build);
                            });
            return getSpecRoot.flatMapMany(getItemFlux).flatMap(getResDto).collectList();
        });
    }
}

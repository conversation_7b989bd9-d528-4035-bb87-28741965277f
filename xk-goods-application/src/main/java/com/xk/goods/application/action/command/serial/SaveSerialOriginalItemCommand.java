package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.serial.SaveSerialOriginalItemDto;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SaveSerialOriginalItemCommand extends AbstractActionCommand {

    List<SaveSerialOriginalItemDto> itemDtoList;

}

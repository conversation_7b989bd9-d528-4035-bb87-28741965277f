package com.xk.goods.application.handler.command.serial;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.xk.goods.application.action.query.business.BusinessResBusinessQueryMany;
import com.xk.goods.application.dto.serial.SaveSerialOriginalItemDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.application.action.command.serial.SaveSerialOriginalItemCommand;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serialitem.SerialItemRoot;
import com.xk.goods.domain.model.serialitem.entity.SerialItemEntity;
import com.xk.goods.domain.model.serialitem.id.SerialItemIdentifier;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.serial.SerialItemRootRepository;
import com.xk.goods.domain.repository.team.TeamMemberRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.serial.SerialItemTypeEnum;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialOriginalItemHandler
        implements IActionCommandHandler<SaveSerialOriginalItemCommand, Void> {

    private final SerialItemRootRepository serialItemRootRepository;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final TeamMemberRootQueryRepository teamMemberRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<SaveSerialOriginalItemCommand> mono) {
        return mono.flatMap(dto -> {
            if (CollectionUtils.isEmpty(dto.getItemDtoList())){
                return Mono.empty();
            }
            return teamMemberRootQueryRepository.searchAll().collectList().flatMap(teamMemberEntities -> {
                Map<String, List<TeamMemberEntity>> teamMemberMap = teamMemberEntities.stream().collect(Collectors.groupingBy(teamMember -> String.format("%s%s%s", teamMember.getMemberEnName(), teamMember.getMemberCnName(), teamMember.getMemberType())));
                ArrayList<SerialOriginalItemEntity> itemEntityList = new ArrayList<>();
                ArrayList<SerialOriginalItemEntity> itemEntityAllList = new ArrayList<>();
                for (SaveSerialOriginalItemDto command : dto.getItemDtoList()) {
                    TeamMemberEntity teamMemberEntity = teamMemberMap.getOrDefault(String.format("%s%s%s", command.getMemberEnName(), command.getMemberCnName(), command.getTeamType()), List.of(new TeamMemberEntity())).getLast();
                    SerialOriginalItemEntity itemEntity = converter.convert(command, SerialOriginalItemEntity.class);
                    itemEntity.setTeamMemberId(teamMemberEntity.getTeamMemberId());

                    // 设置默认值
                    itemEntity.setUpdateId(command.getUserId());
                    itemEntity.setCreateId(command.getUserId());
                    itemEntity.setSeriesCategoryId(command.getSeriesCategoryId());

                    if (teamMemberEntity.getTeamMemberId() != null) {
                        itemEntityList.add(itemEntity);
                    } else {
                        itemEntityAllList.add(itemEntity);
                    }
                }
                List<Long> teamMemberIdList = itemEntityList.stream().map(SerialOriginalItemEntity::getTeamMemberId).collect(Collectors.toList());
                BusinessResBusinessQueryMany resQuery = BusinessResBusinessQueryMany.builder()
                        .businessIds(teamMemberIdList)
                        .businessGroupTypeEnum(BusinessGroupTypeEnum.MEMBER).build();
                return queryManyDispatcher.executeQuery(Mono.just(resQuery), BusinessResBusinessQueryMany.class, BusinessResDto.class).collectList()
                        .flatMap(businessResDtos -> {
                            return Mono.just(businessResDtos.stream().collect(Collectors.groupingBy(BusinessResDto::getBusinessId)));
                        }).flatMap(map -> {
                            for (SerialOriginalItemEntity itemEntity : itemEntityList) {
                                List<BusinessResDto> resources = map.get(itemEntity.getTeamMemberId());
                                // 设置球员图片
                                List<BusinessResDto> memberPicList = resources.stream()
                                        .filter(tmp -> BusinessResTypeEnum.MEMBER_PICTURE.getCode()
                                                .equals(tmp.getBusinessResType()))
                                        .toList();
                                if (!memberPicList.isEmpty()) {
                                    itemEntity.setMemberPicAddr(memberPicList.getFirst().getAddr());
                                    log.debug("Set member picture: {}", memberPicList.getFirst().getAddr());
                                }

                                // 设置球员头像
                                List<BusinessResDto> memberAvatarList = resources.stream()
                                        .filter(tmp -> BusinessResTypeEnum.MEMBER_AVATAR.getCode()
                                                .equals(tmp.getBusinessResType()))
                                        .toList();
                                if (!memberAvatarList.isEmpty()) {
                                    itemEntity.setMemberAvatarAddr(memberAvatarList.getFirst().getAddr());
                                    log.debug("Set member avatar: {}", memberAvatarList.getFirst().getAddr());
                                }
                                itemEntityAllList.add(itemEntity);
                            }
                            return Mono.just(itemEntityAllList);
                        });
            }).flatMap(serialOriginalItemEntities ->{
                Long userId = dto.getItemDtoList().getFirst().getUserId();
                ArrayList<SerialItemRoot> list = new ArrayList<>();
                for (SerialOriginalItemEntity command : serialOriginalItemEntities) {
                    SerialItemEntity serialItemEntity = SerialItemEntity.builder()
                            .serialItemId(command.getSerialItemId())
                            .serialGroupId(command.getSerialGroupId())
                            .serialItemType(SerialItemTypeEnum.ORIGINAL.getCode())
                            .status(0)
                            .updateId(userId)
                            .createId(userId)
                            .build();
                    SerialItemIdentifier identifier = SerialItemIdentifier.builder()
                            .serialItemId(command.getSerialItemId()).build();
                    log.debug("Saving SerialOriginalItem with ID: {}, member: {}",
                            command.getSerialItemId(), command.getMemberCnName());
                    SerialItemRoot root = SerialItemRoot.builder()
                            .identifier(identifier).serialItemEntity(serialItemEntity).serialOriginalItemEntity(command).build();
                    list.add(root);
                }
                return serialItemRootRepository.batchSave(list);
            });
        });
    }
}

package com.xk.tp.application.handler.query.userauth;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.tp.application.action.query.userauth.AuthUserInfoQuery;
import com.xk.tp.domain.model.userauth.UserAuthConfigValObj;
import com.xk.tp.domain.model.userauth.UserAuthInfoEntity;
import com.xk.tp.domain.model.userauth.UserAuthPlatformTypeEntity;
import com.xk.tp.domain.model.userauth.UserAuthRoot;
import com.xk.tp.domain.model.userauth.ids.UserAuthIdentifier;
import com.xk.tp.domain.service.userauth.UserAuthRootService;
import com.xk.tp.enums.access.UserAuthChannelTypeEnum;
import com.xk.tp.infrastructure.commons.util.BeanUtil;
import com.xk.tp.interfaces.dto.res.userauth.AuthUserInfoResDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class AuthUserInfoQueryHandler
        implements IActionQueryHandler<AuthUserInfoQuery, AuthUserInfoResDto> {

    private final Converter converter;
    private final UserAuthRootService userAuthRootService;

    @Override
    public Mono<AuthUserInfoResDto> execute(Mono<AuthUserInfoQuery> queryMono) {
        return queryMono.flatMap(query -> {
            UserAuthRoot userAuthRoot = UserAuthRoot.builder()
                    .userAuthIdentifier(UserAuthIdentifier.builder().userAuthId(-1L).build())
                    .userAuthInfoEntity(UserAuthInfoEntity.builder()
                            .code(query.getCode()).build())
                    .userAuthConfigValObj(
                            BeanUtil.mapTo(query.getConfigMap(), UserAuthConfigValObj.class))
                    .userAuthPlatformTypeEntity(UserAuthPlatformTypeEntity.builder()
                            .channelType(
                                    UserAuthChannelTypeEnum.getEnumByValue(query.getChannelType()))
                            .build())
                    .build();
            return userAuthRootService.getUserInfo(userAuthRoot);
        }).map(userAuthRoot -> {
            UserAuthInfoEntity userAuthInfoEntity = userAuthRoot.getUserAuthInfoEntity();
            UserAuthPlatformTypeEntity platformTypeEntity =
                    userAuthRoot.getUserAuthPlatformTypeEntity();
            return AuthUserInfoResDto.builder()
                    .channelType(platformTypeEntity.getChannelType().getValue())
                    .nickname(userAuthInfoEntity.getNickname()).sex(userAuthInfoEntity.getSex())
                    .openid(userAuthInfoEntity.getOpenid()).unionid(userAuthInfoEntity.getUnionid())
                    .city(userAuthInfoEntity.getCity()).country(userAuthInfoEntity.getCountry())
                    .mobile(userAuthInfoEntity.getMobile())
                    .province(userAuthInfoEntity.getProvince()).build();
        });
    }
}

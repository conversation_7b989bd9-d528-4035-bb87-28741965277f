package com.xk.tp.application.service.share;

import java.util.*;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.util.CollectionUtil;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.tp.application.action.command.log.SendCreateUseLogCommand;
import com.xk.tp.application.action.command.share.CreateShareBusinessCommand;
import com.xk.tp.application.action.query.access.AccessAccountExtInfoMapQuery;
import com.xk.tp.application.action.query.access.AccessAccountSearchListQuery;
import com.xk.tp.application.action.query.access.AccessByGroupSearchQuery;
import com.xk.tp.application.dto.share.ShareShowDto;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import com.xk.tp.enums.log.UseLogBusiTypeEnum;
import com.xk.tp.enums.log.UseLogCateTypeEnum;
import com.xk.tp.enums.share.ShareBusinessTypeEnum;
import com.xk.tp.enums.share.ShareTypeEnum;
import com.xk.tp.interfaces.dto.req.share.ShareBusinessReqDto;
import com.xk.tp.interfaces.dto.res.access.AccessAccountResDto;
import com.xk.tp.interfaces.dto.res.access.AccessResDto;
import com.xk.tp.interfaces.dto.res.share.ShareBusinessResDto;
import com.xk.tp.interfaces.service.share.ShareBusinessService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 业务分享
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 10:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShareBusinessServiceImpl implements ShareBusinessService {

    @BusiCode
    @Override
    public Mono<ShareBusinessResDto> createBusinessConfig(Mono<ShareBusinessReqDto> mono) {

        return mono.flatMap(dto -> {
            CreateShareBusinessCommand createShareBusinessCommand = converter.convert(dto, CreateShareBusinessCommand.class);
            if (dto.getShareBusinessType() == null) {
                dto.setShareBusinessType(ShareBusinessTypeEnum.GOODS.getCode());
            }
            if (dto.getChannelType() == null) {
                dto.setChannelType(AccessChannelTypeEnum.weixin.getValue());
            }
            if (dto.getShareType() == null) {
                dto.setShareType(ShareTypeEnum.WEBPAGE.getCode());
            }
            return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {

                createShareBusinessCommand.setUserId(userId);
                String logBusinessId = UseLogBusiTypeEnum.SHARE.getCode() + "" + dto.getShareBusinessType() + dto.getShareBusinessId() + System.currentTimeMillis();
                createShareBusinessCommand.setLogBusinessId(logBusinessId);
                //通道查询
                return queryManyDispatcher.process(Mono.just(AccessByGroupSearchQuery.builder()
                                //根据业务分组和业务类型查询 通道信息
                                .businessGroup(BusinessGroupEnum.SHARE.getCode()).businessType(BusinessTypeEnum.XING_KA.getValue())
                                .status(AccessDomainStatusEnum.ENABLED.getStatus()).build()), AccessByGroupSearchQuery.class, AccessResDto.class)
                        //晒寻可用且渠道匹配的通道
                        .filter(accessResDto ->
                                Objects.equals(accessResDto.getStatus(), AccessDomainStatusEnum.ENABLED.getStatus()) && Objects.equals(accessResDto.getChannelType(), dto.getChannelType()))
                        .flatMap(accessResDto ->
                                //查询通道账号信息
                                queryManyDispatcher.process(Mono.just(AccessAccountSearchListQuery.builder()
                                                .accessId(accessResDto.getAccessId()).status(AccessDomainStatusEnum.ENABLED.getStatus()).build()), AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                        .collectList()
                                        .doOnNext(accessResDto::setAccessAccountResDtos)
                                        .thenReturn(accessResDto)
                        ).collectList().map(accessResDtos -> accessResDtos.stream() //数据组装
                                .flatMap(accessResDto ->
                                        accessResDto.getAccessAccountResDtos().stream()
                                                .map(accountResDto -> {
                                                    accountResDto.setPlatformType(accessResDto.getPlatformType());
                                                    accountResDto.setChannelType(accessResDto.getChannelType());
                                                    accountResDto.setBusinessType(accessResDto.getBusinessType());
                                                    return accountResDto;
                                                })
                                ).toList()
                        ).flatMap(accessAccountResDtos -> {
                            //渠道筛选
                            if (CollectionUtil.isNullOrEmpty(accessAccountResDtos)) {
                                log.error("没有查到合适的分享渠道: {}", JSON.toJSONString(dto));
                                return Mono.error(new XkApplicationException(XkApplicationErrorEnum.DATA_ERROR));
                            }
                            AccessAccountResDto accessAccountResDto = null;
                            //排序
                            List<AccessAccountResDto> sortList = accessAccountResDtos.stream()
                                    .sorted(Comparator.comparing(AccessAccountResDto::getSort).reversed()).toList();
                            //优先级
                            int prioritySum = sortList.stream().mapToInt(AccessAccountResDto::getPriority).sum();
                            //随机数
                            int i = this.random.nextInt(prioritySum);
                            int j = 0;
                            for (AccessAccountResDto resDto : sortList) {
                                j += resDto.getPriority();
                                if (j >= i) {
                                    accessAccountResDto = resDto;
                                    break;  // 找到目标后立即终止循环
                                }
                            }
                            if (accessAccountResDto == null) {
                                log.error("没有查到合适的分享渠道账号: {}", JSON.toJSONString(dto));
                                return Mono.error(new XkApplicationException(XkApplicationErrorEnum.DATA_ERROR));
                            }
                            createShareBusinessCommand.setAccessAccountId(accessAccountResDto.getAccessAccountId());
                            createShareBusinessCommand.setAccessId(accessAccountResDto.getAccessId());
                            //查询通道附加数据
                            return queryDispatcher.process(Mono.just(AccessAccountExtInfoMapQuery.builder().accessAccountId(accessAccountResDto.getAccessAccountId()).build()),
                                            AccessAccountExtInfoMapQuery.class, Map.class)
                                    .switchIfEmpty(Mono.empty())
                                    .flatMap(ext -> {
                                        String appId = String.valueOf(ext.get(APP_ID));
                                        if (ShareBusinessTypeEnum.isExist(dto.getShareBusinessType())) {
                                            String shareH5UrlKey = ShareBusinessTypeEnum.getEnum(dto.getShareBusinessType()).name().toLowerCase() + SHARE_H5_URL;
                                            String shareH5Url = String.valueOf(ext.get(shareH5UrlKey));
                                            createShareBusinessCommand.setShareH5Url(shareH5Url);
                                        }
                                        createShareBusinessCommand.setAppId(appId);
                                        //执行数据处理命令
                                        return dispatcher.executeCommand(Mono.just(createShareBusinessCommand), CreateShareBusinessCommand.class, ShareShowDto.class);
                                    }).flatMap(shareShowDto -> {
                                    //类型转换
                                    ShareBusinessResDto shareBusinessResDto = converter.convert(shareShowDto, ShareBusinessResDto.class);
                                    shareBusinessResDto.setChannelType(dto.getChannelType());
                                    //log写入事件发送
                                    return this.dispatcher.executeCommand(SendCreateUseLogCommand.builder().createId(createShareBusinessCommand.getUserId()).apiUrl(String.valueOf(ShareTypeEnum.WEBPAGE.getCode())).params(JSON.toJSONString(dto))
                                                    .response(JSON.toJSONString(shareBusinessResDto)).busiType(UseLogBusiTypeEnum.SHARE.getCode()).busiId(createShareBusinessCommand.getLogBusinessId())
                                                    .cateType(UseLogCateTypeEnum.SHARE.getCode()).sourcePlatform(BusinessTypeEnum.XING_KA.getValue().toString()).targetAccessId(createShareBusinessCommand.getAccessId()).build(),
                                                    SendCreateUseLogCommand.class, Boolean.class)
                                            .thenReturn(shareBusinessResDto);
                            });
                        });
            });

        });
    }

    private static final String APP_ID = "appId";
    private static final String SHARE_H5_URL = "shareH5Url";
    private final Random random = new Random();
    private final ActionCommandDispatcher<AbstractActionCommand> dispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final Converter converter;
}

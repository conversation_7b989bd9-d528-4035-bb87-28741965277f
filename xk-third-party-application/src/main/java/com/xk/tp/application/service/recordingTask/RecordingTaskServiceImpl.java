package com.xk.tp.application.service.recordingTask;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.tp.application.action.query.access.AccessAccountExtInfoMapQuery;
import com.xk.tp.application.action.query.access.AccessAccountSearchListQuery;
import com.xk.tp.application.action.query.access.AccessByGroupSearchQuery;
import com.xk.tp.domain.model.live.LiveEntity;
import com.xk.tp.domain.model.live.LivePlatformTypeEntity;
import com.xk.tp.domain.model.live.LiveRoot;
import com.xk.tp.domain.model.live.ids.LiveIdentifier;
import com.xk.tp.domain.model.recording.RecordingPlatformTypeEntity;
import com.xk.tp.domain.model.recordingTask.RecordingTaskEntity;
import com.xk.tp.domain.model.recordingTask.RecordingTaskPlatformTypeEntity;
import com.xk.tp.domain.model.recordingTask.RecordingTaskRoot;
import com.xk.tp.domain.model.recordingTask.ids.RecordingTaskIdentifier;
import com.xk.tp.domain.repository.recordingTask.RecordingTaskRootQueryRepository;
import com.xk.tp.domain.repository.recordingTask.RecordingTaskRootRepository;
import com.xk.tp.domain.service.recordingTask.RecordingTaskRootService;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import com.xk.tp.enums.reconciled.OutPutFormatEnum;
import com.xk.tp.enums.recordingTask.ReconciledStatusEnum;
import com.xk.tp.interfaces.dto.res.access.AccessAccountResDto;
import com.xk.tp.interfaces.dto.res.access.AccessResDto;
import com.xk.tp.interfaces.dto.res.recordingTask.CreateTaskRecordingRspDto;
import com.xk.tp.interfaces.service.recordingTask.RecordingTaskService;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.tp.domain.model.recording.RecordingEntity;
import com.xk.tp.domain.model.recording.RecordingRoot;
import com.xk.tp.domain.model.recording.ids.RecordingIdentifier;
import com.xk.tp.domain.model.recording.valobj.MixTranscodeConfigValObj;
import com.xk.tp.domain.model.recording.valobj.RecordingConfigValObj;
import com.xk.tp.domain.service.recording.RecordingRootService;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.live.LiveUserTypeEnum;
import com.xk.tp.interfaces.dto.req.recordingTask.CreateRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DeleteRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DescribeRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.UpdateRecordingReqDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecordingTaskServiceImpl implements RecordingTaskService {

    private final ActionCommandDispatcher<AbstractActionCommand> dispatcher;
    private final Converter converter;


    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;

    private final RecordingRootService recordingRootService;

    private final RecordingTaskRootService recordingTaskRootService;

    private final RecordingTaskRootRepository recordingTaskRootRepository;

    private final RecordingTaskRootQueryRepository recordingTaskRootQueryRepository;

    @BusiCode
    @Override
    public Mono<CreateTaskRecordingRspDto> createCloudRecording(Mono<CreateRecordingReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            // 查询通道
            AccessByGroupSearchQuery searchListQuery = AccessByGroupSearchQuery.builder()
                    .businessGroup(BusinessGroupEnum.LIVE.getCode())
                    .businessType(BusinessTypeEnum.XING_KA.getValue())
                    .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
            return queryManyDispatcher
                    .process(Mono.just(searchListQuery), AccessByGroupSearchQuery.class,
                            AccessResDto.class)
                    .filter(accessResDto -> Objects.equals(accessResDto.getPlatformType(),
                            dto.getLivePlatformType()))
                    .filter(accessResDto -> Objects.equals(accessResDto.getStatus(),
                            AccessDomainStatusEnum.ENABLED.getStatus()))
                    .flatMap(accessResDto -> {
                        // 查询账号
                        AccessAccountSearchListQuery accountSearchListQuery =
                                AccessAccountSearchListQuery.builder()
                                        .accessId(accessResDto.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
                        return queryManyDispatcher.process(Mono.just(accountSearchListQuery),
                                AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                .collectList().map(accessAccountResDtos -> {
                                    accessResDto.setAccessAccountResDtos(accessAccountResDtos);
                                    return accessResDto;
                                });
                    }).collectList().map(accessResDtos -> {
                        List<AccessAccountResDto> accessAccountResDtos = new ArrayList<>();
                        for (AccessResDto accessResDto : accessResDtos) {
                            for (AccessAccountResDto accountResDto : accessResDto
                                    .getAccessAccountResDtos()) {
                                accountResDto.setPlatformType(accessResDto.getPlatformType());
                                accountResDto.setChannelType(accessResDto.getChannelType());
                                accountResDto.setBusinessType(accessResDto.getBusinessType());
                                accessAccountResDtos.add(accountResDto);
                            }
                        }
                        return accessAccountResDtos;
                    }).flatMap(accessAccountResDtos -> {
                        AccessAccountResDto accountResDto = accessAccountResDtos.getFirst();
                        return queryDispatcher
                                .process(
                                        Mono.just(AccessAccountExtInfoMapQuery.builder()
                                                .accessAccountId(accountResDto.getAccessAccountId())
                                                .build()),
                                        AccessAccountExtInfoMapQuery.class, Map.class)
                                .flatMap(config -> {
                                    return recordingRootService
                                            .createCloudRecording(RecordingRoot.builder()
                                                    .identifier(RecordingIdentifier.builder()
                                                            .recordingTaskId("-1")
                                                            .liveId(dto.getLiveId())
                                                            .liveUserTypeEnum(
                                                                    LiveUserTypeEnum.getEnumByValue(
                                                                            dto.getLiveUserType()))
                                                            .livePlatformTypeEnum(
                                                                    LivePlatformTypeEnum
                                                                            .getEnumByValue(
                                                                                    dto.getLivePlatformType()))
                                                            .build())
                                                    .recordingPlatformTypeEntity(
                                                            RecordingPlatformTypeEntity
                                                                    .builder()
                                                                    .livePlatformTypeEnum(
                                                                            LivePlatformTypeEnum
                                                                                    .getEnumByValue(
                                                                                            dto.getLivePlatformType()))
                                                                    .accessAccountId(accountResDto
                                                                            .getAccessAccountId())
                                                                    .config(config).build())
                                                    .recordingEntity(RecordingEntity.builder()
                                                            .recordingTaskId("-1")
                                                            .liveId(dto.getLiveId())
                                                            .liveUserTypeEnum(
                                                                    LiveUserTypeEnum.getEnumByValue(
                                                                            dto.getLiveUserType()))
                                                            .livePlatformTypeEnum(
                                                                    LivePlatformTypeEnum
                                                                            .getEnumByValue(dto
                                                                                    .getLivePlatformType()))
                                                            .creator(dto.getCreator())
                                                            .recordingConfigValObj(
                                                                    RecordingConfigValObj.builder()
                                                                            .outputFormat(
                                                                                    OutPutFormatEnum.MP4
                                                                                            .getValue())
                                                                            .build())
                                                            .mixTranscodeConfigValObj(
                                                                    MixTranscodeConfigValObj
                                                                            .builder()
                                                                            .width(dto.getWidth())
                                                                            .height(dto.getHeight())
                                                                            .build())
                                                            .build())
                                                    .build())
                                            .flatMap(taskId -> {
                                                return recordingTaskRootService.generateId()
                                                        .flatMap(id -> {
                                                            Date date = new Date();
                                                            return ReadSynchronizationUtils
                                                                    .getUserIdMono()
                                                                    .flatMap(userId -> {
                                                                        return dtoMono.flatMap(
                                                                                createRecordingReqDto -> {
                                                                                    RecordingTaskRoot recordingTaskRoot =
                                                                                            RecordingTaskRoot
                                                                                                    .builder()
                                                                                                    .identifier(
                                                                                                            RecordingTaskIdentifier
                                                                                                                    .builder()
                                                                                                                    .livePlatformTypeEnum(
                                                                                                                            LivePlatformTypeEnum
                                                                                                                                    .getEnumByValue(
                                                                                                                                            createRecordingReqDto
                                                                                                                                                    .getLivePlatformType()))
                                                                                                                    .build())
                                                                                                    .recordingTaskEntity(
                                                                                                            RecordingTaskEntity
                                                                                                                    .builder()
                                                                                                                    .id(id)
                                                                                                                    .livePlatformTypeEnum(
                                                                                                                            LivePlatformTypeEnum
                                                                                                                                    .getEnumByValue(
                                                                                                                                            createRecordingReqDto
                                                                                                                                                    .getLivePlatformType()))
                                                                                                                    .recordingTaskId(
                                                                                                                            taskId)
                                                                                                                    .startTime(
                                                                                                                            date)
                                                                                                                    .liveId(createRecordingReqDto
                                                                                                                            .getLiveId())
                                                                                                                    .status(ReconciledStatusEnum.RECORDING
                                                                                                                            .getValue())
                                                                                                                    .createTime(
                                                                                                                            date)
                                                                                                                    .createId(
                                                                                                                            userId)
                                                                                                                    .build())
                                                                                                    .build();
                                                                                    return recordingTaskRootRepository
                                                                                            .save(recordingTaskRoot)
                                                                                            .thenReturn(
                                                                                                    CreateTaskRecordingRspDto.builder()
                                                                                                            .taskId(taskId)
                                                                                                            .videoUrl(String.format("live/%s/%s_%s.mp4", taskId, config.get("appid"),createRecordingReqDto.getLiveId()))
                                                                                                            .build());
                                                                                });
                                                                    });
                                                        });
                                            });
                                });
                    });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> deleteCloudRecording(Mono<DeleteRecordingReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            // 查询通道
            AccessByGroupSearchQuery searchListQuery = AccessByGroupSearchQuery.builder()
                    .businessGroup(BusinessGroupEnum.LIVE.getCode())
                    .businessType(BusinessTypeEnum.XING_KA.getValue())
                    .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
            return queryManyDispatcher
                    .process(Mono.just(searchListQuery), AccessByGroupSearchQuery.class,
                            AccessResDto.class)
                    .filter(accessResDto -> Objects.equals(accessResDto.getPlatformType(),
                            dto.getLivePlatformType()))
                    .filter(accessResDto -> Objects.equals(accessResDto.getStatus(),
                            AccessDomainStatusEnum.ENABLED.getStatus()))
                    .flatMap(accessResDto -> {
                        // 查询账号
                        AccessAccountSearchListQuery accountSearchListQuery =
                                AccessAccountSearchListQuery.builder()
                                        .accessId(accessResDto.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
                        return queryManyDispatcher.process(Mono.just(accountSearchListQuery),
                                AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                .collectList().map(accessAccountResDtos -> {
                                    accessResDto.setAccessAccountResDtos(accessAccountResDtos);
                                    return accessResDto;
                                });
                    }).collectList().map(accessResDtos -> {
                        List<AccessAccountResDto> accessAccountResDtos = new ArrayList<>();
                        for (AccessResDto accessResDto : accessResDtos) {
                            for (AccessAccountResDto accountResDto : accessResDto
                                    .getAccessAccountResDtos()) {
                                accountResDto.setPlatformType(accessResDto.getPlatformType());
                                accountResDto.setChannelType(accessResDto.getChannelType());
                                accountResDto.setBusinessType(accessResDto.getBusinessType());
                                accessAccountResDtos.add(accountResDto);
                            }
                        }
                        return accessAccountResDtos;
                    }).flatMap(accessAccountResDtos -> {
                        AccessAccountResDto accountResDto = accessAccountResDtos.getFirst();
                        return queryDispatcher
                                .process(
                                        Mono.just(AccessAccountExtInfoMapQuery.builder()
                                                .accessAccountId(accountResDto.getAccessAccountId())
                                                .build()),
                                        AccessAccountExtInfoMapQuery.class, Map.class)
                                .flatMap(config -> {
                                    return recordingTaskRootQueryRepository
                                            .selectById(StringIdentifier.builder()
                                                    .id(dto.getLiveId()).build())
                                            .flatMap(recordingTaskEntity -> {
                                                return recordingRootService
                                                        .deleteCloudRecording(RecordingRoot
                                                                .builder()
                                                                .identifier(RecordingIdentifier
                                                                        .builder()
                                                                        .recordingTaskId(
                                                                                recordingTaskEntity
                                                                                        .getRecordingTaskId())
                                                                        .liveId(dto.getLiveId())
                                                                        .liveUserTypeEnum(
                                                                                LiveUserTypeEnum.TENCENT_NUMBER)
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .recordingPlatformTypeEntity(
                                                                        RecordingPlatformTypeEntity
                                                                                .builder()
                                                                                .livePlatformTypeEnum(
                                                                                        LivePlatformTypeEnum
                                                                                                .getEnumByValue(
                                                                                                        dto.getLivePlatformType()))
                                                                                .accessAccountId(
                                                                                        accountResDto
                                                                                                .getAccessAccountId())
                                                                                .config(config)
                                                                                .build())
                                                                .recordingEntity(RecordingEntity
                                                                        .builder()
                                                                        .recordingTaskId(
                                                                                recordingTaskEntity
                                                                                        .getRecordingTaskId())
                                                                        .liveId(dto.getLiveId())
                                                                        .liveUserTypeEnum(
                                                                                LiveUserTypeEnum.TENCENT_NUMBER)
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .build())
                                                        .thenReturn(recordingTaskEntity);
                                            }).flatMap(recordingTaskEntity -> {
                                                Date date = new Date();
                                                return recordingTaskRootRepository
                                                        .update(RecordingTaskRoot.builder()
                                                                .identifier(RecordingTaskIdentifier
                                                                        .builder()
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .recordingTaskEntity(
                                                                        RecordingTaskEntity
                                                                                .builder()
                                                                                .id(recordingTaskEntity
                                                                                        .getId())
                                                                                .endTime(date)
                                                                                .time(date.getTime()
                                                                                        - recordingTaskEntity
                                                                                                .getStartTime()
                                                                                                .getTime())
                                                                                .status(ReconciledStatusEnum.RECORDING_SUCCESS
                                                                                        .getValue())
                                                                                .build())
                                                                .build());
                                            }).then();
                                });
                    });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> describeCloudRecording(Mono<DescribeRecordingReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            // 查询通道
            AccessByGroupSearchQuery searchListQuery = AccessByGroupSearchQuery.builder()
                    .businessGroup(BusinessGroupEnum.LIVE.getCode())
                    .businessType(BusinessTypeEnum.XING_KA.getValue())
                    .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
            return queryManyDispatcher
                    .process(Mono.just(searchListQuery), AccessByGroupSearchQuery.class,
                            AccessResDto.class)
                    .filter(accessResDto -> Objects.equals(accessResDto.getPlatformType(),
                            dto.getLivePlatformType()))
                    .filter(accessResDto -> Objects.equals(accessResDto.getStatus(),
                            AccessDomainStatusEnum.ENABLED.getStatus()))
                    .flatMap(accessResDto -> {
                        // 查询账号
                        AccessAccountSearchListQuery accountSearchListQuery =
                                AccessAccountSearchListQuery.builder()
                                        .accessId(accessResDto.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
                        return queryManyDispatcher.process(Mono.just(accountSearchListQuery),
                                AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                .collectList().map(accessAccountResDtos -> {
                                    accessResDto.setAccessAccountResDtos(accessAccountResDtos);
                                    return accessResDto;
                                });
                    }).collectList().map(accessResDtos -> {
                        List<AccessAccountResDto> accessAccountResDtos = new ArrayList<>();
                        for (AccessResDto accessResDto : accessResDtos) {
                            for (AccessAccountResDto accountResDto : accessResDto
                                    .getAccessAccountResDtos()) {
                                accountResDto.setPlatformType(accessResDto.getPlatformType());
                                accountResDto.setChannelType(accessResDto.getChannelType());
                                accountResDto.setBusinessType(accessResDto.getBusinessType());
                                accessAccountResDtos.add(accountResDto);
                            }
                        }
                        return accessAccountResDtos;
                    }).flatMap(accessAccountResDtos -> {
                        AccessAccountResDto accountResDto = accessAccountResDtos.getFirst();
                        return queryDispatcher
                                .process(
                                        Mono.just(AccessAccountExtInfoMapQuery.builder()
                                                .accessAccountId(accountResDto.getAccessAccountId())
                                                .build()),
                                        AccessAccountExtInfoMapQuery.class, Map.class)
                                .flatMap(config -> {
                                    return recordingTaskRootQueryRepository
                                            .selectById(StringIdentifier.builder()
                                                    .id(dto.getLiveId()).build())
                                            .flatMap(recordingTaskEntity -> {
                                                return recordingRootService.describeCloudRecording(
                                                        RecordingRoot.builder()
                                                                .identifier(RecordingIdentifier
                                                                        .builder()
                                                                        .recordingTaskId(
                                                                                recordingTaskEntity
                                                                                        .getRecordingTaskId())
                                                                        .liveId(dto.getLiveId())
                                                                        .liveUserTypeEnum(
                                                                                LiveUserTypeEnum.TENCENT_NUMBER)
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .recordingPlatformTypeEntity(
                                                                        RecordingPlatformTypeEntity
                                                                                .builder()
                                                                                .livePlatformTypeEnum(
                                                                                        LivePlatformTypeEnum
                                                                                                .getEnumByValue(
                                                                                                        dto.getLivePlatformType()))
                                                                                .accessAccountId(
                                                                                        accountResDto
                                                                                                .getAccessAccountId())
                                                                                .config(config)
                                                                                .build())
                                                                .recordingEntity(RecordingEntity
                                                                        .builder()
                                                                        .recordingTaskId(
                                                                                recordingTaskEntity
                                                                                        .getRecordingTaskId())
                                                                        .liveId(dto.getLiveId())
                                                                        .liveUserTypeEnum(
                                                                                LiveUserTypeEnum.TENCENT_NUMBER)
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .build());
                                            }).then();
                                });
                    });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> modifyCloudRecording(Mono<UpdateRecordingReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            // 查询通道
            AccessByGroupSearchQuery searchListQuery = AccessByGroupSearchQuery.builder()
                    .businessGroup(BusinessGroupEnum.LIVE.getCode())
                    .businessType(BusinessTypeEnum.XING_KA.getValue())
                    .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
            return queryManyDispatcher
                    .process(Mono.just(searchListQuery), AccessByGroupSearchQuery.class,
                            AccessResDto.class)
                    .filter(accessResDto -> Objects.equals(accessResDto.getPlatformType(),
                            dto.getLivePlatformType()))
                    .filter(accessResDto -> Objects.equals(accessResDto.getStatus(),
                            AccessDomainStatusEnum.ENABLED.getStatus()))
                    .flatMap(accessResDto -> {
                        // 查询账号
                        AccessAccountSearchListQuery accountSearchListQuery =
                                AccessAccountSearchListQuery.builder()
                                        .accessId(accessResDto.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
                        return queryManyDispatcher.process(Mono.just(accountSearchListQuery),
                                AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                .collectList().map(accessAccountResDtos -> {
                                    accessResDto.setAccessAccountResDtos(accessAccountResDtos);
                                    return accessResDto;
                                });
                    }).collectList().map(accessResDtos -> {
                        List<AccessAccountResDto> accessAccountResDtos = new ArrayList<>();
                        for (AccessResDto accessResDto : accessResDtos) {
                            for (AccessAccountResDto accountResDto : accessResDto
                                    .getAccessAccountResDtos()) {
                                accountResDto.setPlatformType(accessResDto.getPlatformType());
                                accountResDto.setChannelType(accessResDto.getChannelType());
                                accountResDto.setBusinessType(accessResDto.getBusinessType());
                                accessAccountResDtos.add(accountResDto);
                            }
                        }
                        return accessAccountResDtos;
                    }).flatMap(accessAccountResDtos -> {
                        AccessAccountResDto accountResDto = accessAccountResDtos.getFirst();
                        return queryDispatcher
                                .process(
                                        Mono.just(AccessAccountExtInfoMapQuery.builder()
                                                .accessAccountId(accountResDto.getAccessAccountId())
                                                .build()),
                                        AccessAccountExtInfoMapQuery.class, Map.class)
                                .flatMap(config -> {
                                    return recordingTaskRootQueryRepository
                                            .selectById(StringIdentifier.builder()
                                                    .id(dto.getLiveId()).build())
                                            .flatMap(recordingTaskEntity -> {
                                                return recordingRootService.modifyCloudRecording(
                                                        RecordingRoot.builder()
                                                                .identifier(RecordingIdentifier
                                                                        .builder()
                                                                        .recordingTaskId(
                                                                                recordingTaskEntity
                                                                                        .getRecordingTaskId())
                                                                        .liveId(dto.getLiveId())
                                                                        .liveUserTypeEnum(
                                                                                LiveUserTypeEnum.TENCENT_NUMBER)
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .recordingPlatformTypeEntity(
                                                                        RecordingPlatformTypeEntity
                                                                                .builder()
                                                                                .livePlatformTypeEnum(
                                                                                        LivePlatformTypeEnum
                                                                                                .getEnumByValue(
                                                                                                        dto.getLivePlatformType()))
                                                                                .accessAccountId(
                                                                                        accountResDto
                                                                                                .getAccessAccountId())
                                                                                .config(config)
                                                                                .build())
                                                                .recordingEntity(RecordingEntity
                                                                        .builder()
                                                                        .recordingTaskId(
                                                                                recordingTaskEntity
                                                                                        .getRecordingTaskId())
                                                                        .liveId(dto.getLiveId())
                                                                        .liveUserTypeEnum(
                                                                                LiveUserTypeEnum.TENCENT_NUMBER)
                                                                        .livePlatformTypeEnum(
                                                                                LivePlatformTypeEnum
                                                                                        .getEnumByValue(
                                                                                                dto.getLivePlatformType()))
                                                                        .build())
                                                                .build());
                                            }).then();
                                });
                    });
        });
    }
}

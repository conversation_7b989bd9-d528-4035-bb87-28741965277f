package com.xk.tp.application.handler.command.log;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.tp.application.action.command.log.SendCreateUseLogCommand;
import com.xk.tp.domain.event.log.CreateUseLogEvent;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 发送创建日志指令
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/9 18:08
 */
@Slf4j
@Component
public class CreateSendUseLogHandler implements IActionCommandHandler<SendCreateUseLogCommand, Bo<PERSON>an> {

    @Override
    public Mono<Boolean> execute(Mono<SendCreateUseLogCommand> commandMono) {
        return commandMono.flatMap(command -> {
            EventRoot event = EventRoot.builder().domainEvent(CreateUseLogEvent.builder().identifier(-1L)
                    .createId(command.getCreateId()).apiUrl(command.getApiUrl()).params(command.getParams())
                    .response(command.getResponse()).busiType(command.getBusiType()).busiId(command.getBusiId()).cateType(command.getCateType()).sourcePlatform(command.getSourcePlatform())
                    .targetAccessId(command.getTargetAccessId()).build()).isQueue(true).build();
            boolean flag = false;
            try {
                flag = eventRootService.publish(event);
                if (!flag) {
                    log.error("创建日志发送事件失败 commandMono = {}", JSON.toJSONString(command));
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("创建日志发送事件失败 commandMono = {},msg:{}", JSON.toJSONString(command),
                        e.getMessage(), e);
            }
            return Mono.just(flag);
        });
    }
    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    private EventRootService eventRootService;
}

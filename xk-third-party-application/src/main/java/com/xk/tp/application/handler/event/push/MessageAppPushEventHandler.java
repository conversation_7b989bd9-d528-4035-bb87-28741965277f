package com.xk.tp.application.handler.event.push;


import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import com.xk.message.domain.event.message.MessageAppPushEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.commons.util.CollectionUtil;
import com.myco.mydata.commons.util.SnowFlakeGenIdHelper;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.acct.interfaces.dto.req.user.UserDevicePaperReqDto;
import com.xk.acct.interfaces.dto.rsp.user.UserDeviceRsqDto;
import com.xk.acct.interfaces.query.UserDeviceQueryService;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.tp.application.action.command.log.SendCreateUseLogCommand;
import com.xk.tp.application.action.query.access.AccessAccountExtInfoMapQuery;
import com.xk.tp.application.action.query.access.AccessAccountSearchListQuery;
import com.xk.tp.application.action.query.access.AccessByGroupSearchQuery;
import com.xk.tp.domain.event.push.PushMessageResultEvent;
import com.xk.tp.domain.model.push.PushMessageRoot;
import com.xk.tp.domain.model.push.entity.DeviceEntity;
import com.xk.tp.domain.model.push.entity.MessageContextEntity;
import com.xk.tp.domain.model.push.entity.PushMessageEntity;
import com.xk.tp.domain.model.push.obj.PushChannelExtObj;
import com.xk.tp.domain.service.push.ApiPushAppRootService;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import com.xk.tp.enums.log.UseLogBusiTypeEnum;
import com.xk.tp.enums.log.UseLogCateTypeEnum;
import com.xk.tp.interfaces.dto.res.access.AccessAccountResDto;
import com.xk.tp.interfaces.dto.res.access.AccessResDto;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 推送事件处理
 * <AUTHOR> 
 */
@Slf4j
@Component
public class MessageAppPushEventHandler extends AbstractEventVerticle<MessageAppPushEvent> {

    @Autowired
    public void setApiPushAppRootService (ApiPushAppRootService apiPushAppRootService) {
        this.apiPushAppRootService = apiPushAppRootService;
    }
    @Autowired
    public void setDispatcher(ActionCommandDispatcher<AbstractActionCommand> dispatcher) {
        this.dispatcher = dispatcher;
    }
    @Autowired
    public void setQueryManyDispatcher(ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher) {
        this.queryManyDispatcher = queryManyDispatcher;
    }
    @Autowired
    public void setQueryDispatcher(ActionQueryDispatcher<IActionQuery> queryDispatcher) {
        this.queryDispatcher = queryDispatcher;
    }
    @Autowired
    public void setUserDeviceQueryService(UserDeviceQueryService userDeviceQueryService) {
        this.userDeviceQueryService = userDeviceQueryService;
    }
    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    private ApiPushAppRootService apiPushAppRootService;
    private ActionCommandDispatcher<AbstractActionCommand> dispatcher;
    private ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private UserDeviceQueryService userDeviceQueryService;
    private EventRootService eventRootService;

    private final Random random = new Random();
    private final SnowFlakeGenIdHelper snowFlakeGenIdHelper = SnowFlakeGenIdHelper.getInstance();
    private static final String APP_ID = "appId";
    private static final String APP_KEY = "appKey";
    private static final String MASTER_SECRET = "masterSecret";
    private static final String LOGO = "logo";
    private static final String LOGO_URL = "logoUrl";
    private static final String DOMAIN = "domain";
    private static final String PAYLOAD = "payload";

    @Override
    public Mono<Void> handle(Mono<MessageAppPushEvent> eventMono) {
        return eventMono.flatMap(event -> {
            AtomicBoolean pushSuccess = new AtomicBoolean(false);
            //默认为个推
            Integer pushChannelType = event.getPushChannelType() == null ? AccessChannelTypeEnum.GE_TUI.getValue() : event.getPushChannelType();
            PushChannelExtObj pushChannelExtObj = PushChannelExtObj.builder()
                    .pushChannelType(AccessChannelTypeEnum.getEnumByValue(pushChannelType)).build();
            return queryManyDispatcher.process(Mono.just(AccessByGroupSearchQuery.builder()
                            //根据业务分组和业务类型查询 通道信息
                            .businessGroup(BusinessGroupEnum.PUSH.getCode()).businessType(BusinessTypeEnum.XING_KA.getValue())
                            .status(AccessDomainStatusEnum.ENABLED.getStatus()).build()), AccessByGroupSearchQuery.class, AccessResDto.class)
                    //晒寻可用且渠道匹配的通道
                    .filter(accessResDto ->
                            Objects.equals(accessResDto.getStatus(), AccessDomainStatusEnum.ENABLED.getStatus()) && Objects.equals(accessResDto.getChannelType(), pushChannelType))
                    .flatMap(accessResDto ->
                            //查询通道账号信息
                            queryManyDispatcher.process(Mono.just(AccessAccountSearchListQuery.builder()
                                            .accessId(accessResDto.getAccessId()).status(AccessDomainStatusEnum.ENABLED.getStatus()).build()), AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                    .collectList()
                                    .doOnNext(accessResDto::setAccessAccountResDtos)
                                    .thenReturn(accessResDto)
                    ).collectList().map(accessResDtos -> accessResDtos.stream() //数据组装
                            .flatMap(accessResDto ->
                                    accessResDto.getAccessAccountResDtos().stream()
                                            .map(accountResDto -> {
                                                accountResDto.setPlatformType(accessResDto.getPlatformType());
                                                accountResDto.setChannelType(accessResDto.getChannelType());
                                                accountResDto.setBusinessType(accessResDto.getBusinessType());
                                                return accountResDto;
                                            })
                            ).toList()
                    ).flatMap(accessAccountResDtos -> {
                        //渠道筛选
                        if (CollectionUtil.isNullOrEmpty(accessAccountResDtos)) {
                            log.error("没有查到合适的推送渠道: {}", JSON.toJSONString(event));
                            return Mono.error(new XkApplicationException(XkApplicationErrorEnum.DATA_ERROR));
                        }
                        AccessAccountResDto accessAccountResDto = null;
                        //排序
                        List<AccessAccountResDto> sortList = accessAccountResDtos.stream()
                                .sorted(Comparator.comparing(AccessAccountResDto::getSort).reversed()).toList();
                        //优先级
                        int prioritySum = sortList.stream().mapToInt(AccessAccountResDto::getPriority).sum();
                        //随机数
                        int i = this.random.nextInt(prioritySum);
                        int j = 0;
                        for (AccessAccountResDto resDto : sortList) {
                            j += resDto.getPriority();
                            if (j >= i) {
                                accessAccountResDto = resDto;
                                break;
                            }
                        }
                        if (accessAccountResDto == null) {
                            log.error("没有查到合适的推送渠道账号: {}", JSON.toJSONString(event));
                            return Mono.error(new XkApplicationException(XkApplicationErrorEnum.DATA_ERROR));
                        }
                        pushChannelExtObj.setAccessAccountId(accessAccountResDto.getAccessAccountId());
                        pushChannelExtObj.setAccessId(accessAccountResDto.getAccessId());
                        return queryDispatcher.process(Mono.just(AccessAccountExtInfoMapQuery.builder().accessAccountId(accessAccountResDto.getAccessAccountId()).build()),
                                        AccessAccountExtInfoMapQuery.class, Map.class)
                                .switchIfEmpty(Mono.empty())
                                .flatMap(ext -> {
                                    //平台补充信息查询
                                    pushChannelExtObj.setAppId(String.valueOf(ext.get(APP_ID)));
                                    pushChannelExtObj.setAppKey(String.valueOf(ext.get(APP_KEY)));
                                    pushChannelExtObj.setMasterSecret(String.valueOf(ext.get(MASTER_SECRET)));
                                    pushChannelExtObj.setLogo(String.valueOf(ext.get(LOGO)));
                                    pushChannelExtObj.setLogoUrl(String.valueOf(ext.get(LOGO_URL)));
                                    pushChannelExtObj.setDomain(String.valueOf(ext.get(DOMAIN)));
                                    //查询user设备id
                                    Long userId = event.getReceiverCode();
                                    UserDevicePaperReqDto userDevicePaperReqDto = UserDevicePaperReqDto.builder().userId(userId).build();
                                    userDevicePaperReqDto.setLimit(100);
                                    userDevicePaperReqDto.setOffset(0);
                                    return userDeviceQueryService.searchUserDevice(Mono.just(userDevicePaperReqDto))
                                            .flatMap(pagination -> Flux.fromIterable(pagination.getRecords())
                                                    .switchIfEmpty(Mono.empty())
                                                    .flatMap(linkHashMap -> {
                                                        UserDeviceRsqDto userDeviceRsqDto = JSON.parseObject(JSON.toJSONString(linkHashMap), UserDeviceRsqDto.class);
                                                        if (!userDeviceRsqDto.getPlatformType().equals(event.getPlatformType())) {
                                                            return Mono.empty();
                                                        }
                                                        String deviceId = userDeviceRsqDto.getDeviceId();
                                                        //构建PushMessageEntity
                                                        PushMessageEntity pushMessageEntity = PushMessageEntity.builder()
                                                                .deviceId(deviceId).pushUserId(userId).platformType(PlatformTypeEnum.getByValue(userDeviceRsqDto.getPlatformType()))
                                                                .pushTime(new Date()).accessAccountId(pushChannelExtObj.getAccessAccountId())
                                                                .pushChannelExtObj(pushChannelExtObj).build();
                                                        //构建MessageContextEntity
                                                        MessageContextEntity messageContextEntity = MessageContextEntity.builder()
                                                                .messageId(event.getMessageId()).messageTemplateId(event.getMessageTemplateId())
                                                                .messageParams(event.getMessageParams()).title(event.getTitle())
                                                                .content(event.getContent()).clickType(PAYLOAD).build();
                                                        return Mono.just(PushMessageRoot.builder()
                                                                .deviceEntity(DeviceEntity.builder().deviceId(deviceId).build())
                                                                .pushMessageEntity(pushMessageEntity)
                                                                .messageContextEntity(messageContextEntity)
                                                                .build());
                                                    })
                                                    .switchIfEmpty(Mono.empty())
                                                    .flatMap(pushMessageRoot -> apiPushAppRootService.pushAppMessage(pushMessageRoot).flatMap(apiPushObj -> {
                                                        //设置推送结果标志
                                                        if (Boolean.TRUE.equals(apiPushObj.getSuccess()) && !pushSuccess.get()) {
                                                            pushSuccess.set(true);
                                                        }
                                                        // 日志记录
                                                        return this.dispatcher.executeCommand(SendCreateUseLogCommand.builder().createId(userId).apiUrl(apiPushObj.getApiUrl())
                                                                                .params(apiPushObj.getParams()).response(apiPushObj.getResponse()).busiType(UseLogBusiTypeEnum.PUSH.getCode()).busiId(event.getMessageId())
                                                                                .cateType(UseLogCateTypeEnum.PUSH.getCode()).sourcePlatform(BusinessTypeEnum.XING_KA.getValue().toString()).targetAccessId(pushChannelExtObj.getAccessAccountId()).build(),
                                                                        SendCreateUseLogCommand.class, Boolean.class)
                                                                .thenReturn(pushMessageRoot);

                                                        })
                                                    ).then()
                                            );
                                });

                    }).doFinally(signalType -> {
                        //发送推送完成事件
                        EventRoot resultEvent = EventRoot.builder().domainEvent(PushMessageResultEvent.builder().identifier(-1L)
                                .messageId(snowFlakeGenIdHelper.nextId())
                                .sourceMessageId(event.getMessageId())
                                .receiverCode(event.getReceiverCode())
                                .messageTemplateId(event.getMessageTemplateId())
                                .title(event.getTitle())
                                .content(event.getContent())
                                .pushChannelType(AccessChannelTypeEnum.GE_TUI.getValue())
                                .messageParams(event.getMessageParams())
                                .sendTime(new Date())
                                .success(pushSuccess.get())
                                .platformType(event.getPlatformType()).build()).isQueue(true).build();
                        try {
                            //发送事件
                            boolean flag = eventRootService.publish(resultEvent);
                            if (!flag) {
                                log.error("创建推送结果消息推送发送事件失败 sourceEvent = {}", JSON.toJSONString(event));
                            }
                        } catch (ExceptionWrapperThrowable e) {
                            log.error("创建推送结果消息推送发送事件失败 sourceEvent = {} ,msg:{}", JSON.toJSONString(event),
                                    e.getMessage(), e);
                        }
                    });
        });
    }
}
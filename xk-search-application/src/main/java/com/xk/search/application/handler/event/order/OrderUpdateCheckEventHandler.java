package com.xk.search.application.handler.event.order;

import static com.xk.search.enums.search.SearchChannelDefaultEnum.DEFAULT_SEARCH;

import java.util.Arrays;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.commons.OrderUtil;
import com.xk.order.domain.event.order.OrderUpdateEvent;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.search.domain.dto.OrderUpdateDto;
import com.xk.search.domain.event.order.OrderUpdateCheckEvent;
import com.xk.search.domain.model.order.OrderSearchRoot;
import com.xk.search.domain.model.order.entity.OrderSearchEntity;
import com.xk.search.domain.model.order.id.OrderSearchIdentifier;
import com.xk.search.domain.model.order.valobj.OrderIndexValueObject;
import com.xk.search.domain.service.order.OrderAdapterSearchService;
import com.xk.search.domain.service.order.OrderSearchRootService;
import com.xk.search.enums.order.OrderTypeSearchEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderUpdateCheckEventHandler extends AbstractEventVerticle<OrderUpdateCheckEvent> {

    private final OrderSearchRootService orderSearchRootService;

    private final OrderAdapterSearchService orderAdapterSearchService;


    private final Converter converter;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderUpdateCheckEvent> event) {
        Function<OrderUpdateCheckEvent, Mono<OrderUpdateCheckEvent>> deleteDocument = orderUpdateEvent -> {
            OrderTypeSearchEnum orderTypeSearchEnum =
                    OrderTypeSearchEnum.getByCode(orderUpdateEvent.getOrderType());

            return Flux.fromIterable(Arrays.stream(OrderStatusEnum.values()).toList())
                    .flatMap(orderStatusEnum -> orderSearchRootService
                    .deleteDocument(OrderSearchRoot.builder().identifier(OrderSearchIdentifier
                            .builder().orderNo(orderUpdateEvent.getOrderNo())
                            .searchChannelType(DEFAULT_SEARCH.getSearchChannelType()).build())
                            .orderIndexValueObject(OrderIndexValueObject.builder()
                                    .productType(orderTypeSearchEnum
                                            .equals(OrderTypeSearchEnum.MERCHANT_PRODUCT)
                                                    ? OrderUtil.getProductType(
                                                            orderUpdateEvent.getOrderNo())
                                                    : orderTypeSearchEnum.getCode())
                                    .orderStatus(orderStatusEnum.getCode())
                                    .searchIndexTypeEnum(
                                            orderTypeSearchEnum.getSearchIndexTypeEnum())
                                    .searchBizTypeEnum(orderTypeSearchEnum.getSearchBizTypeEnum())
                                    .build())
                            .orderSearchEntity(OrderSearchEntity.builder()
                                    .orderNo(orderUpdateEvent.getOrderNo()).build())
                            .build()))
                    .then().thenReturn(orderUpdateEvent);
        };

        return event.flatMap(deleteDocument)
                .flatMap(orderUpdateEvent -> orderAdapterSearchService.updateDocument(Mono.just(
                        OrderUpdateDto.builder().id(orderUpdateEvent.getOrderNo()).build()))
                        .doOnSuccess(x -> {
                            log.debug("更新订单成功：{}", orderUpdateEvent.getOrderNo());
                        }).doOnError(x -> {
                            log.error("更新订单失败：{}", orderUpdateEvent.getOrderNo());
                        }));

    }
}

package com.xk.search.application.action.query.goods;

import java.util.HashMap;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.search.interfaces.dto.req.goods.LiveAllReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = LiveAllReqDto.class, convertGenerate = false)})
public class LiveAllQuery extends PagerQuery implements IActionQuery {

    /**
     * 搜索名称
     */
    private String searchName;

    /**
     * 公示状态 1-已公示,0-未公示
     */
    private Integer publicityStatus;

    /**
     * 直播状态
     * 10-待直播
     * 20-直播中
     * 30-直播完成
     */
    private Integer liveStatus;

    /**
     * 索引下标
     */
    private HashMap<String, Integer> keysCursor;

}

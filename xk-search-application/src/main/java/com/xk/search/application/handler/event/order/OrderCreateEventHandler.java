package com.xk.search.application.handler.event.order;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.enums.filetask.FileTaskBizStatusEnum;
import com.xk.ewd.domain.event.filetask.TaskOrderUpdateEvent;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.search.domain.event.order.OrderUpdateCheckEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.order.OrderCreateEvent;
import com.xk.search.domain.dto.OrderUpdateDto;
import com.xk.search.domain.service.order.OrderAdapterSearchService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCreateEventHandler extends AbstractEventVerticle<OrderCreateEvent> {

    private final OrderAdapterSearchService orderAdapterSearchService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    private final Converter converter;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderCreateEvent> event) {
        return event.flatMap(orderCreateEvent -> orderAdapterSearchService
                .updateDocument(Mono
                        .just(OrderUpdateDto.builder().id(orderCreateEvent.getOrderNo()).build()))
                .then(Mono.fromCallable(() -> {
                    EventRoot eventRoot = EventRoot.builder().domainEvent(OrderUpdateCheckEvent
                            .builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(OrderUpdateCheckEvent.class))
                            .orderNo(orderCreateEvent.getOrderNo())
                            .orderType(orderCreateEvent.getOrderType().getCode())
                            .build())
                            .isQueue(false).build();
                    return eventRootService.publisheByMono(eventRoot);
                })).then()
                .doOnSuccess(x -> {


                    log.debug("创建订单成功：{}", orderCreateEvent.getOrderNo());
                }).doOnError(x -> {
                    log.error("创建订单失败：{}", orderCreateEvent.getOrderNo());
                })

        );

    }
}

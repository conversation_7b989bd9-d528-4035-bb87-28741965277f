package com.xk.search.application.handler.query.goods;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.xk.goods.enums.goods.LiveStatusEnum;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.commons.util.StringUtils;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.interfaces.dto.page.TairPagination;
import com.xk.search.application.action.query.goods.LiveAllQuery;
import com.xk.search.domain.model.common.SearchLimitValueObject;
import com.xk.search.domain.model.common.SearchSortValueObject;
import com.xk.search.domain.model.goods.GoodsMerchantSearchEntity;
import com.xk.search.domain.model.goods.GoodsSearchRoot;
import com.xk.search.domain.model.goods.valobj.GoodsIndexValueObject;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.model.search.valobj.TairSearchValueObject;
import com.xk.search.domain.service.goods.GoodsSearchRootService;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;
import com.xk.search.interfaces.dto.rsp.goods.LiveAllRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class LiveAllQueryHandler implements IActionQueryHandler<LiveAllQuery, Pagination> {

    private final GoodsSearchRootService goodsSearchRootService;

    @Override
    public Mono<Pagination> execute(Mono<LiveAllQuery> query) {

        return query.flatMap(liveAllQuery -> goodsSearchRootService
                .idxMSearchDocument(GoodsSearchRoot.builder()
                        .identifier(LongIdentifier.builder().id(-1L).build())
                        .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MERCHANT).build())
                        .searchIdentifier(SearchIdentifier.builder()
                                .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                        .getSearchChannelType())
                                .build())
                        .goodsMerchantSearchEntity(GoodsMerchantSearchEntity.builder().build())
                        .searchSortValueObject(
                                Collections.singletonList(buildSearchSortValueObject(liveAllQuery)))
                        .searchLimitValueObject(buildSearchLimitValueObject(liveAllQuery))
                        .goodsMerchantSearchEntity(buildSearchEntity(liveAllQuery)).build())
                .flatMap(searchDocumentValObj -> processSearchResult(
                        (TairSearchValueObject) searchDocumentValObj)));
    }

    /**
     * 处理搜索结果并应用库存和折扣
     */
    private Mono<Pagination> processSearchResult(TairSearchValueObject tairSearchValueObject) {
        TairPagination pagination = new TairPagination();
        pagination.setTotalCount(tairSearchValueObject.getTotalCount());
        pagination.setKeysCursor(tairSearchValueObject.getKeysCursor());
        pagination.setRecords(new ArrayList<>());

        if (tairSearchValueObject.getTotalCount() == 0) {
            return Mono.just(pagination);
        }

        List<LiveAllRspDto> goodsList = new ArrayList<>();

        List<String> documents = tairSearchValueObject.getDocument();
        // 解析文档并构建映射
        for (String document : documents) {
            goodsList.add(JSONObject.parseObject(document, LiveAllRspDto.class));
        }

        pagination.setRecords(goodsList);
        return Mono.just(pagination);
    }

    private SearchSortValueObject buildSearchSortValueObject(LiveAllQuery query) {
        if (!StringUtils.isEmpty(query.getSort())) {
            return SearchSortValueObject.builder().fileName(query.getSort())
                    .sortBy(query.getOrder()).build();
        }
        return SearchSortValueObject.builder().fileName("sort")
                .sortBy(SystemConstant.BusinessOrderType.DESC.name()).build();
    }

    /**
     * 构建搜索限制对象
     */
    private SearchLimitValueObject buildSearchLimitValueObject(LiveAllQuery query) {
        return SearchLimitValueObject.builder().limit(query.getLimit()).offset(query.getOffset())
                .keysCursor(query.getKeysCursor()).build();
    }

    /**
     * 构建搜索实体
     */
    private GoodsMerchantSearchEntity buildSearchEntity(LiveAllQuery query) {
        return GoodsMerchantSearchEntity.builder().groupStatus(CommonStatusEnum.ENABLE.getCode())
                .liveShowStatus(CommonStatusEnum.ENABLE.getCode())
                .publicityStatus(query.getPublicityStatus())
                .queryLiveStatus(query.getLiveStatus() == null ? List.of(LiveStatusEnum.LIVE.getCode(), LiveStatusEnum.LIVE_SUCCESS.getCode())
                        : List.of(query.getLiveStatus()))
                .collectibleCardName(query.getSearchName()).goodsName(query.getSearchName())
                .corpInfoName(query.getSearchName()).build();
    }

}

package com.xk.search.application.handler.query.goods;

import java.util.*;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.domain.model.stock.valobj.StockRemainValObj;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.enums.goods.ListingStatusEnum;
import com.xk.goods.enums.serial.ShowEnum;
import com.xk.infrastructure.cache.dao.user.UserBoughtGoodsDao;
import com.xk.infrastructure.cache.key.user.UserBoughtGoodsKey;
import com.xk.search.application.action.query.goods.HomePageGoodsMerchantSoldQuery;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.common.SearchLimitValueObject;
import com.xk.search.domain.model.common.SearchSortValueObject;
import com.xk.search.domain.model.goods.GoodsMerchantSearchEntity;
import com.xk.search.domain.model.goods.GoodsSearchRoot;
import com.xk.search.domain.model.goods.valobj.GoodsIndexValueObject;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.model.search.valobj.TairSearchValueObject;
import com.xk.search.domain.service.goods.GoodsSearchRootService;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;
import com.xk.search.interfaces.dto.rsp.goods.MerchantGoodsAppRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class HomePageGoodsMerchantSoldQueryHandler
        implements IActionQueryHandler<HomePageGoodsMerchantSoldQuery, Pagination> {


    private final GoodsSearchRootService goodsSearchRootService;
    private final Converter converter;
    private final StockRootService stockRootService;
    private final UserBoughtGoodsDao userBoughtGoodsDao;

    @Override
    public Mono<Pagination> execute(Mono<HomePageGoodsMerchantSoldQuery> query) {
        return query.flatMap(this::processQuery);
    }

    /**
     * 构建搜索实体
     */
    private GoodsMerchantSearchEntity buildSearchEntity(HomePageGoodsMerchantSoldQuery query) {
        return GoodsMerchantSearchEntity.builder().soldStatus(CommonStatusEnum.ENABLE.getCode())
                .soldOutStatus(CommonStatusEnum.DISABLE.getCode())
                .finishStatus(CommonStatusEnum.DISABLE.getCode())
                .listingStatus(ListingStatusEnum.UP.getCode()).showStatus(ShowEnum.SHOW.getCode())
                .corpInfoId(StringUtils.toString(query.getCorpInfoId()))
                .productType(query.getProductType()).publicityStatus(query.getPublicityStatus())
                .goodsName(query.getSearchName()).collectibleCardName(query.getSearchName())
                .productTypeName(query.getSearchName())
                .reportStatus(query.getReportStatus()).build();
    }

    /**
     * 处理搜索结果并应用库存和折扣
     */
    private Mono<Pagination> processSearchResult(TairSearchValueObject tairSearchValueObject,
            Long userId) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(tairSearchValueObject.getTotalCount());
        pagination.setRecords(new ArrayList<>());

        if (tairSearchValueObject.getTotalCount() == 0) {
            return Mono.just(pagination);
        }

        List<MerchantGoodsAppRspDto> goodsList = new ArrayList<>();
        Map<StringIdentifier, MerchantGoodsAppRspDto> identifierMap = new HashMap<>();
        List<String> documents = tairSearchValueObject.getDocument();
        long[] goodsIdArray = new long[documents.size()];

        // 解析文档并构建映射
        for (int i = 0; i < documents.size(); i++) {
            String document = documents.get(i);
            MerchantGoodsAppRspDto dto =
                    JSONObject.parseObject(document, MerchantGoodsAppRspDto.class);

            // 处理随机剩余状态
            if (CommonStatusEnum.ENABLE.getCode().equals(dto.getRemainRandomStatus())) {
                dto.setAmount(dto.getRemainRandomAmount());
            }

            StringIdentifier identifier =
                    StringIdentifier.builder().id(String.valueOf(dto.getGoodsId())).build();
            identifierMap.put(identifier, dto);
            goodsList.add(dto);
            goodsIdArray[i] = dto.getGoodsId();
        }

        // 获取用户购买记录
        Map<Long, Boolean> userBoughtBits = new HashMap<>();
        if (userId != -1L) {
            userBoughtBits.putAll(userBoughtGoodsDao
                    .getBits(UserBoughtGoodsKey.builder().userId(userId).build(), goodsIdArray));
        }
        pagination.setRecords(goodsList);

        // 处理库存和折扣
        return stockRootService
                .findRemainRealStock(StockBusinessTypeEnum.GOODS,
                        identifierMap.keySet().toArray(new StringIdentifier[0]))
                .doOnNext(stock -> processStockAndDiscount(stock, identifierMap, userBoughtBits))
                .then().thenReturn(pagination);
    }

    /**
     * 处理库存和折扣逻辑
     */
    private void processStockAndDiscount(StockRemainValObj stockValObj,
            Map<StringIdentifier, MerchantGoodsAppRspDto> identifierMap,
            Map<Long, Boolean> userBoughtBits) {
        if (stockValObj == null)
            return;

        // 根据实际的stock对象类型进行处理
        MerchantGoodsAppRspDto dto = identifierMap.get(stockValObj.getBusinessId());
        if (dto == null)
            return;

        dto.setResidueStockAmount(stockValObj.getRemainRealStock());
        applyFirstBuyDiscount(dto, userBoughtBits);
    }

    /**
     * 应用首购折扣
     */
    private void applyFirstBuyDiscount(MerchantGoodsAppRspDto dto,
            Map<Long, Boolean> userBoughtBits) {
        boolean isDiscountEnabled =
                CommonStatusEnum.ENABLE.getCode().equals(dto.getFirstBuyDiscountStatus());
        if (!isDiscountEnabled || dto.getFirstBuyDiscountAmount()== null) {
            return;
        }
        if (MapUtils.isEmpty(userBoughtBits)) {
            dto.setAmount(dto.getAmount() - dto.getFirstBuyDiscountAmount());
            return;
        }

        boolean isGoodsNotInBits = !userBoughtBits.containsKey(dto.getGoodsId())
                || !userBoughtBits.get(dto.getGoodsId());

        if (isGoodsNotInBits) {
            dto.setAmount(dto.getAmount() - dto.getFirstBuyDiscountAmount());
        }
    }

    /**
     * 处理查询请求
     */
    private Mono<Pagination> processQuery(HomePageGoodsMerchantSoldQuery query) {
        GoodsMerchantSearchEntity searchEntity = buildSearchEntity(query);
        GoodsSearchRoot goodsSearchRoot =
                GoodsSearchRoot.builder().identifier(LongIdentifier.builder().id(-1L).build())
                        .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MERCHANT)
                                .blockType(searchEntity.getBlockType()).build())
                        .searchIdentifier(SearchIdentifier.builder()
                                .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                        .getSearchChannelType())
                                .build())
                        .goodsMerchantSearchEntity(searchEntity)
                        .searchSortValueObject(Collections.singletonList(SearchSortValueObject
                                .builder()
                                .fileName(StringUtils.isEmpty(query.getSort()) ? "createTime"
                                        : query.getSort())
                                .sortBy(StringUtils.isEmpty(query.getOrder())
                                        ? SystemConstant.BusinessOrderType.DESC.name()
                                        : query.getOrder())
                                .build()))
                        .searchLimitValueObject(SearchLimitValueObject.builder()
                                .limit(query.getLimit()).offset(query.getOffset()).build())
                        .build();

        return goodsSearchRootService.idxSearchDocument(goodsSearchRoot)
                .flatMap(searchResult -> ReadSynchronizationUtils.getUserObjectMono(false)
                        .map(v -> v.getUserDataObjectEntity().getUserId())
                        .switchIfEmpty(Mono.just(-1L))
                        .flatMap(userId -> processSearchResult((TairSearchValueObject) searchResult,
                                userId)));
    }

}

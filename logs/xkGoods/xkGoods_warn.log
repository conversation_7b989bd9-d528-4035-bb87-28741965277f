[main:1]2025-08-21 13:56:40.094 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 13:56:40.094 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 13:56:44.482 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.524 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.545 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:45.123 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.127 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-21 13:56:45.200 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.208 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.216 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.239 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.256 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.263 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.305 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.342 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.359 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.364 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.402 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.444 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.450 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.456 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'liveObjectDao' of type [com.xk.infrastructure.cache.dao.object.LiveObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.461 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.464 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.469 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.476 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.485 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.489 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.495 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.497 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.497 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.499 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:50.628 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.628 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.637 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.637 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.645 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.645 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.655 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.655 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.663 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.663 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.671 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.672 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.679 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.679 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.687 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.687 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.696 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.696 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.704 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.704 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.713 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.713 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.723 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.723 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.733 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.733 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.741 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.742 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.751 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.751 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.761 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.761 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.877 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.877 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.887 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.887 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.897 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.897 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.906 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.906 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.917 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.917 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.927 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.927 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.946 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.946 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.957 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.957 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.966 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.966 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.976 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.976 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.988 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.988 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:52.087 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-21 13:56:53.132 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main:1]2025-08-21 13:56:57.398 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-21 13:56:59.110 WARN  [DefaultStdSchedulerFactoryBean:] - 没有可用的Jobs
[Thread-2:46]2025-08-21 13:57:56.306 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-2:46]2025-08-21 13:57:56.306 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-6:56]2025-08-21 13:57:56.306 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[Thread-6:56]2025-08-21 13:57:56.306 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
[Thread-4:50]2025-08-21 13:57:56.307 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-4:50]2025-08-21 13:57:56.307 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end

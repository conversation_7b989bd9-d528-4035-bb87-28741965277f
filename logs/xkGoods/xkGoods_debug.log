[main:1]2025-08-21 13:56:39.370 DEBUG [Loggers:] - Using Slf4j logging framework
[main:1]2025-08-21 13:56:39.371 DEBUG [Hooks:] - Enabling stacktrace debugging via onOperatorDebug
[main:1]2025-08-21 13:56:40.092 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 13:56:40.094 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 13:56:40.094 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 13:56:40.094 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 13:56:40.094 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
[main:1]2025-08-21 13:56:40.094 INFO  [NacosLogging:] - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
[background-preinit:44]2025-08-21 13:56:40.107 DEBUG [logging:] - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[background-preinit:44]2025-08-21 13:56:40.113 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[background-preinit:44]2025-08-21 13:56:40.114 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[background-preinit:44]2025-08-21 13:56:40.114 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[background-preinit:44]2025-08-21 13:56:40.114 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[background-preinit:44]2025-08-21 13:56:40.118 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[background-preinit:44]2025-08-21 13:56:40.140 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[background-preinit:44]2025-08-21 13:56:40.197 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[background-preinit:44]2025-08-21 13:56:40.201 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
[background-preinit:44]2025-08-21 13:56:40.201 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[background-preinit:44]2025-08-21 13:56:40.201 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[background-preinit:44]2025-08-21 13:56:40.201 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[background-preinit:44]2025-08-21 13:56:40.201 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 13:56:42.104 INFO  [XkGoodsServer:] - The following 10 profiles are active: "commons", "data", "jms", "cache", "http", "schedule", "proxy", "os", "server", "dev"
[main:1]2025-08-21 13:56:42.105 DEBUG [SpringApplication:685] - Loading source class com.xk.goods.server.XkGoodsServer,class org.springframework.cloud.bootstrap.BootstrapApplicationListener$BootstrapMarkerConfiguration
[main:1]2025-08-21 13:56:42.117 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 13:56:42.117 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] content: 
jobs:
    
[main:1]2025-08-21 13:56:42.117 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 13:56:42.117 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] content: 
configuration:
    webClient:
        loadBalanced:
            maxInMemorySize: 5120
            connectTimeoutMillis: 30000
            responseTimeout: 30
            user: admin
            password: 6RirvS
    validation: 
        key: 
            admin: 'Kth1HURxA5mWcGpBYcUg6GgXV0hATl6u'
            corp: 'ykkqhUH6b6WAdm4ipfusTAEZ0cnuBBtv'
            ios: 'XtPp4XG9NanpAGGp5WIaMYv0lmVMjrXb'
            android: 'HCgN0RwmXKVFlIKaDGi3tAfS4moSqURb'
            harmony: 'ngZ1E0yVv1gyZLuw7D6XiiupOzN1nInL'
        expiresTime: 60
        status: false
    settings:
        test:
            sig1: "RPBHwTNdRvymgnC5kEWS1EDHE7x06BaC"
        session:
            timeout: 28800
        user:
            nickname: "用户"
        os.bucket.cos.10:
            id: 1331099099
            name: "haoshang-test-1331099099"
            domain: "http://files.xmjihaoyun.cn"
            region: "ap-shanghai"
        os.bucket.oss.10:
            id: 1098742611924356
            name: "xka-test"
            domain: "https://files.xmjihaoyun.cn"
            region: "cn-hangzhou"
    queue:
        disruptor:
            maxDrainAttemptsBeforeShutdown: 200
            sleepMillisBetweenDrainAttempts: 50 
            ringBufferSize: 1024 
            timeout: 5000
            strategy: "TIMEOUT"
            sleepTimeNs: 10
            retries: 200
            waitTimeout: 10
            timeUnit: MILLISECONDS
            notifyProgressThreshold: 2048
    vertx:
        vertx:
            #eventLoopPoolSize: 8
            maxEventLoopExecuteTime: 3000000000
        eventbus:
        deployment:
                threadingModel: VIRTUAL_THREAD
                #blockPoolSize: 8
                instances: 1
    ncs:
        zookeeper: 
            connectionString: "*************:2181"
    scheduling:
        quartz:
            startupDelay:5
    os:
        cos:
            secretId: IKIDujOsAFH6tTr21oQq46vcItL2fBXkojU6
            secretKey: a1MXuGTWTK3c4LSNAk9MPIBxYyAY9yhH
            appId: 1331099099
            regionStr: ap-shanghai
        oss:
            secretId: LTAI5tEpzBC6KSKdcSTtHY2R
            secretKey: ******************************
            appId: 1098742611924356
            roleArn: acs:ram::1098742611924356:role/ramossdev1
            regionStr: cn-hangzhou
    jms: 
        rocketmq-producer:
            namesrvAddr: *************:9876
        rocketmq-consumer:
            namesrvAddr: *************:9876
        zmq-producer:
            nameServer: "MYDATA_IM_1:127.0.0.1:18100,MYDATA_IM_2:127.0.0.1:18200"
            zmqConnectUrl: "tcp://%s:%d"
            zmqRequestFormat: "requ|%s|%s"
            sendTimeout: 1000
            receiveTimeout: 1000
            reload: false
    http:
        defaultHttpClient: 
            connectTimeout: 5000 
            socketTimeout: 5000 
            connTimeToLive: 3
            retry: 2
            busiRetry: 2
    redis:
        zmqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2
            jmxEnabled: false
            maxTotal: 10000
        seqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3
            jmxEnabled: false
            maxTotal: 10000
        tableRedisClient: 
            connectionString: redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379,redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379
            jmxEnabled: false
            maxTotal: 10000
        busiRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379
            jmxEnabled: false
            maxTotal: 10000
    jdbc:
        xk_config: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: **********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_goods: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_auth: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_stock: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
    sharding:
        datasource.mapping:
            xk_log: 
                module: log
                dataSourceKeys: xk_log
                startId: 0
            xk_config: 
                module: config
                dataSourceKeys: xk_config
                startId: 0
            xk_goods: 
                module: goods
                dataSourceKeys: xk_goods
                startId: 0
            xk_auth: 
                module: auth
                dataSourceKeys: xk_auth
                startId: 0
            xk_stock:
                module: stock
                dataSourceKeys: xk_stock
                startId: 0    
        module.mapping:
            log: 
                tableRule: c_.*,log_.*
            config: 
                tableRule: t_.*
            goods: 
                tableRule: g_.*
            auth: 
                tableRule: auth_.*
            stock:
                tableRule: s_.*
[main:1]2025-08-21 13:56:42.117 DEBUG [AnnotationConfigReactiveWebServerApplicationContext:674] - Refreshing org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@38fb151a
[main:1]2025-08-21 13:56:43.258 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.259 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorDeleteEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.260 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorUpdateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.261 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.corp.CorpCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.261 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportCreateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.262 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportDeleteListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.262 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportUpdateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.263 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.CreateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.264 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.DeleteGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.264 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.GoodsStockEmptyListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.265 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.265 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.265 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.266 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductFirstListingListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.266 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductRemainRandomListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.267 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.267 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateRemainStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.268 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.269 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.order.OrderPaidListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.270 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScoreListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.270 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScorePagerListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.271 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.UpdateScoreRuleListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.272 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.CreateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.273 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.DeleteSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.273 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.UpdateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.274 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.CreateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.275 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.DeleteTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.275 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.UpdateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.858 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 13:56:43.871 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:43.871 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:43.873 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 13:56:43.935 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'userObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 13:56:43.936 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'corpObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 13:56:43.939 DEBUG [EncryptResourceManager:] - Registering class: com.xk.goods.infrastructure.data.po.random.GDistributionItemPO
[main:1]2025-08-21 13:56:43.939 DEBUG [EncryptScannerRegistrar:] - Registered specified classes: [com.xk.goods.infrastructure.data.po.random.GDistributionItemPO]
[main:1]2025-08-21 13:56:43.953 INFO  [CacheData:] - config listener notify warn timeout millis use default 60000 millis 
[main:1]2025-08-21 13:56:43.953 INFO  [CacheData:] - nacos.cache.data.init.snapshot = true 
[main:1]2025-08-21 13:56:43.954 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-dev.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 13:56:43.960 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 13:56:44.471 DEBUG [LogFactory:] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[main:1]2025-08-21 13:56:44.482 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 INFO  [SystemParamTableHolder:] - System settings initializing.
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.524 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.545 INFO  [RoutingConfigHolder:] - {log-routing: [xk_log, 0 - 9223372036854775807],goods-routing: [xk_goods, 0 - 9223372036854775807],auth-routing: [xk_auth, 0 - 9223372036854775807],config-routing: [xk_config, 0 - 9223372036854775807],stock-routing: [xk_stock, 0 - 9223372036854775807] }
[main:1]2025-08-21 13:56:44.545 DEBUG [ShardingNodeExecutor:] - Routing-Config Holder initialization is complete.
[main:1]2025-08-21 13:56:44.545 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.698 INFO  [GenericScope:] - BeanFactory id=2e62ea10-36c6-3108-956c-d9a46c3a22d6
[main:1]2025-08-21 13:56:45.123 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.127 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:zookeeper.version=3.6.4--d65253dcf68e9097c6e95a126463fd5fdeb4521c, built on 12/18/2022 18:10 GMT
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:host.name=*************
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.version=21.0.7
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.vendor=Oracle Corporation
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.home=C:\Program Files\Java\jdk-21
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.class.path=D:\code\xk\xk-goods\xk-goods-server\target\classes;D:\maven\repository\com\xk\xk-start-server\0.0.1-SNAPSHOT\xk-start-server-0.0.1-20250818.091612-116.jar;D:\maven\repository\com\myco\mydata\mydata-start-server\0.0.1-SNAPSHOT\mydata-start-server-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-core\0.0.1-SNAPSHOT\mydata-start-domain-core-0.0.1-20250819.023657-99.jar;D:\maven\repository\com\myco\mydata\mydata-start-commons\0.0.1-SNAPSHOT\mydata-start-commons-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\myco-framework-6\0.0.1-SNAPSHOT\myco-framework-6-0.0.1-20250819.023657-80.jar;D:\maven\repository\com\alibaba\nacos\nacos-client\2.4.3\nacos-client-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-auth-plugin\2.4.3\nacos-auth-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-encryption-plugin\2.4.3\nacos-encryption-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-logback-adapter-12\2.4.3\nacos-logback-adapter-12-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\logback-adapter\1.1.3\logback-adapter-1.1.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-log4j2-adapter\2.4.3\nacos-log4j2-adapter-2.4.3.jar;D:\maven\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\maven\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\maven\repository\org\zeromq\jeromq\0.6.0\jeromq-0.6.0.jar;D:\maven\repository\eu\neilalexander\jnacl\1.0.0\jnacl-1.0.0.jar;D:\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\maven\repository\org\aspectj\aspectjrt\1.9.22.1\aspectjrt-1.9.22.1.jar;D:\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\maven\repository\org\springframework\spring-jdbc\6.2.3\spring-jdbc-6.2.3.jar;D:\maven\repository\org\apache\curator\curator-framework\4.3.0\curator-framework-4.3.0.jar;D:\maven\repository\org\apache\curator\curator-client\4.3.0\curator-client-4.3.0.jar;D:\maven\repository\com\google\guava\guava\27.0.1-jre\guava-27.0.1-jre.jar;D:\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven\repository\org\checkerframework\checker-qual\2.5.2\checker-qual-2.5.2.jar;D:\maven\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;D:\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;D:\maven\repository\org\apache\curator\curator-recipes\4.3.0\curator-recipes-4.3.0.jar;D:\maven\repository\org\apache\zookeeper\zookeeper\3.6.4\zookeeper-3.6.4.jar;D:\maven\repository\org\apache\zookeeper\zookeeper-jute\3.6.4\zookeeper-jute-3.6.4.jar;D:\maven\repository\org\apache\yetus\audience-annotations\0.13.0\audience-annotations-0.13.0.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final.jar;D:\maven\repository\org\mozilla\rhino\1.8.0\rhino-1.8.0.jar;D:\maven\repository\org\apache\groovy\groovy\4.0.26\groovy-4.0.26.jar;D:\maven\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;D:\maven\repository\cglib\cglib-nodep\3.3.0\cglib-nodep-3.3.0.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2\2.0.57\fastjson2-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson\2.0.57\fastjson-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2-extension\2.0.57\fastjson2-extension-2.0.57.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.18.2\jackson-dataformat-yaml-2.18.2.jar;D:\maven\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;D:\maven\repository\joda-time\joda-time\2.14.0\joda-time-2.14.0.jar;D:\maven\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-event\0.0.1-SNAPSHOT\mydata-start-domain-event-0.0.1-20250819.023657-92.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-enum\0.0.1-SNAPSHOT\mydata-start-domain-enum-0.0.1-20250819.023657-93.jar;D:\maven\repository\org\hibernate\validator\hibernate-validator\9.0.1.Final\hibernate-validator-9.0.1.Final.jar;D:\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\maven\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-interfaces\0.0.1-SNAPSHOT\mydata-start-interfaces-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\springframework\spring-webflux\6.2.3\spring-webflux-6.2.3.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-http\1.2.7\reactor-netty-http-1.2.7.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-classes-macos\4.2.2.Final\netty-resolver-dns-classes-macos-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-classes-epoll\4.2.2.Final\netty-transport-classes-epoll-4.2.2.Final.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-core\1.2.7\reactor-netty-core-1.2.7.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.2.0\spring-cloud-starter-loadbalancer-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-loadbalancer\4.2.0\spring-cloud-loadbalancer-4.2.0.jar;D:\maven\repository\io\projectreactor\addons\reactor-extra\3.5.2\reactor-extra-3.5.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-cache\3.4.3\spring-boot-starter-cache-3.4.3.jar;D:\maven\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-joda\2.18.3\jackson-datatype-joda-2.18.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;D:\maven\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;D:\maven\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-application\0.0.1-SNAPSHOT\mydata-start-application-0.0.1-20250819.023657-86.jar;D:\maven\repository\com\myco\mydata\mydata-start-gateway\0.0.1-SNAPSHOT\mydata-start-gateway-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-http\0.0.1-SNAPSHOT\mydata-start-infrastructure-http-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;D:\maven\repository\org\apache\httpcomponents\httpclient\4.5.9\httpclient-4.5.9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-data\0.0.1-SNAPSHOT\mydata-start-infrastructure-data-0.0.1-20250819.023657-93.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-commons\0.0.1-SNAPSHOT\mydata-start-infrastructure-commons-0.0.1-20250819.023657-89.jar;D:\maven\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;D:\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven\repository\com\lmax\disruptor\3.4.4\disruptor-3.4.4.jar;D:\maven\repository\com\google\zxing\core\3.5.3\core-3.5.3.jar;D:\maven\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.18.3\jackson-dataformat-xml-2.18.3.jar;D:\maven\repository\org\codehaus\woodstox\stax2-api\4.2.2\stax2-api-4.2.2.jar;D:\maven\repository\com\fasterxml\woodstox\woodstox-core\7.0.0\woodstox-core-7.0.0.jar;D:\maven\repository\io\github\jopenlibs\vault-java-driver\6.2.0\vault-java-driver-6.2.0.jar;D:\maven\repository\com\mysql\mysql-connector-j\9.3.0\mysql-connector-j-9.3.0.jar;D:\maven\repository\com\google\protobuf\protobuf-java\4.29.0\protobuf-java-4.29.0.jar;D:\maven\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\maven\repository\org\springframework\spring-context-support\6.2.3\spring-context-support-6.2.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2023.0.1.3\spring-cloud-starter-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2023.0.1.3\spring-cloud-alibaba-commons-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-alibaba-nacos-config\2023.0.1.3\spring-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2023.0.1.3\spring-cloud-starter-alibaba-nacos-discovery-2023.0.1.3.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-jms\0.0.1-SNAPSHOT\mydata-start-infrastructure-jms-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-client\4.9.8\rocketmq-client-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-common\4.9.8\rocketmq-common-4.9.8.jar;D:\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-acl\4.9.8\rocketmq-acl-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-remoting\4.9.8\rocketmq-remoting-4.9.8.jar;D:\maven\repository\io\netty\netty-all\4.2.2.Final\netty-all-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec\4.2.2.Final\netty-codec-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-haproxy\4.2.2.Final\netty-codec-haproxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http3\4.2.2.Final\netty-codec-http3-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-memcache\4.2.2.Final\netty-codec-memcache-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-mqtt\4.2.2.Final\netty-codec-mqtt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-redis\4.2.2.Final\netty-codec-redis-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-smtp\4.2.2.Final\netty-codec-smtp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-stomp\4.2.2.Final\netty-codec-stomp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-xml\4.2.2.Final\netty-codec-xml-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-protobuf\4.2.2.Final\netty-codec-protobuf-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-marshalling\4.2.2.Final\netty-codec-marshalling-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-ssl-ocsp\4.2.2.Final\netty-handler-ssl-ocsp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-rxtx\4.2.2.Final\netty-transport-rxtx-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-sctp\4.2.2.Final\netty-transport-sctp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-udt\4.2.2.Final\netty-transport-udt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-kqueue\4.2.2.Final\netty-transport-classes-kqueue-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-io_uring\4.2.2.Final\netty-transport-classes-io_uring-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-classes-quic\4.2.2.Final\netty-codec-classes-quic-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-windows-x86_64.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-logging\4.9.8\rocketmq-logging-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.8\rocketmq-srvutil-4.9.8.jar;D:\maven\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;D:\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven\repository\org\springframework\kafka\spring-kafka\3.3.7\spring-kafka-3.3.7.jar;D:\maven\repository\org\springframework\spring-messaging\6.2.3\spring-messaging-6.2.3.jar;D:\maven\repository\org\springframework\spring-tx\6.2.3\spring-tx-6.2.3.jar;D:\maven\repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar;D:\maven\repository\org\apache\kafka\kafka-clients\3.9.1\kafka-clients-3.9.1.jar;D:\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;D:\maven\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\maven\repository\io\projectreactor\kafka\reactor-kafka\1.3.23\reactor-kafka-1.3.23.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-cache\0.0.1-SNAPSHOT\mydata-start-infrastructure-cache-0.0.1-20250819.023657-87.jar;D:\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-os\0.0.1-SNAPSHOT\mydata-start-infrastructure-os-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\qcloud\cos_api\5.6.242\cos_api-5.6.242.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-kms\3.1.1138\tencentcloud-sdk-java-kms-3.1.1138.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-common\3.1.1138\tencentcloud-sdk-java-common-3.1.1138.jar;D:\maven\repository\com\squareup\okhttp3\okhttp\3.12.13\okhttp-3.12.13.jar;D:\maven\repository\com\squareup\okio\okio\1.15.0\okio-1.15.0.jar;D:\maven\repository\com\squareup\okhttp3\logging-interceptor\3.12.13\logging-interceptor-3.12.13.jar;D:\maven\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\maven\repository\com\thoughtworks\xstream\xstream\1.4.21\xstream-1.4.21.jar;D:\maven\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\maven\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\maven\repository\com\auth0\java-jwt\4.4.0\java-jwt-4.4.0.jar;D:\maven\repository\com\qcloud\cos-sts_api\3.1.1\cos-sts_api-3.1.1.jar;D:\maven\repository\com\aliyun\alibabacloud-sts20150401\1.0.7\alibabacloud-sts20150401-1.0.7.jar;D:\maven\repository\com\aliyun\aliyun-gateway-pop\0.2.15-beta\aliyun-gateway-pop-0.2.15-beta.jar;D:\maven\repository\com\aliyun\darabonba-java-core\0.2.15-beta\darabonba-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-http-apache\0.2.15-beta\aliyun-http-apache-0.2.15-beta.jar;D:\maven\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;D:\maven\repository\com\aliyun\aliyun-java-core\0.2.15-beta\aliyun-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-auth\0.2.15-beta\aliyun-java-auth-0.2.15-beta.jar;D:\maven\repository\com\aliyun\oss\aliyun-sdk-oss\3.18.2\aliyun-sdk-oss-3.18.2.jar;D:\maven\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\maven\repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-core\4.7.3\aliyun-java-sdk-core-4.7.3.jar;D:\maven\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\maven\repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar;D:\maven\repository\commons-logging\commons-logging\1.3.4\commons-logging-1.3.4.jar;D:\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\maven\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\maven\repository\com\aliyun\java-trace-api\0.2.11-beta\java-trace-api-0.2.11-beta.jar;D:\maven\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;D:\maven\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-proxy\0.0.1-SNAPSHOT\mydata-start-infrastructure-proxy-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-security\0.0.1-SNAPSHOT\mydata-start-infrastructure-security-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-validation\0.0.1-SNAPSHOT\mydata-start-infrastructure-validation-0.0.1-20250819.023657-86.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.4.3\spring-boot-starter-validation-3.4.3.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.36\tomcat-embed-el-10.1.36.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-webflux\3.4.3\spring-boot-starter-webflux-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.4.3\spring-boot-starter-json-3.4.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.4.3\spring-boot-starter-reactor-netty-3.4.3.jar;D:\maven\repository\org\springframework\spring-web\6.2.3\spring-web-6.2.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\4.2.0\spring-cloud-starter-bootstrap-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter\4.2.0\spring-cloud-starter-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;D:\maven\repository\org\springframework\security\spring-security-crypto\6.4.3\spring-security-crypto-6.4.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-commons\4.2.0\spring-cloud-commons-4.2.0.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk18on\1.78.1\bcprov-jdk18on-1.78.1.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.3\spring-boot-starter-actuator-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.3\spring-boot-actuator-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator\3.4.3\spring-boot-actuator-3.4.3.jar;D:\maven\repository\io\micrometer\micrometer-observation\1.14.4\micrometer-observation-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-commons\1.14.4\micrometer-commons-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-jakarta9\1.14.4\micrometer-jakarta9-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-core\1.14.4\micrometer-core-1.14.4.jar;D:\maven\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;D:\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven\repository\com\xk\xk-start-application\0.0.1-SNAPSHOT\xk-start-application-0.0.1-20250818.091612-115.jar;D:\maven\repository\com\xk\xk-start-domain-core\0.0.1-SNAPSHOT\xk-start-domain-core-0.0.1-20250818.091612-127.jar;D:\maven\repository\com\xk\xk-start-domain-event\0.0.1-SNAPSHOT\xk-start-domain-event-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-interfaces\0.0.1-SNAPSHOT\xk-start-interfaces-0.0.1-20250818.091612-122.jar;D:\maven\repository\com\xk\xk-start-domain-enum\0.0.1-SNAPSHOT\xk-start-domain-enum-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-infrastructure\0.0.1-SNAPSHOT\xk-start-infrastructure-0.0.1-20250818.091612-118.jar;D:\maven\repository\com\alibaba\easyexcel\4.0.3\easyexcel-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-core\4.0.3\easyexcel-core-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;D:\maven\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;D:\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\maven\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\maven\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;D:\maven\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;D:\maven\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;D:\maven\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;D:\maven\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;D:\maven\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;D:\maven\repository\org\apache\commons\commons-csv\1.11.0\commons-csv-1.11.0.jar;D:\maven\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\maven\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\maven\repository\com\xk\xk-start-gateway\0.0.1-SNAPSHOT\xk-start-gateway-0.0.1-20250818.091612-121.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-openfeign\4.2.0\spring-cloud-starter-openfeign-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-openfeign-core\4.2.0\spring-cloud-openfeign-core-4.2.0.jar;D:\maven\repository\io\github\openfeign\feign-form-spring\13.5\feign-form-spring-13.5.jar;D:\maven\repository\io\github\openfeign\feign-form\13.5\feign-form-13.5.jar;D:\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\maven\repository\io\github\openfeign\feign-core\13.5\feign-core-13.5.jar;D:\maven\repository\io\github\openfeign\feign-slf4j\13.5\feign-slf4j-13.5.jar;D:\code\xk\xk-goods\xk-goods-application\target\classes;D:\maven\repository\com\myco\mydata\config\mydata-config-application\0.0.1-SNAPSHOT\mydata-config-application-0.0.1-20250719.075944-7.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-domain\0.0.1-SNAPSHOT\mydata-config-domain-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-interfaces\0.0.1-SNAPSHOT\mydata-config-interfaces-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-gateway\0.0.1-SNAPSHOT\mydata-config-gateway-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-infrastructure\0.0.1-SNAPSHOT\mydata-config-infrastructure-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-event\0.0.1-SNAPSHOT\mydata-start-infrastructure-event-0.0.1-20250819.023657-88.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-schedule\0.0.1-SNAPSHOT\mydata-start-infrastructure-schedule-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\quartz-scheduler\quartz\2.5.0\quartz-2.5.0.jar;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-core\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-event\target\classes;D:\code\xk\xk-goods\xk-goods-interfaces\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-enum\target\classes;D:\code\xk\xk-goods\xk-goods-gateway\target\classes;D:\maven\repository\com\xk\corp\xk-corp-interfaces\0.0.1-SNAPSHOT\xk-corp-interfaces-0.0.1-20250728.125932-33.jar;D:\maven\repository\com\xk\corp\xk-corp-domain-enum\0.0.1-SNAPSHOT\xk-corp-domain-enum-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\message\xk-message-domain-enum\0.0.1-SNAPSHOT\xk-message-domain-enum-0.0.1-20250819.084230-25.jar;D:\maven\repository\com\xk\acct\xk-acct-interfaces\0.0.1-SNAPSHOT\xk-acct-interfaces-0.0.1-20250818.092336-74.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-enum\0.0.1-SNAPSHOT\xk-acct-domain-enum-0.0.1-20250813.030141-70.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-enum\target\classes;D:\maven\repository\com\xk\acct\xk-acct-domain-event\0.0.1-SNAPSHOT\xk-acct-domain-event-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\config\xk-config-interfaces\0.0.1-SNAPSHOT\xk-config-interfaces-0.0.1-20250805.030408-16.jar;D:\maven\repository\com\xk\config\xk-config-domain-enum\0.0.1-SNAPSHOT\xk-config-domain-enum-0.0.1-20250805.030408-16.jar;D:\code\xk\xk-third-party\xk-third-party-interfaces\target\classes;D:\maven\repository\com\xk\search\xk-search-interfaces\0.0.1-SNAPSHOT\xk-search-interfaces-0.0.1-20250818.123046-199.jar;D:\maven\repository\com\xk\search\xk-search-domain-enum\0.0.1-SNAPSHOT\xk-search-domain-enum-0.0.1-20250818.123046-208.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-enum\target\classes;D:\maven\repository\com\xk\corp\xk-corp-domain-event\0.0.1-SNAPSHOT\xk-corp-domain-event-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-enum\0.0.1-SNAPSHOT\xk-auth-domain-enum-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\promotion\xk-promotion-interfaces\0.0.1-SNAPSHOT\xk-promotion-interfaces-0.0.1-20250804.073817-18.jar;D:\maven\repository\com\xk\promotion\xk-promotion-domain-enum\0.0.1-SNAPSHOT\xk-promotion-domain-enum-0.0.1-20250804.073817-16.jar;D:\code\xk\xk-goods\xk-goods-infrastructure\target\classes;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-spring-boot-starter\1.4.8\mapstruct-plus-spring-boot-starter-1.4.8.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus\1.4.8\mapstruct-plus-1.4.8.jar;D:\maven\repository\org\mapstruct\mapstruct\1.5.5.Final\mapstruct-1.5.5.Final.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-object-convert\1.4.8\mapstruct-plus-object-convert-1.4.8.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.3\spring-boot-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar;D:\maven\repository\org\springframework\spring-context\6.2.3\spring-context-6.2.3.jar;D:\maven\repository\org\springframework\spring-aop\6.2.3\spring-aop-6.2.3.jar;D:\maven\repository\org\springframework\spring-beans\6.2.3\spring-beans-6.2.3.jar;D:\maven\repository\org\springframework\spring-expression\6.2.3\spring-expression-6.2.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.4.3\spring-boot-starter-3.4.3.jar;D:\maven\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;D:\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\maven\repository\org\springframework\spring-core\6.2.3\spring-core-6.2.3.jar;D:\maven\repository\org\springframework\spring-jcl\6.2.3\spring-jcl-6.2.3.jar;D:\maven\repository\io\vertx\vertx-core\5.0.1\vertx-core-5.0.1.jar;D:\maven\repository\io\vertx\vertx-core-logging\5.0.1\vertx-core-logging-5.0.1.jar;D:\maven\repository\io\netty\netty-common\4.2.2.Final\netty-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-buffer\4.2.2.Final\netty-buffer-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport\4.2.2.Final\netty-transport-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler\4.2.2.Final\netty-handler-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-unix-common\4.2.2.Final\netty-transport-native-unix-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-base\4.2.2.Final\netty-codec-base-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-proxy\4.2.2.Final\netty-handler-proxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-socks\4.2.2.Final\netty-codec-socks-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http\4.2.2.Final\netty-codec-http-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-compression\4.2.2.Final\netty-codec-compression-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http2\4.2.2.Final\netty-codec-http2-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver\4.2.2.Final\netty-resolver-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver-dns\4.2.2.Final\netty-resolver-dns-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-dns\4.2.2.Final\netty-codec-dns-4.2.2.Final.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-log4j2\3.4.3\spring-boot-starter-log4j2-3.4.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.3\log4j-slf4j2-impl-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-jul\2.24.3\log4j-jul-2.24.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.3\spring-boot-configuration-processor-3.4.3.jar;D:\maven\repository\io\projectreactor\reactor-core\3.7.7\reactor-core-3.7.7.jar;D:\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\lib\idea_rt.jar
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.library.path=C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\bin;C:\Program Files\JetBrains\PyCharm 2025.1.3.1\bin;;.
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.compiler=<NA>
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.name=Windows 11
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.arch=amd64
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.version=10.0
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:user.name=ShiJia
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:user.home=C:\Users\<USER>\code\xk
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.memory.free=91MB
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.memory.max=8048MB
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.memory.total=180MB
[main:1]2025-08-21 13:56:45.183 INFO  [CuratorFrameworkImpl:] - Starting
[main:1]2025-08-21 13:56:45.183 DEBUG [CuratorZookeeperClient:] - Starting
[main:1]2025-08-21 13:56:45.183 DEBUG [ConnectionState:] - Starting
[main:1]2025-08-21 13:56:45.183 DEBUG [ConnectionState:] - reset
[main:1]2025-08-21 13:56:45.184 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@28d61fc
[main:1]2025-08-21 13:56:45.187 INFO  [X509Util:] - Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
[main:1]2025-08-21 13:56:45.191 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main:1]2025-08-21 13:56:45.195 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main:1]2025-08-21 13:56:45.200 INFO  [CuratorFrameworkImpl:] - Default schema
[main:1]2025-08-21 13:56:45.200 INFO  [ZookeeperClientFactoryBean:] - ZK connection is successful.
[main:1]2025-08-21 13:56:45.200 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.208 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.216 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.239 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.256 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.263 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.305 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.338 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.ncs.zookeeper.lockTimeout.user' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type Integer
[main:1]2025-08-21 13:56:45.342 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.359 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-producer.rmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:45.359 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.364 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.402 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.444 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.450 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.456 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'liveObjectDao' of type [com.xk.infrastructure.cache.dao.object.LiveObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.461 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.464 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.469 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.476 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.485 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.487 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.readonlyRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:45.487 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.requiredRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [create*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [revise*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [sync*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [add*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [update*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [handle] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [incr*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [amend*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [save*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [persist*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [remove*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [terminate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [delete*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [insert*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [commit*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [merge*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [apply*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [initiate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [alter*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [cancel*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [mod*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [retire*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [store*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.489 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.494 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:45.495 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.497 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.497 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 13:56:45.497 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.499 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:45.499 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.794 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.level: simple
[main:1]2025-08-21 13:56:45.794 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.targetRecords: 4
[main:1]2025-08-21 13:56:45.867 DEBUG [GlobalEventExecutor:] - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[main:1]2025-08-21 13:56:46.732 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.zmq-producer.zmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:47.319 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.redis.tableRedisClientRef' in PropertySource 'Config resource 'class path resource [application-data.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:49.948 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:49.948 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:49.948 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:49.948 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:49.949 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:50.628 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.628 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.637 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.637 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.645 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.645 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.655 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.655 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.663 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.663 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.671 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.672 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.679 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.679 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.687 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.687 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.696 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.696 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.704 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.704 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.713 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.713 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.723 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.723 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.733 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.733 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.741 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.742 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.751 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.751 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.761 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.761 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.877 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.877 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.887 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.887 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.897 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.897 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.906 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.906 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.917 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.917 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.927 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.927 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.946 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.946 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.957 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.957 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.966 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.966 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.976 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.976 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.988 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.988 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:51.358 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:51.359 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:51.359 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:52.087 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-21 13:56:52.130 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[main:1]2025-08-21 13:56:52.132 DEBUG [AbstractConfigurationImpl:] - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
[main:1]2025-08-21 13:56:52.133 DEBUG [AbstractConfigurationImpl:] - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
[main:1]2025-08-21 13:56:52.135 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[main:1]2025-08-21 13:56:52.135 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via user class loader
[main:1]2025-08-21 13:56:52.135 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[main:1]2025-08-21 13:56:52.135 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[main:1]2025-08-21 13:56:52.136 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[main:1]2025-08-21 13:56:52.136 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[main:1]2025-08-21 13:56:52.137 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[main:1]2025-08-21 13:56:52.138 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
[main:1]2025-08-21 13:56:52.138 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[main:1]2025-08-21 13:56:52.138 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[main:1]2025-08-21 13:56:52.138 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[main:1]2025-08-21 13:56:52.138 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 13:56:53.132 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.276 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.276 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.289 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:54081, server: *************/*************:2181
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.306 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x100000114063391, negotiated timeout = 40000
[main-EventThread:87]2025-08-21 13:56:54.307 DEBUG [ConnectionState:] - Negotiated session timeout: 40000
[main-EventThread:87]2025-08-21 13:56:54.310 INFO  [ConnectionStateManager:] - State change: CONNECTED
[main-EventThread:87]2025-08-21 13:56:54.310 DEBUG [CuratorFrameworkImpl:] - Clearing sleep for 10 operations
[Curator-ConnectionStateManager-0:85]2025-08-21 13:56:54.311 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: CONNECTED
[main-EventThread:87]2025-08-21 13:56:54.327 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 13:56:54.327 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main-EventThread:87]2025-08-21 13:56:54.327 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 13:56:54.327 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main:1]2025-08-21 13:56:55.139 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorCreateEvent
[main:1]2025-08-21 13:56:55.179 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorDeleteEvent
[main:1]2025-08-21 13:56:55.185 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorUpdateEvent
[main:1]2025-08-21 13:56:55.192 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_CORP-CORP-CorpCreateEvent
[main:1]2025-08-21 13:56:55.199 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportCreateEvent
[main:1]2025-08-21 13:56:55.204 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportDeleteEvent
[main:1]2025-08-21 13:56:55.211 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportUpdateEvent
[main:1]2025-08-21 13:56:55.219 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateGoodsEvent
[main:1]2025-08-21 13:56:55.227 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteGoodsEvent
[main:1]2025-08-21 13:56:55.234 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GoodsStockEmptyEvent
[main:1]2025-08-21 13:56:55.241 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 13:56:55.250 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent
[main:1]2025-08-21 13:56:55.257 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 13:56:55.263 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductFirstListingEvent
[main:1]2025-08-21 13:56:55.269 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductRemainRandomEvent
[main:1]2025-08-21 13:56:55.275 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateGoodsEvent
[main:1]2025-08-21 13:56:55.281 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateRemainStockEvent
[main:1]2025-08-21 13:56:55.296 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateStockEvent
[main:1]2025-08-21 13:56:55.302 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_ORDER-ORDER-OrderPaidEvent
[main:1]2025-08-21 13:56:55.308 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScoreEvent
[main:1]2025-08-21 13:56:55.313 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScorePagerEvent
[main:1]2025-08-21 13:56:55.318 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateScoreRuleEvent
[main:1]2025-08-21 13:56:55.324 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateSeriesCateEvent
[main:1]2025-08-21 13:56:55.331 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteSeriesCateEvent
[main:1]2025-08-21 13:56:55.337 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateSeriesCateEvent
[main:1]2025-08-21 13:56:55.343 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateTeamMemberEvent
[main:1]2025-08-21 13:56:55.350 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteTeamMemberEvent
[main:1]2025-08-21 13:56:55.356 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateTeamMemberEvent
[main:1]2025-08-21 13:56:55.395 DEBUG [AutoConfigurationPackages:213] - @EnableAutoConfiguration was declared on a class in the package 'com.myco.mydata.server'. Automatic @Repository and @Entity scanning is enabled.
[main:1]2025-08-21 13:56:55.523 DEBUG [Mappings:] - 
	c.x.g.a.q.g.MerchantProductAppQueryServiceImpl:
	{POST /goods/merchant-product/app/query/buyer/detail}: searchBuyerDetail(Mono)
	{POST /goods/merchant-product/app/query/pick/stock}: searchPickStock(Mono)
	{POST /goods/merchant-product/app/query/pick/category}: searchPickCategory(Mono)
	{POST /goods/merchant-product/app/query/promotion/detail}: searchPromotionDetail(Mono)
	{POST /goods/merchant-product/app/query/goodsRule}: goodsRule(Mono)
[main:1]2025-08-21 13:56:55.530 DEBUG [RequestMappingHandlerMapping:164] - 5 mappings in 'requestMappingHandlerMapping'
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/color/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorQueryServiceRoutingConfig$$Lambda/0x0000020801dca440@7a7ebcf3
 ((POST && /inner/search) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorQueryServiceRoutingConfig$$Lambda/0x0000020801dcb090@7e64a758
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/color => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x0000020801dcb9d8@53c29dad
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x0000020801dcbbf0@731fd11e
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x0000020801dcbe08@7c8962aa
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/gift/report/query => {
 ((POST && /sync) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcc020@278c5404
 ((POST && /pre/query/current) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcc238@60648aad
 ((POST && /pre/query) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcc450@6d7e209e
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcc668@5ad583a9
 ((POST && /search/update/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcc880@1fbcd083
 ((POST && /inner/search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcca98@5b49003b
 ((POST && /inner/search) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x0000020801dcccb0@47afb11c
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/gift/report => {
 ((POST && /multi/create) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dccec8@3191ba8b
 ((POST && /lock) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcd0e0@48505cf2
 ((POST && /unlock) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcd2f8@5b8bc51b
 ((POST && /next) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcd510@6196a827
 ((POST && /pre/create) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcd728@5def137c
 ((POST && /multi/execute) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcd940@179273ff
 ((POST && /single/execute) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcdb58@12b4cf0b
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcdd70@46379aea
 ((POST && /count/reset) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x0000020801dcdf88@63e605a3
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/product-category/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dce1a0@676f8fab
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/product-category/app/query => {
 ((POST && /search/parent) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dce3b8@5397c8da
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/query => {
 ((POST && /material/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dce5d0@290df888
 ((POST && /mall/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dce7e8@7a953b17
 ((POST && /collectible/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcea00@611ce139
 ((POST && /merchant/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcec18@ca024d8
 ((POST && /merchant/ewd/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcee30@4aaf547c
 ((POST && /specification/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcf048@19b183bd
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/materials-product/query => {
 ((POST && /app/search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcf260@1f22ac9b
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcf478@12a9cd43
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcf690@221577de
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/mall-product/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcf8a8@37aca44a
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcfac0@3de08050
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/collectible-card/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dcfcd8@617f817a
 ((POST && /search/show/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0000@68ddb10c
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0218@3a81d6a9
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/merchant-product/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0430@3da33a9
 ((POST && /search/corp/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0648@1eb306f9
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0860@1be391f2
 ((POST && /corp/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0a78@2fe328b9
 ((POST && /inner/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0c90@7df60197
 ((POST && /status/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd0ea8@7b74541e
 ((POST && /status/corp/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd10c0@7c9f2683
 ((POST && /corp/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd12d8@4a8f6f49
 ((POST && /show/price) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd14f0@7383536
 ((POST && /pay/price) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd1708@5eb5556b
 ((POST && /inner/spec/serial) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd1920@5b9bdb00
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/merchant-product/app/query => {
 ((POST && /promotion/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd1b38@6adf021d
 ((POST && /buyer/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd1d50@2c080bf5
 ((POST && /pick/category) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd1f68@58620d12
 ((POST && /pick/stock) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd2180@5da48ec5
 ((POST && /goodsRule) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd2398@4f79a086
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/live/query => {
 ((POST && /replay) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x0000020801dd25b0@23c25d3f
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/product-category => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd27c8@6b00dec8
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd29e0@12aba7cb
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd2bf8@********
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/materials-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd2e10@21ced418
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3028@4c55b877
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3240@38fdbc9
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3458@31adc338
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3670@6694bf01
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/mall-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3888@7e325a4
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3aa0@7acff702
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3cb8@25245f60
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd3ed0@668b9072
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd40e8@78db5702
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4300@7d29d8fc
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/collectible-card => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4518@7a715766
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4730@4043405a
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4948@62027c17
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4b60@51dfb693
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4d78@216501a9
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd4f90@7bfec44f
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/merchant-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd51a8@aedede3
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd53c0@26aab903
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd55d8@2f4b8c36
 ((POST && /update/status) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd57f0@221ae92c
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd5a08@477d8a79
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd5c20@34ec4134
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd5e38@47b541a3
 ((POST && /audit/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd6050@329383f0
 ((POST && /recycle/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd6268@1916f999
 ((POST && /copy/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd6480@4f25fe6e
 ((POST && /update/publicity) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd6698@22f00670
 ((POST && /update/remain/random) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd68b0@c529ddd
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/merchant/score/rule => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x0000020801dd6ac8@222667ff
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/object => {
 ((POST && /getGoodsObject) && Accept: application/json) -> com.xk.goods.server.endpoints.object.ObjectQueryRoutingConfig$$Lambda/0x0000020801dd6ce0@42103b5b
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/random-distribution => {
 ((POST && /get-item) && Accept: application/json) -> com.xk.goods.server.endpoints.random.RandomDistributionServiceRoutingConfig$$Lambda/0x0000020801dd6ef8@60f166ef
 ((POST && /callback-item) && Accept: application/json) -> com.xk.goods.server.endpoints.random.RandomDistributionServiceRoutingConfig$$Lambda/0x0000020801dd7110@7855b359
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/score/rule/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreQueryRoutingConfig$$Lambda/0x0000020801dd7328@695d2a33
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/score/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreQueryRoutingConfig$$Lambda/0x0000020801dd7540@2ad41ea0
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/score/rule => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreServiceRoutingConfig$$Lambda/0x0000020801dd7758@4179d30b
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/score => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreServiceRoutingConfig$$Lambda/0x0000020801dd7970@14e37f06
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/group-category/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd7b88@14efd31b
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/group/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd7da0@1ec4fe38
 ((POST && /searchQuick) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8000@112a8760
 ((POST && /searchOriginalDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8218@1ebec10c
 ((POST && /searchSpecialDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8430@7423becb
 ((POST && /app/searchGroupTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8648@4dafab49
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/item/query => {
 ((POST && /app/searchOriginalTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8860@87b341b
 ((POST && /app/searchGiftItem) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8a78@602532b9
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/item/query => {
 ((POST && /searchOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8c90@9190d90
 ((POST && /searchOriginalTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd8ea8@39dc0a19
 ((POST && /searchSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd90c0@68562ad5
 ((POST && /searchTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd92d8@21af7d9c
 ((POST && /searchNum) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd94f0@609b91b4
 ((POST && /downloadTemplate) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd9708@394ec37c
 ((POST && /downloadOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd9920@********
 ((POST && /corp/downloadOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd9b38@3a8e9401
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/template/query => {
 ((POST && /getList) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd9d50@6a4b4d5
 ((POST && /getDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x0000020801dd9f68@493ec25e
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/group-category => {
 ((POST && /save) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801dda180@13c33a3d
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801dda398@5da39ad8
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801dda5b0@7e829fa5
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/group => {
 ((POST && /saveOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801dda7c8@41919e19
 ((POST && /updateOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801dda9e0@4b0946c4
 ((POST && /updateForbidden) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddabf8@abd6027
 ((POST && /saveSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddae10@6028b962
 ((POST && /updateSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddb028@2d048228
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/item => {
 ((POST && /saveSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddb240@2c3ade2a
 ((POST && /updateSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddb458@4de73fd2
 ((POST && /updateSpecialShow) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddb670@68217ca5
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/serial/template => {
 ((POST && /save) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddb888@613da64b
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddbaa0@1b0cefc8
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801ddbcb8@6f614ad4
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/query/series => {
 ((POST && /category/tree) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesQueryRoutingConfig$$Lambda/0x0000020801ddbed0@cc6c7c9
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/series => {
 ((POST && /category/save) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x0000020801ddc0e8@4527e4
 ((POST && /category/update) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x0000020801ddc300@7ccbfa8b
 ((POST && /category/delete) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x0000020801ddc518@1919382c
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/stock => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.stock.StockServiceRoutingConfig$$Lambda/0x0000020801ddc730@235b4c49
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/query/team => {
 ((POST && /member/searchPager) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamQueryRoutingConfig$$Lambda/0x0000020801ddc948@a0825c1
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /goods/team => {
 ((POST && /member/save) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x0000020801ddcb60@29b489a9
 ((POST && /member/update) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x0000020801ddcd78@79bc0ceb
 ((POST && /member/delete) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x0000020801ddcf90@fc2e91
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped (GET && /favicon.ico) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x0000020801defd88@4b113562
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /server => {
 (GET && /check) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x0000020801df0000@********
}
[main:1]2025-08-21 13:56:55.602 DEBUG [Mappings:] - Mapped /tag => {
 ((POST && /save) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x0000020801df0210@5fa7931b
 ((POST && /update) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x0000020801df0428@3e4ea6d8
 ((POST && /remove) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x0000020801df0640@7c362e56
}
[main:1]2025-08-21 13:56:55.628 DEBUG [Mappings:] - 'resourceHandlerMapping' {/webjars/**=ResourceWebHandler [classpath [META-INF/resources/webjars/]], /**=ResourceWebHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/]]}
[main:1]2025-08-21 13:56:56.035 INFO  [StdSchedulerFactory:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 13:56:56.044 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 13:56:56.044 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 13:56:56.045 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 13:56:56.045 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 13:56:56.045 INFO  [StdSchedulerFactory:] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[main:1]2025-08-21 13:56:56.045 INFO  [StdSchedulerFactory:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 13:56:56.046 INFO  [QuartzScheduler:] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@239e7554
[main:1]2025-08-21 13:56:56.225 DEBUG [InternalLoggerFactory:] - Using SLF4J as the default logging framework
[main:1]2025-08-21 13:56:56.459 INFO  [EndpointLinksResolver:60] - Exposing 19 endpoints beneath base path '/actuator'
[main:1]2025-08-21 13:56:56.474 DEBUG [WebFluxEndpointHandlerMapping:164] - 34 mappings in 'webEndpointReactiveHandlerMapping'
[main:1]2025-08-21 13:56:56.534 DEBUG [ControllerMethodResolver:289] - ControllerAdvice beans: none
[main:1]2025-08-21 13:56:56.649 DEBUG [HttpWebHandlerAdapter:267] - enableLoggingRequestDetails='false': form data and headers will be masked to prevent unsafe logging of potentially sensitive data
[main:1]2025-08-21 13:56:56.802 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:56.802 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:56.802 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:56.802 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:56.802 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:56.816 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.log.errLogDataSourceName' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:56.867 INFO  [JvmCacheConsumerFactoryBean:] - The JVM cache to start listening...
[main:1]2025-08-21 13:56:56.881 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.language.exception.baseNames' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 13:56:56.986 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 13:56:56.986 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: main
[main:1]2025-08-21 13:56:56.987 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 13:56:56.987 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 13:56:56.987 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 13:56:56.987 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 13:56:56.987 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[main:1]2025-08-21 13:56:56.987 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 13:56:57.028 INFO  [JobDetailBuilder:] - 没有服务器[192.168.13.28],需要执行的任务
[main:1]2025-08-21 13:56:57.028 INFO  [QuartzSchedulerManager:] - 共获取到【0】个需要处理的Jobs!
[main:1]2025-08-21 13:56:57.398 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-21 13:56:57.676 DEBUG [SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin:131] - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
[main:1]2025-08-21 13:56:58.518 INFO  [NettyWebServer:126] - Netty started on port 11006 (http)
[main:1]2025-08-21 13:56:58.526 INFO  [naming:] - Nacos client key init properties: 
	serverAddr=*************:8848
	namespace=dev
	username=nacos
	password=EQ********3u

[main:1]2025-08-21 13:56:58.527 INFO  [naming:] - initializer namespace from ans.namespace attribute : null
[main:1]2025-08-21 13:56:58.527 INFO  [naming:] - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[main:1]2025-08-21 13:56:58.527 INFO  [naming:] - initializer namespace from namespace attribute :null
[main:1]2025-08-21 13:56:58.531 INFO  [naming:] - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[main:1]2025-08-21 13:56:58.533 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[main:1]2025-08-21 13:56:58.533 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[main:1]2025-08-21 13:56:58.682 INFO  [client:] - [RpcClientFactory] create a new rpc client of 0261d77b-6247-4d8b-9182-b201a1c19535
[main:1]2025-08-21 13:56:58.684 INFO  [naming:] - Create naming rpc client for uuid->0261d77b-6247-4d8b-9182-b201a1c19535
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
[main:1]2025-08-21 13:56:58.684 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main:1]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Success to connect to server [*************:8848] on start up, connectionId = 1755755817605_221.12.20.178_36845
[main:1]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[main:1]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00000208015e26d8
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 13:56:58.729 INFO  [naming:] - Grpc connection connect
[main:1]2025-08-21 13:56:58.730 INFO  [naming:] - [REGISTER-SERVICE] dev registering service xkGoods with instance Instance{instanceId='null', ip='*************', port=11006, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkGoods', serviceName='null', metadata={preserved.heart.beat.timeout=20000, preserved.ip.delete.timeout=60000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=10000}}
[main:1]2025-08-21 13:56:58.766 INFO  [NacosServiceRegistry:] - nacos registry, DEFAULT_GROUP xkGoods *************:11006 register finished
[main:1]2025-08-21 13:56:58.778 DEBUG [LoggerFactory:] - Using io.vertx.core.logging.SLF4JLogDelegateFactory
[main:1]2025-08-21 13:56:58.886 INFO  [VertxEventBusManager:] - Event bus 'CONFIG' started.
[main:1]2025-08-21 13:56:58.890 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 13:56:58.890 INFO  [VertxEventBusManager:] - Event queue 'CONFIG' started.
[main:1]2025-08-21 13:56:58.922 INFO  [VertxEventBusManager:] - Event bus 'ORDER' started.
[main:1]2025-08-21 13:56:58.922 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 13:56:58.923 INFO  [VertxEventBusManager:] - Event queue 'ORDER' started.
[main:1]2025-08-21 13:56:58.954 INFO  [VertxEventBusManager:] - Event bus 'CORP' started.
[main:1]2025-08-21 13:56:58.954 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 13:56:58.954 INFO  [VertxEventBusManager:] - Event queue 'CORP' started.
[main:1]2025-08-21 13:56:58.987 INFO  [VertxEventBusManager:] - Event bus 'AUTH' started.
[main:1]2025-08-21 13:56:58.987 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 13:56:58.987 INFO  [VertxEventBusManager:] - Event queue 'AUTH' started.
[main:1]2025-08-21 13:56:59.020 INFO  [VertxEventBusManager:] - Event bus 'GOODS' started.
[main:1]2025-08-21 13:56:59.021 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 13:56:59.021 INFO  [VertxEventBusManager:] - Event queue 'GOODS' started.
[main:1]2025-08-21 13:56:59.050 INFO  [VertxEventBusManager:] - Event bus 'USER' started.
[main:1]2025-08-21 13:56:59.051 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 13:56:59.051 INFO  [VertxEventBusManager:] - Event queue 'USER' started.
[vert.x-virtual-thread-0:289]2025-08-21 13:56:59.066 INFO  [AbstractEventVerticle:] - Deploying 'DeleteItemDefineEventHandler-0'...
[vert.x-virtual-thread-0:289]2025-08-21 13:56:59.067 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteItemDefineEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 13:56:59.067 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler
[main:1]2025-08-21 13:56:59.068 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler
[vert.x-virtual-thread-1:295]2025-08-21 13:56:59.068 INFO  [AbstractEventVerticle:] - Deploying 'UpdateItemDefineIdEventHandler-1'...
[vert.x-virtual-thread-1:295]2025-08-21 13:56:59.068 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateItemDefineIdEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:ORDER, handler:com.xk.goods.application.handler.event.order.OrderPaidEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:296]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'OrderPaidEventHandler-2'...
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:CORP, handler:com.xk.goods.application.handler.event.corp.CorpCreateEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:296]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'OrderPaidEvent[ORDER[YD_ORDER]]'
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:AUTH, handler:com.xk.application.handler.event.log.LogSecureEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:298]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'CorpCreateEventHandler-3'...
[vert.x-virtual-thread-0:299]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'LogSecureEventHandler-4'...
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateStockEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-2:307]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'ColorUpdateEventHandler-5'...
[vert.x-virtual-thread-3:309]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportCreateEventHandler-6'...
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:301]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'ColorCreateEventHandler-7'...
[vert.x-virtual-thread-4:310]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportDeleteEventHandler-8'...
[vert.x-virtual-thread-1:305]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'ColorDeleteEventHandler-9'...
[vert.x-virtual-thread-5:311]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportUpdateEventHandler-10'...
[vert.x-virtual-thread-6:313]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'CreateGoodsEventHandler-11'...
[vert.x-virtual-thread-8:316]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'GoodsStockEmptyEventHandler-12'...
[vert.x-virtual-thread-8:316]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GoodsStockEmptyEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-7:315]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'DeleteGoodsEventHandler-13'...
[vert.x-virtual-thread-2:307]2025-08-21 13:56:59.075 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:298]2025-08-21 13:56:59.075 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CorpCreateEvent[CORP[YD_CORP]]'
[vert.x-virtual-thread-0:299]2025-08-21 13:56:59.075 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'LogSecureEvent[AUTH[YD_AUTH]]'
[vert.x-virtual-thread-7:315]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-6:313]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:305]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-5:311]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-4:310]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:301]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorCreateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-3:309]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportCreateEvent[GOODS[YD_GOODS]]'
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScoreEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserCreateEventHandler
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserDeleteEventHandler
[main:1]2025-08-21 13:56:59.076 INFO  [NacosDiscoveryHeartBeatPublisher:] - Start nacos heartBeat task scheduler.
[main:1]2025-08-21 13:56:59.078 INFO  [SchedulerFactoryBean:] - Starting Quartz Scheduler now
[main:1]2025-08-21 13:56:59.079 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[quartzScheduler_QuartzSchedulerThread:160]2025-08-21 13:56:59.079 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[vert.x-virtual-thread-14:322]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductFirstListingEventHandler-14'...
[vert.x-virtual-thread-12:320]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownEventHandler-16'...
[vert.x-virtual-thread-9:317]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'UpdateGoodsEventHandler-15'...
[vert.x-virtual-thread-15:323]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductRemainRandomEventHandler-17'...
[vert.x-virtual-thread-10:318]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'UpdateRemainStockEventHandler-18'...
[vert.x-virtual-thread-11:319]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'UpdateStockEventHandler-19'...
[vert.x-virtual-thread-14:322]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductFirstListingEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-11:319]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-9:317]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-10:318]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateRemainStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-13:321]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownJobEventHandler-20'...
[vert.x-virtual-thread-16:324]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'QueryScoreEventHandler-21'...
[vert.x-virtual-thread-15:323]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductRemainRandomEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-17:325]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'QueryScorePagerEventHandler-22'...
[vert.x-virtual-thread-16:324]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScoreEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-18:326]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UpdateScoreRuleEventHandler-23'...
[vert.x-virtual-thread-19:327]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'CreateSeriesCateEventHandler-24'...
[vert.x-virtual-thread-18:326]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateScoreRuleEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:328]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'DeleteSeriesCateEventHandler-25'...
[vert.x-virtual-thread-21:329]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UpdateSeriesCateEventHandler-26'...
[vert.x-virtual-thread-22:330]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'CreateTeamMemberEventHandler-27'...
[vert.x-virtual-thread-23:331]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'DeleteTeamMemberEventHandler-28'...
[vert.x-virtual-thread-24:332]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UpdateTeamMemberEventHandler-29'...
[vert.x-virtual-thread-17:325]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScorePagerEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-13:321]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownJobEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:333]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UserCreateEventHandler-30'...
[vert.x-virtual-thread-24:332]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:333]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserCreateEvent[USER[YD_USER]]'
[vert.x-virtual-thread-1:334]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UserDeleteEventHandler-31'...
[vert.x-virtual-thread-12:320]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:334]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserDeleteEvent[USER[YD_USER]]'
[vert.x-virtual-thread-23:331]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-22:330]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:328]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-21:329]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-19:327]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:336]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.order.OrderPaidEventHandler' with ID: 3
[vert.x-eventloop-thread-0:341]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserCreateEventHandler' with ID: 31
[vert.x-eventloop-thread-0:341]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserDeleteEventHandler' with ID: 32
[vert.x-eventloop-thread-0:339]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.corp.CorpCreateEventHandler' with ID: 4
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorUpdateEventHandler' with ID: 8
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorCreateEventHandler' with ID: 6
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler' with ID: 9
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler' with ID: 14
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler' with ID: 11
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler' with ID: 12
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorDeleteEventHandler' with ID: 7
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler' with ID: 10
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler' with ID: 13
[vert.x-eventloop-thread-0:337]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler' with ID: 2
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler' with ID: 20
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler' with ID: 15
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateStockEventHandler' with ID: 17
[vert.x-eventloop-thread-0:337]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler' with ID: 1
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler' with ID: 16
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler' with ID: 21
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScoreEventHandler' with ID: 22
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler' with ID: 24
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler' with ID: 23
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler' with ID: 19
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler' with ID: 30
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler' with ID: 18
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler' with ID: 29
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler' with ID: 28
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler' with ID: 27
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler' with ID: 25
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler' with ID: 26
[vert.x-eventloop-thread-0:338]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.application.handler.event.log.LogSecureEventHandler' with ID: 5
[main:1]2025-08-21 13:56:59.093 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:59.093 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 13:56:59.094 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 13:56:59.105 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 13:56:59.105 INFO  [ServiceApplicationListener:] -  The xkGoods:dev has been started.
[main:1]2025-08-21 13:56:59.105 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 13:56:59.106 INFO  [XkGoodsServer:] - Started XkGoodsServer in 19.408 seconds (process running for 20.763)
[main:1]2025-08-21 13:56:59.107 DEBUG [ApplicationAvailabilityBean:77] - Application availability state LivenessState changed to CORRECT
[main:1]2025-08-21 13:56:59.110 WARN  [DefaultStdSchedulerFactoryBean:] - 没有可用的Jobs
[main:1]2025-08-21 13:56:59.110 INFO  [QuartzSchedulerManager:] - Will start Quartz Scheduler [DefaultQuartzScheduler] in 5 seconds
[main:1]2025-08-21 13:56:59.120 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-schedule.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 13:56:59.120 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 13:56:59.120 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 13:56:59.120 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=2
[main:1]2025-08-21 13:56:59.120 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-dev.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 13:56:59.120 DEBUG [ApplicationAvailabilityBean:77] - Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
[Thread-14:120]2025-08-21 13:57:00.211 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorCreateEvent' queue.
[Thread-15:121]2025-08-21 13:57:00.249 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorDeleteEvent' queue.
[Thread-16:122]2025-08-21 13:57:00.255 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorUpdateEvent' queue.
[Thread-18:124]2025-08-21 13:57:00.267 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportCreateEvent' queue.
[Thread-19:125]2025-08-21 13:57:00.283 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportDeleteEvent' queue.
[Thread-20:126]2025-08-21 13:57:00.291 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportUpdateEvent' queue.
[Thread-21:127]2025-08-21 13:57:00.293 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateGoodsEvent' queue.
[Thread-22:128]2025-08-21 13:57:00.296 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteGoodsEvent' queue.
[Thread-17:123]2025-08-21 13:57:00.307 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_CORP-CORP-CorpCreateEvent' queue.
[Thread-23:129]2025-08-21 13:57:00.311 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GoodsStockEmptyEvent' queue.
[Thread-24:130]2025-08-21 13:57:00.311 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-25:131]2025-08-21 13:57:00.315 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownJobEvent' queue.
[Thread-26:132]2025-08-21 13:57:00.318 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-27:133]2025-08-21 13:57:00.333 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductFirstListingEvent' queue.
[Thread-29:135]2025-08-21 13:57:00.346 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateGoodsEvent' queue.
[Thread-31:137]2025-08-21 13:57:00.373 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateStockEvent' queue.
[Thread-28:134]2025-08-21 13:57:00.376 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductRemainRandomEvent' queue.
[Thread-32:138]2025-08-21 13:57:00.378 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_ORDER-ORDER-OrderPaidEvent' queue.
[Thread-34:140]2025-08-21 13:57:00.378 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScorePagerEvent' queue.
[Thread-33:139]2025-08-21 13:57:00.380 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScoreEvent' queue.
[Thread-30:136]2025-08-21 13:57:00.384 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateRemainStockEvent' queue.
[Thread-35:141]2025-08-21 13:57:00.389 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateScoreRuleEvent' queue.
[Thread-36:142]2025-08-21 13:57:00.401 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateSeriesCateEvent' queue.
[Thread-37:143]2025-08-21 13:57:00.406 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteSeriesCateEvent' queue.
[Thread-38:144]2025-08-21 13:57:00.410 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateSeriesCateEvent' queue.
[Thread-39:145]2025-08-21 13:57:00.418 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateTeamMemberEvent' queue.
[Thread-41:147]2025-08-21 13:57:00.428 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateTeamMemberEvent' queue.
[Thread-40:146]2025-08-21 13:57:00.431 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteTeamMemberEvent' queue.
[DefaultQuartzScheduler:342]2025-08-21 13:57:04.111 INFO  [QuartzSchedulerManager:] - Starting Quartz Scheduler now
[DefaultQuartzScheduler:342]2025-08-21 13:57:04.111 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 13:57:04.111 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:665]2025-08-21 13:57:04.115 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:665]2025-08-21 13:57:04.115 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8216,"timeFormat":"250821135703"}]
[event-1:669]2025-08-21 13:57:04.149 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:665]2025-08-21 13:57:04.252 INFO  [AbstractDispatchMessageListener:] - The total time of processing 137ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:676]2025-08-21 13:57:05.117 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:676]2025-08-21 13:57:05.117 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8217,"timeFormat":"250821135704"}]
[event-2:679]2025-08-21 13:57:05.118 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:676]2025-08-21 13:57:05.146 INFO  [AbstractDispatchMessageListener:] - The total time of processing 29ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:684]2025-08-21 13:57:07.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:684]2025-08-21 13:57:07.113 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8219,"timeFormat":"250821135706"}]
[event-3:688]2025-08-21 13:57:07.114 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:684]2025-08-21 13:57:07.136 INFO  [AbstractDispatchMessageListener:] - The total time of processing 23ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:692]2025-08-21 13:57:09.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:692]2025-08-21 13:57:09.113 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8221,"timeFormat":"250821135708"}]
[event-4:696]2025-08-21 13:57:09.115 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:692]2025-08-21 13:57:09.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 23ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:701]2025-08-21 13:57:12.112 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:701]2025-08-21 13:57:12.112 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8224,"timeFormat":"250821135711"}]
[event-5:705]2025-08-21 13:57:12.113 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:701]2025-08-21 13:57:12.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 25ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:710]2025-08-21 13:57:16.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:710]2025-08-21 13:57:16.113 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8228,"timeFormat":"250821135715"}]
[event-6:714]2025-08-21 13:57:16.115 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:710]2025-08-21 13:57:16.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 24ms
[quartzScheduler_QuartzSchedulerThread:160]2025-08-21 13:57:26.972 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:749]2025-08-21 13:57:27.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:749]2025-08-21 13:57:27.113 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8239,"timeFormat":"250821135726"}]
[event-7:753]2025-08-21 13:57:27.115 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:749]2025-08-21 13:57:27.139 INFO  [AbstractDispatchMessageListener:] - The total time of processing 26ms
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 13:57:28.403 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[reactor-http-nio-2:756]2025-08-21 13:57:29.907 DEBUG [HttpWebHandlerAdapter:120] - [0fd1de6b-1] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-2:756]2025-08-21 13:57:29.997 DEBUG [RouterFunctionMapping:189] - [0fd1de6b-1] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x0000020801dda7c8@41919e19
[loomBoundedElastic-1:759]2025-08-21 13:57:30.047 DEBUG [Jackson2JsonDecoder:127] - [0fd1de6b-1] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-1:760]2025-08-21 13:57:30.058 DEBUG [AOProxyAspect:] - The Service is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:762]2025-08-21 13:57:30.112 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:762]2025-08-21 13:57:30.113 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8242,"timeFormat":"************"}]
[event-8:766]2025-08-21 13:57:30.115 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:762]2025-08-21 13:57:30.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 25ms
[service-1:760]2025-08-21 13:57:30.148 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-1:760]2025-08-21 13:57:30.389 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-1:760]2025-08-21 13:57:30.418 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-1:760]2025-08-21 13:57:30.438 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-1:760]2025-08-21 13:57:30.543 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-1:760]2025-08-21 13:57:30.545 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:163502
[service-1:760]2025-08-21 13:57:30.611 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-1:760]2025-08-21 13:57:30.807 DEBUG [SheetUtils:] - The first is read by default.
[service-1:760]2025-08-21 13:57:30.807 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-1:760]2025-08-21 13:57:30.807 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@1b5911c0
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.009 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:760]2025-08-21 13:57:31.112 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.133 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.161 INFO  [DruidDataSource:] - {dataSource-1} inited
[service-1:760]2025-08-21 13:57:31.500 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-1:760]2025-08-21 13:57:31.519 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-1:760]2025-08-21 13:57:31.540 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.542 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:760]2025-08-21 13:57:31.562 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.581 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.581 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:760]2025-08-21 13:57:31.584 DEBUG [insertSelective:] - ==> Parameters: 243(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 8940(Long), 283(Long), 283(Long)
[service-1:760]2025-08-21 13:57:31.603 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:760]2025-08-21 13:57:31.605 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:760]2025-08-21 13:57:31.677 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.677 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.677 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.678 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:31.688 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.688 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:760]2025-08-21 13:57:31.722 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.722 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.723 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.723 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:31.733 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.742 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.743 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.743 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.743 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:31.754 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.770 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.770 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.770 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.770 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:31.778 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.787 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.787 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.787 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.787 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:31.797 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.807 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.807 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.807 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.808 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:31.817 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.825 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.825 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.825 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.825 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:31.834 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.842 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.843 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.843 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.843 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:31.853 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.862 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.862 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.862 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.863 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:31.872 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.881 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.881 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.881 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.881 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:31.891 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:31.900 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.900 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.900 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.901 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:31.909 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:31.919 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.919 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.919 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.919 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:31.928 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:31.937 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.938 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.938 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.938 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:31.947 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:31.957 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.957 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.957 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.957 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:31.967 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:31.977 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.977 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.977 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.977 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:31.986 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:31.995 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:31.995 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:31.995 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:31.995 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:32.005 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.015 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.015 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.015 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.016 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:32.025 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.034 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.035 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.035 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.035 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:32.044 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.053 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.054 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.054 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.054 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:32.062 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.071 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.071 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.071 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.071 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:32.080 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.091 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.091 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.091 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.091 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:32.100 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.109 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.109 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.109 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.109 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:32.119 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.127 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.128 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.128 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.128 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:32.137 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.147 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.148 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.148 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.148 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:32.158 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.168 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.168 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.168 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.168 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:32.177 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.186 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.186 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.186 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.186 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:32.196 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.204 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.205 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.205 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.205 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:32.214 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.225 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.226 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.226 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.226 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:32.235 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.249 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.249 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.249 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.249 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:32.258 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.267 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.267 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.267 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.267 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:32.277 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.286 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.286 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.286 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.286 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:32.298 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.306 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.307 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.307 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.307 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:32.316 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.345 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.345 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.345 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.345 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:32.353 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.367 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.367 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.367 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.367 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:32.375 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.387 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.387 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.387 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.388 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:32.397 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.407 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.408 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.408 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.408 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:32.417 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.426 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.426 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.426 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.426 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:32.436 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.444 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.444 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.444 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.444 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:32.453 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.462 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.462 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.462 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.462 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:32.472 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.480 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.480 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.481 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.481 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:32.489 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.498 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.499 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.499 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.499 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:32.507 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.516 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.516 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.516 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.517 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:32.527 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.537 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.537 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.537 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.537 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:32.546 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.554 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.554 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.554 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.554 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:32.564 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.574 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.574 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.575 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.575 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:32.584 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.593 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.593 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.593 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.594 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:32.603 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.613 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.613 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.613 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.613 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:32.622 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.631 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.632 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.632 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.632 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:32.641 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.651 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.651 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.651 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.651 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:32.660 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.669 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.669 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.669 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.669 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:32.682 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.691 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.691 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.691 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.691 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:32.698 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.706 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.706 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.706 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.706 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:32.715 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.724 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.724 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.724 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.725 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:32.733 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.741 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.741 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.741 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.741 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:32.750 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.759 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.759 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.759 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.759 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:32.768 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.776 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.776 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.776 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.776 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:32.785 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.793 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.793 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.794 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.794 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:32.802 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.811 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.811 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.811 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.811 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:32.821 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.829 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.829 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.830 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.830 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:32.840 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.849 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.849 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.849 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.850 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:32.859 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.866 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.867 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.867 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.867 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:32.875 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.884 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.885 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.885 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.885 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:32.893 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.901 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.901 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.901 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.901 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:32.911 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.919 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.919 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.920 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.920 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:32.928 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:32.936 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.937 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.937 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.937 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:32.946 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.954 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.954 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.954 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.955 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:32.964 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:32.982 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:32.982 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:32.982 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:32.982 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:32.991 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.000 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.000 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.000 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.000 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:33.009 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.018 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.018 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.018 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.018 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:33.027 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.035 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.035 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.035 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.035 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:33.056 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.066 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.066 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.066 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.066 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:33.075 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.083 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.083 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.083 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.084 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:33.092 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.100 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.100 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.100 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.100 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:33.111 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.120 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.120 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.120 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.121 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:33.128 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.136 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.136 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.136 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.136 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:33.146 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.156 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.156 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.156 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.156 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:33.164 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.181 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.181 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.181 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.181 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:33.190 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.198 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.198 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.198 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.198 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:33.208 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.215 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.216 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.216 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.216 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:33.225 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.233 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.233 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.234 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.234 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:33.243 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.259 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.259 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.259 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.260 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:33.267 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.276 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.276 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.276 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.276 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:33.285 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.293 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.293 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.293 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.293 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:33.302 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.310 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.310 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.310 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.310 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:33.320 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.329 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.329 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.329 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.329 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:33.338 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.346 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.346 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.346 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.346 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:33.355 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.363 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.363 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.363 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.363 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:33.372 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.382 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.383 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.383 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.383 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:33.391 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.400 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.400 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.400 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.400 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:33.409 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.417 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.417 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.417 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.417 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:33.426 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.434 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.434 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.434 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.434 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:33.443 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.455 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.455 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.455 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.455 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:33.465 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.477 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.478 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.478 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.478 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:33.486 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.495 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.495 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.495 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.495 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:33.504 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.512 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.512 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.512 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.512 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:33.520 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.529 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.529 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.529 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.529 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:33.538 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.546 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.546 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.546 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.546 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:33.555 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.563 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.563 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.563 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.563 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:33.572 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.580 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.581 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.581 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.581 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:33.591 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.600 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.600 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.600 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.600 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:33.611 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.619 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.620 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.620 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.620 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:33.629 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.638 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.638 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.638 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.638 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:33.646 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.656 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.656 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.656 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.656 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:33.665 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.674 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.674 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.674 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.674 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:33.683 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.691 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.691 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.691 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.691 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:33.747 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.755 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.755 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.756 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.756 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:33.765 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.774 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.774 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.774 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.774 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:33.783 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.792 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.792 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.792 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.792 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:33.801 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.809 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.809 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.809 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.809 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:33.819 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.827 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.828 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.828 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.828 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:33.838 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.850 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.850 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.850 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.850 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:33.859 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.868 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.868 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.868 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.869 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:33.878 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:33.887 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.887 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.887 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.887 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:33.896 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.904 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.904 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.904 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.905 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:33.916 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.924 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.924 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.924 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.925 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:33.933 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.943 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.943 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.943 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.943 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:33.952 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.961 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.961 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.961 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.962 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:33.970 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.979 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.979 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.979 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.979 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:33.989 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:33.998 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:33.998 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:33.998 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:33.998 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:34.008 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.016 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.016 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.016 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.016 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:34.027 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.036 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.036 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.036 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.036 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:34.045 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.053 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.053 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.053 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.053 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:34.062 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.070 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.071 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.071 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.071 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:34.078 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.088 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.088 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.088 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.088 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:34.098 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.106 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.107 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.107 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.107 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:34.116 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.125 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.125 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.125 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.126 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:34.135 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.144 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.145 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.145 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.145 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:34.153 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.162 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.162 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.162 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.162 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:34.170 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.180 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.180 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.180 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.180 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:34.189 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.199 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.199 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.199 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.200 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:34.208 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.215 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.216 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.216 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.216 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:34.226 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.234 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.235 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.235 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.235 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:34.243 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.252 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.252 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.252 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.253 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:34.261 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.272 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.272 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.272 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.273 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:34.282 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.290 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.290 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.290 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.291 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:34.299 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.307 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.307 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.307 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.308 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:34.317 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.324 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.324 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.324 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.324 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:34.334 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.342 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.343 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.343 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.343 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:34.354 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.362 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.362 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.362 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.362 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:34.371 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.378 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.379 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.379 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.379 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:34.387 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.395 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.395 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.395 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.395 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:34.405 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.414 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.414 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.414 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.415 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:34.424 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.433 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.433 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.433 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.433 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:34.442 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.451 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.451 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.451 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.451 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:34.460 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.468 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.468 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.468 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.468 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:34.476 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.485 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.486 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.486 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.486 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:34.495 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.504 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.504 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.504 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.504 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:34.513 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.521 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.521 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.521 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.521 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:34.530 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.538 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.538 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.538 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.538 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:34.546 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.557 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.557 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.557 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.557 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:34.568 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.577 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.577 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.577 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.577 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:34.586 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.594 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.594 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.594 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.594 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:34.602 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.611 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.612 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.612 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.612 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:34.620 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.628 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.629 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.629 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.629 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:34.637 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.645 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.645 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.646 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.646 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:34.656 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.665 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.665 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.665 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.665 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:34.677 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.686 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.686 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.686 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.686 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:34.695 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.703 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.703 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.703 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.703 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:34.712 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.721 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.721 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.721 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.721 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:34.728 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.737 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.737 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.737 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.737 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:34.746 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.754 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.755 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.755 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.755 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:34.765 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.772 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.773 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.773 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.773 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:34.781 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.789 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.789 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.789 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.789 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:34.799 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.805 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.805 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.805 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.807 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:34.818 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.826 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.826 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.826 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.827 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:34.835 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.843 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.843 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.843 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.843 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:34.852 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.860 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.860 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.860 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.860 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:34.868 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.876 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.877 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.877 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.877 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:34.886 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.895 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.895 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.895 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.895 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:34.907 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.916 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.916 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.916 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.917 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:34.924 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:34.932 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.933 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.933 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.933 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:34.941 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.949 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.949 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.949 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.949 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:34.959 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.968 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.968 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.968 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.968 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:34.976 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:34.985 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:34.985 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:34.985 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:34.985 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:34.996 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.006 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.006 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.006 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.006 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:35.014 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.023 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.023 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.023 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.024 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:35.034 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.042 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.042 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.042 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.043 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:35.052 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.061 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.061 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.061 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.062 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:35.073 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.083 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.083 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.083 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.083 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:35.093 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.101 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.101 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.101 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.101 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:35.110 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.119 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.119 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.119 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.119 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:35.128 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.138 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.138 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.138 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.138 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:35.147 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.156 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.156 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.156 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.156 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:35.165 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.172 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.173 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.173 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.173 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:35.181 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.190 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.190 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.190 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.190 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:35.200 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.208 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.208 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.208 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.208 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:35.218 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.226 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.227 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.227 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.227 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:35.236 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.244 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.244 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.244 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.245 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:35.252 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.262 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.263 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.263 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.263 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:35.272 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.284 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.284 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.284 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.284 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:35.295 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.304 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.304 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.304 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.304 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:35.313 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.322 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.323 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.323 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.323 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:35.333 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.341 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.341 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.341 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.341 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:35.350 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.358 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.358 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.358 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.358 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:35.368 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.377 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.377 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.377 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.377 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:35.387 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.395 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.395 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.395 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.395 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:35.404 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.412 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.412 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.412 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.412 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:35.421 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.430 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.430 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.430 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.430 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:35.439 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.447 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.448 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.448 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.448 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:35.457 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.466 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.466 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.466 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.466 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:35.476 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.484 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.484 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.484 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.485 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:35.494 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.504 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.504 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.504 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.504 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:35.512 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.520 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.521 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.521 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.521 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:35.530 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.539 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.539 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.539 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.539 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:35.550 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.557 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.557 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.557 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.557 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:35.566 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.574 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.574 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.574 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.574 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:35.582 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.591 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.591 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.591 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.591 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:35.601 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.610 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.610 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.610 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.610 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:35.619 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.628 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.628 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.628 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.629 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:35.637 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.646 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.646 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.646 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.646 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:35.655 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.662 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.662 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.662 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.663 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:35.672 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.683 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.683 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.683 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.683 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:35.692 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.701 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.702 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.702 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.702 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:35.711 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.718 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.720 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.720 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.720 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:35.728 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.736 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.736 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.736 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.737 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:35.746 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.754 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.754 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.754 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.754 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:35.763 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.771 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.772 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.772 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.772 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:35.780 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.789 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.789 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.789 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.789 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:35.798 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.807 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.807 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.807 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.807 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:35.816 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.826 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.826 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.826 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.826 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:35.835 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.842 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.842 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.842 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.843 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:35.852 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.860 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.860 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.860 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.860 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:35.869 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.877 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.877 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.877 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.877 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:35.890 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.899 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.899 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.899 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.900 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:35.909 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.917 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.918 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.918 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.918 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:35.927 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.936 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.936 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.936 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.936 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:35.945 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.954 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.954 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.954 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.954 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:35.963 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:35.972 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.972 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.972 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.972 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:35.981 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:35.989 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:35.990 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:35.990 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:35.990 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:35.999 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.008 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.008 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.008 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.008 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:36.016 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.025 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.025 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.025 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.025 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:36.035 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.042 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.043 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.043 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.043 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:36.051 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.062 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.062 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.062 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.062 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:36.072 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.081 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.082 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.082 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.082 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:36.095 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.104 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.104 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.104 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.104 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:36.113 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.122 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.122 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.122 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.122 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:36.130 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.139 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.139 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.139 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.139 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:36.147 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.156 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.156 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.156 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.156 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:36.164 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.173 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.173 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.173 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.173 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:36.182 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.190 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.190 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.190 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.190 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:36.199 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.208 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.208 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.208 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.208 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:36.217 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.224 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.224 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.224 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.224 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:36.233 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.241 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.242 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.242 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.242 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:36.251 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.260 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.260 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.260 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.260 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:36.269 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.278 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.278 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.278 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.278 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:36.296 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.305 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.306 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.306 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.306 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:36.317 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.326 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.326 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.326 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.326 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:36.334 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.342 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.343 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.343 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.343 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:36.353 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.361 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.361 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.361 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.361 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:36.369 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.378 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.378 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.378 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.379 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:36.388 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.396 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.398 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.398 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.398 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:36.407 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.437 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.438 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.438 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.438 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:36.467 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.476 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.477 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.477 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.477 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:36.485 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.494 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.495 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.495 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.495 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:36.503 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.512 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.512 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.512 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.512 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:36.521 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.530 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.531 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.531 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.531 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:36.539 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.548 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.548 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.548 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.549 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:36.556 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.563 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.563 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.564 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.564 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:36.572 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.580 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.581 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.581 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.581 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:36.589 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.598 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.598 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.598 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.598 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:36.609 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.617 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.617 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.617 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.617 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:36.625 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.635 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.635 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.635 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.635 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:36.643 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.651 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.652 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.652 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.652 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:36.660 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.669 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.669 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.669 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.669 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:36.678 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.685 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.687 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.687 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.687 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:36.695 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.703 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.703 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.703 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.703 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:36.712 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.720 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.720 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.720 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.720 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:36.729 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.736 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.736 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.736 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.736 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:36.745 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.754 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.754 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.754 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.754 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:36.762 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.770 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.770 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.770 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.770 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:36.778 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.787 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.787 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.787 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.787 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:36.795 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.803 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.803 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.803 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.803 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:36.814 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.823 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.823 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.823 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.823 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:36.832 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.840 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.840 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.840 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.840 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:36.849 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.858 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.858 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.858 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.858 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:36.866 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.874 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.874 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.874 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.874 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:36.883 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.892 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.892 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.892 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.892 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:36.901 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.908 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.908 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.908 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.910 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:36.917 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.925 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.926 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.926 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.926 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:36.934 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.942 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.942 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.942 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.942 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:36.951 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.959 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.959 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.959 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.959 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:36.968 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:36.976 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.976 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.976 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.976 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:36.984 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:36.992 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:36.992 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:36.992 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:36.992 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:37.001 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.009 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.009 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.009 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.009 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:37.019 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.026 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.026 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.026 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.026 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:37.035 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.043 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.043 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.043 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.043 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:37.052 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.061 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.061 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.061 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.061 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:37.069 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.078 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.078 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.078 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.078 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:37.087 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.095 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.096 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.096 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.096 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:37.128 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.136 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.136 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.136 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.136 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:37.146 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.154 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.154 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.154 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.154 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:37.162 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.170 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.170 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.171 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.171 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:37.179 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.187 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.188 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.188 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.188 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:37.194 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.203 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.203 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.204 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.204 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:37.212 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.220 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.220 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.220 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.220 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:37.227 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.237 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.237 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.237 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.237 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:37.244 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.253 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.253 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.253 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.253 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:37.261 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.269 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.270 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.270 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.270 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:37.278 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.285 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.286 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.286 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.286 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:760]2025-08-21 13:57:37.294 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.307 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.308 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.308 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.308 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:37.316 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.324 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.324 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.324 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.324 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:37.333 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.341 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.341 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.341 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.341 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:37.349 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.358 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.358 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.358 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.358 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:37.367 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.376 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.376 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.376 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.376 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:37.385 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.392 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.392 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.392 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.393 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:37.401 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.409 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.409 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.409 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.409 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:37.418 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.426 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.426 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.426 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.426 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:37.435 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.444 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.444 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.444 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.444 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:37.452 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.461 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.461 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.461 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.461 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:37.470 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.478 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.478 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.478 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.478 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:37.487 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.495 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.495 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.495 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.495 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:37.505 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.514 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.514 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.514 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.514 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:37.522 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.529 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.529 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.529 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.529 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:37.537 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.545 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.546 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.546 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.546 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:37.555 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.563 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.563 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.563 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.564 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:37.572 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.580 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.580 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.580 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.580 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:37.588 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.596 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.596 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.596 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.596 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:37.604 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.612 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.612 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.612 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.612 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:37.621 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.629 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.630 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.630 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.630 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:37.638 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.647 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.647 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.647 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.647 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:37.655 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.664 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.664 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.664 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.664 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:37.676 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.684 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.685 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.685 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.685 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:37.693 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.702 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.703 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.703 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.703 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:760]2025-08-21 13:57:37.711 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.718 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.719 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.719 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.719 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:37.726 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.734 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.734 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.735 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.735 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:37.743 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.751 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.752 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.752 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.752 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:37.760 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.768 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.768 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.768 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.768 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:760]2025-08-21 13:57:37.776 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.785 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.785 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.785 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.786 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:37.794 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.802 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.802 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.802 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.802 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:37.810 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.819 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.819 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.819 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.819 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:760]2025-08-21 13:57:37.828 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.837 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.837 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.837 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.837 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:760]2025-08-21 13:57:37.846 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.854 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.854 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.854 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.854 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:37.863 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.871 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.871 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.871 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.871 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:760]2025-08-21 13:57:37.881 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.890 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.890 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.890 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.890 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:37.899 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.907 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.907 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.907 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.907 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:37.915 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.924 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.924 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.924 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.924 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:37.932 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.940 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.941 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.941 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.941 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:760]2025-08-21 13:57:37.948 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.956 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.956 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.956 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.956 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:37.966 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:37.974 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.974 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.974 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.974 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:37.983 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:37.991 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:37.991 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:37.991 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:37.991 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:37.999 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.007 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.007 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.007 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.007 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:760]2025-08-21 13:57:38.016 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.023 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.023 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.023 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.023 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:38.032 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.040 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.040 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.040 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.040 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:38.048 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.056 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.056 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.056 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.056 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:38.064 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.072 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.072 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.072 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.072 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:38.080 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.089 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.089 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.089 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.089 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:760]2025-08-21 13:57:38.099 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.108 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.108 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.108 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.108 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:802]2025-08-21 13:57:38.115 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:802]2025-08-21 13:57:38.115 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":8250,"timeFormat":"************"}]
[service-1:760]2025-08-21 13:57:38.116 DEBUG [selectByCategoryName:] - <==      Total: 0
[event-9:806]2025-08-21 13:57:38.117 DEBUG [AOProxyAspect:] - The event is called proxies!
[service-1:760]2025-08-21 13:57:38.124 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.124 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.124 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.124 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:38.133 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.141 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.141 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.141 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.141 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:802]2025-08-21 13:57:38.148 INFO  [AbstractDispatchMessageListener:] - The total time of processing 33ms
[service-1:760]2025-08-21 13:57:38.150 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.158 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.158 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.158 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.158 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:760]2025-08-21 13:57:38.166 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.176 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.176 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.176 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.176 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:760]2025-08-21 13:57:38.185 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.193 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.193 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.193 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.193 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:760]2025-08-21 13:57:38.202 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.210 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.211 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.211 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.211 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:38.219 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.227 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.227 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.227 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.228 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:38.236 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.243 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.243 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.244 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.244 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:760]2025-08-21 13:57:38.252 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.260 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.260 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.260 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.260 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:760]2025-08-21 13:57:38.269 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.277 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.277 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.277 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.277 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:38.285 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.293 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.293 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.293 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.293 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:38.302 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.319 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.319 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.319 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.319 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:760]2025-08-21 13:57:38.329 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.339 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.339 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.339 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.339 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:760]2025-08-21 13:57:38.348 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.356 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.356 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.356 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.356 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:38.365 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.373 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.374 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.374 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.374 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:760]2025-08-21 13:57:38.383 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.390 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.391 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.391 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.391 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:38.399 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.412 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.412 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.412 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.412 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:760]2025-08-21 13:57:38.421 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.430 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.431 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.431 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.431 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:760]2025-08-21 13:57:38.440 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.450 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.450 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.450 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.450 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:760]2025-08-21 13:57:38.460 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.468 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.468 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.468 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.468 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:760]2025-08-21 13:57:38.477 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.484 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.484 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.486 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.486 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:760]2025-08-21 13:57:38.502 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.510 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.511 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.511 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.511 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:760]2025-08-21 13:57:38.519 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.527 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.527 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.527 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.527 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:760]2025-08-21 13:57:38.536 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.544 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.544 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.544 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.545 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:760]2025-08-21 13:57:38.554 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.562 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.563 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.563 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.563 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:760]2025-08-21 13:57:38.572 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.580 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.580 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.580 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.580 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:760]2025-08-21 13:57:38.589 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.597 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.597 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.597 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.598 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:760]2025-08-21 13:57:38.607 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:760]2025-08-21 13:57:38.614 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.614 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.614 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.614 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:760]2025-08-21 13:57:38.623 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.631 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.631 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.631 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.631 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:760]2025-08-21 13:57:38.639 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:760]2025-08-21 13:57:38.647 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:760]2025-08-21 13:57:38.648 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:760]2025-08-21 13:57:38.649 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:760]2025-08-21 13:57:38.649 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)

[main:1]2025-08-21 15:47:48.277 DEBUG [Loggers:] - Using Slf4j logging framework
[main:1]2025-08-21 15:47:48.279 DEBUG [Hooks:] - Enabling stacktrace debugging via onOperatorDebug
[main:1]2025-08-21 15:47:48.963 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 15:47:48.963 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 15:47:48.964 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 15:47:48.965 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 15:47:48.965 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
[main:1]2025-08-21 15:47:48.965 INFO  [NacosLogging:] - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
[background-preinit:44]2025-08-21 15:47:48.981 DEBUG [logging:] - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[background-preinit:44]2025-08-21 15:47:48.986 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[background-preinit:44]2025-08-21 15:47:48.987 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[background-preinit:44]2025-08-21 15:47:48.987 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[background-preinit:44]2025-08-21 15:47:48.987 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[background-preinit:44]2025-08-21 15:47:48.990 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[background-preinit:44]2025-08-21 15:47:49.012 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[background-preinit:44]2025-08-21 15:47:49.068 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[background-preinit:44]2025-08-21 15:47:49.072 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
[background-preinit:44]2025-08-21 15:47:49.072 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[background-preinit:44]2025-08-21 15:47:49.072 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[background-preinit:44]2025-08-21 15:47:49.072 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[background-preinit:44]2025-08-21 15:47:49.072 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 15:47:51.004 INFO  [XkGoodsServer:] - The following 10 profiles are active: "commons", "data", "jms", "cache", "http", "schedule", "proxy", "os", "server", "dev"
[main:1]2025-08-21 15:47:51.004 DEBUG [SpringApplication:685] - Loading source class com.xk.goods.server.XkGoodsServer,class org.springframework.cloud.bootstrap.BootstrapApplicationListener$BootstrapMarkerConfiguration
[main:1]2025-08-21 15:47:51.014 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 15:47:51.014 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] content: 
jobs:
    
[main:1]2025-08-21 15:47:51.015 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 15:47:51.015 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] content: 
configuration:
    webClient:
        loadBalanced:
            maxInMemorySize: 5120
            connectTimeoutMillis: 30000
            responseTimeout: 30
            user: admin
            password: 6RirvS
    validation: 
        key: 
            admin: 'Kth1HURxA5mWcGpBYcUg6GgXV0hATl6u'
            corp: 'ykkqhUH6b6WAdm4ipfusTAEZ0cnuBBtv'
            ios: 'XtPp4XG9NanpAGGp5WIaMYv0lmVMjrXb'
            android: 'HCgN0RwmXKVFlIKaDGi3tAfS4moSqURb'
            harmony: 'ngZ1E0yVv1gyZLuw7D6XiiupOzN1nInL'
        expiresTime: 60
        status: false
    settings:
        test:
            sig1: "RPBHwTNdRvymgnC5kEWS1EDHE7x06BaC"
        session:
            timeout: 28800
        user:
            nickname: "用户"
        os.bucket.cos.10:
            id: 1331099099
            name: "haoshang-test-1331099099"
            domain: "http://files.xmjihaoyun.cn"
            region: "ap-shanghai"
        os.bucket.oss.10:
            id: 1098742611924356
            name: "xka-test"
            domain: "https://files.xmjihaoyun.cn"
            region: "cn-hangzhou"
    queue:
        disruptor:
            maxDrainAttemptsBeforeShutdown: 200
            sleepMillisBetweenDrainAttempts: 50 
            ringBufferSize: 1024 
            timeout: 5000
            strategy: "TIMEOUT"
            sleepTimeNs: 10
            retries: 200
            waitTimeout: 10
            timeUnit: MILLISECONDS
            notifyProgressThreshold: 2048
    vertx:
        vertx:
            #eventLoopPoolSize: 8
            maxEventLoopExecuteTime: 3000000000
        eventbus:
        deployment:
                threadingModel: VIRTUAL_THREAD
                #blockPoolSize: 8
                instances: 1
    ncs:
        zookeeper: 
            connectionString: "*************:2181"
    scheduling:
        quartz:
            startupDelay:5
    os:
        cos:
            secretId: IKIDujOsAFH6tTr21oQq46vcItL2fBXkojU6
            secretKey: a1MXuGTWTK3c4LSNAk9MPIBxYyAY9yhH
            appId: 1331099099
            regionStr: ap-shanghai
        oss:
            secretId: LTAI5tEpzBC6KSKdcSTtHY2R
            secretKey: ******************************
            appId: 1098742611924356
            roleArn: acs:ram::1098742611924356:role/ramossdev1
            regionStr: cn-hangzhou
    jms: 
        rocketmq-producer:
            namesrvAddr: *************:9876
        rocketmq-consumer:
            namesrvAddr: *************:9876
        zmq-producer:
            nameServer: "MYDATA_IM_1:127.0.0.1:18100,MYDATA_IM_2:127.0.0.1:18200"
            zmqConnectUrl: "tcp://%s:%d"
            zmqRequestFormat: "requ|%s|%s"
            sendTimeout: 1000
            receiveTimeout: 1000
            reload: false
    http:
        defaultHttpClient: 
            connectTimeout: 5000 
            socketTimeout: 5000 
            connTimeToLive: 3
            retry: 2
            busiRetry: 2
    redis:
        zmqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2
            jmxEnabled: false
            maxTotal: 10000
        seqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3
            jmxEnabled: false
            maxTotal: 10000
        tableRedisClient: 
            connectionString: redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379,redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379
            jmxEnabled: false
            maxTotal: 10000
        busiRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379
            jmxEnabled: false
            maxTotal: 10000
    jdbc:
        xk_config: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: **********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_goods: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_auth: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_stock: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
    sharding:
        datasource.mapping:
            xk_log: 
                module: log
                dataSourceKeys: xk_log
                startId: 0
            xk_config: 
                module: config
                dataSourceKeys: xk_config
                startId: 0
            xk_goods: 
                module: goods
                dataSourceKeys: xk_goods
                startId: 0
            xk_auth: 
                module: auth
                dataSourceKeys: xk_auth
                startId: 0
            xk_stock:
                module: stock
                dataSourceKeys: xk_stock
                startId: 0    
        module.mapping:
            log: 
                tableRule: c_.*,log_.*
            config: 
                tableRule: t_.*
            goods: 
                tableRule: g_.*
            auth: 
                tableRule: auth_.*
            stock:
                tableRule: s_.*
[main:1]2025-08-21 15:47:51.015 DEBUG [AnnotationConfigReactiveWebServerApplicationContext:674] - Refreshing org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@1073c664
[main:1]2025-08-21 15:47:52.103 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.103 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorDeleteEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.104 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorUpdateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.105 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.corp.CorpCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.105 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportCreateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.106 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportDeleteListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.106 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportUpdateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.107 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.CreateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.107 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.DeleteGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.108 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.GoodsStockEmptyListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.108 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.109 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.109 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.109 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductFirstListingListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.110 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductRemainRandomListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.110 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.110 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateRemainStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.111 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.112 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.order.OrderPaidListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.112 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScoreListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.113 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScorePagerListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.114 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.UpdateScoreRuleListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.114 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.CreateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.116 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.DeleteSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.116 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.UpdateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.117 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.CreateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.117 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.DeleteTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.118 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.UpdateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:47:52.682 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 15:47:52.696 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:52.696 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:52.697 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 15:47:52.749 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'userObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 15:47:52.749 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'corpObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 15:47:52.753 DEBUG [EncryptResourceManager:] - Registering class: com.xk.goods.infrastructure.data.po.random.GDistributionItemPO
[main:1]2025-08-21 15:47:52.753 DEBUG [EncryptScannerRegistrar:] - Registered specified classes: [com.xk.goods.infrastructure.data.po.random.GDistributionItemPO]
[main:1]2025-08-21 15:47:52.766 INFO  [CacheData:] - config listener notify warn timeout millis use default 60000 millis 
[main:1]2025-08-21 15:47:52.766 INFO  [CacheData:] - nacos.cache.data.init.snapshot = true 
[main:1]2025-08-21 15:47:52.767 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-dev.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 15:47:52.772 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 15:47:53.233 DEBUG [LogFactory:] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[main:1]2025-08-21 15:47:53.243 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-21 15:47:53.270 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.270 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.270 INFO  [SystemParamTableHolder:] - System settings initializing.
[main:1]2025-08-21 15:47:53.270 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.270 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.270 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.286 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.310 INFO  [RoutingConfigHolder:] - {log-routing: [xk_log, 0 - 9223372036854775807],goods-routing: [xk_goods, 0 - 9223372036854775807],auth-routing: [xk_auth, 0 - 9223372036854775807],config-routing: [xk_config, 0 - 9223372036854775807],stock-routing: [xk_stock, 0 - 9223372036854775807] }
[main:1]2025-08-21 15:47:53.310 DEBUG [ShardingNodeExecutor:] - Routing-Config Holder initialization is complete.
[main:1]2025-08-21 15:47:53.310 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:47:53.457 INFO  [GenericScope:] - BeanFactory id=72c0bf22-faf7-3dda-ae60-2f9c5ff137a6
[main:1]2025-08-21 15:47:53.862 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:53.866 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:zookeeper.version=3.6.4--d65253dcf68e9097c6e95a126463fd5fdeb4521c, built on 12/18/2022 18:10 GMT
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:host.name=*************
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.version=21.0.7
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.vendor=Oracle Corporation
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.home=C:\Program Files\Java\jdk-21
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.class.path=D:\code\xk\xk-goods\xk-goods-server\target\classes;D:\maven\repository\com\xk\xk-start-server\0.0.1-SNAPSHOT\xk-start-server-0.0.1-20250818.091612-116.jar;D:\maven\repository\com\myco\mydata\mydata-start-server\0.0.1-SNAPSHOT\mydata-start-server-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-core\0.0.1-SNAPSHOT\mydata-start-domain-core-0.0.1-20250819.023657-99.jar;D:\maven\repository\com\myco\mydata\mydata-start-commons\0.0.1-SNAPSHOT\mydata-start-commons-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\myco-framework-6\0.0.1-SNAPSHOT\myco-framework-6-0.0.1-20250819.023657-80.jar;D:\maven\repository\com\alibaba\nacos\nacos-client\2.4.3\nacos-client-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-auth-plugin\2.4.3\nacos-auth-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-encryption-plugin\2.4.3\nacos-encryption-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-logback-adapter-12\2.4.3\nacos-logback-adapter-12-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\logback-adapter\1.1.3\logback-adapter-1.1.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-log4j2-adapter\2.4.3\nacos-log4j2-adapter-2.4.3.jar;D:\maven\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\maven\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\maven\repository\org\zeromq\jeromq\0.6.0\jeromq-0.6.0.jar;D:\maven\repository\eu\neilalexander\jnacl\1.0.0\jnacl-1.0.0.jar;D:\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\maven\repository\org\aspectj\aspectjrt\1.9.22.1\aspectjrt-1.9.22.1.jar;D:\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\maven\repository\org\springframework\spring-jdbc\6.2.3\spring-jdbc-6.2.3.jar;D:\maven\repository\org\apache\curator\curator-framework\4.3.0\curator-framework-4.3.0.jar;D:\maven\repository\org\apache\curator\curator-client\4.3.0\curator-client-4.3.0.jar;D:\maven\repository\com\google\guava\guava\27.0.1-jre\guava-27.0.1-jre.jar;D:\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven\repository\org\checkerframework\checker-qual\2.5.2\checker-qual-2.5.2.jar;D:\maven\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;D:\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;D:\maven\repository\org\apache\curator\curator-recipes\4.3.0\curator-recipes-4.3.0.jar;D:\maven\repository\org\apache\zookeeper\zookeeper\3.6.4\zookeeper-3.6.4.jar;D:\maven\repository\org\apache\zookeeper\zookeeper-jute\3.6.4\zookeeper-jute-3.6.4.jar;D:\maven\repository\org\apache\yetus\audience-annotations\0.13.0\audience-annotations-0.13.0.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final.jar;D:\maven\repository\org\mozilla\rhino\1.8.0\rhino-1.8.0.jar;D:\maven\repository\org\apache\groovy\groovy\4.0.26\groovy-4.0.26.jar;D:\maven\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;D:\maven\repository\cglib\cglib-nodep\3.3.0\cglib-nodep-3.3.0.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2\2.0.57\fastjson2-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson\2.0.57\fastjson-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2-extension\2.0.57\fastjson2-extension-2.0.57.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.18.2\jackson-dataformat-yaml-2.18.2.jar;D:\maven\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;D:\maven\repository\joda-time\joda-time\2.14.0\joda-time-2.14.0.jar;D:\maven\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-event\0.0.1-SNAPSHOT\mydata-start-domain-event-0.0.1-20250819.023657-92.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-enum\0.0.1-SNAPSHOT\mydata-start-domain-enum-0.0.1-20250819.023657-93.jar;D:\maven\repository\org\hibernate\validator\hibernate-validator\9.0.1.Final\hibernate-validator-9.0.1.Final.jar;D:\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\maven\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-interfaces\0.0.1-SNAPSHOT\mydata-start-interfaces-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\springframework\spring-webflux\6.2.3\spring-webflux-6.2.3.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-http\1.2.7\reactor-netty-http-1.2.7.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-classes-macos\4.2.2.Final\netty-resolver-dns-classes-macos-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-classes-epoll\4.2.2.Final\netty-transport-classes-epoll-4.2.2.Final.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-core\1.2.7\reactor-netty-core-1.2.7.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.2.0\spring-cloud-starter-loadbalancer-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-loadbalancer\4.2.0\spring-cloud-loadbalancer-4.2.0.jar;D:\maven\repository\io\projectreactor\addons\reactor-extra\3.5.2\reactor-extra-3.5.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-cache\3.4.3\spring-boot-starter-cache-3.4.3.jar;D:\maven\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-joda\2.18.3\jackson-datatype-joda-2.18.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;D:\maven\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;D:\maven\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-application\0.0.1-SNAPSHOT\mydata-start-application-0.0.1-20250819.023657-86.jar;D:\maven\repository\com\myco\mydata\mydata-start-gateway\0.0.1-SNAPSHOT\mydata-start-gateway-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-http\0.0.1-SNAPSHOT\mydata-start-infrastructure-http-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;D:\maven\repository\org\apache\httpcomponents\httpclient\4.5.9\httpclient-4.5.9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-data\0.0.1-SNAPSHOT\mydata-start-infrastructure-data-0.0.1-20250819.023657-93.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-commons\0.0.1-SNAPSHOT\mydata-start-infrastructure-commons-0.0.1-20250819.023657-89.jar;D:\maven\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;D:\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven\repository\com\lmax\disruptor\3.4.4\disruptor-3.4.4.jar;D:\maven\repository\com\google\zxing\core\3.5.3\core-3.5.3.jar;D:\maven\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.18.3\jackson-dataformat-xml-2.18.3.jar;D:\maven\repository\org\codehaus\woodstox\stax2-api\4.2.2\stax2-api-4.2.2.jar;D:\maven\repository\com\fasterxml\woodstox\woodstox-core\7.0.0\woodstox-core-7.0.0.jar;D:\maven\repository\io\github\jopenlibs\vault-java-driver\6.2.0\vault-java-driver-6.2.0.jar;D:\maven\repository\com\mysql\mysql-connector-j\9.3.0\mysql-connector-j-9.3.0.jar;D:\maven\repository\com\google\protobuf\protobuf-java\4.29.0\protobuf-java-4.29.0.jar;D:\maven\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\maven\repository\org\springframework\spring-context-support\6.2.3\spring-context-support-6.2.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2023.0.1.3\spring-cloud-starter-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2023.0.1.3\spring-cloud-alibaba-commons-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-alibaba-nacos-config\2023.0.1.3\spring-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2023.0.1.3\spring-cloud-starter-alibaba-nacos-discovery-2023.0.1.3.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-jms\0.0.1-SNAPSHOT\mydata-start-infrastructure-jms-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-client\4.9.8\rocketmq-client-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-common\4.9.8\rocketmq-common-4.9.8.jar;D:\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-acl\4.9.8\rocketmq-acl-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-remoting\4.9.8\rocketmq-remoting-4.9.8.jar;D:\maven\repository\io\netty\netty-all\4.2.2.Final\netty-all-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec\4.2.2.Final\netty-codec-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-haproxy\4.2.2.Final\netty-codec-haproxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http3\4.2.2.Final\netty-codec-http3-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-memcache\4.2.2.Final\netty-codec-memcache-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-mqtt\4.2.2.Final\netty-codec-mqtt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-redis\4.2.2.Final\netty-codec-redis-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-smtp\4.2.2.Final\netty-codec-smtp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-stomp\4.2.2.Final\netty-codec-stomp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-xml\4.2.2.Final\netty-codec-xml-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-protobuf\4.2.2.Final\netty-codec-protobuf-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-marshalling\4.2.2.Final\netty-codec-marshalling-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-ssl-ocsp\4.2.2.Final\netty-handler-ssl-ocsp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-rxtx\4.2.2.Final\netty-transport-rxtx-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-sctp\4.2.2.Final\netty-transport-sctp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-udt\4.2.2.Final\netty-transport-udt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-kqueue\4.2.2.Final\netty-transport-classes-kqueue-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-io_uring\4.2.2.Final\netty-transport-classes-io_uring-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-classes-quic\4.2.2.Final\netty-codec-classes-quic-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-windows-x86_64.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-logging\4.9.8\rocketmq-logging-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.8\rocketmq-srvutil-4.9.8.jar;D:\maven\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;D:\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven\repository\org\springframework\kafka\spring-kafka\3.3.7\spring-kafka-3.3.7.jar;D:\maven\repository\org\springframework\spring-messaging\6.2.3\spring-messaging-6.2.3.jar;D:\maven\repository\org\springframework\spring-tx\6.2.3\spring-tx-6.2.3.jar;D:\maven\repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar;D:\maven\repository\org\apache\kafka\kafka-clients\3.9.1\kafka-clients-3.9.1.jar;D:\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;D:\maven\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\maven\repository\io\projectreactor\kafka\reactor-kafka\1.3.23\reactor-kafka-1.3.23.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-cache\0.0.1-SNAPSHOT\mydata-start-infrastructure-cache-0.0.1-20250819.023657-87.jar;D:\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-os\0.0.1-SNAPSHOT\mydata-start-infrastructure-os-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\qcloud\cos_api\5.6.242\cos_api-5.6.242.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-kms\3.1.1138\tencentcloud-sdk-java-kms-3.1.1138.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-common\3.1.1138\tencentcloud-sdk-java-common-3.1.1138.jar;D:\maven\repository\com\squareup\okhttp3\okhttp\3.12.13\okhttp-3.12.13.jar;D:\maven\repository\com\squareup\okio\okio\1.15.0\okio-1.15.0.jar;D:\maven\repository\com\squareup\okhttp3\logging-interceptor\3.12.13\logging-interceptor-3.12.13.jar;D:\maven\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\maven\repository\com\thoughtworks\xstream\xstream\1.4.21\xstream-1.4.21.jar;D:\maven\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\maven\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\maven\repository\com\auth0\java-jwt\4.4.0\java-jwt-4.4.0.jar;D:\maven\repository\com\qcloud\cos-sts_api\3.1.1\cos-sts_api-3.1.1.jar;D:\maven\repository\com\aliyun\alibabacloud-sts20150401\1.0.7\alibabacloud-sts20150401-1.0.7.jar;D:\maven\repository\com\aliyun\aliyun-gateway-pop\0.2.15-beta\aliyun-gateway-pop-0.2.15-beta.jar;D:\maven\repository\com\aliyun\darabonba-java-core\0.2.15-beta\darabonba-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-http-apache\0.2.15-beta\aliyun-http-apache-0.2.15-beta.jar;D:\maven\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;D:\maven\repository\com\aliyun\aliyun-java-core\0.2.15-beta\aliyun-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-auth\0.2.15-beta\aliyun-java-auth-0.2.15-beta.jar;D:\maven\repository\com\aliyun\oss\aliyun-sdk-oss\3.18.2\aliyun-sdk-oss-3.18.2.jar;D:\maven\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\maven\repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-core\4.7.3\aliyun-java-sdk-core-4.7.3.jar;D:\maven\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\maven\repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar;D:\maven\repository\commons-logging\commons-logging\1.3.4\commons-logging-1.3.4.jar;D:\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\maven\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\maven\repository\com\aliyun\java-trace-api\0.2.11-beta\java-trace-api-0.2.11-beta.jar;D:\maven\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;D:\maven\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-proxy\0.0.1-SNAPSHOT\mydata-start-infrastructure-proxy-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-security\0.0.1-SNAPSHOT\mydata-start-infrastructure-security-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-validation\0.0.1-SNAPSHOT\mydata-start-infrastructure-validation-0.0.1-20250819.023657-86.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.4.3\spring-boot-starter-validation-3.4.3.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.36\tomcat-embed-el-10.1.36.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-webflux\3.4.3\spring-boot-starter-webflux-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.4.3\spring-boot-starter-json-3.4.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.4.3\spring-boot-starter-reactor-netty-3.4.3.jar;D:\maven\repository\org\springframework\spring-web\6.2.3\spring-web-6.2.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\4.2.0\spring-cloud-starter-bootstrap-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter\4.2.0\spring-cloud-starter-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;D:\maven\repository\org\springframework\security\spring-security-crypto\6.4.3\spring-security-crypto-6.4.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-commons\4.2.0\spring-cloud-commons-4.2.0.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk18on\1.78.1\bcprov-jdk18on-1.78.1.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.3\spring-boot-starter-actuator-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.3\spring-boot-actuator-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator\3.4.3\spring-boot-actuator-3.4.3.jar;D:\maven\repository\io\micrometer\micrometer-observation\1.14.4\micrometer-observation-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-commons\1.14.4\micrometer-commons-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-jakarta9\1.14.4\micrometer-jakarta9-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-core\1.14.4\micrometer-core-1.14.4.jar;D:\maven\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;D:\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven\repository\com\xk\xk-start-application\0.0.1-SNAPSHOT\xk-start-application-0.0.1-20250818.091612-115.jar;D:\maven\repository\com\xk\xk-start-domain-core\0.0.1-SNAPSHOT\xk-start-domain-core-0.0.1-20250818.091612-127.jar;D:\maven\repository\com\xk\xk-start-domain-event\0.0.1-SNAPSHOT\xk-start-domain-event-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-interfaces\0.0.1-SNAPSHOT\xk-start-interfaces-0.0.1-20250818.091612-122.jar;D:\maven\repository\com\xk\xk-start-domain-enum\0.0.1-SNAPSHOT\xk-start-domain-enum-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-infrastructure\0.0.1-SNAPSHOT\xk-start-infrastructure-0.0.1-20250818.091612-118.jar;D:\maven\repository\com\alibaba\easyexcel\4.0.3\easyexcel-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-core\4.0.3\easyexcel-core-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;D:\maven\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;D:\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\maven\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\maven\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;D:\maven\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;D:\maven\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;D:\maven\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;D:\maven\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;D:\maven\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;D:\maven\repository\org\apache\commons\commons-csv\1.11.0\commons-csv-1.11.0.jar;D:\maven\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\maven\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\maven\repository\com\xk\xk-start-gateway\0.0.1-SNAPSHOT\xk-start-gateway-0.0.1-20250818.091612-121.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-openfeign\4.2.0\spring-cloud-starter-openfeign-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-openfeign-core\4.2.0\spring-cloud-openfeign-core-4.2.0.jar;D:\maven\repository\io\github\openfeign\feign-form-spring\13.5\feign-form-spring-13.5.jar;D:\maven\repository\io\github\openfeign\feign-form\13.5\feign-form-13.5.jar;D:\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\maven\repository\io\github\openfeign\feign-core\13.5\feign-core-13.5.jar;D:\maven\repository\io\github\openfeign\feign-slf4j\13.5\feign-slf4j-13.5.jar;D:\code\xk\xk-goods\xk-goods-application\target\classes;D:\maven\repository\com\myco\mydata\config\mydata-config-application\0.0.1-SNAPSHOT\mydata-config-application-0.0.1-20250719.075944-7.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-domain\0.0.1-SNAPSHOT\mydata-config-domain-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-interfaces\0.0.1-SNAPSHOT\mydata-config-interfaces-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-gateway\0.0.1-SNAPSHOT\mydata-config-gateway-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-infrastructure\0.0.1-SNAPSHOT\mydata-config-infrastructure-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-event\0.0.1-SNAPSHOT\mydata-start-infrastructure-event-0.0.1-20250819.023657-88.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-schedule\0.0.1-SNAPSHOT\mydata-start-infrastructure-schedule-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\quartz-scheduler\quartz\2.5.0\quartz-2.5.0.jar;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-core\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-event\target\classes;D:\code\xk\xk-goods\xk-goods-interfaces\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-enum\target\classes;D:\code\xk\xk-goods\xk-goods-gateway\target\classes;D:\maven\repository\com\xk\corp\xk-corp-interfaces\0.0.1-SNAPSHOT\xk-corp-interfaces-0.0.1-20250728.125932-33.jar;D:\maven\repository\com\xk\corp\xk-corp-domain-enum\0.0.1-SNAPSHOT\xk-corp-domain-enum-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\message\xk-message-domain-enum\0.0.1-SNAPSHOT\xk-message-domain-enum-0.0.1-20250819.084230-25.jar;D:\maven\repository\com\xk\acct\xk-acct-interfaces\0.0.1-SNAPSHOT\xk-acct-interfaces-0.0.1-20250818.092336-74.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-enum\0.0.1-SNAPSHOT\xk-acct-domain-enum-0.0.1-20250813.030141-70.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-enum\target\classes;D:\maven\repository\com\xk\acct\xk-acct-domain-event\0.0.1-SNAPSHOT\xk-acct-domain-event-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\config\xk-config-interfaces\0.0.1-SNAPSHOT\xk-config-interfaces-0.0.1-20250805.030408-16.jar;D:\maven\repository\com\xk\config\xk-config-domain-enum\0.0.1-SNAPSHOT\xk-config-domain-enum-0.0.1-20250805.030408-16.jar;D:\code\xk\xk-third-party\xk-third-party-interfaces\target\classes;D:\maven\repository\com\xk\search\xk-search-interfaces\0.0.1-SNAPSHOT\xk-search-interfaces-0.0.1-20250818.123046-199.jar;D:\maven\repository\com\xk\search\xk-search-domain-enum\0.0.1-SNAPSHOT\xk-search-domain-enum-0.0.1-20250818.123046-208.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-enum\target\classes;D:\maven\repository\com\xk\corp\xk-corp-domain-event\0.0.1-SNAPSHOT\xk-corp-domain-event-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-enum\0.0.1-SNAPSHOT\xk-auth-domain-enum-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\promotion\xk-promotion-interfaces\0.0.1-SNAPSHOT\xk-promotion-interfaces-0.0.1-20250804.073817-18.jar;D:\maven\repository\com\xk\promotion\xk-promotion-domain-enum\0.0.1-SNAPSHOT\xk-promotion-domain-enum-0.0.1-20250804.073817-16.jar;D:\code\xk\xk-goods\xk-goods-infrastructure\target\classes;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-spring-boot-starter\1.4.8\mapstruct-plus-spring-boot-starter-1.4.8.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus\1.4.8\mapstruct-plus-1.4.8.jar;D:\maven\repository\org\mapstruct\mapstruct\1.5.5.Final\mapstruct-1.5.5.Final.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-object-convert\1.4.8\mapstruct-plus-object-convert-1.4.8.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.3\spring-boot-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar;D:\maven\repository\org\springframework\spring-context\6.2.3\spring-context-6.2.3.jar;D:\maven\repository\org\springframework\spring-aop\6.2.3\spring-aop-6.2.3.jar;D:\maven\repository\org\springframework\spring-beans\6.2.3\spring-beans-6.2.3.jar;D:\maven\repository\org\springframework\spring-expression\6.2.3\spring-expression-6.2.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.4.3\spring-boot-starter-3.4.3.jar;D:\maven\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;D:\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\maven\repository\org\springframework\spring-core\6.2.3\spring-core-6.2.3.jar;D:\maven\repository\org\springframework\spring-jcl\6.2.3\spring-jcl-6.2.3.jar;D:\maven\repository\io\vertx\vertx-core\5.0.1\vertx-core-5.0.1.jar;D:\maven\repository\io\vertx\vertx-core-logging\5.0.1\vertx-core-logging-5.0.1.jar;D:\maven\repository\io\netty\netty-common\4.2.2.Final\netty-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-buffer\4.2.2.Final\netty-buffer-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport\4.2.2.Final\netty-transport-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler\4.2.2.Final\netty-handler-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-unix-common\4.2.2.Final\netty-transport-native-unix-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-base\4.2.2.Final\netty-codec-base-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-proxy\4.2.2.Final\netty-handler-proxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-socks\4.2.2.Final\netty-codec-socks-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http\4.2.2.Final\netty-codec-http-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-compression\4.2.2.Final\netty-codec-compression-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http2\4.2.2.Final\netty-codec-http2-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver\4.2.2.Final\netty-resolver-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver-dns\4.2.2.Final\netty-resolver-dns-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-dns\4.2.2.Final\netty-codec-dns-4.2.2.Final.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-log4j2\3.4.3\spring-boot-starter-log4j2-3.4.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.3\log4j-slf4j2-impl-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-jul\2.24.3\log4j-jul-2.24.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.3\spring-boot-configuration-processor-3.4.3.jar;D:\maven\repository\io\projectreactor\reactor-core\3.7.7\reactor-core-3.7.7.jar;D:\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\lib\idea_rt.jar
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.library.path=C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\bin;C:\Program Files\JetBrains\PyCharm 2025.1.3.1\bin;;.
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:java.compiler=<NA>
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:os.name=Windows 11
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:os.arch=amd64
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:os.version=10.0
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:user.name=ShiJia
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:user.home=C:\Users\<USER>\code\xk
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:os.memory.free=99MB
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:os.memory.max=8048MB
[main:1]2025-08-21 15:47:53.901 INFO  [ZooKeeper:] - Client environment:os.memory.total=180MB
[main:1]2025-08-21 15:47:53.919 INFO  [CuratorFrameworkImpl:] - Starting
[main:1]2025-08-21 15:47:53.919 DEBUG [CuratorZookeeperClient:] - Starting
[main:1]2025-08-21 15:47:53.919 DEBUG [ConnectionState:] - Starting
[main:1]2025-08-21 15:47:53.919 DEBUG [ConnectionState:] - reset
[main:1]2025-08-21 15:47:53.921 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@28d61fc
[main:1]2025-08-21 15:47:53.923 INFO  [X509Util:] - Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
[main:1]2025-08-21 15:47:53.927 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main:1]2025-08-21 15:47:53.931 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main:1]2025-08-21 15:47:53.937 INFO  [CuratorFrameworkImpl:] - Default schema
[main:1]2025-08-21 15:47:53.937 INFO  [ZookeeperClientFactoryBean:] - ZK connection is successful.
[main:1]2025-08-21 15:47:53.937 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:53.944 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:53.954 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:53.978 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:53.996 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.003 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.045 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.084 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.ncs.zookeeper.lockTimeout.user' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type Integer
[main:1]2025-08-21 15:47:54.088 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.101 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-producer.rmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:54.103 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.107 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.133 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.167 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.174 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.179 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'liveObjectDao' of type [com.xk.infrastructure.cache.dao.object.LiveObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.186 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.189 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.194 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.203 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.212 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.214 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.readonlyRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:54.214 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.requiredRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [create*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [revise*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [sync*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [add*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [update*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [handle] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [incr*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [amend*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [save*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [persist*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [remove*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [terminate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [delete*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [insert*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [commit*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [merge*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [apply*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [initiate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [alter*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [cancel*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [mod*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [retire*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.215 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [store*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.216 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.221 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:54.222 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.225 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.225 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 15:47:54.225 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.227 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:54.227 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:47:54.523 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.level: simple
[main:1]2025-08-21 15:47:54.523 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.targetRecords: 4
[main:1]2025-08-21 15:47:54.596 DEBUG [GlobalEventExecutor:] - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[main:1]2025-08-21 15:47:55.457 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.zmq-producer.zmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:56.045 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.redis.tableRedisClientRef' in PropertySource 'Config resource 'class path resource [application-data.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:47:58.701 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:47:58.702 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:47:58.702 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:47:58.703 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:47:58.703 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:47:59.371 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.371 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.388 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.388 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.397 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.397 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.405 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.405 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.413 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.413 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.421 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.421 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.429 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.429 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.437 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.437 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.447 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.447 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.455 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.455 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.464 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.464 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.472 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.472 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.481 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.481 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.488 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.488 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.498 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.498 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.507 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.507 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.620 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.620 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.630 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.630 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.639 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.639 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.649 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.649 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.661 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.661 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.671 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.671 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.680 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.680 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.689 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.689 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.698 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.698 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.707 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.707 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.716 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.716 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.726 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:47:59.726 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:48:00.099 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:48:00.099 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:48:00.100 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:48:00.846 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-21 15:48:00.892 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[main:1]2025-08-21 15:48:00.894 DEBUG [AbstractConfigurationImpl:] - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
[main:1]2025-08-21 15:48:00.894 DEBUG [AbstractConfigurationImpl:] - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
[main:1]2025-08-21 15:48:00.896 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[main:1]2025-08-21 15:48:00.896 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via user class loader
[main:1]2025-08-21 15:48:00.896 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[main:1]2025-08-21 15:48:00.896 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[main:1]2025-08-21 15:48:00.896 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[main:1]2025-08-21 15:48:00.897 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[main:1]2025-08-21 15:48:00.898 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[main:1]2025-08-21 15:48:00.899 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
[main:1]2025-08-21 15:48:00.899 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[main:1]2025-08-21 15:48:00.899 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[main:1]2025-08-21 15:48:00.899 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[main:1]2025-08-21 15:48:00.899 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 15:48:01.861 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main-SendThread(*************:2181):86]2025-08-21 15:48:02.991 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):86]2025-08-21 15:48:02.991 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):86]2025-08-21 15:48:03.006 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:56153, server: *************/*************:2181
[main-SendThread(*************:2181):86]2025-08-21 15:48:03.031 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x1000001140633b0, negotiated timeout = 40000
[main-EventThread:87]2025-08-21 15:48:03.033 DEBUG [ConnectionState:] - Negotiated session timeout: 40000
[main-EventThread:87]2025-08-21 15:48:03.035 INFO  [ConnectionStateManager:] - State change: CONNECTED
[main-EventThread:87]2025-08-21 15:48:03.035 DEBUG [CuratorFrameworkImpl:] - Clearing sleep for 10 operations
[Curator-ConnectionStateManager-0:85]2025-08-21 15:48:03.035 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: CONNECTED
[main-EventThread:87]2025-08-21 15:48:03.052 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 15:48:03.052 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main-EventThread:87]2025-08-21 15:48:03.053 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 15:48:03.053 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main:1]2025-08-21 15:48:03.865 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorCreateEvent
[main:1]2025-08-21 15:48:03.914 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorDeleteEvent
[main:1]2025-08-21 15:48:03.921 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorUpdateEvent
[main:1]2025-08-21 15:48:03.928 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_CORP-CORP-CorpCreateEvent
[main:1]2025-08-21 15:48:03.934 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportCreateEvent
[main:1]2025-08-21 15:48:03.940 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportDeleteEvent
[main:1]2025-08-21 15:48:03.947 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportUpdateEvent
[main:1]2025-08-21 15:48:03.953 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateGoodsEvent
[main:1]2025-08-21 15:48:03.959 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteGoodsEvent
[main:1]2025-08-21 15:48:03.966 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GoodsStockEmptyEvent
[main:1]2025-08-21 15:48:03.971 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 15:48:03.978 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent
[main:1]2025-08-21 15:48:03.984 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 15:48:03.990 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductFirstListingEvent
[main:1]2025-08-21 15:48:03.996 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductRemainRandomEvent
[main:1]2025-08-21 15:48:04.002 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateGoodsEvent
[main:1]2025-08-21 15:48:04.008 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateRemainStockEvent
[main:1]2025-08-21 15:48:04.015 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateStockEvent
[main:1]2025-08-21 15:48:04.023 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_ORDER-ORDER-OrderPaidEvent
[main:1]2025-08-21 15:48:04.029 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScoreEvent
[main:1]2025-08-21 15:48:04.036 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScorePagerEvent
[main:1]2025-08-21 15:48:04.043 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateScoreRuleEvent
[main:1]2025-08-21 15:48:04.048 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateSeriesCateEvent
[main:1]2025-08-21 15:48:04.054 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteSeriesCateEvent
[main:1]2025-08-21 15:48:04.060 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateSeriesCateEvent
[main:1]2025-08-21 15:48:04.065 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateTeamMemberEvent
[main:1]2025-08-21 15:48:04.071 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteTeamMemberEvent
[main:1]2025-08-21 15:48:04.078 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateTeamMemberEvent
[main:1]2025-08-21 15:48:04.120 DEBUG [AutoConfigurationPackages:213] - @EnableAutoConfiguration was declared on a class in the package 'com.myco.mydata.server'. Automatic @Repository and @Entity scanning is enabled.
[main:1]2025-08-21 15:48:04.248 DEBUG [Mappings:] - 
	c.x.g.a.q.g.MerchantProductAppQueryServiceImpl:
	{POST /goods/merchant-product/app/query/goodsRule}: goodsRule(Mono)
	{POST /goods/merchant-product/app/query/pick/category}: searchPickCategory(Mono)
	{POST /goods/merchant-product/app/query/pick/stock}: searchPickStock(Mono)
	{POST /goods/merchant-product/app/query/buyer/detail}: searchBuyerDetail(Mono)
	{POST /goods/merchant-product/app/query/promotion/detail}: searchPromotionDetail(Mono)
[main:1]2025-08-21 15:48:04.256 DEBUG [RequestMappingHandlerMapping:164] - 5 mappings in 'requestMappingHandlerMapping'
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/color/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorQueryServiceRoutingConfig$$Lambda/0x000002010adb9118@25fc90aa
 ((POST && /inner/search) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorQueryServiceRoutingConfig$$Lambda/0x000002010adb9d68@1e53dc3b
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/color => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x000002010adba6b0@f3bea57
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x000002010adba8c8@78ee1819
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x000002010adbaae0@3956ae6e
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/gift/report/query => {
 ((POST && /sync) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbacf8@39640f7b
 ((POST && /pre/query/current) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbaf10@69dd3352
 ((POST && /pre/query) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbb128@61fbd0a0
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbb340@6dd2372f
 ((POST && /search/update/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbb558@74d4e239
 ((POST && /inner/search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbb770@********
 ((POST && /inner/search) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000002010adbb988@4bd2a8b0
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/gift/report => {
 ((POST && /multi/create) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbbba0@37c595aa
 ((POST && /lock) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbbdb8@64536c41
 ((POST && /unlock) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbbfd0@86ea911
 ((POST && /next) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbc1e8@56a70784
 ((POST && /pre/create) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbc400@4d54b03f
 ((POST && /multi/execute) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbc618@321aecb0
 ((POST && /single/execute) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbc830@4fc1edc3
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbca48@21b44898
 ((POST && /count/reset) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000002010adbcc60@4989d1d9
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/product-category/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbce78@2c13db34
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/product-category/app/query => {
 ((POST && /search/parent) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbd090@6c7c8480
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/query => {
 ((POST && /material/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbd2a8@6f0fe11f
 ((POST && /mall/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbd4c0@688ff23e
 ((POST && /collectible/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbd6d8@5af75e60
 ((POST && /merchant/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbd8f0@52a7893a
 ((POST && /merchant/ewd/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbdb08@42b09d68
 ((POST && /specification/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbdd20@********
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/materials-product/query => {
 ((POST && /app/search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbdf38@35ddc99b
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbe150@5b5d7843
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbe368@1dc0f59d
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/mall-product/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbe580@4a80ebe
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbe798@5fe29ea1
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/collectible-card/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbe9b0@6fcd6a7e
 ((POST && /search/show/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbebc8@753b0607
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbede0@31222be0
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/merchant-product/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbeff8@7a7ebcf3
 ((POST && /search/corp/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbf210@7e64a758
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbf428@53c29dad
 ((POST && /corp/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbf640@731fd11e
 ((POST && /inner/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbf858@7c8962aa
 ((POST && /status/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbfa70@278c5404
 ((POST && /status/corp/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adbfc88@60648aad
 ((POST && /corp/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0000@6d7e209e
 ((POST && /show/price) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0218@5ad583a9
 ((POST && /pay/price) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0430@1fbcd083
 ((POST && /inner/spec/serial) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0648@5b49003b
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/merchant-product/app/query => {
 ((POST && /promotion/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0860@47afb11c
 ((POST && /buyer/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0a78@3191ba8b
 ((POST && /pick/category) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0c90@48505cf2
 ((POST && /pick/stock) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc0ea8@5b8bc51b
 ((POST && /goodsRule) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc10c0@6196a827
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/live/query => {
 ((POST && /replay) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000002010adc12d8@5def137c
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/product-category => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc14f0@179273ff
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc1708@12b4cf0b
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc1920@46379aea
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/materials-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc1b38@63e605a3
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc1d50@676f8fab
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc1f68@5397c8da
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc2180@290df888
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc2398@7a953b17
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/mall-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc25b0@611ce139
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc27c8@ca024d8
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc29e0@4aaf547c
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc2bf8@19b183bd
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc2e10@1f22ac9b
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3028@12a9cd43
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/collectible-card => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3240@221577de
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3458@37aca44a
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3670@3de08050
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3888@617f817a
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3aa0@68ddb10c
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3cb8@3a81d6a9
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/merchant-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc3ed0@3da33a9
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc40e8@1eb306f9
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4300@1be391f2
 ((POST && /update/status) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4518@2fe328b9
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4730@7df60197
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4948@7b74541e
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4b60@7c9f2683
 ((POST && /audit/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4d78@4a8f6f49
 ((POST && /recycle/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc4f90@7383536
 ((POST && /copy/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc51a8@5eb5556b
 ((POST && /update/publicity) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc53c0@5b9bdb00
 ((POST && /update/remain/random) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc55d8@6adf021d
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/merchant/score/rule => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000002010adc57f0@2c080bf5
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/object => {
 ((POST && /getGoodsObject) && Accept: application/json) -> com.xk.goods.server.endpoints.object.ObjectQueryRoutingConfig$$Lambda/0x000002010adc5a08@58620d12
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/random-distribution => {
 ((POST && /get-item) && Accept: application/json) -> com.xk.goods.server.endpoints.random.RandomDistributionServiceRoutingConfig$$Lambda/0x000002010adc5c20@5da48ec5
 ((POST && /callback-item) && Accept: application/json) -> com.xk.goods.server.endpoints.random.RandomDistributionServiceRoutingConfig$$Lambda/0x000002010adc5e38@4f79a086
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/score/rule/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreQueryRoutingConfig$$Lambda/0x000002010adc6050@23c25d3f
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/score/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreQueryRoutingConfig$$Lambda/0x000002010adc6268@6b00dec8
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/score/rule => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreServiceRoutingConfig$$Lambda/0x000002010adc6480@12aba7cb
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/score => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreServiceRoutingConfig$$Lambda/0x000002010adc6698@********
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/group-category/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc68b0@21ced418
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/group/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc6ac8@4c55b877
 ((POST && /searchQuick) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc6ce0@38fdbc9
 ((POST && /searchOriginalDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc6ef8@31adc338
 ((POST && /searchSpecialDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7110@6694bf01
 ((POST && /app/searchGroupTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7328@7e325a4
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/item/query => {
 ((POST && /app/searchOriginalTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7540@7acff702
 ((POST && /app/searchGiftItem) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7758@25245f60
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/item/query => {
 ((POST && /searchOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7970@668b9072
 ((POST && /searchOriginalTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7b88@78db5702
 ((POST && /searchSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc7da0@7d29d8fc
 ((POST && /searchTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8000@7a715766
 ((POST && /searchNum) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8218@4043405a
 ((POST && /downloadTemplate) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8430@62027c17
 ((POST && /downloadOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8648@51dfb693
 ((POST && /corp/downloadOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8860@216501a9
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/template/query => {
 ((POST && /getList) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8a78@7bfec44f
 ((POST && /getDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000002010adc8c90@aedede3
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/group-category => {
 ((POST && /save) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc8ea8@26aab903
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc90c0@2f4b8c36
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc92d8@221ae92c
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/group => {
 ((POST && /saveOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc94f0@477d8a79
 ((POST && /updateOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc9708@34ec4134
 ((POST && /updateForbidden) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc9920@47b541a3
 ((POST && /saveSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc9b38@329383f0
 ((POST && /updateSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc9d50@1916f999
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/item => {
 ((POST && /saveSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc9f68@4f25fe6e
 ((POST && /updateSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adca180@22f00670
 ((POST && /updateSpecialShow) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adca398@c529ddd
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/serial/template => {
 ((POST && /save) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adca5b0@222667ff
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adca7c8@42103b5b
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adca9e0@60f166ef
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/query/series => {
 ((POST && /category/tree) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesQueryRoutingConfig$$Lambda/0x000002010adcabf8@7855b359
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/series => {
 ((POST && /category/save) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x000002010adcae10@695d2a33
 ((POST && /category/update) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x000002010adcb028@2ad41ea0
 ((POST && /category/delete) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x000002010adcb240@4179d30b
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/stock => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.stock.StockServiceRoutingConfig$$Lambda/0x000002010adcb458@14e37f06
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/query/team => {
 ((POST && /member/searchPager) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamQueryRoutingConfig$$Lambda/0x000002010adcb670@14efd31b
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /goods/team => {
 ((POST && /member/save) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x000002010adcb888@1ec4fe38
 ((POST && /member/update) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x000002010adcbaa0@112a8760
 ((POST && /member/delete) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x000002010adcbcb8@1ebec10c
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped (GET && /favicon.ico) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x000002010addf718@7423becb
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /server => {
 (GET && /check) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x000002010addf928@4dafab49
}
[main:1]2025-08-21 15:48:04.318 DEBUG [Mappings:] - Mapped /tag => {
 ((POST && /save) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000002010addfb38@87b341b
 ((POST && /update) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000002010addfd50@602532b9
 ((POST && /remove) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000002010ade0000@9190d90
}
[main:1]2025-08-21 15:48:04.346 DEBUG [Mappings:] - 'resourceHandlerMapping' {/webjars/**=ResourceWebHandler [classpath [META-INF/resources/webjars/]], /**=ResourceWebHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/]]}
[main:1]2025-08-21 15:48:04.770 INFO  [StdSchedulerFactory:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 15:48:04.781 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 15:48:04.781 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 15:48:04.781 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 15:48:04.782 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 15:48:04.782 INFO  [StdSchedulerFactory:] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[main:1]2025-08-21 15:48:04.782 INFO  [StdSchedulerFactory:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 15:48:04.782 INFO  [QuartzScheduler:] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1b9e0eb0
[main:1]2025-08-21 15:48:04.967 DEBUG [InternalLoggerFactory:] - Using SLF4J as the default logging framework
[main:1]2025-08-21 15:48:05.185 INFO  [EndpointLinksResolver:60] - Exposing 19 endpoints beneath base path '/actuator'
[main:1]2025-08-21 15:48:05.203 DEBUG [WebFluxEndpointHandlerMapping:164] - 34 mappings in 'webEndpointReactiveHandlerMapping'
[main:1]2025-08-21 15:48:05.257 DEBUG [ControllerMethodResolver:289] - ControllerAdvice beans: none
[main:1]2025-08-21 15:48:05.385 DEBUG [HttpWebHandlerAdapter:267] - enableLoggingRequestDetails='false': form data and headers will be masked to prevent unsafe logging of potentially sensitive data
[main:1]2025-08-21 15:48:05.533 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:48:05.533 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:48:05.533 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:48:05.533 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:48:05.534 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:48:05.547 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.log.errLogDataSourceName' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:48:05.626 INFO  [JvmCacheConsumerFactoryBean:] - The JVM cache to start listening...
[main:1]2025-08-21 15:48:05.641 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.language.exception.baseNames' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:48:05.739 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 15:48:05.739 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: main
[main:1]2025-08-21 15:48:05.740 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 15:48:05.740 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 15:48:05.740 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 15:48:05.740 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 15:48:05.740 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[main:1]2025-08-21 15:48:05.740 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 15:48:05.780 INFO  [JobDetailBuilder:] - 没有服务器[192.168.13.28],需要执行的任务
[main:1]2025-08-21 15:48:05.780 INFO  [QuartzSchedulerManager:] - 共获取到【0】个需要处理的Jobs!
[main:1]2025-08-21 15:48:06.152 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-21 15:48:06.436 DEBUG [SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin:131] - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
[main:1]2025-08-21 15:48:07.335 INFO  [NettyWebServer:126] - Netty started on port 11006 (http)
[main:1]2025-08-21 15:48:07.344 INFO  [naming:] - Nacos client key init properties: 
	serverAddr=*************:8848
	namespace=dev
	username=nacos
	password=EQ********3u

[main:1]2025-08-21 15:48:07.345 INFO  [naming:] - initializer namespace from ans.namespace attribute : null
[main:1]2025-08-21 15:48:07.345 INFO  [naming:] - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[main:1]2025-08-21 15:48:07.345 INFO  [naming:] - initializer namespace from namespace attribute :null
[main:1]2025-08-21 15:48:07.349 INFO  [naming:] - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[main:1]2025-08-21 15:48:07.352 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[main:1]2025-08-21 15:48:07.352 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[main:1]2025-08-21 15:48:07.477 INFO  [client:] - [RpcClientFactory] create a new rpc client of e8ad5374-f971-4b1a-acb6-710681e81057
[main:1]2025-08-21 15:48:07.478 INFO  [naming:] - Create naming rpc client for uuid->e8ad5374-f971-4b1a-acb6-710681e81057
[main:1]2025-08-21 15:48:07.478 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[main:1]2025-08-21 15:48:07.478 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[main:1]2025-08-21 15:48:07.478 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[main:1]2025-08-21 15:48:07.478 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
[main:1]2025-08-21 15:48:07.478 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main:1]2025-08-21 15:48:07.539 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Success to connect to server [*************:8848] on start up, connectionId = 1755762486303_221.12.20.178_25401
[main:1]2025-08-21 15:48:07.539 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[main:1]2025-08-21 15:48:07.539 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x000002010a5dc1d0
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 15:48:07.539 INFO  [client:] - [e8ad5374-f971-4b1a-acb6-710681e81057] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 15:48:07.539 INFO  [naming:] - Grpc connection connect
[main:1]2025-08-21 15:48:07.540 INFO  [naming:] - [REGISTER-SERVICE] dev registering service xkGoods with instance Instance{instanceId='null', ip='*************', port=11006, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkGoods', serviceName='null', metadata={preserved.heart.beat.timeout=20000, preserved.ip.delete.timeout=60000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=10000}}
[main:1]2025-08-21 15:48:07.574 INFO  [NacosServiceRegistry:] - nacos registry, DEFAULT_GROUP xkGoods *************:11006 register finished
[main:1]2025-08-21 15:48:07.584 DEBUG [LoggerFactory:] - Using io.vertx.core.logging.SLF4JLogDelegateFactory
[main:1]2025-08-21 15:48:07.683 INFO  [VertxEventBusManager:] - Event bus 'CONFIG' started.
[main:1]2025-08-21 15:48:07.689 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:48:07.689 INFO  [VertxEventBusManager:] - Event queue 'CONFIG' started.
[main:1]2025-08-21 15:48:07.718 INFO  [VertxEventBusManager:] - Event bus 'ORDER' started.
[main:1]2025-08-21 15:48:07.718 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:48:07.718 INFO  [VertxEventBusManager:] - Event queue 'ORDER' started.
[main:1]2025-08-21 15:48:07.748 INFO  [VertxEventBusManager:] - Event bus 'CORP' started.
[main:1]2025-08-21 15:48:07.748 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:48:07.748 INFO  [VertxEventBusManager:] - Event queue 'CORP' started.
[main:1]2025-08-21 15:48:07.784 INFO  [VertxEventBusManager:] - Event bus 'AUTH' started.
[main:1]2025-08-21 15:48:07.784 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:48:07.784 INFO  [VertxEventBusManager:] - Event queue 'AUTH' started.
[main:1]2025-08-21 15:48:07.819 INFO  [VertxEventBusManager:] - Event bus 'GOODS' started.
[main:1]2025-08-21 15:48:07.819 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:48:07.819 INFO  [VertxEventBusManager:] - Event queue 'GOODS' started.
[main:1]2025-08-21 15:48:07.850 INFO  [VertxEventBusManager:] - Event bus 'USER' started.
[main:1]2025-08-21 15:48:07.850 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:48:07.850 INFO  [VertxEventBusManager:] - Event queue 'USER' started.
[vert.x-virtual-thread-0:288]2025-08-21 15:48:07.866 INFO  [AbstractEventVerticle:] - Deploying 'DeleteItemDefineEventHandler-0'...
[main:1]2025-08-21 15:48:07.866 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler
[main:1]2025-08-21 15:48:07.867 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler
[vert.x-virtual-thread-1:294]2025-08-21 15:48:07.867 INFO  [AbstractEventVerticle:] - Deploying 'UpdateItemDefineIdEventHandler-1'...
[vert.x-virtual-thread-0:288]2025-08-21 15:48:07.867 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteItemDefineEvent[CONFIG[YD_CONFIG]]'
[vert.x-virtual-thread-1:294]2025-08-21 15:48:07.867 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateItemDefineIdEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 15:48:07.867 INFO  [VertxEventBusManager:] - register event bus:ORDER, handler:com.xk.goods.application.handler.event.order.OrderPaidEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:295]2025-08-21 15:48:07.867 INFO  [AbstractEventVerticle:] - Deploying 'OrderPaidEventHandler-2'...
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:CORP, handler:com.xk.goods.application.handler.event.corp.CorpCreateEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:296]2025-08-21 15:48:07.868 INFO  [AbstractEventVerticle:] - Deploying 'CorpCreateEventHandler-3'...
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:AUTH, handler:com.xk.application.handler.event.log.LogSecureEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:295]2025-08-21 15:48:07.868 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'OrderPaidEvent[ORDER[YD_ORDER]]'
[vert.x-virtual-thread-0:297]2025-08-21 15:48:07.868 INFO  [AbstractEventVerticle:] - Deploying 'LogSecureEventHandler-4'...
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateStockEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.868 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-3:303]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportCreateEventHandler-5'...
[main:1]2025-08-21 15:48:07.869 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScoreEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.869 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-2:302]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'ColorUpdateEventHandler-6'...
[vert.x-virtual-thread-4:304]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportDeleteEventHandler-7'...
[vert.x-virtual-thread-1:301]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'ColorDeleteEventHandler-8'...
[vert.x-virtual-thread-6:307]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'CreateGoodsEventHandler-9'...
[vert.x-virtual-thread-5:305]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportUpdateEventHandler-10'...
[vert.x-virtual-thread-0:298]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'ColorCreateEventHandler-11'...
[vert.x-virtual-thread-7:308]2025-08-21 15:48:07.869 INFO  [AbstractEventVerticle:] - Deploying 'DeleteGoodsEventHandler-12'...
[vert.x-virtual-thread-8:311]2025-08-21 15:48:07.870 INFO  [AbstractEventVerticle:] - Deploying 'GoodsStockEmptyEventHandler-13'...
[vert.x-virtual-thread-8:311]2025-08-21 15:48:07.870 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GoodsStockEmptyEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:297]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'LogSecureEvent[AUTH[YD_AUTH]]'
[vert.x-virtual-thread-0:296]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CorpCreateEvent[CORP[YD_CORP]]'
[vert.x-virtual-thread-3:303]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportCreateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-7:308]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-5:305]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:298]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorCreateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-6:307]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:301]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-4:304]2025-08-21 15:48:07.874 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-2:302]2025-08-21 15:48:07.876 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorUpdateEvent[GOODS[YD_GOODS]]'
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserCreateEventHandler
[main:1]2025-08-21 15:48:07.876 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserDeleteEventHandler
[main:1]2025-08-21 15:48:07.876 INFO  [NacosDiscoveryHeartBeatPublisher:] - Start nacos heartBeat task scheduler.
[main:1]2025-08-21 15:48:07.878 INFO  [SchedulerFactoryBean:] - Starting Quartz Scheduler now
[main:1]2025-08-21 15:48:07.878 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[quartzScheduler_QuartzSchedulerThread:159]2025-08-21 15:48:07.878 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[vert.x-virtual-thread-9:313]2025-08-21 15:48:07.886 INFO  [AbstractEventVerticle:] - Deploying 'UpdateGoodsEventHandler-14'...
[vert.x-virtual-thread-9:313]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-16:323]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Deploying 'QueryScoreEventHandler-16'...
[vert.x-virtual-thread-13:319]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownJobEventHandler-15'...
[vert.x-virtual-thread-15:322]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductRemainRandomEventHandler-17'...
[vert.x-virtual-thread-14:321]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductFirstListingEventHandler-18'...
[vert.x-virtual-thread-12:318]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownEventHandler-19'...
[vert.x-virtual-thread-14:321]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductFirstListingEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-16:323]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScoreEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-12:318]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-15:322]2025-08-21 15:48:07.887 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductRemainRandomEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-11:316]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UpdateStockEventHandler-20'...
[vert.x-virtual-thread-18:325]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UpdateScoreRuleEventHandler-21'...
[vert.x-virtual-thread-17:324]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'QueryScorePagerEventHandler-22'...
[vert.x-virtual-thread-19:326]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'CreateSeriesCateEventHandler-23'...
[vert.x-virtual-thread-18:325]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateScoreRuleEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-17:324]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScorePagerEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:327]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'DeleteSeriesCateEventHandler-24'...
[vert.x-virtual-thread-10:315]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UpdateRemainStockEventHandler-25'...
[vert.x-virtual-thread-11:316]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-21:328]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UpdateSeriesCateEventHandler-26'...
[vert.x-virtual-thread-22:329]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'CreateTeamMemberEventHandler-27'...
[vert.x-virtual-thread-13:319]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownJobEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-21:328]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-24:331]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UpdateTeamMemberEventHandler-29'...
[vert.x-virtual-thread-23:330]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'DeleteTeamMemberEventHandler-28'...
[vert.x-virtual-thread-1:333]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UserDeleteEventHandler-31'...
[vert.x-virtual-thread-0:332]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Deploying 'UserCreateEventHandler-30'...
[vert.x-virtual-thread-10:315]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateRemainStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:333]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserDeleteEvent[USER[YD_USER]]'
[vert.x-virtual-thread-0:332]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserCreateEvent[USER[YD_USER]]'
[vert.x-virtual-thread-19:326]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:327]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-23:330]2025-08-21 15:48:07.888 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-24:331]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-22:329]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:336]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.corp.CorpCreateEventHandler' with ID: 4
[vert.x-eventloop-thread-0:340]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserCreateEventHandler' with ID: 31
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler' with ID: 11
[vert.x-eventloop-thread-0:340]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserDeleteEventHandler' with ID: 32
[vert.x-eventloop-thread-0:339]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.application.handler.event.log.LogSecureEventHandler' with ID: 5
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorUpdateEventHandler' with ID: 8
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler' with ID: 9
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler' with ID: 13
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler' with ID: 14
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler' with ID: 12
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler' with ID: 10
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler' with ID: 15
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorDeleteEventHandler' with ID: 7
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler' with ID: 20
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScoreEventHandler' with ID: 22
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler' with ID: 21
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler' with ID: 18
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler' with ID: 24
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler' with ID: 23
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateStockEventHandler' with ID: 17
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler' with ID: 27
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler' with ID: 19
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler' with ID: 16
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorCreateEventHandler' with ID: 6
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler' with ID: 25
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler' with ID: 26
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler' with ID: 29
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler' with ID: 30
[vert.x-eventloop-thread-0:338]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler' with ID: 28
[vert.x-eventloop-thread-0:335]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler' with ID: 2
[vert.x-eventloop-thread-0:335]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler' with ID: 1
[vert.x-eventloop-thread-0:337]2025-08-21 15:48:07.889 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.order.OrderPaidEventHandler' with ID: 3
[main:1]2025-08-21 15:48:07.897 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:48:07.897 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:48:07.897 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:48:07.908 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 15:48:07.908 INFO  [ServiceApplicationListener:] -  The xkGoods:dev has been started.
[main:1]2025-08-21 15:48:07.908 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 15:48:07.910 INFO  [XkGoodsServer:] - Started XkGoodsServer in 19.298 seconds (process running for 20.677)
[main:1]2025-08-21 15:48:07.911 DEBUG [ApplicationAvailabilityBean:77] - Application availability state LivenessState changed to CORRECT
[main:1]2025-08-21 15:48:07.923 WARN  [DefaultStdSchedulerFactoryBean:] - 没有可用的Jobs
[main:1]2025-08-21 15:48:07.923 INFO  [QuartzSchedulerManager:] - Will start Quartz Scheduler [DefaultQuartzScheduler] in 5 seconds
[main:1]2025-08-21 15:48:07.925 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-schedule.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 15:48:07.925 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 15:48:07.925 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 15:48:07.925 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=2
[main:1]2025-08-21 15:48:07.925 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-dev.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 15:48:07.926 DEBUG [ApplicationAvailabilityBean:77] - Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
[Thread-14:120]2025-08-21 15:48:08.960 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorCreateEvent' queue.
[Thread-16:122]2025-08-21 15:48:09.009 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorUpdateEvent' queue.
[Thread-19:125]2025-08-21 15:48:09.022 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportDeleteEvent' queue.
[Thread-15:121]2025-08-21 15:48:09.027 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorDeleteEvent' queue.
[Thread-17:123]2025-08-21 15:48:09.039 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_CORP-CORP-CorpCreateEvent' queue.
[Thread-18:124]2025-08-21 15:48:09.041 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportCreateEvent' queue.
[Thread-20:126]2025-08-21 15:48:09.058 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportUpdateEvent' queue.
[Thread-23:129]2025-08-21 15:48:09.074 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GoodsStockEmptyEvent' queue.
[Thread-25:131]2025-08-21 15:48:09.079 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownJobEvent' queue.
[Thread-22:128]2025-08-21 15:48:09.090 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteGoodsEvent' queue.
[Thread-26:132]2025-08-21 15:48:09.095 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-28:134]2025-08-21 15:48:09.095 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductRemainRandomEvent' queue.
[Thread-21:127]2025-08-21 15:48:09.105 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateGoodsEvent' queue.
[Thread-24:130]2025-08-21 15:48:09.105 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-31:137]2025-08-21 15:48:09.108 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateStockEvent' queue.
[Thread-32:138]2025-08-21 15:48:09.120 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_ORDER-ORDER-OrderPaidEvent' queue.
[Thread-34:140]2025-08-21 15:48:09.120 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScorePagerEvent' queue.
[Thread-27:133]2025-08-21 15:48:09.125 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductFirstListingEvent' queue.
[Thread-30:136]2025-08-21 15:48:09.129 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateRemainStockEvent' queue.
[Thread-36:142]2025-08-21 15:48:09.129 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateSeriesCateEvent' queue.
[Thread-29:135]2025-08-21 15:48:09.130 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateGoodsEvent' queue.
[Thread-33:139]2025-08-21 15:48:09.135 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScoreEvent' queue.
[Thread-39:145]2025-08-21 15:48:09.151 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateTeamMemberEvent' queue.
[Thread-41:147]2025-08-21 15:48:09.158 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateTeamMemberEvent' queue.
[Thread-38:144]2025-08-21 15:48:09.187 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateSeriesCateEvent' queue.
[Thread-35:141]2025-08-21 15:48:09.190 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateScoreRuleEvent' queue.
[Thread-37:143]2025-08-21 15:48:09.193 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteSeriesCateEvent' queue.
[Thread-40:146]2025-08-21 15:48:09.225 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteTeamMemberEvent' queue.
[DefaultQuartzScheduler:341]2025-08-21 15:48:12.925 INFO  [QuartzSchedulerManager:] - Starting Quartz Scheduler now
[DefaultQuartzScheduler:341]2025-08-21 15:48:12.925 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 15:48:12.925 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:667]2025-08-21 15:48:16.224 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:667]2025-08-21 15:48:16.224 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14888,"timeFormat":"250821154815"}]
[event-1:671]2025-08-21 15:48:16.258 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:667]2025-08-21 15:48:16.423 INFO  [AbstractDispatchMessageListener:] - The total time of processing 199ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:678]2025-08-21 15:48:17.219 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:678]2025-08-21 15:48:17.219 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14889,"timeFormat":"250821154816"}]
[event-2:681]2025-08-21 15:48:17.221 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:678]2025-08-21 15:48:17.251 INFO  [AbstractDispatchMessageListener:] - The total time of processing 32ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:686]2025-08-21 15:48:20.217 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:686]2025-08-21 15:48:20.217 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14892,"timeFormat":"250821154819"}]
[event-3:690]2025-08-21 15:48:20.219 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:686]2025-08-21 15:48:20.246 INFO  [AbstractDispatchMessageListener:] - The total time of processing 29ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:722]2025-08-21 15:48:28.218 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:722]2025-08-21 15:48:28.218 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14900,"timeFormat":"250821154827"}]
[event-4:726]2025-08-21 15:48:28.220 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:722]2025-08-21 15:48:28.261 INFO  [AbstractDispatchMessageListener:] - The total time of processing 43ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:730]2025-08-21 15:48:29.219 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:730]2025-08-21 15:48:29.219 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14901,"timeFormat":"250821154828"}]
[event-5:734]2025-08-21 15:48:29.220 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:730]2025-08-21 15:48:29.243 INFO  [AbstractDispatchMessageListener:] - The total time of processing 24ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:737]2025-08-21 15:48:31.221 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:737]2025-08-21 15:48:31.221 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14903,"timeFormat":"250821154830"}]
[event-6:741]2025-08-21 15:48:31.222 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:737]2025-08-21 15:48:31.260 INFO  [AbstractDispatchMessageListener:] - The total time of processing 39ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:745]2025-08-21 15:48:32.223 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:745]2025-08-21 15:48:32.223 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14904,"timeFormat":"250821154831"}]
[event-7:749]2025-08-21 15:48:32.224 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:745]2025-08-21 15:48:32.281 INFO  [AbstractDispatchMessageListener:] - The total time of processing 58ms
[quartzScheduler_QuartzSchedulerThread:159]2025-08-21 15:48:37.599 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 15:48:38.442 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:759]2025-08-21 15:48:40.219 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:759]2025-08-21 15:48:40.219 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14912,"timeFormat":"250821154839"}]
[event-8:763]2025-08-21 15:48:40.221 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:759]2025-08-21 15:48:40.243 INFO  [AbstractDispatchMessageListener:] - The total time of processing 24ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:767]2025-08-21 15:48:43.220 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:767]2025-08-21 15:48:43.220 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":14915,"timeFormat":"250821154842"}]
[event-9:771]2025-08-21 15:48:43.221 DEBUG [AOProxyAspect:] - The event is called proxies!
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:767]2025-08-21 15:48:43.246 INFO  [AbstractDispatchMessageListener:] - The total time of processing 26ms
[reactor-http-nio-2:775]2025-08-21 15:48:47.018 DEBUG [HttpWebHandlerAdapter:120] - [e7c9d68d-1] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-2:775]2025-08-21 15:48:47.080 DEBUG [RouterFunctionMapping:189] - [e7c9d68d-1] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000002010adc94f0@477d8a79
[loomBoundedElastic-1:776]2025-08-21 15:48:47.107 DEBUG [Jackson2JsonDecoder:127] - [e7c9d68d-1] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-1:777]2025-08-21 15:48:47.116 DEBUG [AOProxyAspect:] - The Service is called proxies!
[service-1:777]2025-08-21 15:48:47.180 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-1:777]2025-08-21 15:48:47.561 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-1:777]2025-08-21 15:48:47.587 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-1:777]2025-08-21 15:48:47.601 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-1:777]2025-08-21 15:48:47.689 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-1:777]2025-08-21 15:48:47.691 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:163502
[service-1:777]2025-08-21 15:48:47.739 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-1:777]2025-08-21 15:48:47.896 DEBUG [SheetUtils:] - The first is read by default.
[service-1:777]2025-08-21 15:48:47.896 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-1:777]2025-08-21 15:48:47.896 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@2e9913f4
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.047 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.048 DEBUG [DefaultAnalysisEventProcessor:] - Empty row!
[service-1:777]2025-08-21 15:48:48.200 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.220 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.242 INFO  [DruidDataSource:] - {dataSource-1} inited
[service-1:777]2025-08-21 15:48:48.652 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-1:777]2025-08-21 15:48:48.667 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-1:777]2025-08-21 15:48:48.690 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.692 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:777]2025-08-21 15:48:48.711 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.728 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.728 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:777]2025-08-21 15:48:48.731 DEBUG [insertSelective:] - ==> Parameters: 247(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 8940(Long), 283(Long), 283(Long)
[service-1:777]2025-08-21 15:48:48.750 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:777]2025-08-21 15:48:48.752 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:777]2025-08-21 15:48:48.802 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.803 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.803 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.803 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:777]2025-08-21 15:48:48.813 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.813 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:777]2025-08-21 15:48:48.836 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.836 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.836 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.836 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:777]2025-08-21 15:48:48.847 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.857 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.857 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.857 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.857 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:777]2025-08-21 15:48:48.868 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.877 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.877 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.877 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.878 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:777]2025-08-21 15:48:48.887 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.897 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.897 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.897 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.897 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:777]2025-08-21 15:48:48.905 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.923 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.923 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.923 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.924 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:777]2025-08-21 15:48:48.933 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.943 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.943 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.943 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.943 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:777]2025-08-21 15:48:48.953 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.962 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.962 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.962 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.962 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:777]2025-08-21 15:48:48.972 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.980 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.980 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.981 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.981 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:777]2025-08-21 15:48:48.990 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:48.998 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:48.999 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:48.999 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:48.999 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:777]2025-08-21 15:48:49.009 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.017 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.017 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.017 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.017 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:777]2025-08-21 15:48:49.026 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.035 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.035 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.035 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.035 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:777]2025-08-21 15:48:49.044 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.052 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.052 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.052 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.052 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:777]2025-08-21 15:48:49.062 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.072 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.072 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.072 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.072 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:777]2025-08-21 15:48:49.082 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.090 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.091 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.091 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.091 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:777]2025-08-21 15:48:49.101 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.110 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.110 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.110 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.110 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:777]2025-08-21 15:48:49.120 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.129 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.129 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.129 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.131 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:777]2025-08-21 15:48:49.140 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.151 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.151 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.151 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.151 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:777]2025-08-21 15:48:49.161 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.171 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.171 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.171 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.172 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:777]2025-08-21 15:48:49.182 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.190 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.191 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.191 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.191 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:777]2025-08-21 15:48:49.202 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.212 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.212 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.212 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.212 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:777]2025-08-21 15:48:49.221 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.230 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.230 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.230 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.230 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:777]2025-08-21 15:48:49.238 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.249 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.249 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.249 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.249 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:777]2025-08-21 15:48:49.258 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.267 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.268 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.268 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.268 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:777]2025-08-21 15:48:49.280 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.288 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.289 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.289 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.289 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:777]2025-08-21 15:48:49.297 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.306 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.306 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.306 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.307 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:777]2025-08-21 15:48:49.316 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.325 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.325 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.325 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.325 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:777]2025-08-21 15:48:49.335 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.343 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.343 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.344 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.344 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:777]2025-08-21 15:48:49.354 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.363 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.363 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.363 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.363 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:777]2025-08-21 15:48:49.374 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.382 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.382 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.382 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.382 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:777]2025-08-21 15:48:49.393 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.402 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.402 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.402 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.402 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:777]2025-08-21 15:48:49.411 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.420 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.420 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.420 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.420 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:777]2025-08-21 15:48:49.429 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.438 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.438 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.438 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.438 DEBUG [selectByCategoryName:] - ==> Parameters: 尼克斯(String)
[service-1:777]2025-08-21 15:48:49.447 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.456 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.456 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.456 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.456 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:777]2025-08-21 15:48:49.467 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.476 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.476 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.476 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.476 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:777]2025-08-21 15:48:49.486 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.497 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.497 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.497 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.497 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:777]2025-08-21 15:48:49.508 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.517 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.517 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.517 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.517 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:777]2025-08-21 15:48:49.527 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.535 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.536 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.536 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.536 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:777]2025-08-21 15:48:49.546 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.562 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.562 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.562 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.563 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:777]2025-08-21 15:48:49.573 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.581 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.582 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.582 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.582 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:777]2025-08-21 15:48:49.593 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.601 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.601 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.601 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.601 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:777]2025-08-21 15:48:49.610 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.619 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.619 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.620 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.620 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:777]2025-08-21 15:48:49.629 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.638 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.638 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.638 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.638 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:777]2025-08-21 15:48:49.647 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.655 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.655 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.655 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.655 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:777]2025-08-21 15:48:49.665 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.676 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.677 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.677 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.677 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:777]2025-08-21 15:48:49.687 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.695 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.695 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.695 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.695 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:777]2025-08-21 15:48:49.705 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.715 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.715 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.715 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.715 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:777]2025-08-21 15:48:49.726 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.737 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.737 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.737 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.738 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:777]2025-08-21 15:48:49.747 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.757 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.757 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.757 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.757 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:777]2025-08-21 15:48:49.767 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.776 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.776 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.776 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.776 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:777]2025-08-21 15:48:49.786 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.794 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.794 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.794 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.794 DEBUG [selectByCategoryName:] - ==> Parameters: 奇才(String)
[service-1:777]2025-08-21 15:48:49.805 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.814 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.814 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.814 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.814 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:777]2025-08-21 15:48:49.824 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.831 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.831 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.831 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.832 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:777]2025-08-21 15:48:49.842 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.850 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.850 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.851 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.851 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:777]2025-08-21 15:48:49.859 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.868 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.868 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.868 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.868 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:777]2025-08-21 15:48:49.877 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.889 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.889 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.889 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.889 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:777]2025-08-21 15:48:49.897 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.906 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.906 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.906 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.906 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:777]2025-08-21 15:48:49.914 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.923 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.923 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.923 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.923 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:777]2025-08-21 15:48:49.933 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:49.941 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.941 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.941 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.941 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:777]2025-08-21 15:48:49.951 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.960 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.960 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.961 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.961 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:777]2025-08-21 15:48:49.969 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.977 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.977 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.977 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.977 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:777]2025-08-21 15:48:49.987 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:49.996 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:49.997 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:49.997 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:49.997 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:777]2025-08-21 15:48:50.007 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.015 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.015 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.015 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.015 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:777]2025-08-21 15:48:50.023 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.031 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.031 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.031 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.031 DEBUG [selectByCategoryName:] - ==> Parameters: 太阳(String)
[service-1:777]2025-08-21 15:48:50.041 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.049 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.049 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.049 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.049 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:777]2025-08-21 15:48:50.059 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.068 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.068 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.068 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.068 DEBUG [selectByCategoryName:] - ==> Parameters: 热火(String)
[service-1:777]2025-08-21 15:48:50.076 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.092 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.092 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.092 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.092 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:777]2025-08-21 15:48:50.102 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.112 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.112 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.112 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.113 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:777]2025-08-21 15:48:50.122 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.132 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.132 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.132 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.132 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:777]2025-08-21 15:48:50.141 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.151 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.151 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.151 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.151 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:777]2025-08-21 15:48:50.161 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.170 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.170 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.170 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.170 DEBUG [selectByCategoryName:] - ==> Parameters: 国王(String)
[service-1:777]2025-08-21 15:48:50.178 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.187 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.188 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.188 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.188 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:777]2025-08-21 15:48:50.196 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.204 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.205 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.205 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.205 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:777]2025-08-21 15:48:50.215 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.224 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.224 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.224 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.224 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:777]2025-08-21 15:48:50.234 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.242 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.242 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.242 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.242 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:777]2025-08-21 15:48:50.252 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.261 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.261 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.261 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.261 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:777]2025-08-21 15:48:50.270 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.280 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.280 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.281 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.281 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:777]2025-08-21 15:48:50.290 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.301 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.301 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.301 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.301 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:777]2025-08-21 15:48:50.311 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.320 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.320 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.320 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.320 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:777]2025-08-21 15:48:50.331 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.341 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.342 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.342 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.342 DEBUG [selectByCategoryName:] - ==> Parameters: 掘金(String)
[service-1:777]2025-08-21 15:48:50.351 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.359 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.359 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.359 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.359 DEBUG [selectByCategoryName:] - ==> Parameters: 骑士(String)
[service-1:777]2025-08-21 15:48:50.369 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.378 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.378 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.378 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.378 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:777]2025-08-21 15:48:50.388 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.396 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.397 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.397 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.397 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:777]2025-08-21 15:48:50.406 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.416 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.416 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.416 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.416 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:777]2025-08-21 15:48:50.426 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.436 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.436 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.436 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.436 DEBUG [selectByCategoryName:] - ==> Parameters: 雷霆(String)
[service-1:777]2025-08-21 15:48:50.445 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.453 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.453 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.453 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.453 DEBUG [selectByCategoryName:] - ==> Parameters: 活塞(String)
[service-1:777]2025-08-21 15:48:50.463 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.471 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.471 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.471 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.471 DEBUG [selectByCategoryName:] - ==> Parameters: 公牛(String)
[service-1:777]2025-08-21 15:48:50.480 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.488 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.489 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.489 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.489 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:777]2025-08-21 15:48:50.497 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.509 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.509 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.509 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.510 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)
[service-1:777]2025-08-21 15:48:50.519 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.529 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.529 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.529 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.529 DEBUG [selectByCategoryName:] - ==> Parameters: 黄蜂(String)
[service-1:777]2025-08-21 15:48:50.538 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.547 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.548 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.548 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.548 DEBUG [selectByCategoryName:] - ==> Parameters: 猛龙(String)
[service-1:777]2025-08-21 15:48:50.559 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.578 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.578 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.578 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.578 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:777]2025-08-21 15:48:50.599 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.633 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.633 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.633 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.633 DEBUG [selectByCategoryName:] - ==> Parameters: 爵士(String)
[service-1:777]2025-08-21 15:48:50.673 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.729 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.729 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.729 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.729 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:777]2025-08-21 15:48:50.760 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.776 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.776 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.776 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.776 DEBUG [selectByCategoryName:] - ==> Parameters: 马刺(String)
[service-1:777]2025-08-21 15:48:50.793 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.802 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.802 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.802 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.803 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:777]2025-08-21 15:48:50.813 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.824 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.824 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.824 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.824 DEBUG [selectByCategoryName:] - ==> Parameters: 火箭(String)
[service-1:777]2025-08-21 15:48:50.834 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.843 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.843 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.843 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.844 DEBUG [selectByCategoryName:] - ==> Parameters: 76人(String)
[service-1:777]2025-08-21 15:48:50.852 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.862 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.863 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.863 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.863 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:777]2025-08-21 15:48:50.874 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.883 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.883 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.883 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.883 DEBUG [selectByCategoryName:] - ==> Parameters: 魔术(String)
[service-1:777]2025-08-21 15:48:50.894 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.907 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.907 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.907 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.907 DEBUG [selectByCategoryName:] - ==> Parameters: 快船(String)
[service-1:777]2025-08-21 15:48:50.917 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.927 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.927 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.927 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.927 DEBUG [selectByCategoryName:] - ==> Parameters: 步行者(String)
[service-1:777]2025-08-21 15:48:50.940 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.952 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.952 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.952 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.952 DEBUG [selectByCategoryName:] - ==> Parameters: 雄鹿(String)
[service-1:777]2025-08-21 15:48:50.961 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:50.972 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.972 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.972 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.972 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:777]2025-08-21 15:48:50.984 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:50.998 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:50.998 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:50.998 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:50.998 DEBUG [selectByCategoryName:] - ==> Parameters: 灰熊(String)
[service-1:777]2025-08-21 15:48:51.013 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:51.026 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.026 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.026 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.026 DEBUG [selectByCategoryName:] - ==> Parameters: 开拓者(String)
[service-1:777]2025-08-21 15:48:51.037 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:51.047 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.047 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.047 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.047 DEBUG [selectByCategoryName:] - ==> Parameters: 凯尔特人(String)
[service-1:777]2025-08-21 15:48:51.060 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:51.069 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.070 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.070 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.070 DEBUG [selectByCategoryName:] - ==> Parameters: 森林狼(String)
[service-1:777]2025-08-21 15:48:51.081 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:51.093 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.093 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.093 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.093 DEBUG [selectByCategoryName:] - ==> Parameters: 勇士(String)
[service-1:777]2025-08-21 15:48:51.105 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:51.114 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.114 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.114 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.114 DEBUG [selectByCategoryName:] - ==> Parameters: 独行侠(String)
[service-1:777]2025-08-21 15:48:51.128 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:51.173 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.174 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.174 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.174 DEBUG [selectByCategoryName:] - ==> Parameters: 湖人(String)
[service-1:777]2025-08-21 15:48:51.190 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:51.203 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.203 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.203 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.204 DEBUG [selectByCategoryName:] - ==> Parameters: 老鹰(String)
[service-1:777]2025-08-21 15:48:51.214 DEBUG [selectByCategoryName:] - <==      Total: 1
[service-1:777]2025-08-21 15:48:51.224 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.224 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.224 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.224 DEBUG [selectByCategoryName:] - ==> Parameters: 篮网(String)
[service-1:777]2025-08-21 15:48:51.236 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:777]2025-08-21 15:48:51.247 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:777]2025-08-21 15:48:51.247 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:777]2025-08-21 15:48:51.248 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:777]2025-08-21 15:48:51.248 DEBUG [selectByCategoryName:] - ==> Parameters: 鹈鹕(String)

[main:1]2025-08-21 13:56:40.092 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 13:56:40.094 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 13:56:40.094 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 13:56:40.094 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 13:56:40.094 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
[main:1]2025-08-21 13:56:40.094 INFO  [NacosLogging:] - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
[main:1]2025-08-21 13:56:42.104 INFO  [XkGoodsServer:] - The following 10 profiles are active: "commons", "data", "jms", "cache", "http", "schedule", "proxy", "os", "server", "dev"
[main:1]2025-08-21 13:56:42.117 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 13:56:42.117 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 13:56:43.258 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.259 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorDeleteEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.260 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorUpdateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.261 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.corp.CorpCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.261 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportCreateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.262 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportDeleteListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.262 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportUpdateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.263 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.CreateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.264 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.DeleteGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.264 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.GoodsStockEmptyListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.265 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.265 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.265 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.266 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductFirstListingListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.266 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductRemainRandomListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.267 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.267 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateRemainStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.268 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.269 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.order.OrderPaidListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.270 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScoreListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.270 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScorePagerListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.271 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.UpdateScoreRuleListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.272 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.CreateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.273 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.DeleteSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.273 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.UpdateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.274 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.CreateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.275 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.DeleteTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.275 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.UpdateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 13:56:43.935 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'userObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 13:56:43.936 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'corpObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 13:56:43.953 INFO  [CacheData:] - config listener notify warn timeout millis use default 60000 millis 
[main:1]2025-08-21 13:56:43.953 INFO  [CacheData:] - nacos.cache.data.init.snapshot = true 
[main:1]2025-08-21 13:56:43.954 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-dev.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 13:56:43.960 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 13:56:44.482 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 INFO  [SystemParamTableHolder:] - System settings initializing.
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.508 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.524 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.545 INFO  [RoutingConfigHolder:] - {log-routing: [xk_log, 0 - 9223372036854775807],goods-routing: [xk_goods, 0 - 9223372036854775807],auth-routing: [xk_auth, 0 - 9223372036854775807],config-routing: [xk_config, 0 - 9223372036854775807],stock-routing: [xk_stock, 0 - 9223372036854775807] }
[main:1]2025-08-21 13:56:44.545 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-21 13:56:44.698 INFO  [GenericScope:] - BeanFactory id=2e62ea10-36c6-3108-956c-d9a46c3a22d6
[main:1]2025-08-21 13:56:45.123 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.127 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:zookeeper.version=3.6.4--d65253dcf68e9097c6e95a126463fd5fdeb4521c, built on 12/18/2022 18:10 GMT
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:host.name=*************
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.version=21.0.7
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.vendor=Oracle Corporation
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.home=C:\Program Files\Java\jdk-21
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.class.path=D:\code\xk\xk-goods\xk-goods-server\target\classes;D:\maven\repository\com\xk\xk-start-server\0.0.1-SNAPSHOT\xk-start-server-0.0.1-20250818.091612-116.jar;D:\maven\repository\com\myco\mydata\mydata-start-server\0.0.1-SNAPSHOT\mydata-start-server-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-core\0.0.1-SNAPSHOT\mydata-start-domain-core-0.0.1-20250819.023657-99.jar;D:\maven\repository\com\myco\mydata\mydata-start-commons\0.0.1-SNAPSHOT\mydata-start-commons-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\myco-framework-6\0.0.1-SNAPSHOT\myco-framework-6-0.0.1-20250819.023657-80.jar;D:\maven\repository\com\alibaba\nacos\nacos-client\2.4.3\nacos-client-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-auth-plugin\2.4.3\nacos-auth-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-encryption-plugin\2.4.3\nacos-encryption-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-logback-adapter-12\2.4.3\nacos-logback-adapter-12-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\logback-adapter\1.1.3\logback-adapter-1.1.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-log4j2-adapter\2.4.3\nacos-log4j2-adapter-2.4.3.jar;D:\maven\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\maven\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\maven\repository\org\zeromq\jeromq\0.6.0\jeromq-0.6.0.jar;D:\maven\repository\eu\neilalexander\jnacl\1.0.0\jnacl-1.0.0.jar;D:\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\maven\repository\org\aspectj\aspectjrt\1.9.22.1\aspectjrt-1.9.22.1.jar;D:\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\maven\repository\org\springframework\spring-jdbc\6.2.3\spring-jdbc-6.2.3.jar;D:\maven\repository\org\apache\curator\curator-framework\4.3.0\curator-framework-4.3.0.jar;D:\maven\repository\org\apache\curator\curator-client\4.3.0\curator-client-4.3.0.jar;D:\maven\repository\com\google\guava\guava\27.0.1-jre\guava-27.0.1-jre.jar;D:\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven\repository\org\checkerframework\checker-qual\2.5.2\checker-qual-2.5.2.jar;D:\maven\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;D:\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;D:\maven\repository\org\apache\curator\curator-recipes\4.3.0\curator-recipes-4.3.0.jar;D:\maven\repository\org\apache\zookeeper\zookeeper\3.6.4\zookeeper-3.6.4.jar;D:\maven\repository\org\apache\zookeeper\zookeeper-jute\3.6.4\zookeeper-jute-3.6.4.jar;D:\maven\repository\org\apache\yetus\audience-annotations\0.13.0\audience-annotations-0.13.0.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final.jar;D:\maven\repository\org\mozilla\rhino\1.8.0\rhino-1.8.0.jar;D:\maven\repository\org\apache\groovy\groovy\4.0.26\groovy-4.0.26.jar;D:\maven\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;D:\maven\repository\cglib\cglib-nodep\3.3.0\cglib-nodep-3.3.0.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2\2.0.57\fastjson2-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson\2.0.57\fastjson-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2-extension\2.0.57\fastjson2-extension-2.0.57.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.18.2\jackson-dataformat-yaml-2.18.2.jar;D:\maven\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;D:\maven\repository\joda-time\joda-time\2.14.0\joda-time-2.14.0.jar;D:\maven\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-event\0.0.1-SNAPSHOT\mydata-start-domain-event-0.0.1-20250819.023657-92.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-enum\0.0.1-SNAPSHOT\mydata-start-domain-enum-0.0.1-20250819.023657-93.jar;D:\maven\repository\org\hibernate\validator\hibernate-validator\9.0.1.Final\hibernate-validator-9.0.1.Final.jar;D:\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\maven\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-interfaces\0.0.1-SNAPSHOT\mydata-start-interfaces-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\springframework\spring-webflux\6.2.3\spring-webflux-6.2.3.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-http\1.2.7\reactor-netty-http-1.2.7.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-classes-macos\4.2.2.Final\netty-resolver-dns-classes-macos-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-classes-epoll\4.2.2.Final\netty-transport-classes-epoll-4.2.2.Final.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-core\1.2.7\reactor-netty-core-1.2.7.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.2.0\spring-cloud-starter-loadbalancer-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-loadbalancer\4.2.0\spring-cloud-loadbalancer-4.2.0.jar;D:\maven\repository\io\projectreactor\addons\reactor-extra\3.5.2\reactor-extra-3.5.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-cache\3.4.3\spring-boot-starter-cache-3.4.3.jar;D:\maven\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-joda\2.18.3\jackson-datatype-joda-2.18.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;D:\maven\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;D:\maven\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-application\0.0.1-SNAPSHOT\mydata-start-application-0.0.1-20250819.023657-86.jar;D:\maven\repository\com\myco\mydata\mydata-start-gateway\0.0.1-SNAPSHOT\mydata-start-gateway-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-http\0.0.1-SNAPSHOT\mydata-start-infrastructure-http-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;D:\maven\repository\org\apache\httpcomponents\httpclient\4.5.9\httpclient-4.5.9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-data\0.0.1-SNAPSHOT\mydata-start-infrastructure-data-0.0.1-20250819.023657-93.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-commons\0.0.1-SNAPSHOT\mydata-start-infrastructure-commons-0.0.1-20250819.023657-89.jar;D:\maven\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;D:\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven\repository\com\lmax\disruptor\3.4.4\disruptor-3.4.4.jar;D:\maven\repository\com\google\zxing\core\3.5.3\core-3.5.3.jar;D:\maven\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.18.3\jackson-dataformat-xml-2.18.3.jar;D:\maven\repository\org\codehaus\woodstox\stax2-api\4.2.2\stax2-api-4.2.2.jar;D:\maven\repository\com\fasterxml\woodstox\woodstox-core\7.0.0\woodstox-core-7.0.0.jar;D:\maven\repository\io\github\jopenlibs\vault-java-driver\6.2.0\vault-java-driver-6.2.0.jar;D:\maven\repository\com\mysql\mysql-connector-j\9.3.0\mysql-connector-j-9.3.0.jar;D:\maven\repository\com\google\protobuf\protobuf-java\4.29.0\protobuf-java-4.29.0.jar;D:\maven\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\maven\repository\org\springframework\spring-context-support\6.2.3\spring-context-support-6.2.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2023.0.1.3\spring-cloud-starter-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2023.0.1.3\spring-cloud-alibaba-commons-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-alibaba-nacos-config\2023.0.1.3\spring-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2023.0.1.3\spring-cloud-starter-alibaba-nacos-discovery-2023.0.1.3.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-jms\0.0.1-SNAPSHOT\mydata-start-infrastructure-jms-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-client\4.9.8\rocketmq-client-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-common\4.9.8\rocketmq-common-4.9.8.jar;D:\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-acl\4.9.8\rocketmq-acl-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-remoting\4.9.8\rocketmq-remoting-4.9.8.jar;D:\maven\repository\io\netty\netty-all\4.2.2.Final\netty-all-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec\4.2.2.Final\netty-codec-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-haproxy\4.2.2.Final\netty-codec-haproxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http3\4.2.2.Final\netty-codec-http3-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-memcache\4.2.2.Final\netty-codec-memcache-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-mqtt\4.2.2.Final\netty-codec-mqtt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-redis\4.2.2.Final\netty-codec-redis-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-smtp\4.2.2.Final\netty-codec-smtp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-stomp\4.2.2.Final\netty-codec-stomp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-xml\4.2.2.Final\netty-codec-xml-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-protobuf\4.2.2.Final\netty-codec-protobuf-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-marshalling\4.2.2.Final\netty-codec-marshalling-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-ssl-ocsp\4.2.2.Final\netty-handler-ssl-ocsp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-rxtx\4.2.2.Final\netty-transport-rxtx-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-sctp\4.2.2.Final\netty-transport-sctp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-udt\4.2.2.Final\netty-transport-udt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-kqueue\4.2.2.Final\netty-transport-classes-kqueue-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-io_uring\4.2.2.Final\netty-transport-classes-io_uring-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-classes-quic\4.2.2.Final\netty-codec-classes-quic-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-windows-x86_64.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-logging\4.9.8\rocketmq-logging-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.8\rocketmq-srvutil-4.9.8.jar;D:\maven\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;D:\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven\repository\org\springframework\kafka\spring-kafka\3.3.7\spring-kafka-3.3.7.jar;D:\maven\repository\org\springframework\spring-messaging\6.2.3\spring-messaging-6.2.3.jar;D:\maven\repository\org\springframework\spring-tx\6.2.3\spring-tx-6.2.3.jar;D:\maven\repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar;D:\maven\repository\org\apache\kafka\kafka-clients\3.9.1\kafka-clients-3.9.1.jar;D:\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;D:\maven\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\maven\repository\io\projectreactor\kafka\reactor-kafka\1.3.23\reactor-kafka-1.3.23.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-cache\0.0.1-SNAPSHOT\mydata-start-infrastructure-cache-0.0.1-20250819.023657-87.jar;D:\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-os\0.0.1-SNAPSHOT\mydata-start-infrastructure-os-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\qcloud\cos_api\5.6.242\cos_api-5.6.242.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-kms\3.1.1138\tencentcloud-sdk-java-kms-3.1.1138.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-common\3.1.1138\tencentcloud-sdk-java-common-3.1.1138.jar;D:\maven\repository\com\squareup\okhttp3\okhttp\3.12.13\okhttp-3.12.13.jar;D:\maven\repository\com\squareup\okio\okio\1.15.0\okio-1.15.0.jar;D:\maven\repository\com\squareup\okhttp3\logging-interceptor\3.12.13\logging-interceptor-3.12.13.jar;D:\maven\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\maven\repository\com\thoughtworks\xstream\xstream\1.4.21\xstream-1.4.21.jar;D:\maven\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\maven\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\maven\repository\com\auth0\java-jwt\4.4.0\java-jwt-4.4.0.jar;D:\maven\repository\com\qcloud\cos-sts_api\3.1.1\cos-sts_api-3.1.1.jar;D:\maven\repository\com\aliyun\alibabacloud-sts20150401\1.0.7\alibabacloud-sts20150401-1.0.7.jar;D:\maven\repository\com\aliyun\aliyun-gateway-pop\0.2.15-beta\aliyun-gateway-pop-0.2.15-beta.jar;D:\maven\repository\com\aliyun\darabonba-java-core\0.2.15-beta\darabonba-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-http-apache\0.2.15-beta\aliyun-http-apache-0.2.15-beta.jar;D:\maven\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;D:\maven\repository\com\aliyun\aliyun-java-core\0.2.15-beta\aliyun-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-auth\0.2.15-beta\aliyun-java-auth-0.2.15-beta.jar;D:\maven\repository\com\aliyun\oss\aliyun-sdk-oss\3.18.2\aliyun-sdk-oss-3.18.2.jar;D:\maven\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\maven\repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-core\4.7.3\aliyun-java-sdk-core-4.7.3.jar;D:\maven\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\maven\repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar;D:\maven\repository\commons-logging\commons-logging\1.3.4\commons-logging-1.3.4.jar;D:\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\maven\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\maven\repository\com\aliyun\java-trace-api\0.2.11-beta\java-trace-api-0.2.11-beta.jar;D:\maven\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;D:\maven\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-proxy\0.0.1-SNAPSHOT\mydata-start-infrastructure-proxy-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-security\0.0.1-SNAPSHOT\mydata-start-infrastructure-security-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-validation\0.0.1-SNAPSHOT\mydata-start-infrastructure-validation-0.0.1-20250819.023657-86.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.4.3\spring-boot-starter-validation-3.4.3.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.36\tomcat-embed-el-10.1.36.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-webflux\3.4.3\spring-boot-starter-webflux-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.4.3\spring-boot-starter-json-3.4.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.4.3\spring-boot-starter-reactor-netty-3.4.3.jar;D:\maven\repository\org\springframework\spring-web\6.2.3\spring-web-6.2.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\4.2.0\spring-cloud-starter-bootstrap-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter\4.2.0\spring-cloud-starter-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;D:\maven\repository\org\springframework\security\spring-security-crypto\6.4.3\spring-security-crypto-6.4.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-commons\4.2.0\spring-cloud-commons-4.2.0.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk18on\1.78.1\bcprov-jdk18on-1.78.1.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.3\spring-boot-starter-actuator-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.3\spring-boot-actuator-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator\3.4.3\spring-boot-actuator-3.4.3.jar;D:\maven\repository\io\micrometer\micrometer-observation\1.14.4\micrometer-observation-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-commons\1.14.4\micrometer-commons-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-jakarta9\1.14.4\micrometer-jakarta9-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-core\1.14.4\micrometer-core-1.14.4.jar;D:\maven\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;D:\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven\repository\com\xk\xk-start-application\0.0.1-SNAPSHOT\xk-start-application-0.0.1-20250818.091612-115.jar;D:\maven\repository\com\xk\xk-start-domain-core\0.0.1-SNAPSHOT\xk-start-domain-core-0.0.1-20250818.091612-127.jar;D:\maven\repository\com\xk\xk-start-domain-event\0.0.1-SNAPSHOT\xk-start-domain-event-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-interfaces\0.0.1-SNAPSHOT\xk-start-interfaces-0.0.1-20250818.091612-122.jar;D:\maven\repository\com\xk\xk-start-domain-enum\0.0.1-SNAPSHOT\xk-start-domain-enum-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-infrastructure\0.0.1-SNAPSHOT\xk-start-infrastructure-0.0.1-20250818.091612-118.jar;D:\maven\repository\com\alibaba\easyexcel\4.0.3\easyexcel-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-core\4.0.3\easyexcel-core-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;D:\maven\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;D:\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\maven\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\maven\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;D:\maven\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;D:\maven\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;D:\maven\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;D:\maven\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;D:\maven\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;D:\maven\repository\org\apache\commons\commons-csv\1.11.0\commons-csv-1.11.0.jar;D:\maven\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\maven\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\maven\repository\com\xk\xk-start-gateway\0.0.1-SNAPSHOT\xk-start-gateway-0.0.1-20250818.091612-121.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-openfeign\4.2.0\spring-cloud-starter-openfeign-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-openfeign-core\4.2.0\spring-cloud-openfeign-core-4.2.0.jar;D:\maven\repository\io\github\openfeign\feign-form-spring\13.5\feign-form-spring-13.5.jar;D:\maven\repository\io\github\openfeign\feign-form\13.5\feign-form-13.5.jar;D:\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\maven\repository\io\github\openfeign\feign-core\13.5\feign-core-13.5.jar;D:\maven\repository\io\github\openfeign\feign-slf4j\13.5\feign-slf4j-13.5.jar;D:\code\xk\xk-goods\xk-goods-application\target\classes;D:\maven\repository\com\myco\mydata\config\mydata-config-application\0.0.1-SNAPSHOT\mydata-config-application-0.0.1-20250719.075944-7.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-domain\0.0.1-SNAPSHOT\mydata-config-domain-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-interfaces\0.0.1-SNAPSHOT\mydata-config-interfaces-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-gateway\0.0.1-SNAPSHOT\mydata-config-gateway-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-infrastructure\0.0.1-SNAPSHOT\mydata-config-infrastructure-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-event\0.0.1-SNAPSHOT\mydata-start-infrastructure-event-0.0.1-20250819.023657-88.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-schedule\0.0.1-SNAPSHOT\mydata-start-infrastructure-schedule-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\quartz-scheduler\quartz\2.5.0\quartz-2.5.0.jar;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-core\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-event\target\classes;D:\code\xk\xk-goods\xk-goods-interfaces\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-enum\target\classes;D:\code\xk\xk-goods\xk-goods-gateway\target\classes;D:\maven\repository\com\xk\corp\xk-corp-interfaces\0.0.1-SNAPSHOT\xk-corp-interfaces-0.0.1-20250728.125932-33.jar;D:\maven\repository\com\xk\corp\xk-corp-domain-enum\0.0.1-SNAPSHOT\xk-corp-domain-enum-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\message\xk-message-domain-enum\0.0.1-SNAPSHOT\xk-message-domain-enum-0.0.1-20250819.084230-25.jar;D:\maven\repository\com\xk\acct\xk-acct-interfaces\0.0.1-SNAPSHOT\xk-acct-interfaces-0.0.1-20250818.092336-74.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-enum\0.0.1-SNAPSHOT\xk-acct-domain-enum-0.0.1-20250813.030141-70.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-enum\target\classes;D:\maven\repository\com\xk\acct\xk-acct-domain-event\0.0.1-SNAPSHOT\xk-acct-domain-event-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\config\xk-config-interfaces\0.0.1-SNAPSHOT\xk-config-interfaces-0.0.1-20250805.030408-16.jar;D:\maven\repository\com\xk\config\xk-config-domain-enum\0.0.1-SNAPSHOT\xk-config-domain-enum-0.0.1-20250805.030408-16.jar;D:\code\xk\xk-third-party\xk-third-party-interfaces\target\classes;D:\maven\repository\com\xk\search\xk-search-interfaces\0.0.1-SNAPSHOT\xk-search-interfaces-0.0.1-20250818.123046-199.jar;D:\maven\repository\com\xk\search\xk-search-domain-enum\0.0.1-SNAPSHOT\xk-search-domain-enum-0.0.1-20250818.123046-208.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-enum\target\classes;D:\maven\repository\com\xk\corp\xk-corp-domain-event\0.0.1-SNAPSHOT\xk-corp-domain-event-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-enum\0.0.1-SNAPSHOT\xk-auth-domain-enum-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\promotion\xk-promotion-interfaces\0.0.1-SNAPSHOT\xk-promotion-interfaces-0.0.1-20250804.073817-18.jar;D:\maven\repository\com\xk\promotion\xk-promotion-domain-enum\0.0.1-SNAPSHOT\xk-promotion-domain-enum-0.0.1-20250804.073817-16.jar;D:\code\xk\xk-goods\xk-goods-infrastructure\target\classes;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-spring-boot-starter\1.4.8\mapstruct-plus-spring-boot-starter-1.4.8.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus\1.4.8\mapstruct-plus-1.4.8.jar;D:\maven\repository\org\mapstruct\mapstruct\1.5.5.Final\mapstruct-1.5.5.Final.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-object-convert\1.4.8\mapstruct-plus-object-convert-1.4.8.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.3\spring-boot-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar;D:\maven\repository\org\springframework\spring-context\6.2.3\spring-context-6.2.3.jar;D:\maven\repository\org\springframework\spring-aop\6.2.3\spring-aop-6.2.3.jar;D:\maven\repository\org\springframework\spring-beans\6.2.3\spring-beans-6.2.3.jar;D:\maven\repository\org\springframework\spring-expression\6.2.3\spring-expression-6.2.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.4.3\spring-boot-starter-3.4.3.jar;D:\maven\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;D:\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\maven\repository\org\springframework\spring-core\6.2.3\spring-core-6.2.3.jar;D:\maven\repository\org\springframework\spring-jcl\6.2.3\spring-jcl-6.2.3.jar;D:\maven\repository\io\vertx\vertx-core\5.0.1\vertx-core-5.0.1.jar;D:\maven\repository\io\vertx\vertx-core-logging\5.0.1\vertx-core-logging-5.0.1.jar;D:\maven\repository\io\netty\netty-common\4.2.2.Final\netty-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-buffer\4.2.2.Final\netty-buffer-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport\4.2.2.Final\netty-transport-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler\4.2.2.Final\netty-handler-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-unix-common\4.2.2.Final\netty-transport-native-unix-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-base\4.2.2.Final\netty-codec-base-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-proxy\4.2.2.Final\netty-handler-proxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-socks\4.2.2.Final\netty-codec-socks-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http\4.2.2.Final\netty-codec-http-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-compression\4.2.2.Final\netty-codec-compression-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http2\4.2.2.Final\netty-codec-http2-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver\4.2.2.Final\netty-resolver-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver-dns\4.2.2.Final\netty-resolver-dns-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-dns\4.2.2.Final\netty-codec-dns-4.2.2.Final.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-log4j2\3.4.3\spring-boot-starter-log4j2-3.4.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.3\log4j-slf4j2-impl-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-jul\2.24.3\log4j-jul-2.24.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.3\spring-boot-configuration-processor-3.4.3.jar;D:\maven\repository\io\projectreactor\reactor-core\3.7.7\reactor-core-3.7.7.jar;D:\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\lib\idea_rt.jar
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.library.path=C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\bin;C:\Program Files\JetBrains\PyCharm 2025.1.3.1\bin;;.
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:java.compiler=<NA>
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.name=Windows 11
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.arch=amd64
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.version=10.0
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:user.name=ShiJia
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:user.home=C:\Users\<USER>\code\xk
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.memory.free=91MB
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.memory.max=8048MB
[main:1]2025-08-21 13:56:45.163 INFO  [ZooKeeper:] - Client environment:os.memory.total=180MB
[main:1]2025-08-21 13:56:45.183 INFO  [CuratorFrameworkImpl:] - Starting
[main:1]2025-08-21 13:56:45.184 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@28d61fc
[main:1]2025-08-21 13:56:45.187 INFO  [X509Util:] - Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
[main:1]2025-08-21 13:56:45.191 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main:1]2025-08-21 13:56:45.195 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main:1]2025-08-21 13:56:45.200 INFO  [CuratorFrameworkImpl:] - Default schema
[main:1]2025-08-21 13:56:45.200 INFO  [ZookeeperClientFactoryBean:] - ZK connection is successful.
[main:1]2025-08-21 13:56:45.200 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.208 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.216 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.239 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.256 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.263 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.305 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.342 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.359 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.364 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.402 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.444 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.450 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.456 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'liveObjectDao' of type [com.xk.infrastructure.cache.dao.object.LiveObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.461 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.464 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.469 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.476 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.485 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.489 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.495 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.497 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.497 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:45.499 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 13:56:50.628 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.628 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.637 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.637 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.645 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.645 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.655 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.655 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.663 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.663 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.671 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.672 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.679 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.679 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.687 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.687 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.696 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.696 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.704 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.704 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.713 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.713 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.723 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.723 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.733 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.733 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.741 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.742 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.751 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.751 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.761 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.761 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.877 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.877 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.887 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.887 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.897 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.897 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.906 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.906 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.917 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.917 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.927 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.927 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.946 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.946 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.957 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.957 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.966 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.966 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.976 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.976 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.988 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:50.988 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 13:56:52.087 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-21 13:56:53.132 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.276 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.276 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.289 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:54081, server: *************/*************:2181
[main-SendThread(*************:2181):86]2025-08-21 13:56:54.306 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x100000114063391, negotiated timeout = 40000
[main-EventThread:87]2025-08-21 13:56:54.310 INFO  [ConnectionStateManager:] - State change: CONNECTED
[Curator-ConnectionStateManager-0:85]2025-08-21 13:56:54.311 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: CONNECTED
[main-EventThread:87]2025-08-21 13:56:54.327 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 13:56:54.327 INFO  [EnsembleTracker:] - New config event received: {}
[main:1]2025-08-21 13:56:55.139 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorCreateEvent
[main:1]2025-08-21 13:56:55.179 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorDeleteEvent
[main:1]2025-08-21 13:56:55.185 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorUpdateEvent
[main:1]2025-08-21 13:56:55.192 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_CORP-CORP-CorpCreateEvent
[main:1]2025-08-21 13:56:55.199 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportCreateEvent
[main:1]2025-08-21 13:56:55.204 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportDeleteEvent
[main:1]2025-08-21 13:56:55.211 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportUpdateEvent
[main:1]2025-08-21 13:56:55.219 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateGoodsEvent
[main:1]2025-08-21 13:56:55.227 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteGoodsEvent
[main:1]2025-08-21 13:56:55.234 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GoodsStockEmptyEvent
[main:1]2025-08-21 13:56:55.241 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 13:56:55.250 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent
[main:1]2025-08-21 13:56:55.257 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 13:56:55.263 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductFirstListingEvent
[main:1]2025-08-21 13:56:55.269 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductRemainRandomEvent
[main:1]2025-08-21 13:56:55.275 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateGoodsEvent
[main:1]2025-08-21 13:56:55.281 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateRemainStockEvent
[main:1]2025-08-21 13:56:55.296 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateStockEvent
[main:1]2025-08-21 13:56:55.302 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_ORDER-ORDER-OrderPaidEvent
[main:1]2025-08-21 13:56:55.308 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScoreEvent
[main:1]2025-08-21 13:56:55.313 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScorePagerEvent
[main:1]2025-08-21 13:56:55.318 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateScoreRuleEvent
[main:1]2025-08-21 13:56:55.324 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateSeriesCateEvent
[main:1]2025-08-21 13:56:55.331 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteSeriesCateEvent
[main:1]2025-08-21 13:56:55.337 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateSeriesCateEvent
[main:1]2025-08-21 13:56:55.343 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateTeamMemberEvent
[main:1]2025-08-21 13:56:55.350 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteTeamMemberEvent
[main:1]2025-08-21 13:56:55.356 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateTeamMemberEvent
[main:1]2025-08-21 13:56:56.035 INFO  [StdSchedulerFactory:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 13:56:56.044 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 13:56:56.044 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 13:56:56.045 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 13:56:56.045 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 13:56:56.045 INFO  [StdSchedulerFactory:] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[main:1]2025-08-21 13:56:56.045 INFO  [StdSchedulerFactory:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 13:56:56.046 INFO  [QuartzScheduler:] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@239e7554
[main:1]2025-08-21 13:56:56.459 INFO  [EndpointLinksResolver:60] - Exposing 19 endpoints beneath base path '/actuator'
[main:1]2025-08-21 13:56:56.867 INFO  [JvmCacheConsumerFactoryBean:] - The JVM cache to start listening...
[main:1]2025-08-21 13:56:56.986 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 13:56:56.986 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: main
[main:1]2025-08-21 13:56:56.987 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 13:56:56.987 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 13:56:56.987 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 13:56:56.987 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 13:56:56.987 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[main:1]2025-08-21 13:56:56.987 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 13:56:57.028 INFO  [JobDetailBuilder:] - 没有服务器[192.168.13.28],需要执行的任务
[main:1]2025-08-21 13:56:57.028 INFO  [QuartzSchedulerManager:] - 共获取到【0】个需要处理的Jobs!
[main:1]2025-08-21 13:56:57.398 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-21 13:56:58.518 INFO  [NettyWebServer:126] - Netty started on port 11006 (http)
[main:1]2025-08-21 13:56:58.526 INFO  [naming:] - Nacos client key init properties: 
	serverAddr=*************:8848
	namespace=dev
	username=nacos
	password=EQ********3u

[main:1]2025-08-21 13:56:58.527 INFO  [naming:] - initializer namespace from ans.namespace attribute : null
[main:1]2025-08-21 13:56:58.527 INFO  [naming:] - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[main:1]2025-08-21 13:56:58.527 INFO  [naming:] - initializer namespace from namespace attribute :null
[main:1]2025-08-21 13:56:58.531 INFO  [naming:] - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[main:1]2025-08-21 13:56:58.533 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[main:1]2025-08-21 13:56:58.533 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[main:1]2025-08-21 13:56:58.682 INFO  [client:] - [RpcClientFactory] create a new rpc client of 0261d77b-6247-4d8b-9182-b201a1c19535
[main:1]2025-08-21 13:56:58.684 INFO  [naming:] - Create naming rpc client for uuid->0261d77b-6247-4d8b-9182-b201a1c19535
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[main:1]2025-08-21 13:56:58.684 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
[main:1]2025-08-21 13:56:58.684 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main:1]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Success to connect to server [*************:8848] on start up, connectionId = 1755755817605_221.12.20.178_36845
[main:1]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[main:1]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00000208015e26d8
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 13:56:58.729 INFO  [client:] - [0261d77b-6247-4d8b-9182-b201a1c19535] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 13:56:58.729 INFO  [naming:] - Grpc connection connect
[main:1]2025-08-21 13:56:58.730 INFO  [naming:] - [REGISTER-SERVICE] dev registering service xkGoods with instance Instance{instanceId='null', ip='*************', port=11006, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkGoods', serviceName='null', metadata={preserved.heart.beat.timeout=20000, preserved.ip.delete.timeout=60000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=10000}}
[main:1]2025-08-21 13:56:58.766 INFO  [NacosServiceRegistry:] - nacos registry, DEFAULT_GROUP xkGoods *************:11006 register finished
[main:1]2025-08-21 13:56:58.886 INFO  [VertxEventBusManager:] - Event bus 'CONFIG' started.
[main:1]2025-08-21 13:56:58.890 INFO  [VertxEventBusManager:] - Event queue 'CONFIG' started.
[main:1]2025-08-21 13:56:58.922 INFO  [VertxEventBusManager:] - Event bus 'ORDER' started.
[main:1]2025-08-21 13:56:58.923 INFO  [VertxEventBusManager:] - Event queue 'ORDER' started.
[main:1]2025-08-21 13:56:58.954 INFO  [VertxEventBusManager:] - Event bus 'CORP' started.
[main:1]2025-08-21 13:56:58.954 INFO  [VertxEventBusManager:] - Event queue 'CORP' started.
[main:1]2025-08-21 13:56:58.987 INFO  [VertxEventBusManager:] - Event bus 'AUTH' started.
[main:1]2025-08-21 13:56:58.987 INFO  [VertxEventBusManager:] - Event queue 'AUTH' started.
[main:1]2025-08-21 13:56:59.020 INFO  [VertxEventBusManager:] - Event bus 'GOODS' started.
[main:1]2025-08-21 13:56:59.021 INFO  [VertxEventBusManager:] - Event queue 'GOODS' started.
[main:1]2025-08-21 13:56:59.050 INFO  [VertxEventBusManager:] - Event bus 'USER' started.
[main:1]2025-08-21 13:56:59.051 INFO  [VertxEventBusManager:] - Event queue 'USER' started.
[vert.x-virtual-thread-0:289]2025-08-21 13:56:59.066 INFO  [AbstractEventVerticle:] - Deploying 'DeleteItemDefineEventHandler-0'...
[vert.x-virtual-thread-0:289]2025-08-21 13:56:59.067 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteItemDefineEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 13:56:59.067 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler
[main:1]2025-08-21 13:56:59.068 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler
[vert.x-virtual-thread-1:295]2025-08-21 13:56:59.068 INFO  [AbstractEventVerticle:] - Deploying 'UpdateItemDefineIdEventHandler-1'...
[vert.x-virtual-thread-1:295]2025-08-21 13:56:59.068 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateItemDefineIdEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:ORDER, handler:com.xk.goods.application.handler.event.order.OrderPaidEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:296]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'OrderPaidEventHandler-2'...
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:CORP, handler:com.xk.goods.application.handler.event.corp.CorpCreateEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:296]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'OrderPaidEvent[ORDER[YD_ORDER]]'
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:AUTH, handler:com.xk.application.handler.event.log.LogSecureEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:298]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'CorpCreateEventHandler-3'...
[vert.x-virtual-thread-0:299]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'LogSecureEventHandler-4'...
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateStockEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-2:307]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'ColorUpdateEventHandler-5'...
[vert.x-virtual-thread-3:309]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportCreateEventHandler-6'...
[main:1]2025-08-21 13:56:59.069 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:301]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'ColorCreateEventHandler-7'...
[vert.x-virtual-thread-4:310]2025-08-21 13:56:59.069 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportDeleteEventHandler-8'...
[vert.x-virtual-thread-1:305]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'ColorDeleteEventHandler-9'...
[vert.x-virtual-thread-5:311]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportUpdateEventHandler-10'...
[vert.x-virtual-thread-6:313]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'CreateGoodsEventHandler-11'...
[vert.x-virtual-thread-8:316]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'GoodsStockEmptyEventHandler-12'...
[vert.x-virtual-thread-8:316]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GoodsStockEmptyEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-7:315]2025-08-21 13:56:59.070 INFO  [AbstractEventVerticle:] - Deploying 'DeleteGoodsEventHandler-13'...
[vert.x-virtual-thread-2:307]2025-08-21 13:56:59.075 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:298]2025-08-21 13:56:59.075 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CorpCreateEvent[CORP[YD_CORP]]'
[vert.x-virtual-thread-0:299]2025-08-21 13:56:59.075 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'LogSecureEvent[AUTH[YD_AUTH]]'
[vert.x-virtual-thread-7:315]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-6:313]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:305]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-5:311]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-4:310]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:301]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorCreateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-3:309]2025-08-21 13:56:59.076 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportCreateEvent[GOODS[YD_GOODS]]'
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScoreEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserCreateEventHandler
[main:1]2025-08-21 13:56:59.076 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserDeleteEventHandler
[main:1]2025-08-21 13:56:59.076 INFO  [NacosDiscoveryHeartBeatPublisher:] - Start nacos heartBeat task scheduler.
[main:1]2025-08-21 13:56:59.078 INFO  [SchedulerFactoryBean:] - Starting Quartz Scheduler now
[main:1]2025-08-21 13:56:59.079 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[vert.x-virtual-thread-14:322]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductFirstListingEventHandler-14'...
[vert.x-virtual-thread-12:320]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownEventHandler-16'...
[vert.x-virtual-thread-9:317]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'UpdateGoodsEventHandler-15'...
[vert.x-virtual-thread-15:323]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductRemainRandomEventHandler-17'...
[vert.x-virtual-thread-10:318]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'UpdateRemainStockEventHandler-18'...
[vert.x-virtual-thread-11:319]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Deploying 'UpdateStockEventHandler-19'...
[vert.x-virtual-thread-14:322]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductFirstListingEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-11:319]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-9:317]2025-08-21 13:56:59.088 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-10:318]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateRemainStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-13:321]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownJobEventHandler-20'...
[vert.x-virtual-thread-16:324]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'QueryScoreEventHandler-21'...
[vert.x-virtual-thread-15:323]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductRemainRandomEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-17:325]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'QueryScorePagerEventHandler-22'...
[vert.x-virtual-thread-16:324]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScoreEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-18:326]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UpdateScoreRuleEventHandler-23'...
[vert.x-virtual-thread-19:327]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'CreateSeriesCateEventHandler-24'...
[vert.x-virtual-thread-18:326]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateScoreRuleEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:328]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'DeleteSeriesCateEventHandler-25'...
[vert.x-virtual-thread-21:329]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UpdateSeriesCateEventHandler-26'...
[vert.x-virtual-thread-22:330]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'CreateTeamMemberEventHandler-27'...
[vert.x-virtual-thread-23:331]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'DeleteTeamMemberEventHandler-28'...
[vert.x-virtual-thread-24:332]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UpdateTeamMemberEventHandler-29'...
[vert.x-virtual-thread-17:325]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScorePagerEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-13:321]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownJobEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:333]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UserCreateEventHandler-30'...
[vert.x-virtual-thread-24:332]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:333]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserCreateEvent[USER[YD_USER]]'
[vert.x-virtual-thread-1:334]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Deploying 'UserDeleteEventHandler-31'...
[vert.x-virtual-thread-12:320]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:334]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserDeleteEvent[USER[YD_USER]]'
[vert.x-virtual-thread-23:331]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-22:330]2025-08-21 13:56:59.089 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:328]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-21:329]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-19:327]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:336]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.order.OrderPaidEventHandler' with ID: 3
[vert.x-eventloop-thread-0:341]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserCreateEventHandler' with ID: 31
[vert.x-eventloop-thread-0:341]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserDeleteEventHandler' with ID: 32
[vert.x-eventloop-thread-0:339]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.corp.CorpCreateEventHandler' with ID: 4
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorUpdateEventHandler' with ID: 8
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorCreateEventHandler' with ID: 6
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler' with ID: 9
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler' with ID: 14
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler' with ID: 11
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler' with ID: 12
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorDeleteEventHandler' with ID: 7
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler' with ID: 10
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler' with ID: 13
[vert.x-eventloop-thread-0:337]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler' with ID: 2
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler' with ID: 20
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler' with ID: 15
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateStockEventHandler' with ID: 17
[vert.x-eventloop-thread-0:337]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler' with ID: 1
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler' with ID: 16
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler' with ID: 21
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScoreEventHandler' with ID: 22
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler' with ID: 24
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler' with ID: 23
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler' with ID: 19
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler' with ID: 30
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler' with ID: 18
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler' with ID: 29
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler' with ID: 28
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler' with ID: 27
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler' with ID: 25
[vert.x-eventloop-thread-0:340]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler' with ID: 26
[vert.x-eventloop-thread-0:338]2025-08-21 13:56:59.090 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.application.handler.event.log.LogSecureEventHandler' with ID: 5
[main:1]2025-08-21 13:56:59.105 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 13:56:59.105 INFO  [ServiceApplicationListener:] -  The xkGoods:dev has been started.
[main:1]2025-08-21 13:56:59.105 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 13:56:59.106 INFO  [XkGoodsServer:] - Started XkGoodsServer in 19.408 seconds (process running for 20.763)
[main:1]2025-08-21 13:56:59.110 WARN  [DefaultStdSchedulerFactoryBean:] - 没有可用的Jobs
[main:1]2025-08-21 13:56:59.110 INFO  [QuartzSchedulerManager:] - Will start Quartz Scheduler [DefaultQuartzScheduler] in 5 seconds
[main:1]2025-08-21 13:56:59.120 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-schedule.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 13:56:59.120 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 13:56:59.120 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 13:56:59.120 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=2
[main:1]2025-08-21 13:56:59.120 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-dev.yml, group=DEFAULT_GROUP
[Thread-14:120]2025-08-21 13:57:00.211 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorCreateEvent' queue.
[Thread-15:121]2025-08-21 13:57:00.249 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorDeleteEvent' queue.
[Thread-16:122]2025-08-21 13:57:00.255 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorUpdateEvent' queue.
[Thread-18:124]2025-08-21 13:57:00.267 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportCreateEvent' queue.
[Thread-19:125]2025-08-21 13:57:00.283 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportDeleteEvent' queue.
[Thread-20:126]2025-08-21 13:57:00.291 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportUpdateEvent' queue.
[Thread-21:127]2025-08-21 13:57:00.293 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateGoodsEvent' queue.
[Thread-22:128]2025-08-21 13:57:00.296 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteGoodsEvent' queue.
[Thread-17:123]2025-08-21 13:57:00.307 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_CORP-CORP-CorpCreateEvent' queue.
[Thread-23:129]2025-08-21 13:57:00.311 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GoodsStockEmptyEvent' queue.
[Thread-24:130]2025-08-21 13:57:00.311 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-25:131]2025-08-21 13:57:00.315 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownJobEvent' queue.
[Thread-26:132]2025-08-21 13:57:00.318 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-27:133]2025-08-21 13:57:00.333 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductFirstListingEvent' queue.
[Thread-29:135]2025-08-21 13:57:00.346 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateGoodsEvent' queue.
[Thread-31:137]2025-08-21 13:57:00.373 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateStockEvent' queue.
[Thread-28:134]2025-08-21 13:57:00.376 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductRemainRandomEvent' queue.
[Thread-32:138]2025-08-21 13:57:00.378 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_ORDER-ORDER-OrderPaidEvent' queue.
[Thread-34:140]2025-08-21 13:57:00.378 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScorePagerEvent' queue.
[Thread-33:139]2025-08-21 13:57:00.380 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScoreEvent' queue.
[Thread-30:136]2025-08-21 13:57:00.384 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateRemainStockEvent' queue.
[Thread-35:141]2025-08-21 13:57:00.389 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateScoreRuleEvent' queue.
[Thread-36:142]2025-08-21 13:57:00.401 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateSeriesCateEvent' queue.
[Thread-37:143]2025-08-21 13:57:00.406 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteSeriesCateEvent' queue.
[Thread-38:144]2025-08-21 13:57:00.410 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateSeriesCateEvent' queue.
[Thread-39:145]2025-08-21 13:57:00.418 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateTeamMemberEvent' queue.
[Thread-41:147]2025-08-21 13:57:00.428 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateTeamMemberEvent' queue.
[Thread-40:146]2025-08-21 13:57:00.431 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteTeamMemberEvent' queue.
[DefaultQuartzScheduler:342]2025-08-21 13:57:04.111 INFO  [QuartzSchedulerManager:] - Starting Quartz Scheduler now
[DefaultQuartzScheduler:342]2025-08-21 13:57:04.111 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:665]2025-08-21 13:57:04.115 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_1:665]2025-08-21 13:57:04.252 INFO  [AbstractDispatchMessageListener:] - The total time of processing 137ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:676]2025-08-21 13:57:05.117 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_2:676]2025-08-21 13:57:05.146 INFO  [AbstractDispatchMessageListener:] - The total time of processing 29ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:684]2025-08-21 13:57:07.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_3:684]2025-08-21 13:57:07.136 INFO  [AbstractDispatchMessageListener:] - The total time of processing 23ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:692]2025-08-21 13:57:09.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_4:692]2025-08-21 13:57:09.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 23ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:701]2025-08-21 13:57:12.112 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_5:701]2025-08-21 13:57:12.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 25ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:710]2025-08-21 13:57:16.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_6:710]2025-08-21 13:57:16.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 24ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:749]2025-08-21 13:57:27.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_7:749]2025-08-21 13:57:27.139 INFO  [AbstractDispatchMessageListener:] - The total time of processing 26ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:762]2025-08-21 13:57:30.112 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_8:762]2025-08-21 13:57:30.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 25ms
[service-1:760]2025-08-21 13:57:31.161 INFO  [DruidDataSource:] - {dataSource-1} inited
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:802]2025-08-21 13:57:38.115 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_9:802]2025-08-21 13:57:38.148 INFO  [AbstractDispatchMessageListener:] - The total time of processing 33ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_10:810]2025-08-21 13:57:40.114 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_10:810]2025-08-21 13:57:40.139 INFO  [AbstractDispatchMessageListener:] - The total time of processing 25ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_11:819]2025-08-21 13:57:42.114 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_11:819]2025-08-21 13:57:42.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 24ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_12:825]2025-08-21 13:57:43.111 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_12:825]2025-08-21 13:57:43.134 INFO  [AbstractDispatchMessageListener:] - The total time of processing 23ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_13:837]2025-08-21 13:57:50.116 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_13:837]2025-08-21 13:57:50.140 INFO  [AbstractDispatchMessageListener:] - The total time of processing 24ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_14:844]2025-08-21 13:57:53.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_14:844]2025-08-21 13:57:53.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 25ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_15:849]2025-08-21 13:57:54.112 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_15:849]2025-08-21 13:57:54.134 INFO  [AbstractDispatchMessageListener:] - The total time of processing 22ms
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_16:855]2025-08-21 13:57:55.113 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_GOODS-GOODS-MerchantProductDownJobEvent/3
[ConsumeMessageThread_xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent_16:855]2025-08-21 13:57:55.135 INFO  [AbstractDispatchMessageListener:] - The total time of processing 22ms
[Thread-2:46]2025-08-21 13:57:56.306 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-2:46]2025-08-21 13:57:56.306 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-6:56]2025-08-21 13:57:56.306 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[Thread-6:56]2025-08-21 13:57:56.306 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
[Thread-4:50]2025-08-21 13:57:56.307 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-4:50]2025-08-21 13:57:56.307 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end
[vert.x-virtual-thread-2:868]2025-08-21 13:57:56.315 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-3:869]2025-08-21 13:57:56.316 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-1:870]2025-08-21 13:57:56.317 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-42:874]2025-08-21 13:57:56.317 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-43:875]2025-08-21 13:57:56.317 INFO  [AbstractEventVerticle:] - Resources closed successfully
[SpringApplicationShutdownHook:43]2025-08-21 13:57:56.317 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:43]2025-08-21 13:57:56.317 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[vert.x-virtual-thread-49:881]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-44:876]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-48:880]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-1:872]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-46:878]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-47:879]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-45:877]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-41:873]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-1:871]2025-08-21 13:57:56.320 INFO  [AbstractEventVerticle:] - Resources closed successfully
[SpringApplicationShutdownHook:43]2025-08-21 13:57:56.321 INFO  [GracefulShutdown:53] - Commencing graceful shutdown. Waiting for active requests to complete
[vert.x-virtual-thread-51:883]2025-08-21 13:57:56.323 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-50:882]2025-08-21 13:57:56.323 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-52:884]2025-08-21 13:57:56.323 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-53:885]2025-08-21 13:57:56.323 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-54:886]2025-08-21 13:57:56.323 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-55:887]2025-08-21 13:57:56.323 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-56:888]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-57:889]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-58:890]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-60:892]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-61:893]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-62:894]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-63:895]2025-08-21 13:57:56.325 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-64:896]2025-08-21 13:57:56.326 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-65:897]2025-08-21 13:57:56.326 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-2:898]2025-08-21 13:57:56.326 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-3:899]2025-08-21 13:57:56.326 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-virtual-thread-59:891]2025-08-21 13:57:56.326 INFO  [AbstractEventVerticle:] - Resources closed successfully
[netty-shutdown:900]2025-08-21 13:57:56.326 INFO  [GracefulShutdown:62] - Graceful shutdown complete

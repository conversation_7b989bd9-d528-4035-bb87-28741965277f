[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:00.001 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:00.001 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:00.002 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:00.179 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:00.191 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:00.191 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:01.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:01.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:01.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:01.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:01.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:01.056 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:01.066 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:01.066 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:02.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:02.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:02.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:02.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:02.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:02.071 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:02.082 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:02.082 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:02.100 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:02.100 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16587}]
[event-409:10998]2025-08-20 16:00:02.102 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-409:10998]2025-08-20 16:00:02.102 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-409:10998]2025-08-20 16:00:02.103 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-409:10998]2025-08-20 16:00:02.125 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-409:10998]2025-08-20 16:00:02.125 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-409:10998]2025-08-20 16:00:02.158 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:02.180 INFO  [AbstractDispatchMessageListener:] - The total time of processing 80ms
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:03.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:03.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:03.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:03.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:03.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:03.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:03.066 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:03.066 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:03.083 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:03.083 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16588}]
[event-410:11001]2025-08-20 16:00:03.084 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-410:11001]2025-08-20 16:00:03.084 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-410:11001]2025-08-20 16:00:03.084 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-410:11001]2025-08-20 16:00:03.095 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-410:11001]2025-08-20 16:00:03.095 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-410:11001]2025-08-20 16:00:03.114 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:03.136 INFO  [AbstractDispatchMessageListener:] - The total time of processing 53ms
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:04.028 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:04.029 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:04.029 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:04.029 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:04.029 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:04.110 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:04.121 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:04.121 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:05.013 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:05.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:05.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:05.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:05.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:05.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:05.080 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:05.080 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:06.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:06.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:06.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:06.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:06.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:06.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:06.065 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:06.065 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:06.084 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:06.084 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16591}]
[event-411:11004]2025-08-20 16:00:06.084 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-411:11004]2025-08-20 16:00:06.084 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-411:11004]2025-08-20 16:00:06.084 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-411:11004]2025-08-20 16:00:06.095 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-411:11004]2025-08-20 16:00:06.095 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-411:11004]2025-08-20 16:00:06.113 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:06.136 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:07.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:07.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:07.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:07.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:07.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:07.071 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:07.081 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:07.081 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:08.013 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:08.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:08.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:08.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:08.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:08.056 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:08.065 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:08.066 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:08.084 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:08.084 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16593}]
[event-412:11007]2025-08-20 16:00:08.084 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-412:11007]2025-08-20 16:00:08.084 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-412:11007]2025-08-20 16:00:08.084 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-412:11007]2025-08-20 16:00:08.094 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-412:11007]2025-08-20 16:00:08.094 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-412:11007]2025-08-20 16:00:08.112 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:08.135 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:09.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:09.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:09.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:09.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:09.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:09.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:09.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:09.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:10.009 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:10.009 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:10.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:10.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:10.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:10.047 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:10.058 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:10.058 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:10.077 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:10.077 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16595}]
[event-413:11010]2025-08-20 16:00:10.077 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-413:11010]2025-08-20 16:00:10.078 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-413:11010]2025-08-20 16:00:10.078 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-413:11010]2025-08-20 16:00:10.089 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-413:11010]2025-08-20 16:00:10.089 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-413:11010]2025-08-20 16:00:10.124 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:10.149 INFO  [AbstractDispatchMessageListener:] - The total time of processing 72ms
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:11.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:11.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:11.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:11.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:11.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:11.051 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:11.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:11.060 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:12.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:12.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:12.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:12.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:12.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:12.049 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:12.059 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:12.059 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:13.016 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:13.017 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:13.017 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:13.017 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:13.017 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:13.062 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:13.074 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:13.074 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[quartzScheduler_QuartzSchedulerThread:189]2025-08-20 16:00:13.132 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:14.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:14.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:14.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:14.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:14.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:14.051 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:14.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:14.060 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:15.018 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:15.018 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:15.018 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:15.018 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:15.018 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:15.071 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:15.080 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:15.080 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:15.099 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:15.099 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16600}]
[event-414:11013]2025-08-20 16:00:15.100 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-414:11013]2025-08-20 16:00:15.100 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-414:11013]2025-08-20 16:00:15.100 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-414:11013]2025-08-20 16:00:15.111 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-414:11013]2025-08-20 16:00:15.111 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-414:11013]2025-08-20 16:00:15.128 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:15.151 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:16.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:16.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:16.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:16.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:16.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:16.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:16.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:16.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:17.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:17.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:17.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:17.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:17.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:17.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:17.059 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:17.059 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:18.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:18.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:18.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:18.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:18.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:18.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:18.065 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:18.065 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:18.117 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:18.118 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16603}]
[event-415:11018]2025-08-20 16:00:18.118 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-415:11018]2025-08-20 16:00:18.118 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-415:11018]2025-08-20 16:00:18.118 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-415:11018]2025-08-20 16:00:18.159 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-415:11018]2025-08-20 16:00:18.159 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-415:11018]2025-08-20 16:00:18.207 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:18.520 INFO  [AbstractDispatchMessageListener:] - The total time of processing 402ms
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:19.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:19.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:19.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:19.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:19.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:19.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:19.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:19.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:19.105 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:19.105 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16604}]
[event-416:11019]2025-08-20 16:00:19.105 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-416:11019]2025-08-20 16:00:19.105 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-416:11019]2025-08-20 16:00:19.106 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-416:11019]2025-08-20 16:00:19.134 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-416:11019]2025-08-20 16:00:19.134 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-416:11019]2025-08-20 16:00:19.179 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:19.262 INFO  [AbstractDispatchMessageListener:] - The total time of processing 157ms
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:20.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:20.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:20.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:20.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:20.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:20.076 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:20.086 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:20.086 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:20.137 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:20.137 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16605}]
[event-417:11020]2025-08-20 16:00:20.138 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-417:11020]2025-08-20 16:00:20.138 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-417:11020]2025-08-20 16:00:20.138 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-417:11020]2025-08-20 16:00:20.181 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-417:11020]2025-08-20 16:00:20.181 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-417:11020]2025-08-20 16:00:20.208 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:20.253 INFO  [AbstractDispatchMessageListener:] - The total time of processing 116ms
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:21.013 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:21.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:21.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:21.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:21.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:21.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:21.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:21.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:21.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:21.080 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16606}]
[event-418:11023]2025-08-20 16:00:21.080 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-418:11023]2025-08-20 16:00:21.080 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-418:11023]2025-08-20 16:00:21.080 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-418:11023]2025-08-20 16:00:21.092 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-418:11023]2025-08-20 16:00:21.092 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-418:11023]2025-08-20 16:00:21.108 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:21.131 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:22.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:22.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:22.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:22.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:22.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:22.049 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:22.058 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:22.058 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:23.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:23.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:23.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:23.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:23.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:23.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:23.061 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:23.061 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:24.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:24.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:24.014 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:24.014 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:24.014 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:24.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:24.064 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:24.064 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:24.587 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:24.587 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16609}]
[event-419:11026]2025-08-20 16:00:24.587 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-419:11026]2025-08-20 16:00:24.587 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-419:11026]2025-08-20 16:00:24.587 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-419:11026]2025-08-20 16:00:24.627 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-419:11026]2025-08-20 16:00:24.627 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-419:11026]2025-08-20 16:00:24.675 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:24.750 INFO  [AbstractDispatchMessageListener:] - The total time of processing 163ms
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:25.023 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:25.023 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:25.023 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:25.024 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:25.024 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:25.074 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:25.085 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:25.085 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:25.105 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:25.105 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16610}]
[event-420:11027]2025-08-20 16:00:25.105 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-420:11027]2025-08-20 16:00:25.105 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-420:11027]2025-08-20 16:00:25.105 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-420:11027]2025-08-20 16:00:25.117 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-420:11027]2025-08-20 16:00:25.117 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-420:11027]2025-08-20 16:00:25.133 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:25.156 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:26.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:26.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:26.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:26.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:26.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:26.049 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:26.058 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:26.058 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:27.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:27.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:27.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:27.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:27.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:27.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:27.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:27.060 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:27.111 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:27.111 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16612}]
[event-421:11030]2025-08-20 16:00:27.111 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-421:11030]2025-08-20 16:00:27.111 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-421:11030]2025-08-20 16:00:27.111 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-421:11030]2025-08-20 16:00:27.154 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-421:11030]2025-08-20 16:00:27.154 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-421:11030]2025-08-20 16:00:27.181 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:27.220 INFO  [AbstractDispatchMessageListener:] - The total time of processing 109ms
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:28.009 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:28.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:28.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:28.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:28.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:28.053 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:28.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:28.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:29.016 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:29.016 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:29.016 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:29.016 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:29.018 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:29.078 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:29.087 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:29.088 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:29.125 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:29.125 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16614}]
[event-422:11033]2025-08-20 16:00:29.127 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-422:11033]2025-08-20 16:00:29.127 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-422:11033]2025-08-20 16:00:29.127 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-422:11033]2025-08-20 16:00:29.138 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-422:11033]2025-08-20 16:00:29.138 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-422:11033]2025-08-20 16:00:29.156 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:29.426 INFO  [AbstractDispatchMessageListener:] - The total time of processing 301ms
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:30.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:30.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:30.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:30.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:30.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:30.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:30.065 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:30.065 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:31.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:31.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:31.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:31.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:31.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:31.051 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:31.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:31.061 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:31.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:31.080 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16616}]
[event-423:11034]2025-08-20 16:00:31.080 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-423:11034]2025-08-20 16:00:31.080 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-423:11034]2025-08-20 16:00:31.080 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-423:11034]2025-08-20 16:00:31.091 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-423:11034]2025-08-20 16:00:31.091 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-423:11034]2025-08-20 16:00:31.107 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:31.127 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:32.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:32.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:32.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:32.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:32.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:32.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:32.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:32.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:32.082 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:32.082 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16617}]
[event-424:11037]2025-08-20 16:00:32.082 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-424:11037]2025-08-20 16:00:32.082 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-424:11037]2025-08-20 16:00:32.082 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-424:11037]2025-08-20 16:00:32.093 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-424:11037]2025-08-20 16:00:32.093 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-424:11037]2025-08-20 16:00:32.109 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:32.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 46ms
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:33.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:33.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:33.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:33.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:33.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:33.055 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:33.072 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:33.072 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:33.091 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:33.091 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16618}]
[event-425:11038]2025-08-20 16:00:33.091 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-425:11038]2025-08-20 16:00:33.091 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-425:11038]2025-08-20 16:00:33.091 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-425:11038]2025-08-20 16:00:33.103 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-425:11038]2025-08-20 16:00:33.103 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-425:11038]2025-08-20 16:00:33.120 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:33.142 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:34.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:34.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:34.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:34.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:34.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:34.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:34.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:34.060 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:34.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:34.080 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16619}]
[event-426:11041]2025-08-20 16:00:34.081 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-426:11041]2025-08-20 16:00:34.081 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-426:11041]2025-08-20 16:00:34.081 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-426:11041]2025-08-20 16:00:34.091 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-426:11041]2025-08-20 16:00:34.091 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-426:11041]2025-08-20 16:00:34.107 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:34.127 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:35.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:35.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:35.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:35.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:35.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:35.056 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:35.065 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:35.065 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:36.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:36.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:36.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:36.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:36.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:36.048 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:36.058 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:36.058 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:36.078 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:36.078 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16621}]
[event-427:11042]2025-08-20 16:00:36.078 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-427:11042]2025-08-20 16:00:36.078 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-427:11042]2025-08-20 16:00:36.078 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-427:11042]2025-08-20 16:00:36.089 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-427:11042]2025-08-20 16:00:36.089 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-427:11042]2025-08-20 16:00:36.106 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:36.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:37.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:37.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:37.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:37.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:37.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:37.053 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:37.063 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:37.063 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:37.082 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:37.082 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16622}]
[event-428:11043]2025-08-20 16:00:37.082 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-428:11043]2025-08-20 16:00:37.082 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-428:11043]2025-08-20 16:00:37.082 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-428:11043]2025-08-20 16:00:37.092 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-428:11043]2025-08-20 16:00:37.092 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-428:11043]2025-08-20 16:00:37.109 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:37.130 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:38.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:38.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:38.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:38.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:38.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:38.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:38.061 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:38.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:39.014 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:39.014 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:39.014 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:39.014 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:39.014 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:39.067 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:39.086 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:39.087 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:40.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:40.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:40.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:40.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:40.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:40.063 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:40.088 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:40.088 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:41.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:41.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:41.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:41.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:41.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:41.058 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:41.081 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:41.081 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:42.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:42.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:42.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:42.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:42.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:42.051 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:42.059 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:42.059 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:42.078 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:42.078 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16627}]
[event-429:11048]2025-08-20 16:00:42.078 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-429:11048]2025-08-20 16:00:42.078 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-429:11048]2025-08-20 16:00:42.078 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-429:11048]2025-08-20 16:00:42.089 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-429:11048]2025-08-20 16:00:42.089 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-429:11048]2025-08-20 16:00:42.105 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:42.125 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[quartzScheduler_QuartzSchedulerThread:189]2025-08-20 16:00:42.448 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:43.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:43.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:43.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:43.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:43.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:43.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:43.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:43.060 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:43.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:43.080 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16628}]
[event-430:11051]2025-08-20 16:00:43.080 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-430:11051]2025-08-20 16:00:43.080 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-430:11051]2025-08-20 16:00:43.080 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-430:11051]2025-08-20 16:00:43.091 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-430:11051]2025-08-20 16:00:43.091 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-430:11051]2025-08-20 16:00:43.107 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:43.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:44.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:44.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:44.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:44.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:44.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:44.054 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:44.064 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:44.064 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:45.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:45.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:45.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:45.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:45.010 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:45.070 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:45.090 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:45.090 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:46.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:46.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:46.012 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:46.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:46.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:46.065 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:46.074 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:46.075 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:46.093 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:46.093 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16631}]
[event-431:11054]2025-08-20 16:00:46.094 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-431:11054]2025-08-20 16:00:46.094 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-431:11054]2025-08-20 16:00:46.094 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-431:11054]2025-08-20 16:00:46.104 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-431:11054]2025-08-20 16:00:46.105 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-431:11054]2025-08-20 16:00:46.122 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:46.143 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:47.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:47.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:47.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:47.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:47.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:47.060 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:47.069 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:47.069 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:48.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:48.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:48.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:48.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:48.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:48.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:48.059 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:48.059 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:48.077 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:48.077 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16633}]
[event-432:11057]2025-08-20 16:00:48.079 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-432:11057]2025-08-20 16:00:48.079 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-432:11057]2025-08-20 16:00:48.079 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-432:11057]2025-08-20 16:00:48.090 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-432:11057]2025-08-20 16:00:48.090 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-432:11057]2025-08-20 16:00:48.106 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:48.126 INFO  [AbstractDispatchMessageListener:] - The total time of processing 49ms
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:49.012 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:49.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:49.013 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:49.013 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:49.013 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:49.053 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:49.062 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:49.062 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:50.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:50.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:50.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:50.012 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:50.012 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:50.057 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:50.067 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:50.067 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:50.086 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:50.086 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16635}]
[event-433:11060]2025-08-20 16:00:50.087 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-433:11060]2025-08-20 16:00:50.087 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-433:11060]2025-08-20 16:00:50.087 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-433:11060]2025-08-20 16:00:50.097 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-433:11060]2025-08-20 16:00:50.097 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-433:11060]2025-08-20 16:00:50.113 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:50.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[DefaultQuartzScheduler_Worker-2:201]2025-08-20 16:00:51.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:51.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:51.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:51.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:51.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:51.057 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:51.068 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:51.068 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-3:202]2025-08-20 16:00:52.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:52.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:52.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:52.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:52.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:52.059 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:52.081 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:52.081 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-4:203]2025-08-20 16:00:53.022 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:53.022 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:53.022 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:53.022 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:53.022 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:53.073 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:53.084 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:53.085 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-5:204]2025-08-20 16:00:54.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:54.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:54.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:54.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:54.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:54.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:54.063 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:54.063 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-6:205]2025-08-20 16:00:55.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:55.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:55.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:55.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:55.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:55.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:55.071 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:55.071 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:55.095 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:55.095 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16640}]
[event-434:11065]2025-08-20 16:00:55.096 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-434:11065]2025-08-20 16:00:55.096 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-434:11065]2025-08-20 16:00:55.096 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-434:11065]2025-08-20 16:00:55.106 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-434:11065]2025-08-20 16:00:55.106 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-434:11065]2025-08-20 16:00:55.122 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:55.143 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[DefaultQuartzScheduler_Worker-7:206]2025-08-20 16:00:56.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:56.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:56.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:56.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:56.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:56.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:56.072 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:56.072 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-8:207]2025-08-20 16:00:57.011 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:57.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:57.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:57.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:57.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:57.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:57.060 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:57.060 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[DefaultQuartzScheduler_Worker-9:208]2025-08-20 16:00:58.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:58.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:58.011 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:58.011 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:58.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:58.052 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:58.061 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:58.061 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:58.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:58.080 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16643}]
[event-435:11066]2025-08-20 16:00:58.080 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-435:11066]2025-08-20 16:00:58.080 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-435:11066]2025-08-20 16:00:58.080 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-435:11066]2025-08-20 16:00:58.091 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-435:11066]2025-08-20 16:00:58.091 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-435:11066]2025-08-20 16:00:58.111 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:58.132 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[DefaultQuartzScheduler_Worker-10:209]2025-08-20 16:00:59.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:59.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 1 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:59.010 DEBUG [PropertySettingJobFactory:] - Producing instance of Job 'DEFAULT.logisticsDetailSyncSchedule', class=com.myco.framework.scheduling.quartz.adapter.DefaultStatefulJobAdapter
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:00:59.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:59.011 DEBUG [JobRunShell:] - Calling execute on job DEFAULT.logisticsDetailSyncSchedule
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:59.050 DEBUG [WatcherRemovalManager:] - Removing watcher for path: /locks/schedule/logisticsDetailSyncSchedule/leases
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:59.059 DEBUG [UserLockTemplate:] - Get object[/locks/schedule/logisticsDetailSyncSchedule] lock object
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:00:59.059 DEBUG [AOProxyAspect:] - The schedule is called proxies!
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:59.077 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:59.077 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":16644}]
[event-436:11069]2025-08-20 16:00:59.078 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-436:11069]2025-08-20 16:00:59.078 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[event-436:11069]2025-08-20 16:00:59.079 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_order
[event-436:11069]2025-08-20 16:00:59.088 DEBUG [getWeekTotalRecords:] - ==>  Preparing: select count(1) from o_logistics_order where create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and deleted = 0 and logistics_no is not null
[event-436:11069]2025-08-20 16:00:59.088 DEBUG [getWeekTotalRecords:] - ==> Parameters: 
[event-436:11069]2025-08-20 16:00:59.105 DEBUG [getWeekTotalRecords:] - <==      Total: 1
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:59.134 INFO  [AbstractDispatchMessageListener:] - The total time of processing 57ms
[DefaultQuartzScheduler_Worker-1:200]2025-08-20 16:01:00.010 DEBUG [UserLockTemplate:] - Release the lock object
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:01:00.010 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:189]2025-08-20 16:01:05.810 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:01:27.196 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:189]2025-08-20 16:01:29.391 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:01:52.561 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:189]2025-08-20 16:01:57.116 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:210]2025-08-20 16:02:17.603 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:189]2025-08-20 16:02:20.422 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers

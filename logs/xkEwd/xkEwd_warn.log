[main-SendThread(118.31.184.84:2181):10737]2025-08-20 17:26:37.608 WARN  [ClientCnxn:] - Session 0x100000114063322 for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x100000114063322, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-118.31.184.84-6469:14632]2025-08-20 17:26:37.622 ERROR [GrpcClient:] - [1755676451136_221.12.20.178_26793]Request stream onCompleted, switch server
[nacos-grpc-client-executor-118.31.184.84-6437:14633]2025-08-20 17:26:37.688 ERROR [GrpcClient:] - [1755676451165_221.12.20.178_26796]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 WARN  [naming:] - mark to redo completed
[main-SendThread(118.31.184.84:2181):10737]2025-08-20 17:26:48.406 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x100000114063322 has expired
[main-EventThread:10738]2025-08-20 17:26:48.406 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(118.31.184.84:2181):10737]2025-08-20 17:26:48.406 WARN  [ClientCnxn:] - Session 0x100000114063322 for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x100000114063322 has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[commons-pool-evictor:102]2025-08-20 17:34:19.455 WARN  [ShardedJedisPool:] - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.flush(Connection.java:345) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:272) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:478) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:93) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.flush(Connection.java:342) ~[jedis-3.10.0.jar:?]
	... 15 more
[commons-pool-evictor:102]2025-08-20 17:34:19.455 WARN  [ShardedJedisPool:] - Error while disconnect
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.disconnect(Connection.java:259) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:165) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.disconnect(BinaryJedis.java:318) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:99) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:255) ~[jedis-3.10.0.jar:?]
	... 15 more
[commons-pool-evictor:102]2025-08-20 17:34:20.714 WARN  [ShardedJedisPool:] - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.flush(Connection.java:345) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:272) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:478) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:93) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.flush(Connection.java:342) ~[jedis-3.10.0.jar:?]
	... 15 more
[commons-pool-evictor:102]2025-08-20 17:34:20.714 WARN  [ShardedJedisPool:] - Error while disconnect
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.disconnect(Connection.java:259) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:165) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.disconnect(BinaryJedis.java:318) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:99) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:255) ~[jedis-3.10.0.jar:?]
	... 15 more
[Thread-6:63]2025-08-20 17:43:14.072 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[Thread-2:50]2025-08-20 17:43:14.072 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-6:63]2025-08-20 17:43:14.072 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
[Thread-4:56]2025-08-20 17:43:14.072 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-2:50]2025-08-20 17:43:14.073 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-4:56]2025-08-20 17:43:14.073 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Start destroying NacosRestTemplate
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Destruction of the end
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Start destroying NacosRestTemplate
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Destruction of the end

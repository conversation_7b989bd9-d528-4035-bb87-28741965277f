[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:02.100 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:02.180 INFO  [AbstractDispatchMessageListener:] - The total time of processing 80ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:03.083 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:03.136 INFO  [AbstractDispatchMessageListener:] - The total time of processing 53ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:06.084 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:06.136 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:08.084 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:08.135 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:10.077 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:10.149 INFO  [AbstractDispatchMessageListener:] - The total time of processing 72ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:15.099 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:15.151 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:18.117 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:18.520 INFO  [AbstractDispatchMessageListener:] - The total time of processing 402ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:19.105 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:19.262 INFO  [AbstractDispatchMessageListener:] - The total time of processing 157ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:20.137 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:20.253 INFO  [AbstractDispatchMessageListener:] - The total time of processing 116ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:21.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:21.131 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:24.587 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:24.750 INFO  [AbstractDispatchMessageListener:] - The total time of processing 163ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:25.105 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:25.156 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:27.111 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:27.220 INFO  [AbstractDispatchMessageListener:] - The total time of processing 109ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:29.125 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:29.426 INFO  [AbstractDispatchMessageListener:] - The total time of processing 301ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:31.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:31.127 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:32.082 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:32.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 46ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:33.091 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:33.142 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:34.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:34.127 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:36.078 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:36.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:37.082 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:37.130 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:42.078 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:42.125 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:43.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:43.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:46.093 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:46.143 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:48.077 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:48.126 INFO  [AbstractDispatchMessageListener:] - The total time of processing 49ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:50.086 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:50.137 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:55.095 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:55.143 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:58.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:58.132 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:59.077 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:00:59.134 INFO  [AbstractDispatchMessageListener:] - The total time of processing 57ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:00.206 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:00.274 INFO  [AbstractDispatchMessageListener:] - The total time of processing 68ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:01.099 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:01.152 INFO  [AbstractDispatchMessageListener:] - The total time of processing 53ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:04.090 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:04.142 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:05.101 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:05.157 INFO  [AbstractDispatchMessageListener:] - The total time of processing 56ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:07.102 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:07.161 INFO  [AbstractDispatchMessageListener:] - The total time of processing 59ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:09.104 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:09.170 INFO  [AbstractDispatchMessageListener:] - The total time of processing 66ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:11.089 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:11.141 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:12.109 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:12.161 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:13.124 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:13.476 INFO  [AbstractDispatchMessageListener:] - The total time of processing 352ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:14.089 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:14.190 INFO  [AbstractDispatchMessageListener:] - The total time of processing 101ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:16.150 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:16.300 INFO  [AbstractDispatchMessageListener:] - The total time of processing 150ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:17.085 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:17.134 INFO  [AbstractDispatchMessageListener:] - The total time of processing 49ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:22.130 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:22.466 INFO  [AbstractDispatchMessageListener:] - The total time of processing 336ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:23.144 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:23.316 INFO  [AbstractDispatchMessageListener:] - The total time of processing 172ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:26.083 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:26.187 INFO  [AbstractDispatchMessageListener:] - The total time of processing 104ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:28.147 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:28.323 INFO  [AbstractDispatchMessageListener:] - The total time of processing 176ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:30.138 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:30.299 INFO  [AbstractDispatchMessageListener:] - The total time of processing 161ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:35.134 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:35.234 INFO  [AbstractDispatchMessageListener:] - The total time of processing 100ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:38.119 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:38.260 INFO  [AbstractDispatchMessageListener:] - The total time of processing 141ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:39.218 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:39.369 INFO  [AbstractDispatchMessageListener:] - The total time of processing 151ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:40.274 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:40.429 INFO  [AbstractDispatchMessageListener:] - The total time of processing 155ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:41.110 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:41.207 INFO  [AbstractDispatchMessageListener:] - The total time of processing 97ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:44.298 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:44.391 INFO  [AbstractDispatchMessageListener:] - The total time of processing 93ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:45.108 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:45.156 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:47.310 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:47.391 INFO  [AbstractDispatchMessageListener:] - The total time of processing 81ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:49.221 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:49.378 INFO  [AbstractDispatchMessageListener:] - The total time of processing 157ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:51.168 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:51.243 INFO  [AbstractDispatchMessageListener:] - The total time of processing 75ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:52.180 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:52.525 INFO  [AbstractDispatchMessageListener:] - The total time of processing 345ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:53.132 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:53.270 INFO  [AbstractDispatchMessageListener:] - The total time of processing 138ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:54.127 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:54.229 INFO  [AbstractDispatchMessageListener:] - The total time of processing 102ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:56.087 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:56.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:57.140 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:30:57.187 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:31:00.437 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 16:31:00.487 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:02.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:02.147 INFO  [AbstractDispatchMessageListener:] - The total time of processing 67ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:03.078 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:03.129 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:06.093 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:06.145 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:08.083 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:08.133 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:10.094 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:10.141 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:15.092 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:15.141 INFO  [AbstractDispatchMessageListener:] - The total time of processing 49ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:18.080 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:18.130 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:19.140 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:19.215 INFO  [AbstractDispatchMessageListener:] - The total time of processing 75ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:20.084 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:20.132 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:21.088 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:21.141 INFO  [AbstractDispatchMessageListener:] - The total time of processing 53ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:24.249 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:24.297 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:25.140 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:25.187 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:27.083 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:27.128 INFO  [AbstractDispatchMessageListener:] - The total time of processing 45ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:29.094 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:29.146 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:31.094 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:31.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 44ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:32.079 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:32.144 INFO  [AbstractDispatchMessageListener:] - The total time of processing 65ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:33.079 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:33.125 INFO  [AbstractDispatchMessageListener:] - The total time of processing 46ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:34.087 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:34.139 INFO  [AbstractDispatchMessageListener:] - The total time of processing 52ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:36.084 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:36.130 INFO  [AbstractDispatchMessageListener:] - The total time of processing 46ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:37.115 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:37.178 INFO  [AbstractDispatchMessageListener:] - The total time of processing 63ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:42.140 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:42.212 INFO  [AbstractDispatchMessageListener:] - The total time of processing 72ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:43.076 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:43.123 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:46.462 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:46.508 INFO  [AbstractDispatchMessageListener:] - The total time of processing 46ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:48.340 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:48.422 INFO  [AbstractDispatchMessageListener:] - The total time of processing 82ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:50.266 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:50.313 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:55.253 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:55.304 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:58.274 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:58.354 INFO  [AbstractDispatchMessageListener:] - The total time of processing 80ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:59.281 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:00:59.394 INFO  [AbstractDispatchMessageListener:] - The total time of processing 113ms
[main-SendThread(*************:2181):10737]2025-08-20 17:26:37.608 WARN  [ClientCnxn:] - Session 0x100000114063322 for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x100000114063322, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[NettyClientSelector_1:468]2025-08-20 17:26:37.611 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[nacos-grpc-client-executor-*************-6469:14632]2025-08-20 17:26:37.621 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 12773
[nacos-grpc-client-executor-*************-6469:14632]2025-08-20 17:26:37.621 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 12773
[nacos-grpc-client-executor-*************-6469:14632]2025-08-20 17:26:37.622 ERROR [GrpcClient:] - [1755676451136_221.12.20.178_26793]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:65]2025-08-20 17:26:37.639 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:65]2025-08-20 17:26:37.640 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[NettyClientSelector_1:198]2025-08-20 17:26:37.664 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:417]2025-08-20 17:26:37.664 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[nacos-grpc-client-executor-*************-6437:14633]2025-08-20 17:26:37.688 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Receive server push request, request = ClientDetectionRequest, requestId = 10122
[nacos-grpc-client-executor-*************-6437:14633]2025-08-20 17:26:37.688 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Ack server push request, request = ClientDetectionRequest, requestId = 10122
[nacos-grpc-client-executor-*************-6437:14633]2025-08-20 17:26:37.688 ERROR [GrpcClient:] - [1755676451165_221.12.20.178_26796]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:293]2025-08-20 17:26:37.688 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:293]2025-08-20 17:26:37.689 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main-EventThread:10738]2025-08-20 17:26:37.733 INFO  [ConnectionStateManager:] - State change: SUSPENDED
[Curator-ConnectionStateManager-0:98]2025-08-20 17:26:37.733 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: SUSPENDED
[com.alibaba.nacos.client.remote.worker.1:65]2025-08-20 17:26:37.892 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Success to connect a server [*************:8848], connectionId = 1755681996437_221.12.20.178_48427
[com.alibaba.nacos.client.remote.worker.1:65]2025-08-20 17:26:37.892 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755676451136_221.12.20.178_26793
[com.alibaba.nacos.client.remote.worker.1:65]2025-08-20 17:26:37.892 INFO  [client:] - Close current connection 1755676451136_221.12.20.178_26793
[com.alibaba.nacos.client.remote.worker.0:64]2025-08-20 17:26:37.893 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:64]2025-08-20 17:26:37.893 INFO  [ClientWorker:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.0:64]2025-08-20 17:26:37.893 INFO  [client:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:64]2025-08-20 17:26:37.893 INFO  [ClientWorker:] - [9fdbfcf0-eee2-434f-ac33-c7842dca8f69_config-0] Connected,notify listen context...
[com.alibaba.nacos.client.remote.worker.1:293]2025-08-20 17:26:38.054 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Success to connect a server [*************:8848], connectionId = 1755681996563_221.12.20.178_48450
[com.alibaba.nacos.client.remote.worker.1:293]2025-08-20 17:26:38.054 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Abandon prev connection, server is *************:8848, connectionId is 1755676451165_221.12.20.178_26796
[com.alibaba.nacos.client.remote.worker.1:293]2025-08-20 17:26:38.054 INFO  [client:] - Close current connection 1755676451165_221.12.20.178_26796
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 INFO  [client:] - [29e3c1c9-56ac-4625-aa4c-a2753dcfec28] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:292]2025-08-20 17:26:38.055 INFO  [naming:] - Grpc connection connect
[com.alibaba.nacos.client.naming.grpc.redo.0:291]2025-08-20 17:26:40.927 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkEwd
[main-SendThread(*************:2181):10737]2025-08-20 17:26:48.386 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):10737]2025-08-20 17:26:48.386 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):10737]2025-08-20 17:26:48.397 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.65.23:50101, server: *************/*************:2181
[main-SendThread(*************:2181):10737]2025-08-20 17:26:48.406 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x100000114063322 has expired
[main-EventThread:10738]2025-08-20 17:26:48.406 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(*************:2181):10737]2025-08-20 17:26:48.406 WARN  [ClientCnxn:] - Session 0x100000114063322 for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x100000114063322 has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:10738]2025-08-20 17:26:48.406 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@2bd2fa2c
[main-EventThread:10738]2025-08-20 17:26:48.410 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main-EventThread:10738]2025-08-20 17:26:48.410 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main-EventThread:10738]2025-08-20 17:26:48.410 INFO  [ConnectionStateManager:] - State change: LOST
[main-EventThread:10738]2025-08-20 17:26:48.410 INFO  [ClientCnxn:] - EventThread shut down for session: 0x100000114063322
[Curator-ConnectionStateManager-0:98]2025-08-20 17:26:48.410 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: LOST
[main-SendThread(*************:2181):14670]2025-08-20 17:26:57.464 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):14670]2025-08-20 17:26:57.464 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):14670]2025-08-20 17:26:57.476 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.65.23:50134, server: *************/*************:2181
[main-SendThread(*************:2181):14670]2025-08-20 17:26:57.489 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x10000011406332b, negotiated timeout = 40000
[main-EventThread:14671]2025-08-20 17:26:57.489 INFO  [ConnectionStateManager:] - State change: RECONNECTED
[Curator-ConnectionStateManager-0:98]2025-08-20 17:26:57.489 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: RECONNECTED
[main-EventThread:14671]2025-08-20 17:26:57.501 INFO  [EnsembleTracker:] - New config event received: {}
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:01.552 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:01.983 INFO  [AbstractDispatchMessageListener:] - The total time of processing 431ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:02.405 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:03.042 INFO  [AbstractDispatchMessageListener:] - The total time of processing 637ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:05.206 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:05.269 INFO  [AbstractDispatchMessageListener:] - The total time of processing 63ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:06.090 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:06.141 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:08.161 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:08.280 INFO  [AbstractDispatchMessageListener:] - The total time of processing 119ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:10.109 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:10.189 INFO  [AbstractDispatchMessageListener:] - The total time of processing 80ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:12.267 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:12.426 INFO  [AbstractDispatchMessageListener:] - The total time of processing 159ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:13.108 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:13.196 INFO  [AbstractDispatchMessageListener:] - The total time of processing 88ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:14.222 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:14.354 INFO  [AbstractDispatchMessageListener:] - The total time of processing 132ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:15.108 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:15.168 INFO  [AbstractDispatchMessageListener:] - The total time of processing 60ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:17.560 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:17.708 INFO  [AbstractDispatchMessageListener:] - The total time of processing 148ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:18.257 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:18.316 INFO  [AbstractDispatchMessageListener:] - The total time of processing 59ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:23.166 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:24.011 INFO  [AbstractDispatchMessageListener:] - The total time of processing 845ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:24.378 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:24.534 INFO  [AbstractDispatchMessageListener:] - The total time of processing 156ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:27.311 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:27.458 INFO  [AbstractDispatchMessageListener:] - The total time of processing 147ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:29.695 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:31.086 INFO  [AbstractDispatchMessageListener:] - The total time of processing 1391ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:31.320 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:31.471 INFO  [AbstractDispatchMessageListener:] - The total time of processing 151ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:36.103 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:36.152 INFO  [AbstractDispatchMessageListener:] - The total time of processing 49ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:39.091 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:39.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:40.095 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:40.145 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:41.100 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:41.148 INFO  [AbstractDispatchMessageListener:] - The total time of processing 48ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:42.120 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:42.175 INFO  [AbstractDispatchMessageListener:] - The total time of processing 55ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:45.207 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:45.389 INFO  [AbstractDispatchMessageListener:] - The total time of processing 182ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:46.139 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:46.193 INFO  [AbstractDispatchMessageListener:] - The total time of processing 54ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:48.100 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:48.145 INFO  [AbstractDispatchMessageListener:] - The total time of processing 45ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:50.103 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:50.154 INFO  [AbstractDispatchMessageListener:] - The total time of processing 51ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:52.091 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:52.140 INFO  [AbstractDispatchMessageListener:] - The total time of processing 49ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:53.092 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:53.138 INFO  [AbstractDispatchMessageListener:] - The total time of processing 46ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:54.097 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:54.147 INFO  [AbstractDispatchMessageListener:] - The total time of processing 50ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:55.090 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:55.134 INFO  [AbstractDispatchMessageListener:] - The total time of processing 44ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:57.101 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/2
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:57.148 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:58.092 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_EWD-EWD-LogisticsDetailSyncJobEvent/3
[ConsumeMessageThread_xkEwd_YD_EWD-EWD-LogisticsDetailSyncJobEvent_1:2384]2025-08-20 17:30:58.139 INFO  [AbstractDispatchMessageListener:] - The total time of processing 47ms
[commons-pool-evictor:102]2025-08-20 17:34:19.455 WARN  [ShardedJedisPool:] - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.flush(Connection.java:345) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:272) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:478) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:93) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.flush(Connection.java:342) ~[jedis-3.10.0.jar:?]
	... 15 more
[commons-pool-evictor:102]2025-08-20 17:34:19.455 WARN  [ShardedJedisPool:] - Error while disconnect
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.disconnect(Connection.java:259) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:165) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.disconnect(BinaryJedis.java:318) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:99) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:255) ~[jedis-3.10.0.jar:?]
	... 15 more
[commons-pool-evictor:102]2025-08-20 17:34:20.714 WARN  [ShardedJedisPool:] - Error while QUIT
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.flush(Connection.java:345) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:272) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.quit(BinaryJedis.java:478) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:93) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.flush(Connection.java:342) ~[jedis-3.10.0.jar:?]
	... 15 more
[commons-pool-evictor:102]2025-08-20 17:34:20.714 WARN  [ShardedJedisPool:] - Error while disconnect
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset by peer
	at redis.clients.jedis.Connection.disconnect(Connection.java:259) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryClient.disconnect(BinaryClient.java:165) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.BinaryJedis.disconnect(BinaryJedis.java:318) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.ShardedJedisPool$ShardedJedisFactory.destroyObject(ShardedJedisPool.java:99) ~[jedis-3.10.0.jar:?]
	at org.apache.commons.pool2.PooledObjectFactory.destroyObject(PooledObjectFactory.java:122) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.destroy(GenericObjectPool.java:600) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.GenericObjectPool.evict(GenericObjectPool.java:722) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.BaseGenericObjectPool$Evictor.run(BaseGenericObjectPool.java:162) ~[commons-pool2-2.12.1.jar:2.12.1]
	at org.apache.commons.pool2.impl.EvictionTimer$WeakRunner.run(EvictionTimer.java:114) ~[commons-pool2-2.12.1.jar:2.12.1]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.net.SocketException: Connection reset by peer
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819) ~[?:?]
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195) ~[?:?]
	at redis.clients.jedis.util.RedisOutputStream.flushBuffer(RedisOutputStream.java:52) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.util.RedisOutputStream.flush(RedisOutputStream.java:133) ~[jedis-3.10.0.jar:?]
	at redis.clients.jedis.Connection.disconnect(Connection.java:255) ~[jedis-3.10.0.jar:?]
	... 15 more
[Thread-6:63]2025-08-20 17:43:14.072 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[Thread-2:50]2025-08-20 17:43:14.072 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-6:63]2025-08-20 17:43:14.072 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
[Thread-4:56]2025-08-20 17:43:14.072 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-2:50]2025-08-20 17:43:14.073 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-4:56]2025-08-20 17:43:14.073 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end
[vert.x-eventloop-thread-10:335]2025-08-20 17:43:14.091 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-11:336]2025-08-20 17:43:14.091 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-5:330]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:325]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:326]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-4:329]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-6:331]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-8:333]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-7:332]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-9:334]2025-08-20 17:43:14.092 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-14:339]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-3:328]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-15:340]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-12:337]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-13:338]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-2:327]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-16:341]2025-08-20 17:43:14.094 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-2:344]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:343]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:342]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-3:345]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-6:353]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-4:351]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:347]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:348]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-2:349]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:346]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-5:352]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-3:350]2025-08-20 17:43:14.095 INFO  [AbstractEventVerticle:] - Resources closed successfully
[SpringApplicationShutdownHook:47]2025-08-20 17:43:14.096 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:14.096 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:14.099 INFO  [GracefulShutdown:53] - Commencing graceful shutdown. Waiting for active requests to complete
[vert.x-eventloop-thread-0:354]2025-08-20 17:43:14.100 INFO  [AbstractEventVerticle:] - Resources closed successfully
[netty-shutdown:15457]2025-08-20 17:43:14.109 INFO  [GracefulShutdown:62] - Graceful shutdown complete
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.149 INFO  [QuartzSchedulerManager:] - Shutting down Quartz Scheduler
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.149 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.149 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.149 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.153 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.153 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: SpringApplicationShutdownHook
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [RAMJobStore:] - RAMJobStore initialized.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.155 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.655 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
[NettyClientSelector_1:198]2025-08-20 17:43:16.689 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:198]2025-08-20 17:43:16.689 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.690 INFO  [NacosServiceRegistry:] - De-registering from Nacos Server now...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.691 INFO  [naming:] - [DEREGISTER-SERVICE] dev deregistering service xkEwd with instance: Instance{instanceId='null', ip='*************', port=11012, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkEwd', serviceName='null', metadata={}}
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.714 INFO  [NacosServiceRegistry:] - De-registration finished.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Start destroying NacosRestTemplate
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Destruction of the end
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Start destroying NacosRestTemplate
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 WARN  [naming:] - [NamingHttpClientManager] Destruction of the end
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - Shutdown naming grpc client proxy for  uuid->29e3c1c9-56ac-4625-aa4c-a2753dcfec28
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [naming:] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@17444cb9[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 5925]
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [client:] - Shutdown rpc client, set status to shutdown
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [client:] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67bd3612[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.716 INFO  [client:] - Close current connection 1755681996563_221.12.20.178_48450
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.719 INFO  [GrpcClient:] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@30b55cae[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 6825]
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.719 INFO  [naming:] - shutdown and remove naming rpc client  for uuid ->29e3c1c9-56ac-4625-aa4c-a2753dcfec28
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.719 INFO  [CredentialWatcher:] - [null] CredentialWatcher is stopped
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.719 INFO  [CredentialService:] - [null] CredentialService is freed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.719 INFO  [naming:] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.720 INFO  [SchedulerFactoryBean:] - Shutting down Quartz Scheduler
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.720 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.720 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:47]2025-08-20 17:43:16.720 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
[NettyClientSelector_1:660]2025-08-20 17:43:16.741 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:660]2025-08-20 17:43:16.741 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:649]2025-08-20 17:43:16.764 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:649]2025-08-20 17:43:16.764 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:639]2025-08-20 17:43:16.786 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:639]2025-08-20 17:43:16.786 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:630]2025-08-20 17:43:16.813 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:630]2025-08-20 17:43:16.813 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:615]2025-08-20 17:43:16.839 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:615]2025-08-20 17:43:16.839 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:602]2025-08-20 17:43:16.860 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:602]2025-08-20 17:43:16.860 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:594]2025-08-20 17:43:16.888 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:594]2025-08-20 17:43:16.888 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:580]2025-08-20 17:43:16.911 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:580]2025-08-20 17:43:16.911 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:568]2025-08-20 17:43:16.942 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:568]2025-08-20 17:43:16.942 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:552]2025-08-20 17:43:16.965 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:552]2025-08-20 17:43:16.965 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:540]2025-08-20 17:43:16.993 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:540]2025-08-20 17:43:16.993 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:529]2025-08-20 17:43:17.019 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:529]2025-08-20 17:43:17.019 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:520]2025-08-20 17:43:17.040 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:520]2025-08-20 17:43:17.040 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:510]2025-08-20 17:43:17.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:510]2025-08-20 17:43:17.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:499]2025-08-20 17:43:17.086 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:499]2025-08-20 17:43:17.086 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:488]2025-08-20 17:43:17.109 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:488]2025-08-20 17:43:17.109 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:479]2025-08-20 17:43:17.133 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:479]2025-08-20 17:43:17.133 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:468]2025-08-20 17:43:17.161 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:468]2025-08-20 17:43:17.161 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:458]2025-08-20 17:43:17.188 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:458]2025-08-20 17:43:17.188 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:450]2025-08-20 17:43:17.216 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:450]2025-08-20 17:43:17.216 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:442]2025-08-20 17:43:17.240 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:442]2025-08-20 17:43:17.240 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:434]2025-08-20 17:43:17.266 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:434]2025-08-20 17:43:17.266 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:426]2025-08-20 17:43:17.289 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:426]2025-08-20 17:43:17.289 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:417]2025-08-20 17:43:17.316 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:417]2025-08-20 17:43:17.316 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:409]2025-08-20 17:43:17.338 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:409]2025-08-20 17:43:17.338 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:403]2025-08-20 17:43:17.360 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:403]2025-08-20 17:43:17.360 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:392]2025-08-20 17:43:17.380 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:392]2025-08-20 17:43:17.380 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[SpringApplicationShutdownHook:47]2025-08-20 17:43:17.386 INFO  [DefaultCosHttpClient:] - shutdown stackTrace:
Class: com.qcloud.cos.http.DefaultCosHttpClient, Method: shutdown, Line: 225
Class: com.qcloud.cos.COSClient, Method: shutdown, Line: 183
Class: com.myco.mydata.infrastructure.os.adapter.cos.TencentCosClientFactoryBean, Method: destroy, Line: 35
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: destroy, Line: 211
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroyBean, Line: 735
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingleton, Line: 692
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingleton, Line: 1401
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingletons, Line: 651
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingletons, Line: 1394
Class: org.springframework.context.support.AbstractApplicationContext, Method: destroyBeans, Line: 1219
Class: org.springframework.context.support.AbstractApplicationContext, Method: doClose, Line: 1180
Class: org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext, Method: doClose, Line: 155
Class: org.springframework.context.support.AbstractApplicationContext, Method: close, Line: 1126
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: closeAndWait, Line: 147
Class: java.lang.Iterable, Method: forEach, Line: 75
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: run, Line: 116
Class: java.lang.Thread, Method: run, Line: 1583
[NettyClientSelector_1:114]2025-08-20 17:43:18.028 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:114]2025-08-20 17:43:18.028 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.028 INFO  [DruidDataSource:] - {dataSource-5} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-5} closed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-4} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-4} closed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-6} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-6} closed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-2} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-2} closed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-7} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-7} closed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.032 INFO  [DruidDataSource:] - {dataSource-3} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.033 INFO  [DruidDataSource:] - {dataSource-3} closed
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.033 INFO  [DruidDataSource:] - {dataSource-1} closing ...
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.033 INFO  [DruidDataSource:] - {dataSource-1} closed
[Curator-Framework-0:101]2025-08-20 17:43:18.240 INFO  [CuratorFrameworkImpl:] - backgroundOperationsLoop exiting
[main-EventThread:14671]2025-08-20 17:43:18.354 INFO  [ClientCnxn:] - EventThread shut down for session: 0x10000011406332b
[SpringApplicationShutdownHook:47]2025-08-20 17:43:18.354 INFO  [ZooKeeper:] - Session: 0x10000011406332b closed

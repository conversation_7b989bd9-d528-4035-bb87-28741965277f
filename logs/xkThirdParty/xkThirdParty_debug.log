[main:1]2025-08-21 15:40:12.662 DEBUG [Loggers:] - Using Slf4j logging framework
[main:1]2025-08-21 15:40:12.663 DEBUG [Hooks:] - Enabling stacktrace debugging via onOperatorDebug
[main:1]2025-08-21 15:40:13.409 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 15:40:13.411 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 15:40:13.411 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 15:40:13.411 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 15:40:13.411 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
[main:1]2025-08-21 15:40:13.411 INFO  [NacosLogging:] - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
[background-preinit:47]2025-08-21 15:40:13.429 DEBUG [logging:] - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[background-preinit:47]2025-08-21 15:40:13.436 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[background-preinit:47]2025-08-21 15:40:13.437 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[background-preinit:47]2025-08-21 15:40:13.438 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[background-preinit:47]2025-08-21 15:40:13.438 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[background-preinit:47]2025-08-21 15:40:13.444 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[background-preinit:47]2025-08-21 15:40:13.462 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[background-preinit:47]2025-08-21 15:40:13.516 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[background-preinit:47]2025-08-21 15:40:13.521 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
[background-preinit:47]2025-08-21 15:40:13.521 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[background-preinit:47]2025-08-21 15:40:13.521 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[background-preinit:47]2025-08-21 15:40:13.521 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[background-preinit:47]2025-08-21 15:40:13.521 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 15:40:15.396 INFO  [XkThirdPartyServer:] - The following 10 profiles are active: "commons", "data", "jms", "cache", "http", "schedule", "proxy", "os", "server", "dev"
[main:1]2025-08-21 15:40:15.398 DEBUG [SpringApplication:685] - Loading source class com.xk.tp.server.XkThirdPartyServer,class org.springframework.cloud.bootstrap.BootstrapApplicationListener$BootstrapMarkerConfiguration
[main:1]2025-08-21 15:40:15.408 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 15:40:15.408 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP] content: 
jobs:
    -   description: "系统消息发送"
        jobName: sendSystemMessageJob
        jobType: stateful
        jobBean: sendSystemMessageJob
        jobPool: custom
        triggerType: cron
        repeatCount: 
        repeatInterval: 
        cronExpression: "0 0 * * * ?"
        status: ACTIVE
        priority: 
        delay: 
        minPoolSize: 1
        maxPoolSize: 10
        keepAliveTime: 
        cacheQueueCapacity:
        serverIp: ***********
        serverIpBak: ***********
    -   description: "系统消息发送"
        jobName: bufferMessageJob
        jobType: stateful
        jobBean: bufferMessageJob
        jobPool: none
        triggerType: cron
        repeatCount: 
        repeatInterval: 
        cronExpression: "0/1 * * * * ?"
        status: ACTIVE
        priority: 
        delay: 
        minPoolSize:
        maxPoolSize:
        keepAliveTime: 
        cacheQueueCapacity:
        serverIp: ***********
        serverIpBak: ***********
    -   description: "对账数据同步"
        jobName: syncReconciledSchedule
        jobType: stateful
        jobBean: syncReconciledSchedule
        jobPool: none
        triggerType: cron
        repeatCount: 
        repeatInterval: 
        cronExpression: "0 0 0 * * ?"
        status: ACTIVE
        priority: 
        delay: 
        minPoolSize:
        maxPoolSize:
        keepAliveTime: 
        cacheQueueCapacity:  
[main:1]2025-08-21 15:40:15.408 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 15:40:15.408 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP] content: 
configuration:
    webClient:
        loadBalanced:
            maxInMemorySize: 5120
            connectTimeoutMillis: 30000
            responseTimeout: 30
            user: admin
            password: 6RirvS
    validation: 
        key: 
            admin: 'Kth1HURxA5mWcGpBYcUg6GgXV0hATl6u'
            corp: 'ykkqhUH6b6WAdm4ipfusTAEZ0cnuBBtv'
            ios: 'XtPp4XG9NanpAGGp5WIaMYv0lmVMjrXb'
            android: 'HCgN0RwmXKVFlIKaDGi3tAfS4moSqURb'
            harmony: 'ngZ1E0yVv1gyZLuw7D6XiiupOzN1nInL'
        expiresTime: 60
        status: false
    settings:
        test:
            sig1: "RPBHwTNdRvymgnC5kEWS1EDHE7x06BaC"
        session:
            timeout: 28800
        user:
            nickname: "用户"
        os.bucket.cos.10:
            id: 1331099099
            name: "haoshang-test-1331099099"
            domain: "http://files.xmjihaoyun.cn"
            region: "ap-shanghai"
        os.bucket.oss.10:
            id: 1098742611924356
            name: "xka-test"
            domain: "https://files.xmjihaoyun.cn"
            region: "cn-hangzhou"
        sms:
            test:
                status: true
    queue:
        disruptor:
            maxDrainAttemptsBeforeShutdown: 200
            sleepMillisBetweenDrainAttempts: 50 
            ringBufferSize: 131072 
            timeout: 5000
            strategy: "TIMEOUT"
            sleepTimeNs: 10
            retries: 200
            waitTimeout: 10
            timeUnit: MILLISECONDS
            notifyProgressThreshold: 2048
    vertx:
        vertx:
            #eventLoopPoolSize: 8
            maxEventLoopExecuteTime: 3000000000
        eventbus:
        deployment:
                threadingModel: EVENT_LOOP
                #blockPoolSize: 8
                instances: 1
    ncs:
        zookeeper: 
            connectionString: "*************:2181"
    scheduling:
        quartz:
            startupDelay:5
    os:
        cos:
            secretId: IKIDujOsAFH6tTr21oQq46vcItL2fBXkojU6
            secretKey: a1MXuGTWTK3c4LSNAk9MPIBxYyAY9yhH
            appId: 1331099099
            regionStr: ap-shanghai
        oss:
            secretId: LTAI5tEpzBC6KSKdcSTtHY2R
            secretKey: ******************************
            appId: 1098742611924356
            roleArn: acs:ram::1098742611924356:role/ramossdev1
            regionStr: cn-hangzhou
    jms: 
        rocketmq-producer:
            namesrvAddr: *************:9876
        rocketmq-consumer:
            namesrvAddr: *************:9876
        zmq-producer:
            nameServer: "MYDATA_IM_1:127.0.0.1:18100,MYDATA_IM_2:127.0.0.1:18200"
            zmqConnectUrl: "tcp://%s:%d"
            zmqRequestFormat: "requ|%s|%s"
            sendTimeout: 1000
            receiveTimeout: 1000
            reload: false
    http:
        defaultHttpClient: 
            connectTimeout: 5000 
            socketTimeout: 5000 
            connTimeToLive: 3
            retry: 2
            busiRetry: 2
    redis:
        zmqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2
            jmxEnabled: false
        seqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3
            jmxEnabled: false
        tableRedisClient: 
            connectionString: redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379,redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379
            jmxEnabled: false
        busiRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379
            jmxEnabled: false
    jdbc:
        xk_log: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
        xk_config: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
        xk_tp: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
    sharding:
        datasource.mapping:
            xk_log: 
                module: log
                dataSourceKeys: xk_log
                startId: 0
            xk_config: 
                module: config
                dataSourceKeys: xk_config
                startId: 0
            xk_tp: 
                module: tp
                dataSourceKeys: xk_tp
                startId: 0
        module.mapping:
            log: 
                tableRule: c_.*,log_.*
            config: 
                tableRule: t_.*
            tp: 
                tableRule: tp_.*
[main:1]2025-08-21 15:40:15.409 DEBUG [AnnotationConfigReactiveWebServerApplicationContext:674] - Refreshing org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@312f3050
[main:1]2025-08-21 15:40:16.569 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.auth.AuthFinishListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.570 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.auth.SyncFaceVerifyListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.571 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.log.CreateUseLogListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.573 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.message.ShortMessageCreateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.574 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.pay.PayFinishListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.574 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.push.PushAppMessageListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.574 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.reconciled.HuiFuMerchantConfigJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:16.575 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.reconciled.SyncReconciledJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 15:40:17.034 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 15:40:17.046 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:17.046 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:17.047 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 15:40:17.096 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'userObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.tp.gateway.config.XkThirdPartyServiceConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/tp/gateway/config/XkThirdPartyServiceConfig.class]]
[main:1]2025-08-21 15:40:17.096 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'corpObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.tp.gateway.config.XkThirdPartyServiceConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/tp/gateway/config/XkThirdPartyServiceConfig.class]]
[main:1]2025-08-21 15:40:17.109 INFO  [CacheData:] - config listener notify warn timeout millis use default 60000 millis 
[main:1]2025-08-21 15:40:17.109 INFO  [CacheData:] - nacos.cache.data.init.snapshot = true 
[main:1]2025-08-21 15:40:17.110 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkThirdParty-dev.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 15:40:17.115 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 15:40:17.507 DEBUG [LogFactory:] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[main:1]2025-08-21 15:40:17.516 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-21 15:40:17.542 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.542 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.542 INFO  [SystemParamTableHolder:] - System settings initializing.
[main:1]2025-08-21 15:40:17.543 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.543 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.543 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.571 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.593 INFO  [RoutingConfigHolder:] - {log-routing: [xk_log, 0 - 9223372036854775807],tp-routing: [xk_tp, 0 - 9223372036854775807],config-routing: [xk_config, 0 - 9223372036854775807] }
[main:1]2025-08-21 15:40:17.593 DEBUG [ShardingNodeExecutor:] - Routing-Config Holder initialization is complete.
[main:1]2025-08-21 15:40:17.593 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-21 15:40:17.730 INFO  [GenericScope:] - BeanFactory id=426eb206-41f7-3a45-b824-b11ffea40a82
[main:1]2025-08-21 15:40:18.112 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.116 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:zookeeper.version=3.6.4--d65253dcf68e9097c6e95a126463fd5fdeb4521c, built on 12/18/2022 18:10 GMT
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:host.name=*************
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.version=21.0.7
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.vendor=Oracle Corporation
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.home=C:\Program Files\Java\jdk-21
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.class.path=D:\code\xk\xk-third-party\xk-third-party-server\target\classes;D:\maven\repository\com\xk\xk-start-server\0.0.1-SNAPSHOT\xk-start-server-0.0.1-20250818.091612-116.jar;D:\maven\repository\com\myco\mydata\mydata-start-server\0.0.1-SNAPSHOT\mydata-start-server-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-core\0.0.1-SNAPSHOT\mydata-start-domain-core-0.0.1-20250819.023657-99.jar;D:\maven\repository\com\myco\mydata\mydata-start-commons\0.0.1-SNAPSHOT\mydata-start-commons-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\myco-framework-6\0.0.1-SNAPSHOT\myco-framework-6-0.0.1-20250819.023657-80.jar;D:\maven\repository\com\alibaba\nacos\nacos-client\2.4.3\nacos-client-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-auth-plugin\2.4.3\nacos-auth-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-encryption-plugin\2.4.3\nacos-encryption-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-logback-adapter-12\2.4.3\nacos-logback-adapter-12-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\logback-adapter\1.1.3\logback-adapter-1.1.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-log4j2-adapter\2.4.3\nacos-log4j2-adapter-2.4.3.jar;D:\maven\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\maven\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\maven\repository\org\quartz-scheduler\quartz\2.5.0\quartz-2.5.0.jar;D:\maven\repository\org\zeromq\jeromq\0.6.0\jeromq-0.6.0.jar;D:\maven\repository\eu\neilalexander\jnacl\1.0.0\jnacl-1.0.0.jar;D:\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\maven\repository\org\aspectj\aspectjrt\1.9.22.1\aspectjrt-1.9.22.1.jar;D:\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\maven\repository\org\springframework\spring-jdbc\6.2.3\spring-jdbc-6.2.3.jar;D:\maven\repository\org\apache\curator\curator-framework\4.3.0\curator-framework-4.3.0.jar;D:\maven\repository\org\apache\curator\curator-client\4.3.0\curator-client-4.3.0.jar;D:\maven\repository\com\google\guava\guava\27.0.1-jre\guava-27.0.1-jre.jar;D:\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven\repository\org\checkerframework\checker-qual\2.5.2\checker-qual-2.5.2.jar;D:\maven\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;D:\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;D:\maven\repository\org\apache\curator\curator-recipes\4.3.0\curator-recipes-4.3.0.jar;D:\maven\repository\org\apache\zookeeper\zookeeper\3.6.4\zookeeper-3.6.4.jar;D:\maven\repository\org\apache\zookeeper\zookeeper-jute\3.6.4\zookeeper-jute-3.6.4.jar;D:\maven\repository\org\apache\yetus\audience-annotations\0.13.0\audience-annotations-0.13.0.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final.jar;D:\maven\repository\org\mozilla\rhino\1.8.0\rhino-1.8.0.jar;D:\maven\repository\org\apache\groovy\groovy\4.0.26\groovy-4.0.26.jar;D:\maven\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;D:\maven\repository\cglib\cglib-nodep\3.3.0\cglib-nodep-3.3.0.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2\2.0.57\fastjson2-2.0.57.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.18.2\jackson-dataformat-yaml-2.18.2.jar;D:\maven\repository\joda-time\joda-time\2.14.0\joda-time-2.14.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-event\0.0.1-SNAPSHOT\mydata-start-domain-event-0.0.1-20250819.023657-92.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-enum\0.0.1-SNAPSHOT\mydata-start-domain-enum-0.0.1-20250819.023657-93.jar;D:\maven\repository\org\hibernate\validator\hibernate-validator\9.0.1.Final\hibernate-validator-9.0.1.Final.jar;D:\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\maven\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-interfaces\0.0.1-SNAPSHOT\mydata-start-interfaces-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\springframework\spring-webflux\6.2.3\spring-webflux-6.2.3.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-http\1.2.7\reactor-netty-http-1.2.7.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-classes-macos\4.2.2.Final\netty-resolver-dns-classes-macos-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-classes-epoll\4.2.2.Final\netty-transport-classes-epoll-4.2.2.Final.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-core\1.2.7\reactor-netty-core-1.2.7.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.2.0\spring-cloud-starter-loadbalancer-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-loadbalancer\4.2.0\spring-cloud-loadbalancer-4.2.0.jar;D:\maven\repository\io\projectreactor\addons\reactor-extra\3.5.2\reactor-extra-3.5.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-cache\3.4.3\spring-boot-starter-cache-3.4.3.jar;D:\maven\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-joda\2.18.3\jackson-datatype-joda-2.18.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;D:\maven\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;D:\maven\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-application\0.0.1-SNAPSHOT\mydata-start-application-0.0.1-20250819.023657-86.jar;D:\maven\repository\com\myco\mydata\mydata-start-gateway\0.0.1-SNAPSHOT\mydata-start-gateway-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-http\0.0.1-SNAPSHOT\mydata-start-infrastructure-http-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-data\0.0.1-SNAPSHOT\mydata-start-infrastructure-data-0.0.1-20250819.023657-93.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-commons\0.0.1-SNAPSHOT\mydata-start-infrastructure-commons-0.0.1-20250819.023657-89.jar;D:\maven\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;D:\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven\repository\com\lmax\disruptor\3.4.4\disruptor-3.4.4.jar;D:\maven\repository\com\google\zxing\core\3.5.3\core-3.5.3.jar;D:\maven\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.18.3\jackson-dataformat-xml-2.18.3.jar;D:\maven\repository\org\codehaus\woodstox\stax2-api\4.2.2\stax2-api-4.2.2.jar;D:\maven\repository\com\fasterxml\woodstox\woodstox-core\7.0.0\woodstox-core-7.0.0.jar;D:\maven\repository\io\github\jopenlibs\vault-java-driver\6.2.0\vault-java-driver-6.2.0.jar;D:\maven\repository\com\mysql\mysql-connector-j\9.3.0\mysql-connector-j-9.3.0.jar;D:\maven\repository\com\google\protobuf\protobuf-java\4.29.0\protobuf-java-4.29.0.jar;D:\maven\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\maven\repository\org\springframework\spring-context-support\6.2.3\spring-context-support-6.2.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2023.0.1.3\spring-cloud-starter-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2023.0.1.3\spring-cloud-alibaba-commons-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-alibaba-nacos-config\2023.0.1.3\spring-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2023.0.1.3\spring-cloud-starter-alibaba-nacos-discovery-2023.0.1.3.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-jms\0.0.1-SNAPSHOT\mydata-start-infrastructure-jms-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-client\4.9.8\rocketmq-client-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-common\4.9.8\rocketmq-common-4.9.8.jar;D:\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-acl\4.9.8\rocketmq-acl-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-remoting\4.9.8\rocketmq-remoting-4.9.8.jar;D:\maven\repository\io\netty\netty-all\4.2.2.Final\netty-all-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec\4.2.2.Final\netty-codec-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-haproxy\4.2.2.Final\netty-codec-haproxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http3\4.2.2.Final\netty-codec-http3-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-memcache\4.2.2.Final\netty-codec-memcache-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-mqtt\4.2.2.Final\netty-codec-mqtt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-redis\4.2.2.Final\netty-codec-redis-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-smtp\4.2.2.Final\netty-codec-smtp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-stomp\4.2.2.Final\netty-codec-stomp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-xml\4.2.2.Final\netty-codec-xml-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-protobuf\4.2.2.Final\netty-codec-protobuf-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-marshalling\4.2.2.Final\netty-codec-marshalling-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-ssl-ocsp\4.2.2.Final\netty-handler-ssl-ocsp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-rxtx\4.2.2.Final\netty-transport-rxtx-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-sctp\4.2.2.Final\netty-transport-sctp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-udt\4.2.2.Final\netty-transport-udt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-kqueue\4.2.2.Final\netty-transport-classes-kqueue-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-io_uring\4.2.2.Final\netty-transport-classes-io_uring-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-classes-quic\4.2.2.Final\netty-codec-classes-quic-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-windows-x86_64.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-logging\4.9.8\rocketmq-logging-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.8\rocketmq-srvutil-4.9.8.jar;D:\maven\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;D:\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven\repository\org\springframework\kafka\spring-kafka\3.3.7\spring-kafka-3.3.7.jar;D:\maven\repository\org\springframework\spring-messaging\6.2.3\spring-messaging-6.2.3.jar;D:\maven\repository\org\springframework\spring-tx\6.2.3\spring-tx-6.2.3.jar;D:\maven\repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar;D:\maven\repository\org\apache\kafka\kafka-clients\3.9.1\kafka-clients-3.9.1.jar;D:\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;D:\maven\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\maven\repository\io\projectreactor\kafka\reactor-kafka\1.3.23\reactor-kafka-1.3.23.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-cache\0.0.1-SNAPSHOT\mydata-start-infrastructure-cache-0.0.1-20250819.023657-87.jar;D:\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-os\0.0.1-SNAPSHOT\mydata-start-infrastructure-os-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\qcloud\cos_api\5.6.242\cos_api-5.6.242.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-kms\3.1.1138\tencentcloud-sdk-java-kms-3.1.1138.jar;D:\maven\repository\com\thoughtworks\xstream\xstream\1.4.21\xstream-1.4.21.jar;D:\maven\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\maven\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\maven\repository\com\auth0\java-jwt\4.4.0\java-jwt-4.4.0.jar;D:\maven\repository\com\qcloud\cos-sts_api\3.1.1\cos-sts_api-3.1.1.jar;D:\maven\repository\com\aliyun\alibabacloud-sts20150401\1.0.7\alibabacloud-sts20150401-1.0.7.jar;D:\maven\repository\com\aliyun\oss\aliyun-sdk-oss\3.18.2\aliyun-sdk-oss-3.18.2.jar;D:\maven\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\maven\repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\maven\repository\com\aliyun\java-trace-api\0.2.11-beta\java-trace-api-0.2.11-beta.jar;D:\maven\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;D:\maven\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-proxy\0.0.1-SNAPSHOT\mydata-start-infrastructure-proxy-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-security\0.0.1-SNAPSHOT\mydata-start-infrastructure-security-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-validation\0.0.1-SNAPSHOT\mydata-start-infrastructure-validation-0.0.1-20250819.023657-86.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.4.3\spring-boot-starter-validation-3.4.3.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.36\tomcat-embed-el-10.1.36.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-webflux\3.4.3\spring-boot-starter-webflux-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.4.3\spring-boot-starter-json-3.4.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.4.3\spring-boot-starter-reactor-netty-3.4.3.jar;D:\maven\repository\org\springframework\spring-web\6.2.3\spring-web-6.2.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\4.2.0\spring-cloud-starter-bootstrap-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter\4.2.0\spring-cloud-starter-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;D:\maven\repository\org\springframework\security\spring-security-crypto\6.4.3\spring-security-crypto-6.4.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-commons\4.2.0\spring-cloud-commons-4.2.0.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.3\spring-boot-starter-actuator-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.3\spring-boot-actuator-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator\3.4.3\spring-boot-actuator-3.4.3.jar;D:\maven\repository\io\micrometer\micrometer-observation\1.14.4\micrometer-observation-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-commons\1.14.4\micrometer-commons-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-jakarta9\1.14.4\micrometer-jakarta9-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-core\1.14.4\micrometer-core-1.14.4.jar;D:\maven\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;D:\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven\repository\com\xk\xk-start-application\0.0.1-SNAPSHOT\xk-start-application-0.0.1-20250818.091612-115.jar;D:\maven\repository\com\xk\xk-start-domain-core\0.0.1-SNAPSHOT\xk-start-domain-core-0.0.1-20250818.091612-127.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-domain\0.0.1-SNAPSHOT\mydata-config-domain-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\xk\xk-start-domain-event\0.0.1-SNAPSHOT\xk-start-domain-event-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-interfaces\0.0.1-SNAPSHOT\xk-start-interfaces-0.0.1-20250818.091612-122.jar;D:\maven\repository\com\xk\xk-start-domain-enum\0.0.1-SNAPSHOT\xk-start-domain-enum-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-infrastructure\0.0.1-SNAPSHOT\xk-start-infrastructure-0.0.1-20250818.091612-118.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-infrastructure\0.0.1-SNAPSHOT\mydata-config-infrastructure-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-event\0.0.1-SNAPSHOT\mydata-start-infrastructure-event-0.0.1-20250819.023657-88.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-schedule\0.0.1-SNAPSHOT\mydata-start-infrastructure-schedule-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\alibaba\easyexcel\4.0.3\easyexcel-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-core\4.0.3\easyexcel-core-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;D:\maven\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;D:\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\maven\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\maven\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;D:\maven\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;D:\maven\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;D:\maven\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;D:\maven\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;D:\maven\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;D:\maven\repository\org\apache\commons\commons-csv\1.11.0\commons-csv-1.11.0.jar;D:\maven\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\maven\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\maven\repository\com\xk\xk-start-gateway\0.0.1-SNAPSHOT\xk-start-gateway-0.0.1-20250818.091612-121.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-gateway\0.0.1-SNAPSHOT\mydata-config-gateway-0.0.1-20250719.075944-9.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-openfeign\4.2.0\spring-cloud-starter-openfeign-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-openfeign-core\4.2.0\spring-cloud-openfeign-core-4.2.0.jar;D:\maven\repository\io\github\openfeign\feign-form-spring\13.5\feign-form-spring-13.5.jar;D:\maven\repository\io\github\openfeign\feign-form\13.5\feign-form-13.5.jar;D:\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\maven\repository\io\github\openfeign\feign-core\13.5\feign-core-13.5.jar;D:\maven\repository\io\github\openfeign\feign-slf4j\13.5\feign-slf4j-13.5.jar;D:\code\xk\xk-third-party\xk-third-party-application\target\classes;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-core\target\classes;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-event\target\classes;D:\code\xk\xk-third-party\xk-third-party-interfaces\target\classes;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-enum\target\classes;D:\code\xk\xk-third-party\xk-third-party-gateway\target\classes;D:\maven\repository\com\xk\acct\xk-acct-interfaces\0.0.1-SNAPSHOT\xk-acct-interfaces-0.0.1-20250818.092336-74.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-enum\0.0.1-SNAPSHOT\xk-acct-domain-enum-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\message\xk-message-domain-enum\0.0.1-SNAPSHOT\xk-message-domain-enum-0.0.1-20250819.084230-25.jar;D:\maven\repository\com\xk\goods\xk-goods-interfaces\0.0.1-SNAPSHOT\xk-goods-interfaces-0.0.1-20250819.095553-180.jar;D:\maven\repository\com\xk\goods\xk-goods-domain-enum\0.0.1-SNAPSHOT\xk-goods-domain-enum-0.0.1-20250819.095553-172.jar;D:\maven\repository\com\xk\corp\xk-corp-interfaces\0.0.1-SNAPSHOT\xk-corp-interfaces-0.0.1-20250728.125932-33.jar;D:\maven\repository\com\xk\corp\xk-corp-domain-enum\0.0.1-SNAPSHOT\xk-corp-domain-enum-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-event\0.0.1-SNAPSHOT\xk-auth-domain-event-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-enum\0.0.1-SNAPSHOT\xk-auth-domain-enum-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-event\0.0.1-SNAPSHOT\xk-acct-domain-event-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\message\xk-message-domain-event\0.0.1-SNAPSHOT\xk-message-domain-event-0.0.1-20250819.084230-24.jar;D:\maven\repository\org\apache\httpcomponents\httpclient\4.5.9\httpclient-4.5.9.jar;D:\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\maven\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;D:\code\xk\xk-third-party\xk-third-party-infrastructure\target\classes;D:\maven\repository\com\alipay\sdk\alipay-sdk-java\4.39.190.ALL\alipay-sdk-java-4.39.190.ALL.jar;D:\maven\repository\com\alibaba\fastjson\2.0.57\fastjson-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2-extension\2.0.57\fastjson2-extension-2.0.57.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk15on\1.62\bcprov-jdk15on-1.62.jar;D:\maven\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;D:\maven\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;D:\maven\repository\com\squareup\okhttp3\okhttp\3.12.13\okhttp-3.12.13.jar;D:\maven\repository\com\squareup\okio\okio\1.15.0\okio-1.15.0.jar;D:\maven\repository\com\github\wechatpay-apiv3\wechatpay-apache-httpclient\0.4.2\wechatpay-apache-httpclient-0.4.2.jar;D:\maven\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;D:\maven\repository\com\github\wechatpay-apiv3\wechatpay-java\0.2.12\wechatpay-java-0.2.12.jar;D:\maven\repository\com\github\wechatpay-apiv3\wechatpay-java-core\0.2.12\wechatpay-java-core-0.2.12.jar;D:\maven\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\maven\repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-intl-en\3.0.988\tencentcloud-sdk-java-intl-en-3.0.988.jar;D:\maven\repository\com\squareup\okhttp3\logging-interceptor\3.12.13\logging-interceptor-3.12.13.jar;D:\maven\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-common\3.1.924\tencentcloud-sdk-java-common-3.1.924.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-trtc\3.1.1315\tencentcloud-sdk-java-trtc-3.1.1315.jar;D:\maven\repository\com\aliyun\alibabacloud-dysmsapi20170525\3.0.4\alibabacloud-dysmsapi20170525-3.0.4.jar;D:\maven\repository\com\aliyun\aliyun-gateway-pop\0.2.15-beta\aliyun-gateway-pop-0.2.15-beta.jar;D:\maven\repository\com\aliyun\darabonba-java-core\0.2.15-beta\darabonba-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-http-apache\0.2.15-beta\aliyun-http-apache-0.2.15-beta.jar;D:\maven\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;D:\maven\repository\com\aliyun\aliyun-java-core\0.2.15-beta\aliyun-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-auth\0.2.15-beta\aliyun-java-auth-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-core\4.7.3\aliyun-java-sdk-core-4.7.3.jar;D:\maven\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk18on\1.78.1\bcprov-jdk18on-1.78.1.jar;D:\maven\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\maven\repository\com\aliyun\alibabacloud-cloudauth20190307\2.0.6\alibabacloud-cloudauth20190307-2.0.6.jar;D:\maven\repository\com\huifu\bspay\sdk\dg-java-sdk\3.0.27\dg-java-sdk-3.0.27.jar;D:\maven\repository\com\aliyun\dypnsapi20170525\1.0.6\dypnsapi20170525-1.0.6.jar;D:\maven\repository\com\aliyun\tea-util\0.2.13\tea-util-0.2.13.jar;D:\maven\repository\com\aliyun\tea-openapi\0.2.2\tea-openapi-0.2.2.jar;D:\maven\repository\com\aliyun\credentials-java\0.2.4\credentials-java-0.2.4.jar;D:\maven\repository\com\sun\xml\bind\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\com\sun\xml\bind\jaxb-impl\4.0.5\jaxb-impl-4.0.5.jar;D:\maven\repository\com\aliyun\alibabacloud-gateway-spi\0.0.1\alibabacloud-gateway-spi-0.0.1.jar;D:\maven\repository\com\aliyun\endpoint-util\0.0.6\endpoint-util-0.0.6.jar;D:\maven\repository\com\aliyun\tea\1.1.14\tea-1.1.14.jar;D:\maven\repository\org\jacoco\org.jacoco.agent\0.8.4\org.jacoco.agent-0.8.4-runtime.jar;D:\maven\repository\com\aliyun\openapiutil\0.1.14\openapiutil-0.1.14.jar;D:\maven\repository\org\bouncycastle\bcpkix-jdk15on\1.65\bcpkix-jdk15on-1.65.jar;D:\maven\repository\com\getui\push\restful-sdk\1.0.7.0\restful-sdk-1.0.7.0.jar;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-spring-boot-starter\1.4.8\mapstruct-plus-spring-boot-starter-1.4.8.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus\1.4.8\mapstruct-plus-1.4.8.jar;D:\maven\repository\org\mapstruct\mapstruct\1.5.5.Final\mapstruct-1.5.5.Final.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-object-convert\1.4.8\mapstruct-plus-object-convert-1.4.8.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.3\spring-boot-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar;D:\maven\repository\org\springframework\spring-context\6.2.3\spring-context-6.2.3.jar;D:\maven\repository\org\springframework\spring-aop\6.2.3\spring-aop-6.2.3.jar;D:\maven\repository\org\springframework\spring-beans\6.2.3\spring-beans-6.2.3.jar;D:\maven\repository\org\springframework\spring-expression\6.2.3\spring-expression-6.2.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.4.3\spring-boot-starter-3.4.3.jar;D:\maven\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;D:\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\maven\repository\org\springframework\spring-core\6.2.3\spring-core-6.2.3.jar;D:\maven\repository\org\springframework\spring-jcl\6.2.3\spring-jcl-6.2.3.jar;D:\maven\repository\io\vertx\vertx-core\5.0.1\vertx-core-5.0.1.jar;D:\maven\repository\io\vertx\vertx-core-logging\5.0.1\vertx-core-logging-5.0.1.jar;D:\maven\repository\io\netty\netty-common\4.2.2.Final\netty-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-buffer\4.2.2.Final\netty-buffer-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport\4.2.2.Final\netty-transport-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler\4.2.2.Final\netty-handler-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-unix-common\4.2.2.Final\netty-transport-native-unix-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-base\4.2.2.Final\netty-codec-base-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-proxy\4.2.2.Final\netty-handler-proxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-socks\4.2.2.Final\netty-codec-socks-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http\4.2.2.Final\netty-codec-http-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-compression\4.2.2.Final\netty-codec-compression-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http2\4.2.2.Final\netty-codec-http2-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver\4.2.2.Final\netty-resolver-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver-dns\4.2.2.Final\netty-resolver-dns-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-dns\4.2.2.Final\netty-codec-dns-4.2.2.Final.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-log4j2\3.4.3\spring-boot-starter-log4j2-3.4.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.3\log4j-slf4j2-impl-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-jul\2.24.3\log4j-jul-2.24.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.3\spring-boot-configuration-processor-3.4.3.jar;D:\maven\repository\io\projectreactor\reactor-core\3.7.7\reactor-core-3.7.7.jar;D:\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\lib\idea_rt.jar
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.library.path=C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\bin;C:\Program Files\JetBrains\PyCharm 2025.1.3.1\bin;;.
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:java.compiler=<NA>
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:os.name=Windows 11
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:os.arch=amd64
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:os.version=10.0
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:user.name=ShiJia
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:user.home=C:\Users\<USER>\code\xk
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:os.memory.free=62MB
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:os.memory.max=8048MB
[main:1]2025-08-21 15:40:18.153 INFO  [ZooKeeper:] - Client environment:os.memory.total=216MB
[main:1]2025-08-21 15:40:18.172 INFO  [CuratorFrameworkImpl:] - Starting
[main:1]2025-08-21 15:40:18.173 DEBUG [CuratorZookeeperClient:] - Starting
[main:1]2025-08-21 15:40:18.173 DEBUG [ConnectionState:] - Starting
[main:1]2025-08-21 15:40:18.173 DEBUG [ConnectionState:] - reset
[main:1]2025-08-21 15:40:18.174 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@71590f85
[main:1]2025-08-21 15:40:18.176 INFO  [X509Util:] - Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
[main:1]2025-08-21 15:40:18.179 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main:1]2025-08-21 15:40:18.183 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main:1]2025-08-21 15:40:18.188 INFO  [CuratorFrameworkImpl:] - Default schema
[main:1]2025-08-21 15:40:18.188 INFO  [ZookeeperClientFactoryBean:] - ZK connection is successful.
[main:1]2025-08-21 15:40:18.188 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.195 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.205 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.228 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.252 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.259 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.294 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.328 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.ncs.zookeeper.lockTimeout.user' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type Integer
[main:1]2025-08-21 15:40:18.332 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.349 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-producer.rmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:18.351 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.358 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.381 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.414 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.420 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.426 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'liveObjectDao' of type [com.xk.infrastructure.cache.dao.object.LiveObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.432 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.435 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.441 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.448 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.465 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.466 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.readonlyRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:18.466 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.requiredRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [create*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [revise*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [sync*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [add*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [update*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [handle] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [incr*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [amend*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [save*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [persist*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [remove*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [terminate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [delete*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [insert*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [commit*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [merge*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [apply*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [initiate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [alter*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [cancel*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [mod*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [retire*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.468 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [store*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.469 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.473 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:18.474 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.476 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.476 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 15:40:18.476 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.478 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:18.478 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 15:40:18.747 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.level: simple
[main:1]2025-08-21 15:40:18.747 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.targetRecords: 4
[main:1]2025-08-21 15:40:18.809 DEBUG [GlobalEventExecutor:] - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[main:1]2025-08-21 15:40:19.676 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.zmq-producer.zmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:20.210 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.redis.tableRedisClientRef' in PropertySource 'Config resource 'class path resource [application-data.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:21.202 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:21.202 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:21.202 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:21.202 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:21.202 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:22.599 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.599 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.791 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.791 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.801 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.801 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.813 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.813 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.825 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.825 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.835 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.835 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.845 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.845 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.885 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.885 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.894 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.894 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.937 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.984 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.984 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.994 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:22.994 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:23.005 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:23.005 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 15:40:23.346 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:23.346 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:23.346 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:24.085 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-21 15:40:24.134 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[main:1]2025-08-21 15:40:24.136 DEBUG [AbstractConfigurationImpl:] - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
[main:1]2025-08-21 15:40:24.136 DEBUG [AbstractConfigurationImpl:] - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
[main:1]2025-08-21 15:40:24.139 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[main:1]2025-08-21 15:40:24.139 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via user class loader
[main:1]2025-08-21 15:40:24.139 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[main:1]2025-08-21 15:40:24.139 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[main:1]2025-08-21 15:40:24.139 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[main:1]2025-08-21 15:40:24.141 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[main:1]2025-08-21 15:40:24.142 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[main:1]2025-08-21 15:40:24.143 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
[main:1]2025-08-21 15:40:24.143 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[main:1]2025-08-21 15:40:24.143 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[main:1]2025-08-21 15:40:24.143 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[main:1]2025-08-21 15:40:24.143 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 15:40:25.368 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main:1]2025-08-21 15:40:27.145 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_THIRD_PARTY-THIRD_PARTY-AuthFinishEvent
[main:1]2025-08-21 15:40:27.187 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_THIRD_PARTY-THIRD_PARTY-SyncFaceVerifyEvent
[main:1]2025-08-21 15:40:27.194 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_LOG-LOG-CreateUseLogEvent
[main:1]2025-08-21 15:40:27.200 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_MESSAGE-MESSAGE-ShortMessageCreateEvent
[main:1]2025-08-21 15:40:27.207 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_THIRD_PARTY-THIRD_PARTY-PayFinishEvent
[main:1]2025-08-21 15:40:27.214 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_MESSAGE-MESSAGE-MessageAppPushEvent
[main:1]2025-08-21 15:40:27.222 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_THIRD_PARTY-THIRD_PARTY-HuiFuMerchantConfigJobEvent
[main:1]2025-08-21 15:40:27.230 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkThirdParty_YD_THIRD_PARTY-THIRD_PARTY-SyncReconciledJobEvent
[main-SendThread(*************:2181):99]2025-08-21 15:40:27.237 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):99]2025-08-21 15:40:27.237 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):99]2025-08-21 15:40:27.248 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:53144, server: *************/*************:2181
[main-SendThread(*************:2181):99]2025-08-21 15:40:27.261 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x1000001140633ab, negotiated timeout = 40000
[main-EventThread:100]2025-08-21 15:40:27.262 DEBUG [ConnectionState:] - Negotiated session timeout: 40000
[main-EventThread:100]2025-08-21 15:40:27.264 INFO  [ConnectionStateManager:] - State change: CONNECTED
[main-EventThread:100]2025-08-21 15:40:27.264 DEBUG [CuratorFrameworkImpl:] - Clearing sleep for 10 operations
[main:1]2025-08-21 15:40:27.265 DEBUG [AutoConfigurationPackages:213] - @EnableAutoConfiguration was declared on a class in the package 'com.myco.mydata.server'. Automatic @Repository and @Entity scanning is enabled.
[Curator-ConnectionStateManager-0:98]2025-08-21 15:40:27.266 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: CONNECTED
[main-EventThread:100]2025-08-21 15:40:27.279 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:100]2025-08-21 15:40:27.279 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main-EventThread:100]2025-08-21 15:40:27.279 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:100]2025-08-21 15:40:27.279 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/query/access => {
 ((POST && /searchAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d29d70@64073f50
 ((POST && /searchAccessList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2a9c0@5b386c8
 ((POST && /searchAccessListByNoLogin) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2abd8@b39a3bf
 ((POST && /searchAccessDetail) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2adf0@4da6546a
 ((POST && /searchAccessExtDetail) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2b008@10060f31
 ((POST && /searchAccessDetailByNoLogin) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2b220@91a5e3f
 ((POST && /searchAccessExtList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2b438@4f7820f8
 ((POST && /searchAccessAccountList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2b650@********
 ((POST && /queryAccessInterfaceAuthInfoByCorp) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2b868@105dc6df
 ((POST && /queryAccessInterfaceAuthInfoByCorpNoLogin) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2ba80@4b6d894d
 ((POST && /searchAccessTpGame) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2bc98@459c0f38
 ((POST && /queryTagRelation) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2beb0@64fc2612
 ((POST && /searchAccessTag) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2c0c8@1a696f6c
 ((POST && /queryTagRelationByAccessTpTag) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2c2e0@3de9aa8d
 ((POST && /queryAccessGameById) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2c4f8@18a997c7
 ((POST && /queryAccessGameByDdGameId) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2c710@1d60ee76
 ((POST && /queryAccessExtInfo) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2c928@4c23f0b9
 ((POST && /searchAccessAndAccountList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2cb40@3c6e8672
 ((POST && /queryAccessInterfaceAuthInfoByAccessId) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2cd58@682d2997
 ((POST && /searchBlackAccessList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2cf70@722479ba
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/query/pay => {
 ((POST && /findByParams) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2d8b8@21d1f97f
 ((POST && /findPayResult) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2dad0@6944a621
 ((POST && /findPayQuota) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2dce8@68a45b0f
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/query/userAuth => {
 ((POST && /findPreUserAuth) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2df00@399c0608
 ((POST && /findUserInfo) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2e118@17a81a45
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/query/auth => {
 ((POST && /findAuthNotifyByParams) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2e330@5b91c33f
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/query/thirdCaptcha => {
 ((POST && /checkCaptcha) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2e548@********
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/useLog/query => {
 ((POST && /searchUseLog) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2e760@48526f0b
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/logistics/query => {
 ((POST && /detail) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x000001da81d2e978@1f7f2e76
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/access => {
 ((POST && /saveAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2eb90@4a229e9d
 ((POST && /updateAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2eda8@********
 ((POST && /deleteAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2efc0@3ce295f9
 ((POST && /saveAccessExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2f1d8@5a08108d
 ((POST && /updateAccessExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2f3f0@400bb27
 ((POST && /deleteAccessExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2f608@7fc9253c
 ((POST && /saveAccessAccount) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2f820@572c3a61
 ((POST && /updateAccessAccount) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2fa38@4dbad471
 ((POST && /deleteAccessAccount) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d2fc50@4ce29762
 ((POST && /saveAccessAccountExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30000@7a34bb07
 ((POST && /updateAccessAccountExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30218@4c7d7912
 ((POST && /deleteAccessAccountExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30430@577f25b0
 ((POST && /updateAccessStatus) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30648@f7ee1ba
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/sms => {
 ((POST && /sendSms) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30860@504d9fd1
 ((POST && /sendSmsByAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30a78@77dfc923
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/pay => {
 ((POST && /createPay) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30c90@59a330cd
 ((POST && /closeOrder) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d30ea8@21a87972
 ((POST && /refundOrder) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d310c0@1d27d981
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/transfer => {
 ((POST && /createPay) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d312d8@14389dd7
 ((POST && /huifu/bind) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d314f0@545df2c5
 ((POST && /huifu/create) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d31708@365c62d5
 ((POST && /huifu/mobile/verify) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d31920@15944e4e
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/payNotify => {
 ((POST && /ali/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d31b38@4512af0e
 ((POST && /wx/notify/{orderNo}/{accessAccountId}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d31d50@7ee2a6b3
 ((POST && /llian/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d31f68@2bfd3970
 ((POST && /llianHst/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d32180@1c2cf47f
 ((POST && /tlian/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d32398@4c16522b
 ((POST && /huifu/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d325b0@272aa224
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/auth => {
 ((POST && /base/auth) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d327c8@655e2908
 ((POST && /face/verify) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d329e0@1e2aa6d0
 ((POST && /face/verify/clear) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d32bf8@60d33834
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/authNotify => {
 ((GET && /ali/face/notify/{accessAccountId}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d32e10@129a9d88
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/useLog => {
 ((POST && /save) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33028@618f0ba
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/share => {
 ((POST && /create/business/config) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33240@4dcfca8f
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/live => {
 ((POST && /remove/user) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33458@34a4599a
 ((POST && /remove/user/str) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33670@7294e45b
 ((POST && /dismiss/room) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33888@33c84793
 ((POST && /dismiss/room/str) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33aa0@e068d7
 ((POST && /sign) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33cb8@161e99d8
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/recording => {
 ((POST && /create/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d33ed0@43fd117c
 ((POST && /delete/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d340e8@3278324f
 ((POST && /describe/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34300@315d4207
 ((POST && /modify/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34518@536fddad
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tp/im => {
 ((POST && /create/group) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34730@78b2504d
 ((POST && /delete/group) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34948@babef5e
 ((POST && /online/number) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34b60@50bdd956
 ((POST && /sign) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34d78@7d12e459
 ((POST && /send/msg/all) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d34f90@1748e38e
 ((POST && /send/msg) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d351a8@43cd2f26
 ((POST && /send/msg/system) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d353c0@7d043960
 ((POST && /tencent/callback) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x000001da81d355d8@17de3ab5
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped (GET && /favicon.ico) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x000001da81d4c2b0@47daff53
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /server => {
 (GET && /check) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x000001da81d4c4c0@6d445c91
}
[main:1]2025-08-21 15:40:27.475 DEBUG [Mappings:] - Mapped /tag => {
 ((POST && /save) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000001da81d4c6d0@3157a760
 ((POST && /update) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000001da81d4c8e8@57ba286d
 ((POST && /remove) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000001da81d4cb00@470a65ae
}
[main:1]2025-08-21 15:40:27.499 DEBUG [Mappings:] - 'resourceHandlerMapping' {/webjars/**=ResourceWebHandler [classpath [META-INF/resources/webjars/]], /**=ResourceWebHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/]]}
[main:1]2025-08-21 15:40:27.918 INFO  [StdSchedulerFactory:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 15:40:27.930 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 15:40:27.930 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 15:40:27.930 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 15:40:27.931 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 15:40:27.931 INFO  [StdSchedulerFactory:] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[main:1]2025-08-21 15:40:27.931 INFO  [StdSchedulerFactory:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 15:40:27.931 INFO  [QuartzScheduler:] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6f451af5
[main:1]2025-08-21 15:40:28.263 DEBUG [InternalLoggerFactory:] - Using SLF4J as the default logging framework
[main:1]2025-08-21 15:40:28.523 INFO  [EndpointLinksResolver:60] - Exposing 19 endpoints beneath base path '/actuator'
[main:1]2025-08-21 15:40:28.544 DEBUG [WebFluxEndpointHandlerMapping:164] - 34 mappings in 'webEndpointReactiveHandlerMapping'
[main:1]2025-08-21 15:40:28.602 DEBUG [ControllerMethodResolver:289] - ControllerAdvice beans: none
[main:1]2025-08-21 15:40:28.723 DEBUG [HttpWebHandlerAdapter:267] - enableLoggingRequestDetails='false': form data and headers will be masked to prevent unsafe logging of potentially sensitive data
[main:1]2025-08-21 15:40:28.820 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:28.820 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:28.820 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 15:40:28.820 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:28.820 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:28.835 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.log.errLogDataSourceName' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:28.896 INFO  [JvmCacheConsumerFactoryBean:] - The JVM cache to start listening...
[main:1]2025-08-21 15:40:28.910 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.language.exception.baseNames' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 15:40:29.024 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 15:40:29.024 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: main
[main:1]2025-08-21 15:40:29.025 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 15:40:29.025 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 15:40:29.025 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 15:40:29.025 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 15:40:29.025 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[main:1]2025-08-21 15:40:29.025 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 15:40:29.067 INFO  [JobDetailBuilder:] - 任务[syncReconciledSchedule]已加入执行计划中..
[main:1]2025-08-21 15:40:29.067 INFO  [QuartzSchedulerManager:] - 通过 bean【jobDetailBuilder】共获取到【1】个需要处理的Jobs!
[main:1]2025-08-21 15:40:29.067 INFO  [QuartzSchedulerManager:] - 共获取到【1】个需要处理的Jobs!
[main:1]2025-08-21 15:40:29.497 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-21 15:40:29.802 DEBUG [SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin:131] - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
[main:1]2025-08-21 15:40:30.686 INFO  [NettyWebServer:126] - Netty started on port 11010 (http)
[main:1]2025-08-21 15:40:30.694 INFO  [naming:] - Nacos client key init properties: 
	serverAddr=*************:8848
	namespace=dev
	username=nacos
	password=EQ********3u

[main:1]2025-08-21 15:40:30.694 INFO  [naming:] - initializer namespace from ans.namespace attribute : null
[main:1]2025-08-21 15:40:30.694 INFO  [naming:] - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[main:1]2025-08-21 15:40:30.694 INFO  [naming:] - initializer namespace from namespace attribute :null
[main:1]2025-08-21 15:40:30.699 INFO  [naming:] - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[main:1]2025-08-21 15:40:30.702 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[main:1]2025-08-21 15:40:30.702 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[main:1]2025-08-21 15:40:30.994 INFO  [client:] - [RpcClientFactory] create a new rpc client of b1f3fb8a-58e2-4d50-8924-76ac5623d61d
[main:1]2025-08-21 15:40:30.995 INFO  [naming:] - Create naming rpc client for uuid->b1f3fb8a-58e2-4d50-8924-76ac5623d61d
[main:1]2025-08-21 15:40:30.995 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[main:1]2025-08-21 15:40:30.995 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[main:1]2025-08-21 15:40:30.996 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[main:1]2025-08-21 15:40:30.996 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
[main:1]2025-08-21 15:40:30.996 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main:1]2025-08-21 15:40:31.070 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Success to connect to server [*************:8848] on start up, connectionId = 1755762029825_221.12.20.178_37022
[main:1]2025-08-21 15:40:31.070 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[main:1]2025-08-21 15:40:31.070 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x000001da81618e70
[com.alibaba.nacos.client.remote.worker.0:276]2025-08-21 15:40:31.070 INFO  [client:] - [b1f3fb8a-58e2-4d50-8924-76ac5623d61d] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:276]2025-08-21 15:40:31.070 INFO  [naming:] - Grpc connection connect
[main:1]2025-08-21 15:40:31.071 INFO  [naming:] - [REGISTER-SERVICE] dev registering service xkThirdParty with instance Instance{instanceId='null', ip='*************', port=11010, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkThirdParty', serviceName='null', metadata={preserved.heart.beat.timeout=20000, preserved.ip.delete.timeout=60000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=10000}}
[main:1]2025-08-21 15:40:31.220 INFO  [NacosServiceRegistry:] - nacos registry, DEFAULT_GROUP xkThirdParty *************:11010 register finished
[main:1]2025-08-21 15:40:31.231 DEBUG [LoggerFactory:] - Using io.vertx.core.logging.SLF4JLogDelegateFactory
[main:1]2025-08-21 15:40:31.343 INFO  [VertxEventBusManager:] - Event bus 'MESSAGE' started.
[main:1]2025-08-21 15:40:31.350 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:40:31.350 INFO  [VertxEventBusManager:] - Event queue 'MESSAGE' started.
[main:1]2025-08-21 15:40:31.383 INFO  [VertxEventBusManager:] - Event bus 'LOG' started.
[main:1]2025-08-21 15:40:31.385 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:40:31.385 INFO  [VertxEventBusManager:] - Event queue 'LOG' started.
[main:1]2025-08-21 15:40:31.417 INFO  [VertxEventBusManager:] - Event bus 'AUTH' started.
[main:1]2025-08-21 15:40:31.419 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:40:31.419 INFO  [VertxEventBusManager:] - Event queue 'AUTH' started.
[main:1]2025-08-21 15:40:31.451 INFO  [VertxEventBusManager:] - Event bus 'GOODS' started.
[main:1]2025-08-21 15:40:31.453 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:40:31.453 INFO  [VertxEventBusManager:] - Event queue 'GOODS' started.
[main:1]2025-08-21 15:40:31.485 INFO  [VertxEventBusManager:] - Event bus 'THIRD_PARTY' started.
[main:1]2025-08-21 15:40:31.487 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 15:40:31.487 INFO  [VertxEventBusManager:] - Event queue 'THIRD_PARTY' started.
[vert.x-eventloop-thread-0:307]2025-08-21 15:40:31.499 INFO  [AbstractEventVerticle:] - Deploying 'ShorMessageCreateEventHandler-0'...
[main:1]2025-08-21 15:40:31.501 INFO  [VertxEventBusManager:] - register event bus:MESSAGE, handler:com.xk.tp.application.handler.event.message.ShorMessageCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:40:31.505 INFO  [VertxEventBusManager:] - register event bus:MESSAGE, handler:com.xk.tp.application.handler.event.push.MessageAppPushEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-1:308]2025-08-21 15:40:31.506 INFO  [AbstractEventVerticle:] - Deploying 'MessageAppPushEventHandler-1'...
[vert.x-eventloop-thread-0:307]2025-08-21 15:40:31.506 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ShortMessageCreateEvent[MESSAGE[YD_MESSAGE]]'
[main:1]2025-08-21 15:40:31.506 INFO  [VertxEventBusManager:] - register event bus:LOG, handler:com.xk.tp.application.handler.event.log.CreateUseLogEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-1:308]2025-08-21 15:40:31.506 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MessageAppPushEvent[MESSAGE[YD_MESSAGE]]'
[main:1]2025-08-21 15:40:31.506 INFO  [VertxEventBusManager:] - register event bus:AUTH, handler:com.xk.application.handler.event.log.LogSecureEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:309]2025-08-21 15:40:31.506 INFO  [AbstractEventVerticle:] - Deploying 'CreateUseLogEventHandler-2'...
[vert.x-eventloop-thread-0:309]2025-08-21 15:40:31.506 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateUseLogEvent[LOG[YD_LOG]]'
[main:1]2025-08-21 15:40:31.507 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.tp.application.handler.event.access.CreateAccessEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:310]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Deploying 'LogSecureEventHandler-3'...
[vert.x-eventloop-thread-0:310]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'LogSecureEvent[AUTH[YD_AUTH]]'
[main:1]2025-08-21 15:40:31.507 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.tp.application.handler.event.access.DeletedAccessEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:311]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Deploying 'CreateAccessEventHandler-4'...
[vert.x-eventloop-thread-0:311]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateAccessEvent[GOODS[YD_GOODS]]'
[main:1]2025-08-21 15:40:31.507 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.access.UpdateAccessAccountEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:40:31.507 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.auth.AuthFinishEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-1:312]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Deploying 'DeletedAccessEventHandler-5'...
[vert.x-eventloop-thread-1:312]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeletedAccessEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:313]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Deploying 'UpdateAccessAccountEventHandler-6'...
[vert.x-eventloop-thread-0:313]2025-08-21 15:40:31.507 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateAccessAccountEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[main:1]2025-08-21 15:40:31.508 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.auth.SyncFaceVerifyEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:40:31.508 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.pay.PayFinishEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:40:31.508 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.reconciled.HuiFuMerchantConfigJobEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-3:316]2025-08-21 15:40:31.508 INFO  [AbstractEventVerticle:] - Deploying 'PayFinishEventHandler-8'...
[vert.x-eventloop-thread-1:314]2025-08-21 15:40:31.508 INFO  [AbstractEventVerticle:] - Deploying 'AuthFinishEventHandler-7'...
[vert.x-eventloop-thread-2:315]2025-08-21 15:40:31.508 INFO  [AbstractEventVerticle:] - Deploying 'SyncFaceVerifyEventHandler-9'...
[vert.x-eventloop-thread-3:316]2025-08-21 15:40:31.508 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'PayFinishEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-1:314]2025-08-21 15:40:31.508 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'AuthFinishEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-2:315]2025-08-21 15:40:31.508 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'SyncFaceVerifyEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[main:1]2025-08-21 15:40:31.508 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.reconciled.SyncReconciledJobEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 15:40:31.509 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.sms.SendSmsEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-4:317]2025-08-21 15:40:31.509 INFO  [AbstractEventVerticle:] - Deploying 'HuiFuMerchantConfigJobEventHandler-10'...
[main:1]2025-08-21 15:40:31.509 INFO  [NacosDiscoveryHeartBeatPublisher:] - Start nacos heartBeat task scheduler.
[vert.x-eventloop-thread-4:317]2025-08-21 15:40:31.509 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'HuiFuMerchantConfigJobEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-6:319]2025-08-21 15:40:31.509 INFO  [AbstractEventVerticle:] - Deploying 'SendSmsEventHandler-11'...
[vert.x-eventloop-thread-6:319]2025-08-21 15:40:31.509 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'SendSmsEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-5:318]2025-08-21 15:40:31.509 INFO  [AbstractEventVerticle:] - Deploying 'SyncReconciledJobEventHandler-12'...
[vert.x-eventloop-thread-5:318]2025-08-21 15:40:31.509 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'SyncReconciledJobEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[main:1]2025-08-21 15:40:31.511 INFO  [SchedulerFactoryBean:] - Starting Quartz Scheduler now
[main:1]2025-08-21 15:40:31.511 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[quartzScheduler_QuartzSchedulerThread:172]2025-08-21 15:40:31.511 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[main:1]2025-08-21 15:40:31.513 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:31.514 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 15:40:31.514 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[vert.x-eventloop-thread-1:323]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.log.CreateUseLogEventHandler' with ID: 3
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.access.UpdateAccessAccountEventHandler' with ID: 7
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.pay.PayFinishEventHandler' with ID: 10
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.reconciled.SyncReconciledJobEventHandler' with ID: 12
[vert.x-eventloop-thread-2:321]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.push.MessageAppPushEventHandler' with ID: 2
[vert.x-eventloop-thread-2:321]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.message.ShorMessageCreateEventHandler' with ID: 1
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.auth.AuthFinishEventHandler' with ID: 8
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.auth.SyncFaceVerifyEventHandler' with ID: 9
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.reconciled.HuiFuMerchantConfigJobEventHandler' with ID: 11
[vert.x-eventloop-thread-7:322]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.sms.SendSmsEventHandler' with ID: 13
[vert.x-eventloop-thread-2:324]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.access.CreateAccessEventHandler' with ID: 5
[vert.x-eventloop-thread-2:324]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.access.DeletedAccessEventHandler' with ID: 6
[vert.x-eventloop-thread-1:325]2025-08-21 15:40:31.526 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.application.handler.event.log.LogSecureEventHandler' with ID: 4
[main:1]2025-08-21 15:40:31.530 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 15:40:31.530 INFO  [ServiceApplicationListener:] -  The xkThirdParty:dev has been started.
[main:1]2025-08-21 15:40:31.530 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 15:40:31.531 INFO  [XkThirdPartyServer:] - Started XkThirdPartyServer in 18.523 seconds (process running for 19.973)
[main:1]2025-08-21 15:40:31.532 DEBUG [ApplicationAvailabilityBean:77] - Application availability state LivenessState changed to CORRECT
[main:1]2025-08-21 15:40:31.544 INFO  [QuartzSchedulerManager:] - Will start Quartz Scheduler [DefaultQuartzScheduler] in 5 seconds
[main:1]2025-08-21 15:40:31.554 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkThirdParty-schedule.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 15:40:31.554 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 15:40:31.554 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 15:40:31.554 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP, cnt=2
[main:1]2025-08-21 15:40:31.554 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 15:40:31.555 DEBUG [ApplicationAvailabilityBean:77] - Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
[RMI TCP Connection(8)-*************:340]2025-08-21 15:40:31.952 INFO  [DruidDataSource:] - {dataSource-1} inited
[Thread-18:156]2025-08-21 15:40:32.426 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-PayFinishEvent' queue.
[Thread-17:155]2025-08-21 15:40:32.426 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_MESSAGE-MESSAGE-ShortMessageCreateEvent' queue.
[Thread-19:157]2025-08-21 15:40:32.426 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_MESSAGE-MESSAGE-MessageAppPushEvent' queue.
[Thread-15:153]2025-08-21 15:40:32.427 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-SyncFaceVerifyEvent' queue.
[Thread-20:158]2025-08-21 15:40:32.428 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-HuiFuMerchantConfigJobEvent' queue.
[Thread-14:151]2025-08-21 15:40:32.428 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-AuthFinishEvent' queue.
[Thread-16:154]2025-08-21 15:40:32.428 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_LOG-LOG-CreateUseLogEvent' queue.
[Thread-21:159]2025-08-21 15:40:32.444 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-SyncReconciledJobEvent' queue.
[RMI TCP Connection(8)-*************:340]2025-08-21 15:40:32.652 INFO  [DruidDataSource:] - {dataSource-2} inited
[RMI TCP Connection(8)-*************:340]2025-08-21 15:40:32.895 INFO  [DruidDataSource:] - {dataSource-3} inited
[DefaultQuartzScheduler:326]2025-08-21 15:40:36.545 INFO  [QuartzSchedulerManager:] - Starting Quartz Scheduler now
[DefaultQuartzScheduler:326]2025-08-21 15:40:36.545 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
[DefaultQuartzScheduler_QuartzSchedulerThread:193]2025-08-21 15:40:36.545 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:172]2025-08-21 15:40:58.121 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:193]2025-08-21 15:41:04.787 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[Thread-2:49]2025-08-21 15:41:11.975 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-2:49]2025-08-21 15:41:11.976 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-4:54]2025-08-21 15:41:11.976 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-6:61]2025-08-21 15:41:11.976 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[SpringApplicationShutdownHook:46]2025-08-21 15:41:11.976 DEBUG [ApplicationAvailabilityBean:77] - Application availability state ReadinessState changed from ACCEPTING_TRAFFIC to REFUSING_TRAFFIC
[Thread-6:61]2025-08-21 15:41:11.976 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
[SpringApplicationShutdownHook:46]2025-08-21 15:41:11.976 DEBUG [AnnotationConfigReactiveWebServerApplicationContext:1158] - Closing org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@312f3050, started on Thu Aug 21 15:40:15 CST 2025, parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@2f98635e
[Thread-4:54]2025-08-21 15:41:11.977 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end
[vert.x-eventloop-thread-1:308]2025-08-21 15:41:11.984 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:307]2025-08-21 15:41:11.984 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:312]2025-08-21 15:41:11.984 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:311]2025-08-21 15:41:11.984 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:309]2025-08-21 15:41:11.984 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:310]2025-08-21 15:41:11.984 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-4:317]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-5:318]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-6:319]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:313]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:314]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-3:316]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-2:315]2025-08-21 15:41:11.985 INFO  [AbstractEventVerticle:] - Resources closed successfully
[SpringApplicationShutdownHook:46]2025-08-21 15:41:11.985 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:46]2025-08-21 15:41:11.985 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.

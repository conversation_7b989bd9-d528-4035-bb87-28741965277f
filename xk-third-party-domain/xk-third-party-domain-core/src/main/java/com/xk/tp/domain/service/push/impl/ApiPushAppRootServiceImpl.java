package com.xk.tp.domain.service.push.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.xk.tp.domain.model.push.PushMessageRoot;
import com.xk.tp.domain.model.push.entity.PushMessageEntity;
import com.xk.tp.domain.model.push.obj.ApiPushObj;
import com.xk.tp.domain.service.push.ApiPushAdapterService;
import com.xk.tp.domain.service.push.ApiPushAppRootService;
import com.xk.tp.enums.access.AccessChannelTypeEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;


@Service
@RequiredArgsConstructor
public class ApiPushAppRootServiceImpl implements ApiPushAppRootService {

    @BeansOfTypeToMap(value = ApiPushAdapterService.class, methodName = "getPushChannelType")
    private Map<AccessChannelTypeEnum , ApiPushAdapterService> apiPushAdapterServiceMap;

    @Override
    public Mono<ApiPushObj> pushAppMessage(PushMessageRoot root) {
        PushMessageEntity pushMessageEntity = root.getPushMessageEntity();
        //使用策略模式获取渠道处理接口
        ApiPushAdapterService apiPushAdapterService = apiPushAdapterServiceMap.get(pushMessageEntity.getPushChannelExtObj().getPushChannelType());
        return apiPushAdapterService.pushAppMessage(root);
    }
}

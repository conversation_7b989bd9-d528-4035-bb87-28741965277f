package com.xk.tp.domain.service.live.impl;

import org.springframework.stereotype.Service;

import com.xk.tp.domain.model.live.LiveRoot;
import com.xk.tp.domain.repository.live.LiveRootRepository;
import com.xk.tp.domain.service.live.LiveRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LiveRootServiceImpl implements LiveRootService {

    private final LiveRootRepository liveRootRepository;

    @Override
    public Mono<Void> removeUser(LiveRoot liveRoot) {
        return liveRootRepository.removeUser(liveRoot);
    }

    @Override
    public Mono<Void> removeUserStr(LiveRoot liveRoot) {
        return liveRootRepository.removeUserStr(liveRoot);
    }

    @Override
    public Mono<Void> dismissRoom(LiveRoot liveRoot) {
        return liveRootRepository.dismissRoom(liveRoot);
    }

    @Override
    public Mono<Void> dismissRoomStr(LiveRoot liveRoot) {
        return liveRootRepository.dismissRoomStr(liveRoot);
    }
}

package com.xk.tp.domain.repository.recordingTask;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.SemResultData;
import com.xk.tp.domain.model.recordingTask.RecordingTaskRoot;
import com.xk.tp.domain.model.sem.SemRoot;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
public interface RecordingTaskRootRepository extends IRepository<RecordingTaskRoot> {

}


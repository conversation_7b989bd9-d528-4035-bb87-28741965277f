package com.xk.tp.domain.repository.pay;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.commons.response.ApiPayResultData;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.CallBackResultData;
import com.xk.tp.domain.commons.response.RefundAmountData;
import com.xk.tp.domain.model.pay.PayRoot;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/15
 */
public interface PayRootRepository extends IRepository<PayRoot> {

    Mono<Void> savePayRecord(PayRoot root);

    Mono<Void> savePayNotify(PayRoot root);

    Mono<Void> createPayStringCache(PayRoot root);

    Mono<Void> removePayStringCache(PayRoot root);


    Mono<ApiResult<ApiPayResultData>> preCreate(PayRoot apiPayRoot);

    Mono<ApiResult<RefundAmountData>> refundOrder(PayRoot apiPayRoot);

    Mono<ApiResult<Void>> closeOrder(PayRoot apiPayRoot);

    Mono<ApiResult<CallBackResultData>> callBack(PayRoot apiPayRoot);

    Mono<ApiResult<RefundAmountData>> refundCallBack(PayRoot apiPayRoot);

    Mono<ApiResult<Boolean>> findPayResult(PayRoot apiPayRoot);
}


package com.xk.tp.domain.service.recordingTask.impl;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.tp.domain.model.recording.RecordingRoot;
import com.xk.tp.domain.repository.recording.RecordingRootRepository;
import com.xk.tp.domain.repository.recordingTask.RecordingTaskRootRepository;
import com.xk.tp.domain.service.recording.RecordingRootService;
import com.xk.tp.domain.service.recordingTask.RecordingTaskRootService;
import com.xk.tp.domain.support.TpSequenceEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecordingTaskRootServiceImpl implements RecordingTaskRootService {

    private  final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Long> generateId() {
        return Mono.just((Long) identifierGenerateService
                .generateIdentifier(IdentifierRoot.builder().identifier(TpSequenceEnum.TP_RECORDING_TASK)
                        .type(IdentifierGenerateEnum.CACHE).build()));
    }
}

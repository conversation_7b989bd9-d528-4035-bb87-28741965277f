package com.xk.tp.domain.service.auth;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.commons.response.BaseAuthResultData;
import com.xk.tp.domain.model.access.AccessRoot;
import com.xk.tp.domain.model.auth.AuthNotifyEntity;
import com.xk.tp.domain.model.auth.AuthRoot;
import com.xk.tp.domain.model.auth.UserEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface ApiAuthRootService extends IDomainService<AuthRoot> {
    Mono<Void> syncNotifyThirdPlatform(AuthRoot authRoot);

    Mono<AuthNotifyEntity> queryNotifyByUserId(Mono<Long> userId);

    Flux<AccessRoot> queryAuthAccessAccount(Mono<BusinessTypeEnum> businessType);

    Mono<AuthNotifyEntity> createFaceVerify(Mono<AuthRoot> accessRootList);

    Mono<BaseAuthResultData> createBaseAuth(Mono<AuthRoot> authRootMono);

    Mono<Void> clearFaceVerify(Mono<UserEntity> userEntity);
}

package com.xk.tp.domain.repository.log;


import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.tp.domain.model.log.UseLogEntity;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
public interface UseLogRootQueryRepository extends IQueryRepository {

    Flux<UseLogEntity> searUseLog(Pagination pagination);

}

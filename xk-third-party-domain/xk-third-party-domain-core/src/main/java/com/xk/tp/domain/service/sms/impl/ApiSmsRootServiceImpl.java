package com.xk.tp.domain.service.sms.impl;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.tp.domain.commons.XkThirdPartyDomainErrorEnum;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.event.sms.SendSmsResultEvent;
import com.xk.tp.domain.model.access.AccessAccountEntity;
import com.xk.tp.domain.model.access.AccessEntity;
import com.xk.tp.domain.model.access.AccessIdentifier;
import com.xk.tp.domain.model.sms.SendSmsEntity;
import com.xk.tp.domain.model.sms.SmsEntity;
import com.xk.tp.domain.model.sms.SmsIdentifier;
import com.xk.tp.domain.model.sms.SmsRoot;
import com.xk.tp.domain.repository.sms.SmsRootRepository;
import com.xk.tp.domain.service.access.AccessRootService;
import com.xk.tp.domain.service.sms.ApiSmsRootService;
import com.xk.tp.domain.support.XkThirdPartyDomainException;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.sms.SmsChannelTypeEnum;
import com.xk.tp.enums.sms.SmsPlatformTypeEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:22
 */
@Service
@RequiredArgsConstructor
public class ApiSmsRootServiceImpl implements ApiSmsRootService {

    private final SmsRootRepository smsRootRepository;

    private final AccessRootService accessRootService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<ApiResult<?>> sendSms(SmsRoot root) {
        return smsRootRepository.sendSms(root);
    }

    @Override
    public Mono<Boolean> sendSmsByAccess(SendSmsEntity entity) {

        Supplier<Mono<SmsRoot>> getSmsRoot = () -> {
            return accessRootService
                    .getRoot(AccessIdentifier.builder().accessId(entity.getAccessId()).build())
                    .flatMap(root -> {
                        AccessAccountEntity accessAccount = root.getAccessAccountEntities().stream()
                                .filter(v -> AccessDomainStatusEnum.ENABLED.equals(v.getStatus()))
                                .findFirst().orElse(null);
                        if (accessAccount == null) {
                            return Mono.error(new XkThirdPartyDomainException(
                                    XkThirdPartyDomainErrorEnum.ACCESS_ACCOUNT_NOT_EXIST));
                        }
                        AccessEntity access = root.getAccessEntity();
                        SmsRoot smsRoot = SmsRoot.builder().identifier(SmsIdentifier.builder()
                                .accessAccountId(accessAccount.getAccessAccountId()).build())
                                .smsEntity(SmsEntity.builder()
                                        .platformType(SmsPlatformTypeEnum
                                                .getEnumByValue(access.getPlatformType()))
                                        .channelType(SmsChannelTypeEnum
                                                .getEnumByValue(access.getChannelType().getValue()))
                                        .config(accessAccount.getConfig()).build())
                                .sendSmsEntity(entity).build();
                        return Mono.just(smsRoot);
                    });
        };

        Function<SmsRoot, Mono<ApiResult<?>>> doSend = smsRootRepository::sendSms;

        Function<ApiResult<?>, Mono<EventRoot>> createEventRoot = (result) -> {
            SendSmsResultEvent.SendSmsResultEventBuilder builder = SendSmsResultEvent.builder();
            builder.identifier(EventRoot.getCommonsDomainEventIdentifier(SendSmsResultEvent.class))
                    .isSuccess(result.isSuccess()).message(result.getMessage()).sendPhone(entity.getPhoneNumber())
                    .templateId(Long.valueOf(entity.getTemplateId())).sendParams(entity.paramsToMap()).ext(entity.getExt());
            return Mono.just(EventRoot.builder().domainEvent(builder.build()).build());
        };

        Function<EventRoot, Mono<Boolean>> publishEvent = (msgEvent) -> Mono.fromCallable(() -> {
            try {
                return eventRootService.publish(msgEvent);
            } catch (ExceptionWrapperThrowable e) {
                throw new RuntimeException(e);
            }
        }).thenReturn(true).onErrorReturn(false);

        // 通过通道id查询通道
        return getSmsRoot.get().flatMap(doSend).flatMap(createEventRoot).flatMap(publishEvent);
    }
}

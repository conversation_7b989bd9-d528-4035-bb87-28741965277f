package com.xk.tp.domain.repository.auth;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.NotifyThirdPlatformResultData;
import com.xk.tp.domain.model.auth.AuthNotifyEntity;
import com.xk.tp.domain.model.auth.AuthRecordEntity;
import com.xk.tp.domain.model.auth.AuthRoot;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/15
 */
public interface AuthRootQueryRepository extends IQueryRepository {

    Mono<AuthNotifyEntity> findAuthNotifyByParams(AuthRoot authRoot);

    /**
     * 根据流程id获取认证记录
     *
     * @param entity
     * @return
     */
    Mono<AuthRecordEntity> findAuthRecordByAuthFlowId(AuthRecordEntity entity);

    Mono<ApiResult<NotifyThirdPlatformResultData>> findNotifyThirdPlatform(AuthRoot authRoot);
}

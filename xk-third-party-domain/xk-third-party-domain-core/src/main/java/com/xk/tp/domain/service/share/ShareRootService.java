package com.xk.tp.domain.service.share;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.obj.ShareShowValObj;
import reactor.core.publisher.Mono;

/**
 * 分享聚合根相关操作
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:32
 */
public interface ShareRootService extends IDomainService<ShareRoot> {

    /**
     * 生成分享数据
     *
     * @param shareRoot shareRoot
     * @return reactor.core.publisher.Mono<com.xk.tp.domain.model.share.obj.ShareShowValObj>
     * <AUTHOR>
     * @date: 2025/8/8 16:15
     */
    Mono<ShareShowValObj> createShareView(ShareRoot shareRoot);
}

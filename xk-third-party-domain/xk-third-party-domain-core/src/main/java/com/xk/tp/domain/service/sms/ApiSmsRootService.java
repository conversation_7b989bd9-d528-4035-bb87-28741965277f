package com.xk.tp.domain.service.sms;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.model.sms.SendSmsEntity;
import com.xk.tp.domain.model.sms.SmsRoot;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:20
 */
public interface ApiSmsRootService extends IDomainService<SmsRoot> {

    Mono<ApiResult<?>> sendSms(SmsRoot root);

    Mono<Boolean> sendSmsByAccess(SendSmsEntity entity);
}

package com.xk.tp.domain.support;


import com.myco.mydata.commons.support.ApplicationContextHolder;
import io.github.linpeilie.Converter;

/**
 * 静态方法从Spring上下文中获取一些Bean
 *
 * <AUTHOR>
 */
public class XkThirdPartyDomainStaticBeanFactory {

	private XkThirdPartyDomainStaticBeanFactory() {
	}


	public static Converter getConverter(){
		return ApplicationContextHolder.getBean(Converter.class);
	}


}

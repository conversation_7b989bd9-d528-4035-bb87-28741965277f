package com.xk.tp.domain.model.push.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageContextEntity {


    /**
     * 消息id（随机生成 要求唯一）
     */
    private String messageId;

    /**
     * 消息模板id
     */
    private Long messageTemplateId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息参数
     */
    private String messageParams;

    /**
     * 点击通知后续动作
     * intent：打开应用内特定页面（intent和want字段必须填写一个）
     * url：打开网页地址
     * payload：自定义消息内容启动应用
     * payload_custom：自定义消息内容不启动应用
     * startapp：打开应用首页
     * none：纯通知，无后续动作
     */
    private String clickType;

    /**
     * 点击通知栏消息时，唤起系统默认浏览器打开此链接
     */
    private String url;
}
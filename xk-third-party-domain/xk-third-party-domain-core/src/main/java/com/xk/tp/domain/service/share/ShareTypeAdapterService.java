package com.xk.tp.domain.service.share;

import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.obj.ShareValObj;
import com.xk.tp.enums.share.ShareTypeEnum;
import reactor.core.publisher.Mono;

/**
 * 分享类型适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:42
 */
public interface ShareTypeAdapterService<T extends ShareValObj> {

    /**
     * 获取分享类型
     *
     * @return com.xk.tp.enums.share.ShareTypeEnum
     * <AUTHOR>
     * @date: 2025/8/8 14:44
     */
    ShareTypeEnum getShareType();

    /**
     * 获取分享参数
     *
     * @param root root
     * @return reactor.core.publisher.Mono<T>
     * <AUTHOR>
     * @date: 2025/8/8 14:47
     */
    Mono<T> getShareVal(ShareRoot root);
}

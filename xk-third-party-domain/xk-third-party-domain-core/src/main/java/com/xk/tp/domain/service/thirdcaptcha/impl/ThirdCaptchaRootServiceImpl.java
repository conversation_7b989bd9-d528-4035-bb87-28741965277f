package com.xk.tp.domain.service.thirdcaptcha.impl;

import com.xk.tp.domain.model.thirdcaptcha.ThirdCaptchaRoot;
import com.xk.tp.domain.repository.thirdcaptcha.ThirdCaptchaRootRepository;
import com.xk.tp.domain.service.thirdcaptcha.ThirdCaptchaRootService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:22
 */
@Service
@RequiredArgsConstructor
public class ThirdCaptchaRootServiceImpl implements ThirdCaptchaRootService {

    private final ThirdCaptchaRootRepository thirdCaptchaRootRepository;

    @Override
    public Mono<Boolean> checkCaptcha(ThirdCaptchaRoot root) {
        return thirdCaptchaRootRepository.checkCaptcha(root)
                .flatMap(booleanApiResult -> {
                    if (!booleanApiResult.isSuccess()) {
                        return Mono.just(Boolean.FALSE);
                    }
                    return Mono.just(booleanApiResult.getData());
                });
    }
}

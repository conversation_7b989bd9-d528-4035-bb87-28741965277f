package com.xk.tp.domain.service.auth;

import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.BaseAuthResultData;
import com.xk.tp.domain.commons.response.FaceVerifyResultData;
import com.xk.tp.domain.commons.response.NotifyThirdPlatformResultData;
import com.xk.tp.domain.model.auth.AuthRoot;

/**
 * <AUTHOR> date 2024/07/20
 */
public interface ApiAuthAdapterService {


    Integer getPlatformType();

    /**
     * 二要素验证
     *
     * @param authRoot root
     * @return 结果
     */
    ApiResult<BaseAuthResultData> baseAuth(AuthRoot authRoot);

    /**
     * 活体验证
     *
     * @param authRoot root
     * @return 结果
     */
    ApiResult<FaceVerifyResultData> createFaceVerify(AuthRoot authRoot);

    ApiResult<NotifyThirdPlatformResultData> findNotifyThirdPlatform(AuthRoot authRoot);
}

package com.xk.tp.domain.service.reconciled;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.commons.response.reconclied.ReconciledData;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;
import com.xk.tp.domain.model.recording.RecordingRoot;

import reactor.core.publisher.Mono;

public interface ReconciledService extends IDomainService<RecordingRoot> {

    /**
     * 获取直播平台类型
     */
    Integer getPayPlatformType();


    Mono<ReconciledData> selectRecording(ReconciledRoot root);
}

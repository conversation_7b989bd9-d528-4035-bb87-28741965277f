package com.xk.tp.domain.service.userauth.impl;

import com.xk.tp.domain.model.userauth.UserAuthRoot;
import com.xk.tp.domain.repository.userauth.UserAuthRootRepository;
import com.xk.tp.domain.service.userauth.UserAuthRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthRootServiceImpl implements UserAuthRootService {

    private final UserAuthRootRepository  userAuthRootRepository;

    @Override
    public Mono<UserAuthRoot> getUserInfo(UserAuthRoot userAuthRoot) {
        return userAuthRootRepository.getUserInfo(userAuthRoot);
    }
}

package com.xk.tp.domain.repository.pay;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.tp.domain.model.pay.PayNotifyEntity;
import com.xk.tp.domain.model.pay.PayRecordEntity;
import com.xk.tp.domain.model.pay.PayRoot;
import com.xk.tp.domain.model.pay.PreCreateEntity;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/15
 */
public interface PayRootQueryRepository extends IQueryRepository {


    Mono<PayNotifyEntity> findOneByParams(PayNotifyEntity entity);


    Mono<PreCreateEntity> findPayOrderCacheByParams(PayRoot root);

    Flux<PayRecordEntity> findRecordListByParams(PayRecordEntity payRecordEntity);

    Mono<PayRecordEntity> findRecordOneByParams(PayRecordEntity payRecordEntity);
}

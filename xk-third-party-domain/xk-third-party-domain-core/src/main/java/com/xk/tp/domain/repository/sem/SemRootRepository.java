package com.xk.tp.domain.repository.sem;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.SemResultData;
import com.xk.tp.domain.model.sem.SemRoot;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
public interface SemRootRepository extends IRepository<SemRoot> {

    Mono<Void> saveBaiduSemRecord(SemRoot root);

    Mono<ApiResult<SemResultData>> uploadConvertData(SemRoot root);
}


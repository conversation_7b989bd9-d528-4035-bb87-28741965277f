package com.xk.tp.domain.repository.thirdcaptcha;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.model.thirdcaptcha.ThirdCaptchaRoot;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/15
 */
public interface ThirdCaptchaRootRepository extends IRepository<ThirdCaptchaRoot> {
    /**
     * 校验验证码
     *
     * @param root
     * @return
     */
    Mono<ApiResult<Boolean>> checkCaptcha(ThirdCaptchaRoot root);
}


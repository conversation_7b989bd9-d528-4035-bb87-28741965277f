package com.xk.tp.domain.service.share.impl;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.entity.ShareBusinessEntity;
import com.xk.tp.domain.model.share.entity.ShareEntity;
import com.xk.tp.domain.model.share.obj.*;
import com.xk.tp.domain.service.share.ShareBusinessAdapterService;
import com.xk.tp.domain.service.share.ShareRootService;
import com.xk.tp.domain.service.share.ShareTypeAdapterService;
import com.xk.tp.enums.share.ShareBusinessTypeEnum;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 分享聚合根相关操作
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShareRootServiceImpl implements ShareRootService {

    @Override
    public Mono<ShareShowValObj> createShareView(ShareRoot shareRoot) {
        ShareBusinessEntity shareBusinessEntity = shareRoot.getShareBusinessEntity();
        ShareEntity shareEntity = shareRoot.getShareEntity();
        ShareShowValObj shareShowValObj = ShareShowValObj.builder().shareBusinessId(shareBusinessEntity.getShareBusinessId())
                .shareBusinessType(shareBusinessEntity.getShareBusinessType())
                .businessExtField(shareBusinessEntity.getBusinessExtField()).shareType(shareEntity.getShareType()).build();
        //获取业务数据适配器
        ShareBusinessTypeEnum shareBusinessType = shareBusinessEntity.getShareBusinessType();
        ShareBusinessAdapterService shareBusinessAdapterService = shareBusinessAdapterServiceMap.get(shareBusinessType);
        if (shareBusinessAdapterService == null) {
            return Mono.empty();
        }
        //获取分享数据适配器
        ShareTypeEnum shareTypeEnum = shareRoot.getIdentifier().shareTypeEnum();
        ShareTypeAdapterService<ShareValObj> shareTypeAdapterService = shareTypeAdapterServiceMap.get(shareTypeEnum);

        return shareBusinessAdapterService.getShareBusinessVal(shareRoot)
                .switchIfEmpty(Mono.empty())
                .flatMap(shareBusinessValObj -> {
                    //数据填充
                    shareBusinessEntity.setShareBusinessValObj(shareBusinessValObj);
                    ShareViewObj viewObj = ShareViewObj.builder().appId(shareEntity.getShareViewObj().getAppId())
                            .title(shareBusinessValObj.getTitle()).description(shareBusinessValObj.getDescription())
                            .thumbUrl(shareBusinessValObj.getThumbUrl()).build();
                    shareEntity.setShareViewObj(viewObj);
                    shareShowValObj.setShareViewObj(viewObj);
                    if (shareTypeAdapterService == null) {
                        return Mono.empty();
                    }
                    //获取分享数据，分享数据填充
                    return shareTypeAdapterService.getShareVal(shareRoot)
                            .switchIfEmpty(Mono.empty())
                            .flatMap(shareValObj -> {
                                switch (shareTypeEnum) {
                                    case ShareTypeEnum.TEST:
                                        TextValObj textValObj = (TextValObj) shareValObj;
                                        shareEntity.setTextValObj(textValObj);
                                        shareShowValObj.setTextValObj(textValObj);
                                        break;
                                    case ShareTypeEnum.IMAGE:
                                        ImageValObj imageValObj = (ImageValObj) shareValObj;
                                        shareEntity.setImageValObj(imageValObj);
                                        shareShowValObj.setImageValObj(imageValObj);
                                        break;
                                    case ShareTypeEnum.VIDEO:
                                        VideoValObj videoValObj = (VideoValObj) shareValObj;
                                        shareEntity.setVideoValObj(videoValObj);
                                        shareShowValObj.setVideoValObj(videoValObj);
                                        break;
                                    case ShareTypeEnum.WEBPAGE:
                                        WebpageValObj webpageValObj = (WebpageValObj) shareValObj;
                                        shareEntity.setWebpageValObj(webpageValObj);
                                        shareShowValObj.setWebpageValObj(webpageValObj);
                                        break;
                                    case ShareTypeEnum.MINI_PROGRAM:
                                        MiniProgramValObj miniProgramValObj = (MiniProgramValObj) shareValObj;
                                        shareEntity.setMiniProgramValObj(miniProgramValObj);
                                        shareShowValObj.setMiniProgramValObj(miniProgramValObj);
                                        break;
                                    case ShareTypeEnum.MUSIC_VIDEO:
                                        MusicVideoValObj musicVideoValObj = (MusicVideoValObj) shareValObj;
                                        shareEntity.setMusicVideoValObj(musicVideoValObj);
                                        shareShowValObj.setMusicVideoValObj(musicVideoValObj);
                                        break;
                                    default:
                                        break;
                                }
                                return Mono.justOrEmpty(shareShowValObj);
                    });
                });
    }

    @BeansOfTypeToMap(value = ShareBusinessAdapterService.class, methodName = "getShareBusinessType")
    private Map<ShareBusinessTypeEnum, ShareBusinessAdapterService> shareBusinessAdapterServiceMap;

    @BeansOfTypeToMap(value = ShareTypeAdapterService.class, methodName = "getShareType")
    private Map<ShareTypeEnum, ShareTypeAdapterService<ShareValObj>> shareTypeAdapterServiceMap;
}

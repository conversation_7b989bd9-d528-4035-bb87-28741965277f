package com.xk.tp.domain.repository.im;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.model.im.ImRoot;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/15
 */
public interface ImRootRepository extends IRepository<ImRoot> {

    Mono<Void> createGroup(ImRoot imRoot);

    Mono<Void> deleteGroup(ImRoot imRoot);

    Mono<Long> selectOnlineNumber(ImRoot imRoot);

    Mono<Long> sendGroupMsgAll(ImRoot imRoot);

    Mono<Long> sendGroupMsg(ImRoot imRoot);

    Mono<Void> sendGroupSystemMsg(ImRoot imRoot);

    Mono<String> sign(ImRoot imRoot);
}


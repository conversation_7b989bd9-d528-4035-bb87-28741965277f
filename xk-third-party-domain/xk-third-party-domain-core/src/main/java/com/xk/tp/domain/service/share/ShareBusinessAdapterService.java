package com.xk.tp.domain.service.share;

import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.obj.ShareBusinessValObj;
import com.xk.tp.enums.share.ShareBusinessTypeEnum;
import reactor.core.publisher.Mono;

/**
 * 业务分享适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 13:38
 */
public interface ShareBusinessAdapterService {

    /**
     * 获取分享业务类型
     *
     * @return com.xk.tp.enums.share.ShareBusinessTypeEnum
     * <AUTHOR>
     * @date: 2025/8/8 13:42
     */
    ShareBusinessTypeEnum getShareBusinessType();

    /**
     * 获取业务数据
     *
     * @param root root
     * @return reactor.core.publisher.Mono<com.xk.tp.domain.model.share.obj.ShareBusinessValObj>
     * <AUTHOR>
     * @date: 2025/8/8 13:42
     */
    Mono<ShareBusinessValObj> getShareBusinessVal(ShareRoot root);
}

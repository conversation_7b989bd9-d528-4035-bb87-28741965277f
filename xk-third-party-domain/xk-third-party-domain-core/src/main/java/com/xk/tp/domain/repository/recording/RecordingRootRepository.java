package com.xk.tp.domain.repository.recording;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.model.recording.RecordingRoot;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/15
 */
public interface RecordingRootRepository extends IRepository<RecordingRoot> {

    Mono<Void> createCloudRecording(RecordingRoot liveRoot);

    Mono<Void> deleteCloudRecording(RecordingRoot liveRoot);

    Mono<Void> describeCloudRecording(RecordingRoot liveRoot);

    Mono<Void> modifyCloudRecording(RecordingRoot liveRoot);

}


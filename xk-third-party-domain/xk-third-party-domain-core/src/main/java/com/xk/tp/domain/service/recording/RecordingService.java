package com.xk.tp.domain.service.recording;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.recording.RecordingRoot;

import reactor.core.publisher.Mono;

public interface RecordingService extends IDomainService<RecordingRoot> {

    /**
     * 获取直播平台类型
     */
    Integer getLivePlatformType();

    Mono<Void> createCloudRecording(RecordingRoot liveRoot);

    Mono<Void> deleteCloudRecording(RecordingRoot liveRoot);

    Mono<Void> describeCloudRecording(RecordingRoot liveRoot);

    Mono<Void> modifyCloudRecording(RecordingRoot liveRoot);

}

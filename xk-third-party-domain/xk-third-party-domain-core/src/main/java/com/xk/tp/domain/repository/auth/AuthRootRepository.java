package com.xk.tp.domain.repository.auth;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.BaseAuthResultData;
import com.xk.tp.domain.commons.response.FaceVerifyResultData;
import com.xk.tp.domain.model.auth.AuthNotifyEntity;
import com.xk.tp.domain.model.auth.AuthRecordEntity;
import com.xk.tp.domain.model.auth.AuthRoot;
import com.xk.tp.domain.model.auth.UserEntity;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/15
 */
public interface AuthRootRepository extends IRepository<AuthRoot> {

    Mono<Void> saveAuthRecord(AuthRecordEntity recordEntity);

    Mono<Void> saveAuthNotify(AuthNotifyEntity notifyEntity);

    Mono<Void> updateAuthNotify(AuthRoot root);

    Mono<ApiResult<FaceVerifyResultData>> createFaceVerify(AuthRoot apiAuthRoot);

    Mono<ApiResult<BaseAuthResultData>> baseAuth(AuthRoot apiAuthRoot);

    Mono<Void> createAuthNotifyStringCache(AuthRoot root);

    Mono<Void> updateAuthNotifyStatusStringCache(AuthRoot root);

    Mono<Void> removeAuthNotifyStringCache(UserEntity entity);
}

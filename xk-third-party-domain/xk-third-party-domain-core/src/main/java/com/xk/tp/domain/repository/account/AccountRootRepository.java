package com.xk.tp.domain.repository.account;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.model.account.AccountRoot;
import reactor.core.publisher.Mono;

public interface AccountRootRepository extends IRepository<AccountRoot> {

    /**
     * 账号筛选
     * @param accountRoot accountRoot
     * @return Mono<AccountRoot>
     */
    Mono<AccountRoot> routeAccount(AccountRoot accountRoot);
}

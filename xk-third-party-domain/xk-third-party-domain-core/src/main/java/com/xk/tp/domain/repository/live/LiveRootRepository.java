package com.xk.tp.domain.repository.live;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.model.live.LiveRoot;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/15
 */
public interface LiveRootRepository extends IRepository<LiveRoot> {

    Mono<Void> removeUser(LiveRoot root);

    Mono<Void> removeUserStr(LiveRoot root);

    Mono<Void> dismissRoom(LiveRoot root);

    Mono<Void> dismissRoomStr(LiveRoot root);

}


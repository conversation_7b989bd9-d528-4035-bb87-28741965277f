package com.xk.tp.domain.service.reconciled.impl;

import org.springframework.stereotype.Service;

import com.xk.tp.domain.commons.response.reconclied.ReconciledData;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;
import com.xk.tp.domain.repository.reconciled.ReconciledRootRepository;
import com.xk.tp.domain.service.reconciled.ReconciledRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReconciledRootServiceImpl implements ReconciledRootService {

    private final ReconciledRootRepository reconciledRootRepository;


    @Override
    public Mono<ReconciledData> selectRecording(ReconciledRoot root) {
        return reconciledRootRepository.selectRecording(root);
    }
}

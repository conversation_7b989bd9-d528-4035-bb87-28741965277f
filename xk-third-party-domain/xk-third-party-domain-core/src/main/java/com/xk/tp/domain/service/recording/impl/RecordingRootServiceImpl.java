package com.xk.tp.domain.service.recording.impl;

import org.springframework.stereotype.Service;

import com.xk.tp.domain.model.recording.RecordingRoot;
import com.xk.tp.domain.repository.recording.RecordingRootRepository;
import com.xk.tp.domain.service.recording.RecordingRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecordingRootServiceImpl implements RecordingRootService {

    private final RecordingRootRepository recordingRootRepository;

    @Override
    public Mono<Void> createCloudRecording(RecordingRoot liveRoot) {
        return recordingRootRepository.createCloudRecording(liveRoot);
    }

    @Override
    public Mono<Void> deleteCloudRecording(RecordingRoot liveRoot) {
        return recordingRootRepository.deleteCloudRecording(liveRoot);
    }

    @Override
    public Mono<Void> describeCloudRecording(RecordingRoot liveRoot) {
        return recordingRootRepository.describeCloudRecording(liveRoot);
    }

    @Override
    public Mono<Void> modifyCloudRecording(RecordingRoot liveRoot) {
        return recordingRootRepository.modifyCloudRecording(liveRoot);
    }
}

package com.xk.tp.domain.service.access;

import java.util.Map;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.access.*;
import com.xk.tp.domain.model.access.ids.AccessAccountIdentifier;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:20
 */
public interface AccessRootService extends IDomainService<AccessRoot> {

    Mono<AccessEntity> findById(AccessIdentifier identifier);

    Mono<AccessRoot> getRoot(AccessIdentifier identifier);

    Flux<AccessAccountEntity> findAccessAccountList(AccessAccountEntity accessAccountEntity);

    Mono<Map<String, String>> findAccessAccountExtByAccessAccountExt(AccessAccountExtValObj accessAccountExtValObj);

    Flux<AccessEntity> findAccessByGroupList(AccessEntity accessEntity);

    Flux<AccessAccountEntity> findAccessAccountByGroup(AccessEntity accessEntity);

    Mono<AccessAccountEntity> findAccessAccountByAccountId(AccessAccountIdentifier identifier);

    /**
     * 校验通道是否有开启的账号
     *
     * @param accessIdentifier accessIdentifier
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkAccessHasEnabledAccount(AccessIdentifier accessIdentifier);

    /**
     * 根据InterfaceKey校验接口权限
     *
     * @param accessInterfaceAuthInfoObject accessInterfaceAuthInfoObject
     * @return Mono<AccessInterfaceAuthInfoEntity>
     */
    Mono<AccessInterfaceAuthInfoEntity> checkAuthByInterfaceKey(
            AccessInterfaceAuthInfoObject accessInterfaceAuthInfoObject);
}

package com.xk.tp.domain.service.live;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.live.LiveRoot;

import reactor.core.publisher.Mono;

public interface LiveRootService extends IDomainService<LiveRoot> {


    Mono<Void> removeUser(LiveRoot liveRoot);

    Mono<Void> removeUserStr(LiveRoot liveRoot);

    Mono<Void> dismissRoom(LiveRoot liveRoot);

    Mono<Void> dismissRoomStr(LiveRoot liveRoot);

}

package com.xk.tp.domain.service.recording;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.recording.RecordingRoot;

import reactor.core.publisher.Mono;

public interface RecordingRootService extends IDomainService<RecordingRoot> {


    Mono<Void> createCloudRecording(RecordingRoot liveRoot);

    Mono<Void> deleteCloudRecording(RecordingRoot liveRoot);

    Mono<Void> describeCloudRecording(RecordingRoot liveRoot);

    Mono<Void> modifyCloudRecording(RecordingRoot liveRoot);

}

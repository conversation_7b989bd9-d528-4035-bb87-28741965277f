package com.xk.tp.domain.model.reconciled.ids;

import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;

import lombok.*;

/**
 * @<PERSON> liucaihong
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReconciledIdentifier implements Identifier<ReconciledIdentifier> {

    /**
     * 账号id
     */
    private Long accessAccountId;

    @NonNull
    @Override
    public ReconciledIdentifier getIdentifier() {
        return ReconciledIdentifier.builder().accessAccountId(accessAccountId).build();
    }

}

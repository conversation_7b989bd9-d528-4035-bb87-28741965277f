package com.xk.tp.domain.repository.access;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.model.access.AccessRoot;
import reactor.core.publisher.Mono;

public interface AccessRootRepository extends IRepository<AccessRoot> {


    Mono<Void> saveAccessAccount(AccessRoot root);

    Mono<Void> updateAccessAccount(AccessRoot root);

    Mono<Void> deleteAccessAccount(AccessRoot root);

    Mono<Void> deleteAccessAccountExt(AccessRoot root);

    Mono<Void> saveAccessAccountExt(AccessRoot root);

    Mono<Void> updateAccessAccountExt(AccessRoot root);

    Mono<Void> saveAccessExt(AccessRoot root);

    Mono<Void> updateAccessExt(AccessRoot root);

    Mono<Void> removeAccessExt(AccessRoot root);

    Mono<Void> createAccessGame(AccessRoot accessRoot);

    Mono<Void> updateAccessGame(AccessRoot accessRoot);

    Mono<Void> updateCancelAccessGameRelation(AccessRoot accessRoot);

    Mono<Void> removeAccessGame(AccessRoot accessRoot);

    Mono<Void> createAccessInterfaceAuthInfo(AccessRoot accessRoot);

    Mono<Void> updateAccessInterfaceAuthInfo(AccessRoot accessRoot);

    Mono<Void> deletedAccessInterfaceAuthInfo(AccessRoot accessRoot);

    Mono<Void> saveAccessTag(AccessRoot accessRoot);

    Mono<Void> updateAccessTag(AccessRoot accessRoot);

    Mono<Void> saveAccessTagRelation(AccessRoot accessRoot);

    Mono<Void> deleteAccessTagRelation(AccessRoot accessRoot);
}

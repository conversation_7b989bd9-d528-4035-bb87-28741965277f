package com.xk.tp.domain.service.auth.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.domain.event.auth.AuthFinishEvent;
import com.xk.enums.auth.AuthFinishTypeEnum;
import com.xk.tp.domain.commons.TpAuthDictEnum;
import com.xk.tp.domain.commons.TpSequenceEnum;
import com.xk.tp.domain.commons.response.BaseAuthResultData;
import com.xk.tp.domain.commons.response.FaceVerifyResultData;
import com.xk.tp.domain.commons.response.NotifyThirdPlatformResultData;
import com.xk.tp.domain.model.access.AccessAccountEntity;
import com.xk.tp.domain.model.access.AccessEntity;
import com.xk.tp.domain.model.access.AccessIdentifier;
import com.xk.tp.domain.model.access.AccessRoot;
import com.xk.tp.domain.model.auth.*;
import com.xk.tp.domain.model.auth.ids.ApiAuthIdentifier;
import com.xk.tp.domain.repository.access.AccessRootQueryRepository;
import com.xk.tp.domain.repository.auth.AuthRootQueryRepository;
import com.xk.tp.domain.repository.auth.AuthRootRepository;
import com.xk.tp.domain.service.auth.ApiAuthRootService;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import com.xk.tp.enums.auth.AuthEventStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiAuthRootServiceImpl implements ApiAuthRootService {

    private final AuthRootQueryRepository authRootQueryRepository;

    private final AuthRootRepository authRootRepository;

    private final AccessRootQueryRepository accessRootQueryRepository;

    private final IdentifierGenerateService identifierGenerateService;

    private final DictObjectDomainService dictObjectDomainService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> syncNotifyThirdPlatform(AuthRoot authRoot) {
        return authRootQueryRepository.findNotifyThirdPlatform(authRoot).flatMap(apiResult -> {
            if (!apiResult.isSuccess()) {
                return Mono.just(apiResult);
            }

            NotifyThirdPlatformResultData resultData = apiResult.getData();
            return authRootQueryRepository
                    .findAuthRecordByAuthFlowId(AuthRecordEntity.builder()
                            .authFlowId(resultData.getAuthFlowId()).build())
                    .flatMap((recordEntity) -> {
                        return eventRootService.publisheByMono(EventRoot.builder()
                                .domainEvent(AuthFinishEvent.builder().identifier(-1L)
                                        .authFlowId(recordEntity.getAuthFlowId())
                                        .userId(recordEntity.getUserId())
                                        .realName(recordEntity.getRealName())
                                        .idCard(recordEntity.getIdCard())
                                        .status(resultData.getIsSuccess()
                                                ? AuthEventStatusEnum.SUCCESS.getStatus()
                                                : AuthEventStatusEnum.FAIL.getStatus())
                                        .authFinishType(AuthFinishTypeEnum.FACE)
                                        .remark(resultData.getRemark()).build())
                                .build());
                    }).then(Mono.just(apiResult));
        }).then();
    }

    @Override
    public Mono<AuthNotifyEntity> queryNotifyByUserId(Mono<Long> userIdMono) {
        return userIdMono.flatMap(userId -> authRootQueryRepository.findAuthNotifyByParams(
                AuthRoot.builder().identifier(ApiAuthIdentifier.builder().build())
                        .userEntity(UserEntity.builder().userId(userId).build()).build()));
    }

    @Override
    public Flux<AccessRoot> queryAuthAccessAccount(Mono<BusinessTypeEnum> mono) {
        return mono.flatMapMany(businessType -> {
            // 订单号+平台+渠道+客户端
            // 查询通道
            return accessRootQueryRepository
                    .findAccessByGroupList(AccessEntity.builder()
                            .businessGroup(BusinessGroupEnum.AUTH).businessType(businessType)
                            .status(AccessDomainStatusEnum.ENABLED.getStatus()).build())
                    .flatMap(accessEntity -> {
                        AccessRoot.AccessRootBuilder accessRootBuilder = AccessRoot.builder()
                                .identifier(AccessIdentifier.builder()
                                        .accessId(accessEntity.getAccessId()).build())
                                .accessEntity(accessEntity);
                        // 查询账号
                        return accessRootQueryRepository
                                .findAccessAccountList(AccessAccountEntity.builder()
                                        .accessId(accessEntity.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED).build())
                                .flatMap(
                                        accessRootQueryRepository::findAccessAccountExtByAccessAccount)
                                .collectList().map(accountList -> {
                                    accountList.sort((o1, o2) -> {
                                        // 为了从大到小排序，返回o2.getSort() - o1.getSort()
                                        return o2.getSort() - o1.getSort();
                                    });
                                    accessRootBuilder.accessAccountEntities(accountList);
                                    return accessRootBuilder.build();
                                });
                    });
        });
    }

    @Override
    public Mono<BaseAuthResultData> createBaseAuth(Mono<AuthRoot> authRootMono) {
        return authRootMono
                .flatMap(authRoot -> authRootRepository.baseAuth(authRoot).flatMap(apiResult -> {
                    BaseAuthResultData data = apiResult.getData();
                    if (Boolean.FALSE.equals(data.getIsSuccess())) {
                        return Mono.just(data);
                    }
                    UserEntity userEntity = authRoot.getUserEntity();
                    // 发送通知
                    EventRoot event = EventRoot.builder()
                            .domainEvent(AuthFinishEvent.builder()
                                    .identifier(EventRoot
                                            .getCommonsDomainEventIdentifier(AuthFinishEvent.class))
                                    .authFlowId(data.getVerifyId()).userId(userEntity.getUserId())
                                    .realName(userEntity.getName()).idCard(userEntity.getIdCard())
                                    .status(AuthEventStatusEnum.SUCCESS.getStatus())
                                    .authFinishType(AuthFinishTypeEnum.BASE).build())
                            .build();
                    return eventRootService.publisheByMono(event).thenReturn(data);
                }));
    }

    @Override
    public Mono<Void> clearFaceVerify(Mono<UserEntity> mono) {
        return mono.flatMap(authRootRepository::removeAuthNotifyStringCache);
    }

    @Override
    public Mono<AuthNotifyEntity> createFaceVerify(Mono<AuthRoot> authRootMono) {
        return authRootMono.flatMap(
                authRoot -> authRootRepository.createFaceVerify(authRoot).flatMap(apiResult -> {
                    if (!apiResult.isSuccess()) {
                        return Mono.empty();
                    }
                    Long authRecordId = (Long) identifierGenerateService.generateIdentifier(
                            IdentifierRoot.builder().identifier(TpSequenceEnum.TP_AUTH_RECORD)
                                    .type(IdentifierGenerateEnum.CACHE).build());
                    Long authNotifyId = (Long) identifierGenerateService.generateIdentifier(
                            IdentifierRoot.builder().identifier(TpSequenceEnum.TP_AUTH_NOTIFY)
                                    .type(IdentifierGenerateEnum.CACHE).build());
                    FaceVerifyResultData resultData = apiResult.getData();
                    // 查询
                    return createAuthRecord(authRoot, authRecordId, resultData)
                            .then(createAuthNotify(authRoot, authNotifyId, resultData))
                            .flatMap(notifyEntity -> authRootRepository
                                    .createAuthNotifyStringCache(authRoot)
                                    .then(Mono.just(notifyEntity)));
                }));
    }

    private Mono<Void> createAuthRecord(AuthRoot authRoot, Long authRecordId,
            FaceVerifyResultData resultData) {
        AuthEntity authEntity = authRoot.getAuthEntity();
        UserEntity userEntity = authRoot.getUserEntity();
        AuthRecordEntity recordEntity = AuthRecordEntity.builder().authRecordId(authRecordId)
                .authFlowId(resultData.getAuthFlowId())
                .accessAccountId(authEntity.getAccessAccountId()).userId(userEntity.getUserId())
                .createTime(new Date()).idCard(userEntity.getIdCard())
                .realName(userEntity.getName()).build();
        authRoot.setAuthRecordEntity(recordEntity);
        return authRootRepository.saveAuthRecord(recordEntity);
    }

    private Mono<AuthNotifyEntity> createAuthNotify(AuthRoot authRoot, Long authNotifyId,
            FaceVerifyResultData resultData) {
        return dictObjectDomainService.getSystemConfigToInt(TpAuthDictEnum.FACE_VERIFY_EXPIRE_TIME)
                .map(expireTime -> {
                    AuthEntity authEntity = authRoot.getAuthEntity();
                    AuthNotifyEntity notifyEntity = AuthNotifyEntity.builder()
                            .notifyId(authNotifyId).authFlowId(resultData.getAuthFlowId())
                            .authStatus(AuthEventStatusEnum.AUTHING)
                            .accessAccountId(authEntity.getAccessAccountId()).expireTime(expireTime)
                            .createTime(new Date()).build();
                    authRoot.setAuthNotifyEntity(notifyEntity);
                    return notifyEntity;
                }).flatMap(notifyEntity -> authRootRepository.saveAuthNotify(notifyEntity)
                        .thenReturn(notifyEntity));
    }
}

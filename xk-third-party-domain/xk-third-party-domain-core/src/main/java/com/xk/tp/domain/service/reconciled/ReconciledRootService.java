package com.xk.tp.domain.service.reconciled;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.commons.response.reconclied.ReconciledData;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;

import reactor.core.publisher.Mono;

public interface ReconciledRootService extends IDomainService<ReconciledRoot> {

    Mono<ReconciledData> selectRecording(ReconciledRoot root);

}

package com.xk.tp.domain.repository.reconciled;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.commons.response.reconclied.ReconciledData;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/15
 */
public interface ReconciledRootRepository extends IRepository<ReconciledRoot> {

    Mono<ReconciledData> selectRecording(ReconciledRoot root);

}


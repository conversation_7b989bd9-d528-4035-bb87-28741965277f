package com.xk.tp.domain.repository.access;


import java.util.Map;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.domain.model.tag.TagIdentifier;
import com.xk.tp.domain.model.access.*;
import com.xk.tp.domain.model.access.ids.*;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
public interface AccessRootQueryRepository extends IQueryRepository {

    Mono<AccessEntity> findById(AccessIdentifier identifier);

    Mono<AccessAccountEntity> findAccessAccountByAccountId(AccessAccountIdentifier identifier);

    Mono<AccessAccountEntity> findAccessAccountExtByAccountExtId(AccessAccountExtIdentifier identifier);

    Mono<AccessAccountEntity> findAccessAccountExtByAccessAccount(AccessAccountEntity accountEntity);

    Flux<AccessAccountEntity> searchAccessAccount(Pagination pagination);

    Flux<AccessAccountEntity> findAccessAccountList(AccessAccountEntity accessAccountEntity);

    Mono<AccessEntity> findAccessExtList(AccessEntity accessEntity);

    Mono<AccessEntity> findAccessExtByAccessExtId(AccessExtIdentifier identifier);

    Mono<Map<String, String>> findAccessAccountExtByAccessAccountExt(AccessAccountEntity accessAccountExtEntity);

    Flux<AccessEntity> searchPager(Pagination pagination);

    Flux<AccessEntity> findAccessList(AccessEntity accessEntity);

    Flux<AccessEntity> findAccessByGroupList(AccessEntity accessEntity);

    // 仅查询出平台游戏id为空的数据
    Flux<AccessGameEntity> searchAccessGame(Pagination pagination);

    Flux<AccessGameEntity> findAccessGameByAccessIdList(AccessIdentifier accessIdentifier);

    Mono<AccessGameEntity> findAccessGameByGameId(AccessGameEntity accessGameEntity);

    Mono<AccessGameEntity> findAccessGameById(AccessGameIdentifier accessGameIdentifier);

    // 查询所有三方游戏
    Flux<AccessGameEntity> findAllAccessGame();

    // 根据公司id 和通道id 查询公司的api认证信息
    Flux<AccessInterfaceAuthInfoEntity> findAccessInterfaceAuthInfoByAccessCorpIdList(
            AccessInterfaceAuthInfoEntity accessInterfaceAuthInfoEntity);

    // 查询接口认证信息
    Mono<AccessInterfaceAuthInfoEntity> findByInerfaceKey(StringIdentifier stringIdentifier);

    // 根据id查询接口认证信息
    Mono<AccessInterfaceAuthInfoEntity> findAccessInterfaceAuthInfoById(LongIdentifier identifier);

    // 查询三方标签详情
    Mono<AccessTagEntity> findAccessTagInfo(AccessTagIdentifier accessTagIdentifier);

    // 查询平台标签已经关联的第三方标签
    Flux<AccessTagRelationEntity> findTagRelationByTagId(TagIdentifier tagIdentifier);

    // 根据组合id查询平台标签关联
    Mono<AccessTagRelationEntity> findTagRelationById(AccessTagRelationIdentifier accessTagRelationIdentifier);

    // 分页查询三方标签
    Flux<AccessTagEntity> findAccessTagList(Pagination pagination);

    /**
     * 根据通道id和三方标签id查询三方标签关联
     *
     * @param accessTagRelationEntity accessTagRelationEntity
     * @return Flux<AccessTagRelationEntity>
     */
    Flux<AccessTagRelationEntity> findTagRelationByAccessTpTag(AccessTagRelationEntity accessTagRelationEntity);

    // 根据通道id 查询api认证信息
    Flux<AccessInterfaceAuthInfoEntity> findAccessInterfaceAuthInfoByAccessId(
            AccessInterfaceAuthInfoEntity accessInterfaceAuthInfoEntity);
}

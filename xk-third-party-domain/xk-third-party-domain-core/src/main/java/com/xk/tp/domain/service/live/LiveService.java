package com.xk.tp.domain.service.live;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.live.LiveRoot;

import reactor.core.publisher.Mono;

public interface LiveService extends IDomainService<LiveRoot> {

    /**
     * 获取直播平台类型
     */
    Integer getLivePlatformType();

    Mono<Void> removeUser(LiveRoot liveRoot);

    Mono<Void> removeUserStr(LiveRoot liveRoot);

    Mono<Void> dismissRoom(LiveRoot liveRoot);

    Mono<Void> dismissRoomStr(LiveRoot liveRoot);

}

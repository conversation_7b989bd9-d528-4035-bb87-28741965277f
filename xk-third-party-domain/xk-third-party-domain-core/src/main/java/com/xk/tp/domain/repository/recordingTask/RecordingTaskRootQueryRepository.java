package com.xk.tp.domain.repository.recordingTask;

import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.NotifyThirdPlatformResultData;
import com.xk.tp.domain.model.auth.AuthNotifyEntity;
import com.xk.tp.domain.model.auth.AuthRecordEntity;
import com.xk.tp.domain.model.auth.AuthRoot;
import com.xk.tp.domain.model.recordingTask.RecordingTaskEntity;
import com.xk.tp.domain.model.recordingTask.RecordingTaskRoot;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/15
 */
public interface RecordingTaskRootQueryRepository extends IQueryRepository {

    Flux<RecordingTaskEntity> selectById(StringIdentifier identifier);
}

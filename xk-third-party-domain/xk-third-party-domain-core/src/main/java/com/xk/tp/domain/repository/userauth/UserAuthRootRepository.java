package com.xk.tp.domain.repository.userauth;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.tp.domain.model.userauth.UserAuthRoot;
import reactor.core.publisher.Mono;

public interface UserAuthRootRepository extends IRepository<UserAuthRoot> {


    /**
     * 获取用户信息
     * @param userAuthRoot userAuthRoot
     * @return Mono<UserAuthRoot>
     */
    Mono<UserAuthRoot> getUserInfo(UserAuthRoot userAuthRoot);
}

package com.xk.tp.domain.service.userauth;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.userauth.UserAuthRoot;
import reactor.core.publisher.Mono;

public interface UserAuthRootService extends IDomainService<UserAuthRoot> {

    /**
     * 获取用户信息
     * @param userAuthRoot userAuthRoot
     * @return Mono<UserAuthRoot>
     */
    Mono<UserAuthRoot> getUserInfo(UserAuthRoot userAuthRoot);
}

package com.xk.tp.domain.support;

import com.myco.mydata.domain.model.identifier.SequenceIdentifier;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum AccessSequenceEnum implements SequenceIdentifier {

    TP_ACCESS_GAME("TP_ACCESS_GAME","id","TpAccessGameMapper"),
    TP_ACCESS_INTERFACE_AUTH_INTO("TP_ACCESS_INTERFACE_AUTH_INTO","id","TpAccessInterfaceAuthIntoMapper"),
    ;

    private final String table;
    private final String pk;
    private final String className;

    @Override
    public @NonNull String getName() {
        return this.name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return this.name();
    }

    @Override
    public String getTable() {
        return this.table;
    }

    public String getPk() {
        return this.pk;
    }

    public String getClassName() {
        return this.className;
    }

}

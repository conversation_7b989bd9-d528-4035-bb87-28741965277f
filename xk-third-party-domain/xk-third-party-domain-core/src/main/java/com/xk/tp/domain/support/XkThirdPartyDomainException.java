package com.xk.tp.domain.support;

import com.xk.tp.domain.commons.XkThirdPartyDomainErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.ExceptionRoot;
import com.myco.mydata.domain.model.exception.wrapper.DomainWrapperThrowable;

/**
 * 领域异常
 * @author: killer
 **/
public class XkThirdPartyDomainException extends DomainWrapperThrowable {

    public XkThirdPartyDomainException(XkThirdPartyDomainErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkThirdPartyDomainException(XkThirdPartyDomainErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}

package com.xk.tp.domain.service.access.impl;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.commons.XkDomainErrorEnum;
import com.xk.domain.support.XkDomainException;
import com.xk.tp.domain.model.access.*;
import com.xk.tp.domain.model.access.ids.AccessAccountIdentifier;
import com.xk.tp.domain.repository.access.AccessRootQueryRepository;
import com.xk.tp.domain.service.access.AccessRootService;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.AccessInterfaceAuthStatusEnum;
import com.xk.tp.enums.access.AccessInterfaceAuthTypeEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:20
 */
@Service
@RequiredArgsConstructor
public class AccessRootServiceImpl implements AccessRootService {

    private final AccessRootQueryRepository accessRootQueryRepository;

    @Override
    public Mono<AccessEntity> findById(AccessIdentifier identifier) {
        return accessRootQueryRepository.findById(identifier);
    }

    @Override
    public Mono<AccessRoot> getRoot(AccessIdentifier identifier) {

        Supplier<Mono<AccessEntity>> getAccess =
                () -> accessRootQueryRepository.findById(identifier);

        Function<AccessEntity, Flux<AccessAccountEntity>> getAccessAccount =
                (access) -> accessRootQueryRepository.findAccessAccountList(
                        AccessAccountEntity.builder().accessId(access.getAccessId())
                                .status(AccessDomainStatusEnum.ENABLED).build());

        BiFunction<AccessEntity, AccessAccountEntity, Mono<AccessAccountEntity>> getAccessAccountExt =
                (access, accessAccount) -> accessRootQueryRepository
                        .findAccessAccountExtByAccessAccount(accessAccount).map(v -> {
                            accessAccount
                                    .setAccessAccountExtValObjMap(v.getAccessAccountExtValObjMap());
                            return accessAccount;
                        });

        BiFunction<AccessEntity, List<AccessAccountEntity>, Mono<AccessRoot>> getRoot =
                (access, accountList) -> {
                    AccessRoot root = AccessRoot.builder().identifier(identifier)
                            .accessEntity(access).accessAccountEntities(accountList).build();
                    return Mono.just(root);
                };

        return getAccess.get()
                .flatMap(access -> getAccessAccount.apply(access)
                        .flatMap(accessAccount -> getAccessAccountExt.apply(access, accessAccount))
                        .collectList().flatMap(v -> getRoot.apply(access, v)));
    }

    @Override
    public Flux<AccessAccountEntity> findAccessAccountList(
            AccessAccountEntity accessAccountEntity) {
        return accessRootQueryRepository.findAccessAccountList(accessAccountEntity);
    }

    @Override
    public Mono<Map<String, String>> findAccessAccountExtByAccessAccountExt(
            AccessAccountExtValObj accessAccountExtValObj) {
        return accessRootQueryRepository.findAccessAccountExtByAccessAccountExt(AccessAccountEntity
                .builder().accessAccountId(accessAccountExtValObj.getAccessAccountId()).build());
    }

    @Override
    public Flux<AccessEntity> findAccessByGroupList(AccessEntity accessEntity) {
        return accessRootQueryRepository.findAccessByGroupList(accessEntity);
    }

    @Override
    public Flux<AccessAccountEntity> findAccessAccountByGroup(AccessEntity entity) {
        return accessRootQueryRepository.findAccessByGroupList(entity).flatMap(accessEntity -> {
            // 查询账号
            AccessAccountEntity accountEntity =
                    AccessAccountEntity.builder().accessId(accessEntity.getAccessId())
                            .status(AccessDomainStatusEnum.ENABLED).build();
            return accessRootQueryRepository.findAccessAccountList(accountEntity)
                    .map(accessAccountEntity -> {
                        accessAccountEntity.setPlatformType(accessEntity.getPlatformType());
                        accessAccountEntity.setChannelType(accessEntity.getChannelType());
                        accessAccountEntity.setBusinessType(accessEntity.getBusinessType());
                        return accessAccountEntity;
                    });
        }).sort(Comparator.comparing(AccessAccountEntity::getSort).reversed());
    }

    @Override
    public Mono<AccessAccountEntity> findAccessAccountByAccountId(
            AccessAccountIdentifier identifier) {
        return accessRootQueryRepository.findAccessAccountByAccountId(identifier);
    }

    /**
     * 校验通道是否有开启的账号
     *
     * @param accessIdentifier accessIdentifier
     * @return Mono<Boolean>
     */
    @Override
    public Mono<Boolean> checkAccessHasEnabledAccount(AccessIdentifier accessIdentifier) {
        return accessRootQueryRepository.findAccessAccountList(
                AccessAccountEntity.builder().accessId(accessIdentifier.getAccessId())
                        .status(AccessDomainStatusEnum.ENABLED).build())
                .hasElements();
    }

    /**
     * 根据InterfaceKey校验接口权限
     *
     * @param accessInterfaceAuthInfoObject accessInterfaceAuthInfoObject
     * @return Mono<AccessInterfaceAuthInfoEntity>
     */
    @Override
    public Mono<AccessInterfaceAuthInfoEntity> checkAuthByInterfaceKey(
            AccessInterfaceAuthInfoObject accessInterfaceAuthInfoObject) {
        return accessRootQueryRepository
                .findByInerfaceKey(StringIdentifier.builder()
                        .id(accessInterfaceAuthInfoObject.getInterfaceKey()).build())
                .flatMap(accessInterfaceAuthInfoEntity -> {
                    if (accessInterfaceAuthInfoObject
                            .getType() != AccessInterfaceAuthTypeEnum.TP_PUSH) {
                        return Mono.error(new XkDomainException(XkDomainErrorEnum.APP_NOT_FOUND));
                    }
                    if (accessInterfaceAuthInfoEntity
                            .getAuthStatus() != AccessInterfaceAuthStatusEnum.ON) {
                        return Mono.error(new XkDomainException(XkDomainErrorEnum.APP_ClOSE));
                    }
                    // 查询通道
                    return accessRootQueryRepository
                            .findById(AccessIdentifier.builder()
                                    .accessId(accessInterfaceAuthInfoEntity.getAccessId()).build())
                            .flatMap(accessEntity -> {
                                if (!Objects.equals(accessEntity.getStatus(),
                                        AccessDomainStatusEnum.ENABLED.getStatus())) {
                                    return Mono.error(
                                            new XkDomainException(XkDomainErrorEnum.APP_ClOSE));
                                }
                                if (accessEntity.getBusinessGroup() != accessInterfaceAuthInfoObject
                                        .getBusinessGroup()) {
                                    return Mono.error(
                                            new XkDomainException(XkDomainErrorEnum.NO_AUTH));
                                }

                                return Mono.just(accessInterfaceAuthInfoEntity);
                            });
                })
                .switchIfEmpty(Mono.error(new XkDomainException(XkDomainErrorEnum.APP_NOT_FOUND)));
    }
}

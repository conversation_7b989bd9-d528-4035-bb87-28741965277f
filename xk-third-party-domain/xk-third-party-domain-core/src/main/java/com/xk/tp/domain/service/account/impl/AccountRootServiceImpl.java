package com.xk.tp.domain.service.account.impl;

import com.xk.tp.domain.model.access.AccessAccountEntity;
import com.xk.tp.domain.model.access.AccessEntity;
import com.xk.tp.domain.model.access.ids.AccessExtIdentifier;
import com.xk.tp.domain.model.account.AccountEntity;
import com.xk.tp.domain.model.account.AccountRoot;
import com.xk.tp.domain.repository.access.AccessRootQueryRepository;
import com.xk.tp.domain.repository.account.AccountRootRepository;
import com.xk.tp.domain.service.account.AccountRootService;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountRootServiceImpl implements AccountRootService {

    private final AccessRootQueryRepository accessRootQueryRepository;
    private final AccountRootRepository accountRootRepository;

    @Override
    public Mono<AccountRoot> routeAccount(Mono<AccountRoot> accountRootMono) {
        return accountRootMono.flatMap(accountRoot -> {
            AccountEntity accountEntity = accountRoot.getAccountEntity();
            //查询渠道
            return accessRootQueryRepository.findAccessByGroupList(AccessEntity.builder()
                            .businessGroup(accountEntity.getBusinessGroup())
                            .channelType(accountEntity.getChannelType())
                            .businessType(accountEntity.getBusinessType())
                            .status(AccessDomainStatusEnum.ENABLED.getStatus())
                    .build()).flatMap(accessEntity -> {
                        //查询渠道扩展
                        return accessRootQueryRepository.findAccessExtByAccessExtId(AccessExtIdentifier.builder()
                                        .accessId(accessEntity.getAccessId())
                                        .fieldKey(accountEntity.getDevice())
                                .build()).map(data ->{
                            return data.getAccessExtValObjMap().values().stream().findFirst().orElse(null);
                        }).map(res ->{
                            if (AccessDomainStatusEnum.ENABLED.getStatus().toString()
                                    .equals(res.getFiledValue())) {
                                return AccessDomainStatusEnum.ENABLED.getStatus();
                            }
                            // 支持当前支付方式
                            return AccessDomainStatusEnum.DISABLED.getStatus();
                        }).switchIfEmpty(
                                        Mono.just(AccessDomainStatusEnum.DISABLED.getStatus()))
                                .map(status -> {
                                    accessEntity.setStatus(status);
                                    return accessEntity;
                                });
            }).filter(accessEntity -> Objects.equals(accessEntity.getStatus(),
                    AccessDomainStatusEnum.ENABLED.getStatus()))
                    .flatMap(accessEntity -> {
                        //查询账号
                        return accessRootQueryRepository.findAccessAccountList(AccessAccountEntity.builder()
                                        .accessId(accessEntity.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED)
                                .build())
                                .collectList()
                                .map(accessAccountEntityList -> {
                                    accessEntity.setAccessAccountEntityList(accessAccountEntityList);
                                    return accessEntity;
                        });
                    }).collectList()
                    .flatMap(accessEntities -> {
                        List<AccountEntity> accountEntityList =  new ArrayList<>();
                        for (AccessEntity accessEntity : accessEntities) {
                            for (AccessAccountEntity accessAccountEntity : accessEntity.getAccessAccountEntityList()) {
                                AccountEntity account = AccountEntity.builder()
                                        .accessId(accessAccountEntity.getAccessId())
                                        .accountName(accessAccountEntity.getAccountName())
                                        .platformType(accessAccountEntity.getPlatformType())
                                        .channelType(accessAccountEntity.getChannelType())
                                        .businessType(accessAccountEntity.getBusinessType())
                                        .businessGroup(accessAccountEntity.getBusinessGroup())
                                        .status(accessAccountEntity.getStatus())
                                        .build();
                                accountEntityList.add(account);
                            }
                        }
                        accountRoot.setRouteAccountEntityList(accountEntityList);
                        //筛选账号
                        return accountRootRepository.routeAccount(accountRoot);
                    }).flatMap(accessAccountEntity -> {
                        return Mono.just(AccountRoot.builder().build());
                    });
        });
    }
}

package com.xk.tp.enums.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/16 10:09
 */
@Getter
@AllArgsConstructor
public enum LogisticsCorpEnum {

    SF("顺丰快递", LogisticsApiEnum.KUAI_DI_100 ,"shunfengkuaiyun"),
    JD_KY("京东快递",LogisticsApiEnum.KUAI_DI_100 , "jingdongkuaiyun"),
    EMS("中国邮政速递物流",LogisticsApiEnum.KUAI_DI_100 , "youzhengbk"),
    ZTO("中通快递",LogisticsApiEnum.KUAI_DI_100 , "zhongtong"),
    YTO("圆通速递",LogisticsApiEnum.KUAI_DI_100 , "yuantong"),
    STO("申通快递",LogisticsApiEnum.KUAI_DI_100 , "shentong"),
    YUN_DA("韵达速递",LogisticsApiEnum.KUAI_DI_100 , "yunda"),
    JT("极兔速递",LogisticsApiEnum.KUAI_DI_100 , "jtexpress"),
    DE_BANG("德邦快递",LogisticsApiEnum.KUAI_DI_100 , "debangkuaidi"),
    ;

    private static final Map<String, LogisticsCorpEnum> MAP;

    static {
        MAP = Arrays.stream(LogisticsCorpEnum.values())
                .collect(Collectors.toMap(LogisticsCorpEnum::getCode, enumValue -> enumValue));
    }

    private final String code;
    private final LogisticsApiEnum logisticsApi;
    private final String msg;

    public static LogisticsCorpEnum getByCode(String code) {
        return MAP.get(code);
    }
}

package com.xk.tp.domain.event.push;

import java.util.Date;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractTpDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * 创建推送结果事件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/15 11:47
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_MESSAGE,
        domainName = DomainNameEnum.MESSAGE)
public class PushMessageResultEvent extends AbstractTpDomainEvent {

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 来源消息id
     */
    private final String sourceMessageId;

    /**
     * 接收方唯一编码
     */
    private final Long receiverCode;

    /**
     * 消息模板id
     */
    private final Long messageTemplateId;

    /**
     * 消息标题
     */
    private final String title;

    /**
     * 消息内容
     */
    private final String content;

    /**
     * 推送渠道
     */
    private final Integer pushChannelType;

    /**
     * 消息参数
     */
    private final String messageParams;

    /**
     * 设备平台类型
     */
    private final Integer platformType;

    /**
     * 发送时间
     */
    private final Date sendTime;

    /**
     * 是否成功
     */
    private final Boolean success;

    @Builder
    public PushMessageResultEvent(@NonNull Long identifier, Map<String, Object> context, String messageId,
                                   String sourceMessageId, Long receiverCode, Long messageTemplateId,
                                   String title, String content, Integer pushChannelType, String messageParams,
                                   Integer platformType, Date sendTime, Boolean success) {
        super(identifier, context);
        this.messageId = messageId;
        this.sourceMessageId = sourceMessageId;
        this.receiverCode = receiverCode;
        this.messageTemplateId = messageTemplateId;
        this.title = title;
        this.content = content;
        this.pushChannelType = pushChannelType;
        this.messageParams = messageParams;
        this.platformType = platformType;
        this.sendTime = sendTime;
        this.success = success;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}

package com.xk.ewd.server.endpoints.reconciliation;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationItemUpdateReq;
import com.xk.ewd.interfaces.query.reconciliation.ReconciliationService;

@Configuration(proxyBeanMethods = false)
public class ReconciliationServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> reconciliationServiceRouter(
            ReconciliationService reconciliationService) {
        return nest(RequestPredicates.path("/ewd/reconciliation"), route()
                .POST("/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                ReconciliationItemUpdateReq.class, reconciliationService::update))
                .build());
    }
}

package com.xk.ewd.server.schedule.reconciliation;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.scheduling.quartz.support.Job2ExecutionContext;
import com.myco.mydata.infrastructure.schedule.job.DefaultJob;
import com.xk.ewd.interfaces.task.reconciliation.ReconciliationTaskService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class ReconciliationDailySchedule extends DefaultJob {

    private final ReconciliationTaskService reconciliationTaskService;

    @Override
    public Mono<Void> handler(Job2ExecutionContext context) {
        Date fireTime = context.getJobExecutionContext().getFireTime();
        Date ceiling = DateUtils.ceiling(DateUtils.addDays(fireTime, -1), Calendar.DATE);
        return reconciliationTaskService.createDailyReconciliation(ceiling);
    }
}

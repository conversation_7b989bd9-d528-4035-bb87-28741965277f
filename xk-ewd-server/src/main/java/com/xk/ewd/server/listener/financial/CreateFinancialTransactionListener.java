package com.xk.ewd.server.listener.financial;


import com.xk.tp.domain.event.reconciled.CreateFinancialTransactionEvent;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@ConsumerListener
@RequiredArgsConstructor
public class CreateFinancialTransactionListener extends AbstractDispatchMessageListener<CreateFinancialTransactionEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(CreateFinancialTransactionEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(false).build();
        eventRootService.handler(eventRoot);
    }

}

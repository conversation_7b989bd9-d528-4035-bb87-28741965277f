package com.xk.ewd.server.endpoints.reconciliation;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationDetailReq;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationSearchPagerReq;
import com.xk.ewd.interfaces.query.reconciliation.ReconciliationQueryService;

@Configuration(proxyBeanMethods = false)
public class ReconciliationQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> reconciliationQueryServiceRouter(
            ReconciliationQueryService reconciliationQueryService) {
        return nest(RequestPredicates.path("/ewd/reconciliation/query"), route()
                .POST("/search/pager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                ReconciliationSearchPagerReq.class,
                                reconciliationQueryService::searchPager))
                .POST("/detail", ACCEPT_JSON, request -> WebFluxHandler.handler(request,
                        ReconciliationDetailReq.class, reconciliationQueryService::detail))
                .build());
    }
}

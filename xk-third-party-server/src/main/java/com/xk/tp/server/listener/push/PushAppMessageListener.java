package com.xk.tp.server.listener.push;

import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.message.domain.event.message.MessageAppPushEvent;

import lombok.RequiredArgsConstructor;

/**
 * 推送消息监听
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/11 13:34
 */
@ConsumerListener
@RequiredArgsConstructor
public class PushAppMessageListener extends AbstractDispatchMessageListener<MessageAppPushEvent> implements MessageListenerConcurrently {

    @Override
    public void doProcessMessage(MessageAppPushEvent messageAppPushEvent) throws Throwable {
        eventRootService.handler(EventRoot.builder().domainEvent(messageAppPushEvent).isQueue(true).build());
    }

    private final EventRootService eventRootService;
}

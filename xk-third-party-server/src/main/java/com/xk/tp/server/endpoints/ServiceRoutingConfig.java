package com.xk.tp.server.endpoints;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.server.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.myco.mydata.domain.model.Root;
import com.myco.mydata.domain.model.commons.HttpStatusEnum;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.tp.interfaces.dto.auth.BaseAuthReqDto;
import com.xk.tp.interfaces.dto.auth.FaceVerifyDto;
import com.xk.tp.interfaces.dto.pay.CloseOrderDto;
import com.xk.tp.interfaces.dto.pay.PlaceOrderDto;
import com.xk.tp.interfaces.dto.pay.RefundOrderDto;
import com.xk.tp.interfaces.dto.req.access.*;
import com.xk.tp.interfaces.dto.req.auth.FaceNotifyReqDto;
import com.xk.tp.interfaces.dto.req.im.*;
import com.xk.tp.interfaces.dto.req.live.DismissRoomReqDto;
import com.xk.tp.interfaces.dto.req.live.RemoveUserReqDto;
import com.xk.tp.interfaces.dto.req.live.SignReqDto;
import com.xk.tp.interfaces.dto.req.log.CreateUseLogReqDto;
import com.xk.tp.interfaces.dto.req.pay.PayNotifyReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.CreateRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DeleteRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DescribeRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.UpdateRecordingReqDto;
import com.xk.tp.interfaces.dto.req.share.ShareBusinessReqDto;
import com.xk.tp.interfaces.dto.req.sms.SendSmsByAccessReqDto;
import com.xk.tp.interfaces.dto.req.sms.SendSmsReqDto;
import com.xk.tp.interfaces.service.access.AccessService;
import com.xk.tp.interfaces.service.auth.ThirdAuthNotifyService;
import com.xk.tp.interfaces.service.auth.ThirdAuthService;
import com.xk.tp.interfaces.service.im.TpImService;
import com.xk.tp.interfaces.service.live.TpLiveService;
import com.xk.tp.interfaces.service.log.UseLogService;
import com.xk.tp.interfaces.service.pay.PayNotifyService;
import com.xk.tp.interfaces.service.pay.PayService;
import com.xk.tp.interfaces.service.recordingTask.RecordingTaskService;
import com.xk.tp.interfaces.service.share.ShareBusinessService;
import com.xk.tp.interfaces.service.sms.SmsService;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR> date 2024/07/22
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
public class ServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON;
    private static final ExecutorService executor;
    private static final Root<Void> voidRoot;
    private static final ServerResponse.BodyBuilder OK;
    private static final ServerResponse.BodyBuilder APPLICATION_ERROR;

    static {
        ACCEPT_JSON = RequestPredicates.accept(MediaType.APPLICATION_JSON);
        voidRoot = new Root<>(SystemErrorEnum.RESPONSE_SUCCESS.getIdentifier(), "success", null);
        OK = ServerResponse.ok().contentType(MediaType.APPLICATION_JSON);
        APPLICATION_ERROR =
                ServerResponse.status(HttpStatusEnum.APPLICATION_ERROR_HTTP_STATUS.getCode())
                        .contentType(MediaType.APPLICATION_JSON);
        executor = Executors.newThreadPerTaskExecutor(Thread.ofVirtual().factory());
    }

    @Bean
    public RouterFunction<ServerResponse> accessServiceRouter(AccessService accessService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/access"),
                RouterFunctions.route().POST("/saveAccess", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, UpdateAccessReqDto.class,
                            accessService::saveAccess);
                }).POST("/updateAccess", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, UpdateAccessReqDto.class,
                            accessService::updateAccess);
                }).POST("/deleteAccess", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessIdReqDto.class,
                            accessService::deleteAccess);
                }).POST("/saveAccessExt", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessExtReqDto.class,
                            accessService::saveAccessExt);
                }).POST("/updateAccessExt", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessExtReqDto.class,
                            accessService::updateAccessExt);
                }).POST("/deleteAccessExt", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessExtReqDto.class,
                            accessService::deleteAccessExt);
                }).POST("/saveAccessAccount", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessAccountReqDto.class,
                            accessService::saveAccessAccount);
                }).POST("/updateAccessAccount", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessAccountReqDto.class,
                            accessService::updateAccessAccount);
                }).POST("/deleteAccessAccount", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessAccountReqDto.class,
                            accessService::deleteAccessAccount);
                }).POST("/saveAccessAccountExt", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessAccountExtReqDto.class,
                            accessService::saveAccessAccountExt);
                }).POST("/updateAccessAccountExt", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessAccountExtReqDto.class,
                            accessService::updateAccessAccountExt);
                }).POST("/deleteAccessAccountExt", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AccessAccountExtReqDto.class,
                            accessService::deleteAccessAccountExt);
                }).POST("/updateAccessStatus", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, UpdateAccessStatusReqDto.class,
                            accessService::updateAccessStatus);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> smsServiceRouter(SmsService smsService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/sms"),
                RouterFunctions.route().POST("/sendSms", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, SendSmsReqDto.class,
                            smsService::sendSms);
                }).POST("/sendSmsByAccess", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, SendSmsByAccessReqDto.class,
                            smsService::sendSmsByAccess);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> payServiceRouter(PayService payService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/pay"),
                RouterFunctions.route().POST("/createPay", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, PlaceOrderDto.class,
                            payService::createPay);
                }).POST("/closeOrder", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, CloseOrderDto.class,
                            payService::closeOrder);
                }).POST("/refundOrder", ACCEPT_JSON, (request -> {
                    return WebFluxHandler.handler(request, RefundOrderDto.class,
                            payService::refundOrder);
                })).build());
    }

    @Bean
    public RouterFunction<ServerResponse> payTransferServiceRouter(PayService payService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/transfer"),
                RouterFunctions.route().POST("/createPay", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, PlaceOrderDto.class,
                            payService::createPay);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> payNotifyServiceRouter(
            PayNotifyService payNotifyService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/payNotify"), RouterFunctions.route()
                .POST("/ali/notify/{accessAccountId}/{device}", ACCEPT_JSON, serverRequest -> {
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    String device = serverRequest.pathVariable("device");
                    return serverRequest.bodyToMono(MultiValueMap.class).map(parameters -> {
                        Map<String, String[]> paramMap = parametersToMap(parameters);
                        // 处理请求参数
                        Map<String, String> params = handleParams(paramMap);
                        String jsonString = JSON.toJSONString(params);
                        log.info("阿里回调参数----------:{}", jsonString);
                        PayNotifyReqDto aliCallReqDto = new PayNotifyReqDto();
                        aliCallReqDto.setSessionId("-1");
                        aliCallReqDto.setParams(params);
                        aliCallReqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        aliCallReqDto.setDevice(device);
                        return aliCallReqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {
                                CompletableFuture<String> future =
                                        CompletableFuture.supplyAsync(() -> {
                                            return payNotifyService
                                                    .createAliNotify(Mono.just(reqDto)).block();
                                        }, executor);
                                return Mono.fromFuture(future);
                            }).flatMap((result) -> {
                                return OK.body(Mono.just(result), String.class);
                            }).onErrorResume((ex) -> {
                                return OK.body(Mono.just("failure"), String.class);
                            });
                }).POST("/wx/notify/{orderNo}/{accessAccountId}", ACCEPT_JSON, serverRequest -> {
                    String orderNo = serverRequest.pathVariable("orderNo");
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    return serverRequest.bodyToMono(String.class).map(str -> {
                        PayNotifyReqDto reqDto = new PayNotifyReqDto();
                        reqDto.setSessionId("-1");
                        reqDto.setOrderNo(orderNo);
                        reqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        reqDto.setTimestamp(
                                serverRequest.headers().firstHeader("Wechatpay-Timestamp"));
                        reqDto.setNonce(serverRequest.headers().firstHeader("Wechatpay-Nonce"));
                        reqDto.setSignature(
                                serverRequest.headers().firstHeader("Wechatpay-Signature"));
                        reqDto.setSignatureType(
                                serverRequest.headers().firstHeader("Wechatpay-Signature-Type"));
                        reqDto.setSerial(serverRequest.headers().firstHeader("Wechatpay-Serial"));
                        reqDto.setParamsStr(str);
                        return reqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {

                                CompletableFuture<Map<String, String>> future =
                                        CompletableFuture.supplyAsync(() -> {
                                            return payNotifyService
                                                    .createWxNotify(Mono.just(reqDto)).block();
                                        }, executor);
                                return Mono.fromFuture(future);
                            }).flatMap((result) -> {
                                return OK.body(Mono.just(result), Map.class);
                            }).onErrorResume((ex) -> {
                                Map<String, String> result = new HashMap<>(2);
                                result.put("code", "FAIL");
                                result.put("message", "交易失败");
                                return ServerResponse.status(503)
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .body(Mono.just(result), Map.class);
                            });
                }).POST("/llian/notify/{accessAccountId}/{device}", ACCEPT_JSON, serverRequest -> {
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    String device = serverRequest.pathVariable("device");
                    return serverRequest.bodyToMono(String.class).map(str -> {
                        PayNotifyReqDto reqDto = new PayNotifyReqDto();
                        reqDto.setSessionId("-1");
                        reqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        reqDto.setParams(JSON.parseObject(str, Map.class));
                        reqDto.setDevice(device);
                        return reqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {

                                CompletableFuture<Map<String, String>> future =
                                        CompletableFuture.supplyAsync(() -> {
                                            return payNotifyService
                                                    .createLLianNotify(Mono.just(reqDto)).block();
                                        }, executor);
                                return Mono.fromFuture(future);
                            }).flatMap((result) -> {
                                return OK.body(Mono.just(result), Map.class);
                            }).onErrorResume((ex) -> {
                                Map<String, String> result = new HashMap<>(2);
                                result.put("ret_code", "0001");
                                result.put("ret_msg", "交易失败");
                                return OK.body(Mono.just(result), Map.class);
                            });
                })
                .POST("/llianHst/notify/{accessAccountId}/{device}", ACCEPT_JSON, serverRequest -> {
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    String device = serverRequest.pathVariable("device");
                    // 请求头数据
                    Map<String, Object> headers = new HashMap<>();
                    headers.put("Signature-Type",
                            serverRequest.headers().firstHeader("Signature-Type"));
                    headers.put("Signature-Data",
                            serverRequest.headers().firstHeader("Signature-Data"));

                    return serverRequest.bodyToMono(String.class).map(str -> {
                        PayNotifyReqDto reqDto = new PayNotifyReqDto();
                        reqDto.setSessionId("-1");
                        reqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        reqDto.setBodyStr(str);
                        reqDto.setDevice(device);
                        reqDto.setHeaders(headers);
                        return reqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {

                                CompletableFuture<String> future =
                                        CompletableFuture.supplyAsync(() -> {
                                            return payNotifyService
                                                    .createLLianHstNotify(Mono.just(reqDto))
                                                    .block();
                                        }, executor);
                                return Mono.fromFuture(future);
                            }).flatMap((result) -> {
                                return OK.body(Mono.just(result), String.class);
                            }).onErrorResume((ex) -> {
                                return OK.body(Mono.just("Success"), String.class);
                            });
                }).POST("/tlian/notify/{accessAccountId}/{device}", ACCEPT_JSON, serverRequest -> {
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    String device = serverRequest.pathVariable("device");
                    return serverRequest.bodyToMono(String.class).map(str -> {
                        PayNotifyReqDto reqDto = new PayNotifyReqDto();
                        reqDto.setSessionId("-1");
                        reqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        reqDto.setParams(decodeUrlEncodedParams(str));
                        reqDto.setDevice(device);
                        return reqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {

                                CompletableFuture<String> future =
                                        CompletableFuture.supplyAsync(() -> {
                                            return payNotifyService
                                                    .createTLianNotify(Mono.just(reqDto)).block();
                                        }, executor);
                                return Mono.fromFuture(future);
                            }).flatMap((result) -> {
                                return OK.body(Mono.just(result), String.class);
                            }).onErrorResume((ex) -> {
                                return OK.body(Mono.just("failure"), String.class);
                            });
                }).POST("/huifu/notify/{accessAccountId}/{device}", ACCEPT_JSON, serverRequest -> {
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    String device = serverRequest.pathVariable("device");
                    return serverRequest.bodyToMono(String.class).map(str -> {
                        log.info("汇付回调参数----------:{}", str);
                        PayNotifyReqDto reqDto = new PayNotifyReqDto();
                        reqDto.setSessionId("-1");
                        reqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        reqDto.setParamsStr(str);
                        reqDto.setDevice(device);
                        return reqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {
                                CompletableFuture<String> future =
                                        CompletableFuture.supplyAsync(() -> {
                                            return payNotifyService
                                                    .createHuifuNotify(Mono.just(reqDto)).block();
                                        }, executor);
                                return Mono.fromFuture(future);
                            }).flatMap((result) -> {
                                return OK.body(Mono.just(result), String.class);
                            }).onErrorResume((ex) -> {
                                return OK.body(Mono.just("failure"), String.class);
                            });
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> authRootServiceRouter(ThirdAuthService authService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/auth"),
                RouterFunctions.route()
                        .POST("/base/auth", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, BaseAuthReqDto.class,
                                        authService::createBaseAuth))
                        .POST("/face/verify", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, FaceVerifyDto.class,
                                        authService::createFaceVerify))
                        .POST("/face/verify/clear", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, RequireSessionDto.class,
                                        authService::clearFaceVerify))
                        .build());
    }

    @Bean
    public RouterFunction<ServerResponse> authNotifyServiceRouter(
            ThirdAuthNotifyService authNotifyService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/authNotify"), RouterFunctions
                .route().GET("/ali/face/notify/{accessAccountId}", ACCEPT_JSON, serverRequest -> {
                    log.info("阿里实人认证回调参数----------:{}",
                            JSONObject.toJSONString(serverRequest.queryParams()));
                    String accessAccountId = serverRequest.pathVariable("accessAccountId");
                    String certifyId = serverRequest.queryParam("certifyId")
                            .orElseThrow(() -> new IllegalArgumentException("certifyId 参数必填"));
                    String callbackToken = serverRequest.queryParam("callbackToken")
                            .orElseThrow(() -> new IllegalArgumentException("callbackToken 参数必填"));
                    return serverRequest.bodyToMono(String.class).map(str -> {
                        FaceNotifyReqDto reqDto = new FaceNotifyReqDto();
                        reqDto.setSessionId(callbackToken);
                        reqDto.setAccessAccountId(Long.parseLong(accessAccountId));
                        reqDto.setAuthFlowId(certifyId);
                        return reqDto;
                    }).subscribeOn(Schedulers.boundedElastic()).switchIfEmpty(Mono.empty())
                            .flatMap((reqDto) -> {
                                CompletableFuture<Void> future =
                                        CompletableFuture.supplyAsync(
                                                () -> authNotifyService
                                                        .faceNotify(Mono.just(reqDto)).block(),
                                                executor);
                                return Mono.fromFuture(future);
                            }).then(OK.build()).onErrorResume((ex) -> ServerResponse.status(301)
                                    .contentType(MediaType.APPLICATION_JSON).build());
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> useLogServiceRouter(UseLogService useLogService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/useLog"),
                RouterFunctions.route().POST("/save", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, CreateUseLogReqDto.class,
                            useLogService::save);
                }).build());
    }

    /**
     * 分享端点
     *
     * @param shareBusinessService shareBusinessService
     * @return org.springframework.web.reactive.function.server.RouterFunction<org.springframework.web.reactive.function.server.ServerResponse>
     * <AUTHOR>
     * @date: 2025/8/8 20:47
     */
    @Bean
    public RouterFunction<ServerResponse> shareServiceRouter(
            ShareBusinessService shareBusinessService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/share"),
                RouterFunctions.route()
                        .POST("/create/business/config", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request,
                                        ShareBusinessReqDto.class,
                                        shareBusinessService::createBusinessConfig))
                        .build());
    }

    @Bean
    public RouterFunction<ServerResponse> liveServiceRouter(TpLiveService tpLiveService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/live"),
                RouterFunctions.route()
                        .POST("/remove/user", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request, RemoveUserReqDto.class,
                                        tpLiveService::removeUser))
                        .POST("/remove/user/str", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request, RemoveUserReqDto.class,
                                        tpLiveService::removeUserStr))
                        .POST("/dismiss/room", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request,
                                        DismissRoomReqDto.class, tpLiveService::dismissRoom))
                        .POST("/dismiss/room/str", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request,
                                        DismissRoomReqDto.class, tpLiveService::dismissRoomStr))
                        .POST("/sign", ACCEPT_JSON, (request) -> WebFluxHandler.handler(request,
                                SignReqDto.class, tpLiveService::sign))
                        .build());
    }

    @Bean
    public RouterFunction<ServerResponse> recordingServiceRouter(
            RecordingTaskService recordingTaskService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/recording"),
                RouterFunctions.route().POST("/create/cloud", ACCEPT_JSON,
                        (request) -> WebFluxHandler.handler(request, CreateRecordingReqDto.class,
                                recordingTaskService::createCloudRecording))
                        .POST("/delete/cloud", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request,
                                        DeleteRecordingReqDto.class,
                                        recordingTaskService::deleteCloudRecording))
                        .POST("/describe/cloud", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request,
                                        DescribeRecordingReqDto.class,
                                        recordingTaskService::describeCloudRecording))
                        .POST("/modify/cloud", ACCEPT_JSON,
                                (request) -> WebFluxHandler.handler(request,
                                        UpdateRecordingReqDto.class,
                                        recordingTaskService::modifyCloudRecording))
                        .build());
    }

    @Bean
    public RouterFunction<ServerResponse> imServiceRouter(TpImService tpImService) {
        return RouterFunctions
                .nest(RequestPredicates.path("/tp/im"),
                        RouterFunctions.route()
                                .POST("/create/group", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                CreateGroupReqDto.class, tpImService::createGroup))
                                .POST("/delete/group", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                DeleteGroupReqDto.class, tpImService::deleteGroup))
                                .POST("/online/number", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                ImOnlineNumberReqDto.class,
                                                tpImService::selectOnlineNumber))
                                .POST("/sign", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                ImSignReqDto.class, tpImService::sign))
                                .POST("/send/msg/all", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                ImSendMsgAllReqDto.class, tpImService::sendMsgAll))
                                .POST("/send/msg", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                ImSendMsgReqDto.class, tpImService::sendMsg))
                                .POST("/send/msg/system", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                ImSendMsgSystemReqDto.class,
                                                tpImService::sendMsgSystem))
                                .POST("/tencent/callback", ACCEPT_JSON,
                                        (request) -> WebFluxHandler.handler(request,
                                                ImCallbackReqDto.class,
                                                tpImService::tencentCallback))
                                .build());
    }


    private Map<String, String> decodeUrlEncodedParams(String urlEncodedString) {
        // 使用Java的URLDecoder解码URL编码的字符串
        String decodedString = URLDecoder.decode(urlEncodedString, StandardCharsets.UTF_8);

        // 将解码后的字符串转换为Map
        return Arrays.stream(decodedString.split("&")).map(s -> s.split("=")).collect(
                Collectors.toMap(split -> split[0], split -> split.length > 1 ? split[1] : ""));
    }

    private Map<String, String[]> parametersToMap(MultiValueMap<String, String> multiValueMap) {
        Map<String, String[]> paramMap = new HashMap<>();
        multiValueMap.forEach((key, values) -> paramMap.put(key, values.toArray(new String[0])));
        return paramMap;
    }

    private Map<String, String> handleParams(Map<String, String[]> requestParams) {
        Map<String, String> handleMap = new HashMap<>(requestParams.size());
        for (Map.Entry<String, String[]> entry : requestParams.entrySet()) {
            String key = entry.getKey();
            String[] value = entry.getValue();
            handleMap.put(key, String.join(",", value));
        }
        return handleMap;
    }
}

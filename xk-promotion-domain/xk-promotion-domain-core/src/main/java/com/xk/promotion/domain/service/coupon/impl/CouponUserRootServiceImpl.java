package com.xk.promotion.domain.service.coupon.impl;

import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootRepository;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.promotion.domain.service.coupon.CouponUserRootService;
import com.xk.promotion.domain.support.CouponSequenceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponUserRootServiceImpl implements CouponUserRootService {

    private final IdentifierGenerateService identifierGenerateService;
    private final CouponRootRepository couponRootRepository;
    private final CouponUserRootRepository couponUserRootRepository;

    @Override
    public Mono<Long> generateId() {
        IdentifierRoot identifierRoot =
                IdentifierRoot.builder().identifier(CouponSequenceEnum.P_COUPON_USER)
                        .type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) identifierGenerateService.generateIdentifier(identifierRoot));
    }

    @Override
    public Mono<Long> generateCouponGoodsId() {
        IdentifierRoot identifierRoot =
                IdentifierRoot.builder().identifier(CouponSequenceEnum.P_COUPON_GOODS)
                        .type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) identifierGenerateService.generateIdentifier(identifierRoot));
    }

    @Override
    public Mono<CouponIdentifier> getCouponExpireCache(CouponRoot root) {
        return couponRootRepository.getCouponExpireCache(root);
    }

    @Override
    public Mono<Void> deleteCancelQueue(CouponRoot root) {
        return couponRootRepository.deleteCancelQueue(root);
    }

    @Override
    public Mono<Integer> updateCouponUserOverdue(CouponUserRoot couponUserRoot) {
        return couponUserRootRepository.updateCouponUserOverdue(couponUserRoot);
    }

    @Override
    public Mono<CouponUserIdentifier> getCouponUserExpireCache(CouponUserRoot couponUserRoot) {
        return couponUserRootRepository.getCouponUserExpireCache(couponUserRoot);
    }

    @Override
    public Mono<Void> deleteUserCancelQueue(CouponUserRoot couponUserRoot) {
        return couponUserRootRepository.deleteUserCancelQueue(couponUserRoot);
    }

    @Override
    public Mono<Void> saveCouponGoods(CouponUserRoot couponUserRoot) {
        return couponUserRootRepository.save(couponUserRoot);
    }

    @Override
    public Mono<Void> deleteUserCancelData(CouponUserRoot couponUserRoot) {
        return couponUserRootRepository.deleteUserCancelData(couponUserRoot);
    }
}

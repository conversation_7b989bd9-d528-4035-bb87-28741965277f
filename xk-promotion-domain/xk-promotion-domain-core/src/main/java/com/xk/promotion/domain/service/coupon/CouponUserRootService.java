package com.xk.promotion.domain.service.coupon;

import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface CouponUserRootService {
    Mono<Long> generateId();

    Mono<Long> generateCouponGoodsId();

    Mono<CouponIdentifier> getCouponExpireCache(CouponRoot root);

    Mono<Void> deleteCancelQueue(CouponRoot root);

    Mono<Integer> updateCouponUserOverdue(CouponUserRoot couponUserRoot);

    Mono<CouponUserIdentifier> getCouponUserExpireCache(CouponUserRoot couponUserRoot);

    Mono<Void> deleteUserCancelQueue(CouponUserRoot couponUserRoot);

    Mono<Void> saveCouponGoods(CouponUserRoot couponUserRoot);

    Mono<Void> deleteUserCancelData(CouponUserRoot couponUserRoot);
}

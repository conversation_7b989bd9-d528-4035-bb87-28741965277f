package com.xk.promotion.domain.service.exchange.impl;

import java.security.SecureRandom;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.promotion.domain.service.exchange.ExchangeRootService;
import com.xk.promotion.domain.support.CouponSequenceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExchangeRootServiceImpl implements ExchangeRootService {

    private final IdentifierGenerateService identifierGenerateService;

    private static final String CODE_PREFIX = "YHQ_";

    private static final String CHARACTERS =
            "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int CODE_LENGTH = 12;
    private static final SecureRandom RANDOM = new SecureRandom();

    @Override
    public Mono<Long> generateId() {
        IdentifierRoot identifierRoot =
                IdentifierRoot.builder().identifier(CouponSequenceEnum.P_EXCHANGE)
                        .type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) identifierGenerateService.generateIdentifier(identifierRoot));
    }

    @Override
    public Mono<Long> generateCodeId() {
        IdentifierRoot identifierRoot =
                IdentifierRoot.builder().identifier(CouponSequenceEnum.P_EXCHANGE_CODE)
                        .type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) identifierGenerateService.generateIdentifier(identifierRoot));
    }

    @Override
    public Mono<Long> generateRecordId() {
        IdentifierRoot identifierRoot =
                IdentifierRoot.builder().identifier(CouponSequenceEnum.P_EXCHANGE_RECORD)
                        .type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) identifierGenerateService.generateIdentifier(identifierRoot));
    }

    @Override
    public synchronized Mono<String> generateUniqueCode() {
        StringBuilder sb = new StringBuilder(CODE_PREFIX);
        for (int i = 0; i < CODE_LENGTH; i++) {
            sb.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return Mono.just(sb.toString());
    }
}

package com.xk.promotion.domain.repository.user;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.promotion.domain.model.user.CouponUserRoot;

import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface CouponUserRootRepository extends IRepository<CouponUserRoot> {

    Mono<Void> addCouponUserExpireCache(CouponUserRoot root);

    Mono<Integer> updateCouponUserOverdue(CouponUserRoot couponUserRoot);

    Mono<CouponUserIdentifier> getCouponUserExpireCache(CouponUserRoot couponUserRoot);

    Mono<Void> deleteUserCancelQueue(CouponUserRoot couponUserRoot);

    Mono<Void> deleteUserCancelData(CouponUserRoot couponUserRoot);

    Mono<Void> updateByCouponId(CouponUserRoot couponUserRoot);
}

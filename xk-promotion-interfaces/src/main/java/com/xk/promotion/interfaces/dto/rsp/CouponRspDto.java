package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRspDto {

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券金额
     */
    private Long discountAmount;

    /**
     * 使用说明
     */
    private String instruction;

    /**
     * 每人领取上限
     */
    private Integer personNum;

    /**
     * 优惠券所属平台 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 领取后有效期：单位天
     */
    private Integer periodNum;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;

    /**
     * 门槛金额
     */
    private Long thresholdAmount;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    private Integer scopeType;

    /**
     * 公共兑换码
     */
    private String commonCode;

    /**
     * 公共兑换码id
     */
    private Long commonCodeId;

    /**
     * 公共兑换码状态
     */
    private Integer commonCodeStatus;

    /**
     * 总数量
     */
    private Integer totalNum;

    /**
     * 已领取数量
     */
    private Integer receivedNum;

    /**
     * 库存数量
     */
    private Integer stockNum;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

    /**
     * 领券中心展示时间
     */
    private Date showCenterDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态（0：正常，1：禁用）
     */
    private Integer status;

    /**
     * 是否已达领取上限
     */
    private Integer isReceivedMax;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 商家头像
     */
    private String corpLogo;


    /**
     * 是否使用：0未使用；1已使用
     */
    private Integer usedStatus;

    /**
     * 审核状态（1：待审核，2：审核通过；3审核拒绝）
     */
    private Integer auditStatus;


}

package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponCommonCodeRspDto {

    /**
     * 公共兑换码
     */
    private String commonCode;

    /**
     * 兑换码状态：0正常；1禁用；
     */
    private Integer status;

    /**
     * 优惠券兑换码Id
     */
    private Long exchangeCodeId;
}

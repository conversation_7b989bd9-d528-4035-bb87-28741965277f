package com.xk.acct.application.action.query.follow;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.acct.domain.model.follow.UserFollowCorpEntity;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryInnerReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers(value = {
        @AutoMapper(target = UserFollowCorpQueryReqDto.class, convertGenerate = false),
        @AutoMapper(target = UserFollowCorpQueryInnerReqDto.class, convertGenerate = false),
        @AutoMapper(target = UserFollowCorpEntity.class, convertGenerate = false)
})
public class UserFollowCorpQuery extends PagerQuery implements IActionQuery {

    private Long userId;

    private Long corpInfoId;

    private String userNick;

}

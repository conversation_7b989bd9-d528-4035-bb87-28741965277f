package com.xk.acct.application.query.follow;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryInnerReqDto;
import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.object.user.UserObjectIdentifier;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.acct.application.action.query.follow.*;
import com.xk.acct.interfaces.dto.req.follow.CorpFollowQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.SelectByUserQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryReqDto;
import com.xk.acct.interfaces.dto.req.user.UserFollowCorpReqDto;
import com.xk.acct.interfaces.dto.rsp.follow.CorpFollowNumberRspDtp;
import com.xk.acct.interfaces.dto.rsp.follow.UserCorpsAllRspDtp;
import com.xk.acct.interfaces.dto.rsp.follow.UserIsFollowCorpRspDto;
import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import com.xk.auth.enums.YesOrNotEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserFollowCorpQueryServiceImpl implements UserFollowCorpQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;

    @BusiCode
    @Override
    public Mono<Pagination> list(Mono<UserFollowCorpQueryReqDto> dtoMono) {
        return actionQueryDispatcher.executeQuery(dtoMono, UserFollowCorpQuery.class, Pagination.class);
    }

    @Override
    public Mono<Pagination> listInner(Mono<UserFollowCorpQueryInnerReqDto> dtoMono) {
        return actionQueryDispatcher.executeQuery(dtoMono, UserFollowCorpQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<CorpFollowNumberRspDtp> followNumber(Mono<CorpFollowQueryReqDto> dtoMono) {
        return actionQueryDispatcher.executeQuery(dtoMono, CorpFollowQuery.class, CorpFollowNumberRspDtp.class);
    }

    @BusiCode
    @Override
    public Mono<Set<Long>> userCorps(Mono<SelectByUserQueryReqDto> dtoMono) {
        return actionQueryManyDispatcher.executeQuery(dtoMono, SelectByUserFollowQuery.class, Long.class).collect(Collectors.toSet());
    }

    @BusiCode
    @Override
    public Mono<UserIsFollowCorpRspDto> followStatus(Mono<UserFollowCorpReqDto> dtoMono) {
        return dtoMono.flatMap(reqDto ->
        ReadSynchronizationUtils.getUserObjectMono(false).switchIfEmpty(Mono.just(UserObjectRoot
                .builder().identifier(UserObjectIdentifier.builder().userId(-1L).build())
                .userDataObjectEntity(UserDataObjectEntity.builder()
                        .identifier(UserObjectIdentifier.builder().userId(-1L).build()).build())
                .build())).flatMap(userObjectRoot -> {
                    if (userObjectRoot.getIdentifier().userId() == -1L) {
                        return Mono.empty();
                    }
                            UserCorpFollowStatusQuery query = UserCorpFollowStatusQuery.builder()
                                    .userId(userObjectRoot.getIdentifier().userId())
                                    .corpInfoId(reqDto.getCorpInfoId())
                                    .build();

                            return actionQueryDispatcher.executeQuery(Mono.just(query), UserCorpFollowStatusQuery.class, Double.class)
                                    // 判断流中是否有元素，返回 Mono<Boolean>
                                    .hasElement()
                                    .map(isFollow -> UserIsFollowCorpRspDto.builder()
                                            .isFollow(isFollow ? YesOrNotEnum.YES.getCode() : YesOrNotEnum.NOT.getCode())
                                            .corpInfoId(reqDto.getCorpInfoId())
                                            .userId(userObjectRoot.getIdentifier().userId())
                                            .build());
                        })
        );
    }

    @BusiCode
    @Override
    public Mono<List<UserCorpsAllRspDtp>> userCorpsAll(Mono<RequireSessionDto> dtoMono) {
        return actionQueryManyDispatcher.executeQuery(dtoMono, UserFollowCorpNoPagerQuery.class, UserCorpsAllRspDtp.class).collectList();
    }

}

package com.xk.acct.application.action.command.user;

import java.util.Date;

import com.xk.acct.enums.user.UserRegisterChannelEnum;
import com.xk.acct.interfaces.dto.req.user.AuthLoginNormalReqDto;
import org.springframework.cglib.beans.BeanCopier;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.commons.util.BeanHelper;
import com.myco.mydata.commons.util.JodaTimeUtil;
import com.myco.mydata.commons.util.RandomHelper;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.user.AccountStatusEnum;
import com.myco.mydata.domain.model.user.LoginTypeEnum;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.xk.acct.domain.model.user.*;
import com.xk.acct.enums.user.IdentifyBindStatusEnum;
import com.xk.acct.enums.user.RegisterTypeEnum;
import com.xk.acct.enums.user.UserDataPwdUpdateStatusEnum;
import com.xk.acct.interfaces.dto.req.user.RegLoginNormalReqDto;
import com.xk.acct.interfaces.dto.req.user.UserRegisterReqDto;
import com.xk.enums.common.CommonStatusEnum;

import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegisterCommand extends AbstractUserCommand {

    /**
     * 用户id
     */
    private LongIdentifier identifier;
    /**
     * 用户数据
     */
    private UserDataEntity userData;

    /**
     * 用户注册
     */
    private UserRegisterEntity userRegister;

    /**
     *
     */
    private UserSecurityEntity userSecurity;

    /**
     * 创建用户登录日志
     */
    private UserLoginLogEntity userLoginLog;

    public void createUserRegisterCommand(RegLoginNormalReqDto regLoginNormalReqDto, Long userId, Long userLoginLogId) {
        this.setIdentifier(LongIdentifier.builder().id(userId).build());
        this.createUserDataEntityInstance(regLoginNormalReqDto);
        this.createUserSecurityEntityInstance(regLoginNormalReqDto);
        this.createUserRegisterEntityInstance(regLoginNormalReqDto);
        this.createUserLoginLogEntityInstance(regLoginNormalReqDto,
                userLoginLogId);
    }

    public void createUserRegisterCommand(AuthLoginNormalReqDto authLoginNormalReqDto, Long userId, Long userLoginLogId) {
        this.setIdentifier(LongIdentifier.builder().id(userId).build());
        this.createUserDataEntityInstance(authLoginNormalReqDto);
        this.createUserSecurityEntityInstance(authLoginNormalReqDto);
        this.createUserRegisterEntityInstance(authLoginNormalReqDto);
        this.createUserLoginLogEntityInstance(authLoginNormalReqDto,
                userLoginLogId);
    }
    /**
     * 创建用户数据
     *
     * @param dto dto
     */
    public void createUserDataEntityInstance(RegLoginNormalReqDto dto) {
        userData = UserDataEntity.builder().build();
        userData.setUserId(identifier.getIdentifier().id());
        BeanCopier copier = BeanCopier.create(RegLoginNormalReqDto.class, UserDataEntity.class, false);
        copier.copy(dto, userData, null);
        RegisterTypeEnum registerType = RegisterTypeEnum.valueOf(dto.getLoginType());
        userData.setRegisterType(registerType);
        userData.setChannelId(dto.getChannelId());
        userData.setAccountPlatformId(dto.getLoginType());
        userData.setStatus(AccountStatusEnum.ACTIVE);
        if (PlatformTypeEnum.PC_BOSS_OMS.equals(dto.getPlatformType())) {
            userData.setUserType(UserTypeEnum.MANAGER);
        } else if (PlatformTypeEnum.PC_COMPANY_OMS.equals(dto.getPlatformType())) {
            userData.setUserType(UserTypeEnum.HS);
        } else if (PlatformTypeEnum.PC_USER_WEB.equals(dto.getPlatformType())) {
            userData.setUserType(UserTypeEnum.USER);
        } else {
            userData.setUserType(UserTypeEnum.USER);
        }
        userData.setCorpApplyStatus(CommonStatusEnum.ENABLE);
        Date date = JodaTimeUtil.getCurrentDateTime();
        userData.setLastLoginTime(date);
        userData.setLastUpdateTime(date);
        if (dto.getMobile() != null) {
            userData.setMobile(dto.getMobile());
            userData.setEmailStatus(IdentifyBindStatusEnum.UNBIND);
        }
        if (dto.getEmail() != null) {
            userData.setEmail(dto.getEmail());
            userData.setEmailStatus(IdentifyBindStatusEnum.UNBIND);
        }
        switch (registerType) {
            case ACCOUNT:
                userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.UPDATE);
                userData.setLoginName(dto.getIdentificationNumber());
                break;
            case EMAIL:
                userData.setEmail(dto.getIdentificationNumber());
                userData.setEmailStatus(IdentifyBindStatusEnum.BIND);
                userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.DEFAULT);
                break;
            case MOBILE:
                userData.setMobile(dto.getIdentificationNumber());
                userData.setMobileStatus(IdentifyBindStatusEnum.BIND);
                userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.DEFAULT);
                break;
            default:
        }
        userData.setLastPlatformType(dto.getPlatformType());
        userData.setCountry(dto.getIpCountry());
        userData.setCity(dto.getIpCity());

    }

    public void createUserDataEntityInstance(UserRegisterReqDto dto) {
        userData = UserDataEntity.builder().build();
        userData.setUserId(identifier.getIdentifier().id());
        BeanCopier copier = BeanCopier.create(UserRegisterReqDto.class, UserDataEntity.class, false);
        copier.copy(dto, userData, null);
        RegisterTypeEnum registerType = RegisterTypeEnum.valueOf(dto.getRegisterType());
        userData.setRegisterType(registerType);
        userData.setAccountPlatformId(dto.getRegisterType());
        userData.setStatus(AccountStatusEnum.ACTIVE);
        if (PlatformTypeEnum.PC_BOSS_OMS.equals(dto.getPlatformType())) {
            userData.setUserType(UserTypeEnum.MANAGER);
        } else if (PlatformTypeEnum.PC_COMPANY_OMS.equals(dto.getPlatformType())) {
            userData.setUserType(UserTypeEnum.HS);
        } else if (PlatformTypeEnum.PC_USER_WEB.equals(dto.getPlatformType())) {
            userData.setUserType(UserTypeEnum.USER);
        } else {
            userData.setUserType(UserTypeEnum.USER);
        }
        Date date = JodaTimeUtil.getCurrentDateTime();
        userData.setLastLoginTime(date);
        userData.setLastUpdateTime(date);
        if (dto.getMobile() != null) {
            userData.setMobile(dto.getMobile());
            userData.setEmailStatus(IdentifyBindStatusEnum.UNBIND);
        }
        if (dto.getEmail() != null) {
            userData.setEmail(dto.getEmail());
            userData.setEmailStatus(IdentifyBindStatusEnum.UNBIND);
        }
        switch (registerType) {
            case ACCOUNT:
                userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.UPDATE);
                userData.setLoginName(dto.getIdentificationNumber());
                break;
            case EMAIL:
                userData.setEmail(dto.getIdentificationNumber());
                userData.setEmailStatus(IdentifyBindStatusEnum.BIND);
                userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.DEFAULT);
                break;
            case MOBILE:
                userData.setMobile(dto.getIdentificationNumber());
                userData.setMobileStatus(IdentifyBindStatusEnum.BIND);
                userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.DEFAULT);
                break;
            default:
        }
        userData.setLastPlatformType(dto.getPlatformType());
        userData.setCountry(dto.getIpCountry());
        userData.setCity(dto.getIpCity());

    }

    public void createUserDataEntityInstance(AuthLoginNormalReqDto dto) {
        userData = UserDataEntity.builder().build();
        userData.setUserId(identifier.getIdentifier().id());
        BeanCopier copier = BeanCopier.create(AuthLoginNormalReqDto.class, UserDataEntity.class, false);
        copier.copy(dto, userData, null);
        userData.setRegisterType(RegisterTypeEnum.THIRD);
        userData.setChannelId(UserRegisterChannelEnum.OFFICIAL);
        userData.setAccountPlatformId("HOME");
        userData.setStatus(AccountStatusEnum.ACTIVE);
        userData.setUserType(UserTypeEnum.USER);
        userData.setCorpApplyStatus(CommonStatusEnum.ENABLE);
        Date date = JodaTimeUtil.getCurrentDateTime();
        userData.setLastLoginTime(date);
        userData.setLastUpdateTime(date);
        userData.setIsPwdUpdate(UserDataPwdUpdateStatusEnum.DEFAULT);
        userData.setLastPlatformType(dto.getPlatformType());
        userData.setCountry(dto.getIpCountry());
        userData.setCity(dto.getIpCity());
        if (dto.getMobile() != null) {
            userData.setMobile(dto.getMobile());
            userData.setMobileStatus(IdentifyBindStatusEnum.UNBIND);
        }

    }

    /**
     * 创建用户安全信息
     *
     * @param dto dto
     */
    public void createUserSecurityEntityInstance(UserRegisterReqDto dto) {
        userSecurity = new UserSecurityEntity();
        userSecurity.setUserId(identifier.getIdentifier().id());
        userSecurity.setLoginPassword(dto.getLoginPassword());
        dto.setLoginPassword(null);
    }

    /**
     * 创建用户安全信息
     *
     * @param dto dto
     */
    public void createUserSecurityEntityInstance(RegLoginNormalReqDto dto) {
        userSecurity = new UserSecurityEntity();
        userSecurity.setUserId(identifier.getIdentifier().id());
        if (dto.getLoginPassword() == null || dto.getLoginPassword().isEmpty()) {
            userSecurity.setLoginPassword(RandomHelper.genCodeWithCharsAndNumbers());
        } else {
            userSecurity.setLoginPassword(dto.getLoginPassword());
        }
        dto.setLoginPassword(null);
    }
    public void createUserSecurityEntityInstance(AuthLoginNormalReqDto dto) {
        userSecurity = new UserSecurityEntity();
        userSecurity.setUserId(identifier.getIdentifier().id());
        userSecurity.setLoginPassword(RandomHelper.genCodeWithCharsAndNumbers());
    }

    /**
     * 创建用户注册信息
     *
     * @param dto dto
     */
    public void createUserRegisterEntityInstance(UserRegisterReqDto dto) {
        userRegister = new UserRegisterEntity();
        userRegister.setUserId(identifier.getIdentifier().id());
        BeanHelper.copyProperties(dto, userRegister);
    }

    public void createUserRegisterEntityInstance(RegLoginNormalReqDto dto) {
        userRegister = new UserRegisterEntity();
        userRegister.setUserId(identifier.getIdentifier().id());
        userRegister.setChannelId(dto.getChannelId());
        BeanHelper.copyProperties(dto, userRegister);
    }

    public void createUserRegisterEntityInstance(AuthLoginNormalReqDto dto) {
        userRegister = new UserRegisterEntity();
        userRegister.setUserId(identifier.getIdentifier().id());
        userRegister.setChannelId(UserRegisterChannelEnum.OFFICIAL);
        BeanHelper.copyProperties(dto, userRegister);
    }

    /**
     * 创建用户登录日志
     *
     * @param dto dto
     */
    public void createUserLoginLogEntityInstance(UserRegisterReqDto dto) {
        userLoginLog = new UserLoginLogEntity();
        userLoginLog.setUserId(identifier.getIdentifier().id());
        userLoginLog.setSessionId(dto.getSessionId());
        userLoginLog.setAppVersions(dto.getAppVersions());
        userLoginLog.setChannelId(dto.getChannelId().name());
        userLoginLog.setComeFrom(dto.getComeFrom());
        userLoginLog.setCreateTime(new Date());
        userLoginLog.setDeviceNumber(dto.getDeviceNumber());
        userLoginLog.setGps(dto.getGps());
        userLoginLog.setIp(dto.getIp());
        userLoginLog.setIsRobot(dto.getIsRobot());
        userLoginLog.setLoginDetail(dto.getLoginDetail());
        userLoginLog.setMac(dto.getMac());
        userLoginLog.setEmail(dto.getEmail());
        userLoginLog.setMobile(dto.getMobile());
        userLoginLog.setPlatformInfo(dto.getPlatformInfo());
        userLoginLog.setPlatformType(dto.getPlatformType());
        userLoginLog.setTerminalVersions(dto.getTerminalVersions());
        userLoginLog.setVisitorUserId(dto.getCookieUserId());
        userLoginLog.setCountry(dto.getIpCountry());
        userLoginLog.setCity(dto.getIpCity());

        if (!RegisterTypeEnum.THIRD.name().equals(dto.getRegisterType())) {
            userLoginLog.setLoginType(LoginTypeEnum.LOGIN_NORMAL);
        } else {
            userLoginLog.setLoginType(LoginTypeEnum.THIRD_BINDED);
        }
        //更新用户数据
        if (userData != null) {
            userData.setLastLoginTime(userLoginLog.getCreateTime());
            userData.setLastLoginCity(userLoginLog.getCity());
            userData.setLastLoginCountry(userLoginLog.getCountry());
            userData.setLastLoginTerminal(userLoginLog.getTerminalVersions());
        }
    }

    public void createUserLoginLogEntityInstance(RegLoginNormalReqDto dto, Long userLoginLogId) {
        userLoginLog = new UserLoginLogEntity();
        userLoginLog.setLoginSn(userLoginLogId);
        userLoginLog.setUserId(identifier.getIdentifier().id());
        userLoginLog.setSessionId(dto.getSessionId());
        userLoginLog.setAppVersions(dto.getAppVersions());
        userLoginLog.setChannelId(dto.getChannelId() == null ? null : dto.getChannelId().name());
        userLoginLog.setComeFrom(dto.getComeFrom());
        userLoginLog.setCreateTime(new Date());
        userLoginLog.setDeviceNumber(dto.getDeviceNumber());
        userLoginLog.setGps(dto.getGps());
        userLoginLog.setIp(dto.getIp());
        userLoginLog.setIsRobot(dto.getIsRobot());
        userLoginLog.setLoginDetail(dto.getLoginDetail());
        userLoginLog.setMac(dto.getMac());
        userLoginLog.setEmail(dto.getEmail());
        userLoginLog.setMobile(dto.getMobile());
        userLoginLog.setPlatformInfo(dto.getPlatformInfo());
        userLoginLog.setPlatformType(dto.getPlatformType());
        userLoginLog.setTerminalVersions(dto.getTerminalVersions());
        userLoginLog.setVisitorUserId(dto.getCookieUserId());
        userLoginLog.setCountry(dto.getIpCountry());
        userLoginLog.setCity(dto.getIpCity());

        if (!RegisterTypeEnum.THIRD.name().equals(dto.getLoginType())) {
            userLoginLog.setLoginType(LoginTypeEnum.LOGIN_NORMAL);
        } else {
            userLoginLog.setLoginType(LoginTypeEnum.THIRD_BINDED);
        }
        //更新用户数据
        if (userData != null) {
            userData.setLastLoginTime(userLoginLog.getCreateTime());
            userData.setLastLoginCity(userLoginLog.getCity());
            userData.setLastLoginCountry(userLoginLog.getCountry());
            userData.setLastLoginTerminal(userLoginLog.getTerminalVersions());
        }
    }

    public void createUserLoginLogEntityInstance(AuthLoginNormalReqDto dto, Long userLoginLogId) {
        userLoginLog = new UserLoginLogEntity();
        userLoginLog.setLoginSn(userLoginLogId);
        userLoginLog.setUserId(identifier.getIdentifier().id());
        userLoginLog.setSessionId(dto.getSessionId());
        userLoginLog.setAppVersions(dto.getAppVersions());
        userLoginLog.setChannelId(UserRegisterChannelEnum.OFFICIAL.name());
        userLoginLog.setComeFrom(dto.getComeFrom());
        userLoginLog.setCreateTime(new Date());
        userLoginLog.setDeviceNumber(dto.getDeviceNumber());
        userLoginLog.setGps(dto.getGps());
        userLoginLog.setIp(dto.getIp());
        userLoginLog.setIsRobot(dto.getIsRobot());
        userLoginLog.setLoginDetail(dto.getLoginDetail());
        userLoginLog.setMac(dto.getMac());
        userLoginLog.setEmail(dto.getEmail());
        userLoginLog.setMobile(dto.getMobile());
        userLoginLog.setPlatformInfo(dto.getPlatformInfo());
        userLoginLog.setPlatformType(dto.getPlatformType());
        userLoginLog.setTerminalVersions(dto.getTerminalVersions());
        userLoginLog.setVisitorUserId(dto.getCookieUserId());
        userLoginLog.setCountry(dto.getIpCountry());
        userLoginLog.setCity(dto.getIpCity());

        userLoginLog.setLoginType(LoginTypeEnum.THIRD_BINDED);
        //更新用户数据
        if (userData != null) {
            userData.setLastLoginTime(userLoginLog.getCreateTime());
            userData.setLastLoginCity(userLoginLog.getCity());
            userData.setLastLoginCountry(userLoginLog.getCountry());
            userData.setLastLoginTerminal(userLoginLog.getTerminalVersions());
        }
    }
}

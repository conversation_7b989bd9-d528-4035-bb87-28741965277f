package com.xk.acct.application.handler.command.user;


import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.acct.domain.commons.XkAcctSequenceEnum;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.acct.application.action.command.user.CreateUserDeviceCommand;
import com.xk.acct.domain.model.user.UserDeviceEntity;
import com.xk.acct.domain.model.user.UserRoot;
import com.xk.acct.domain.repository.user.UserRootRepository;

import lombok.AllArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@AllArgsConstructor
public class CreateUserDeviceHandler implements IActionCommandHandler<CreateUserDeviceCommand, Void> {

    private final UserRootRepository userRootRepository;
    private final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Void> execute(Mono<CreateUserDeviceCommand> commandMono) {
        return commandMono.flatMap(command -> {
            // 1. 构建用户设备实体
            UserDeviceEntity userDeviceEntity = UserDeviceEntity.builder()
                    .id((Long)identifierGenerateService.generateIdentifier(
                            IdentifierRoot.builder().identifier(XkAcctSequenceEnum.A_USER_DEVICE)
                                    .type(IdentifierGenerateEnum.CACHE).build()))
                    .userId(command.getUserId())
                    .deviceId(command.getDeviceId())
                    .appVersion(command.getAppVersion())
                    .platformType(PlatformTypeEnum.getByValue(command.getPlatformType()))
                    .build();

            // 2. 构建用户聚合根
            UserRoot userRoot = UserRoot.builder()
                    .identifier(LongIdentifier.builder().id(command.getUserId()).build())
                    .userDevice(userDeviceEntity)
                    .build();

            // 3. 创建用户设备
            return userRootRepository.saveUserDevice(userRoot);
        }).then();

    }
}

package com.xk.acct.application.service;

import static com.xk.acct.application.commons.XkAcctApplicationErrorEnum.ACCOUNT_PASSWORD_ERROR;
import static com.xk.acct.application.commons.XkAcctApplicationErrorEnum.APPLICATION_ERROR;

import java.util.Date;
import java.util.function.*;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.object.user.UserObjectIdentifier;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.xk.acct.domain.model.user.UserBindaccountEntity;
import com.xk.acct.interfaces.dto.req.user.*;
import com.xk.tp.interfaces.dto.req.userauth.AuthUserInfoReqDto;
import com.xk.tp.interfaces.dto.res.userauth.AuthUserInfoResDto;
import com.xk.tp.interfaces.query.userauth.UserAuthQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.util.DateHelper;
import com.myco.mydata.commons.util.DesensitizationUtils;
import com.myco.mydata.commons.util.JodaTimeUtil;
import com.myco.mydata.commons.util.StringHelper;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.model.session.Session;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.model.user.AccountStatusEnum;
import com.myco.mydata.domain.model.user.LoginTypeEnum;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.CacheObjectModifierService;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.domain.service.session.SessionRootDomainService;
import com.myco.mydata.interfaces.dto.commons.id.SelectId;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.acct.application.action.command.session.BindSessionCommand;
import com.xk.acct.application.action.command.session.UnbindSessionCommand;
import com.xk.acct.application.action.command.user.DeleteUserDataCommand;
import com.xk.acct.application.action.command.user.EditUserDataCommand;
import com.xk.acct.application.action.command.user.UserRegisterCommand;
import com.xk.acct.application.action.query.user.UserDataByIdentificationNumberQuery;
import com.xk.acct.application.action.query.user.UserDataIdQuery;
import com.xk.acct.application.commons.XkAcctApplicationErrorEnum;
import com.xk.acct.application.support.XkAcctApplicationException;
import com.xk.acct.domain.commons.AcctDictEnum;
import com.xk.acct.domain.event.user.DeletedUserEvent;
import com.xk.acct.domain.event.user.UserLoginEvent;
import com.xk.acct.domain.event.user.UserRegisterEvent;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.acct.domain.model.user.UserLoginLogEntity;
import com.xk.acct.domain.model.user.UserRoot;
import com.xk.acct.domain.service.user.UserRootDomainService;
import com.xk.acct.enums.user.RegisterTypeEnum;
import com.xk.acct.interfaces.dto.req.user.*;
import com.xk.acct.interfaces.dto.rsp.user.SendAuthCodeRspDto;
import com.xk.acct.interfaces.service.UserAcctService;
import com.xk.auth.interfaces.service.auth.userrole.UserRoleService;
import com.xk.corp.interfaces.dto.res.corp.CorpInfoIdentifierResDto;
import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.interfaces.dto.req.user.UserIdReqDto;
import com.xk.message.enums.validate.IdentifyTypeEnum;
import com.xk.message.enums.validate.ValidateCodeBusinessContentTypeEnum;
import com.xk.message.enums.validate.ValidateCodeBusinessTypeEnum;
import com.xk.message.interfaces.dto.req.validate.CheckValidateCodeReqDto;
import com.xk.message.interfaces.dto.req.validate.SendValidateCodeReqDto;
import com.xk.message.interfaces.query.validate.ValidateCodeQueryService;
import com.xk.message.interfaces.service.validate.ValidateCodeService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAcctServiceImpl implements UserAcctService {

    private final UserRootDomainService userRootDomainService;

    private final SessionRootDomainService sessionRootDomainService;

    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final ValidateCodeQueryService validateCodeQueryService;

    private final LockRootService lockRootService;

    private final UserRoleService userRoleService;

    private final ValidateCodeService validateCodeService;

    private final DictObjectDomainService dictObjectService;

    private final CorpQueryService corpQueryService;

    private final CacheObjectModifierService cacheObjectModifierService;

    private final SelectorRootService selectorRootService;

    private final UserAuthQueryService userAuthQueryService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }


    @BusiCode
    @Override
    public Mono<Void> test(Mono<SelectId> selectIdMono) {
        return selectorRootService.getCorpObject(32L).switchIfEmpty(Mono.defer(() -> {
            log.info("123");
            return Mono.empty();
        })).flatMap(corpObjectRoot -> {
            return lockRootService
                    .acquireTransactionObjectLockMono(ZookeeperLockObject.LOCKS_CORP, 32L)
                    .flatMap(aBoolean -> {
                        corpObjectRoot.getCorpInfoObjectEntity().setCorpName("456");

                        return cacheObjectModifierService.rebindCorpObjectMono(corpObjectRoot);
                    })

                    .then();

        });
    }

    @BusiCode
    @Override
    public Mono<SendAuthCodeRspDto> sendAuthCodeByAcct(Mono<SendAuthCodeReqDto> mono) {
        return mono.flatMap(dto -> {
            // 发送验证码
            Function<String, Mono<String>> sendVerificationCodeFunction = idNumber -> {
                SendValidateCodeReqDto reqDto = new SendValidateCodeReqDto();
                reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                reqDto.setBusinessType(BusinessTypeEnum.XING_KA.getValue());
                reqDto.setValidateCodeBusinessType(
                        ValidateCodeBusinessTypeEnum.LOGIN_VALID.getCode());
                reqDto.setIdentifyType(dto.getIdentifyType().getCode());
                reqDto.setValidateCodeBusinessContentType(
                        ValidateCodeBusinessContentTypeEnum.NUMBERS.getCode());
                reqDto.setLanguage(dto.getLanguage().name());
                reqDto.setIdentifyCode(idNumber);
                reqDto.setMobileCode(dto.getMobileCode());
                return validateCodeService.sendValidateCode(Mono.just(reqDto)).thenReturn(idNumber);
            };
            // 获取发送手机号
            Function<UserDataEntity, Mono<String>> getSendMobile = userData -> {
                if (UserTypeEnum.MANAGER.equals(userData.getUserType())) {
                    return dictObjectService.getSystemConfigValue(AcctDictEnum.SYS_MANAGER_PHONE);
                } else if (UserTypeEnum.MERCHANT_KAS.equals(userData.getUserType())) {
                    UserIdReqDto userIdReqDto = new UserIdReqDto();
                    userIdReqDto.setUserId(userData.getUserId());
                    return corpQueryService.findByUserId(Mono.just(userIdReqDto)).filter(
                            v -> CommonStatusEnum.ENABLE.getCode().equals(v.getCorpStatus()))
                            .map(CorpInfoIdentifierResDto::getContactPhone)
                            .switchIfEmpty(Mono.error(new XkAcctApplicationException(
                                    SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
                } else {
                    return Mono.error(new XkAcctApplicationException(
                            XkAcctApplicationErrorEnum.USER_NOT_ALLOW_VALIDATE));
                }
            };
            // 登录
            return actionQueryDispatcher
                    .executeQuery(dto, UserDataByIdentificationNumberQuery.class,
                            UserDataEntity.class)
                    .flatMap(userData -> userRootDomainService
                            .checkPassword(userData.getIdentifier(), dto.getLoginPassword())
                            .thenReturn(userData))
                    .switchIfEmpty(
                            Mono.error(new XkAcctApplicationException(ACCOUNT_PASSWORD_ERROR)))
                    .flatMap(
                            userData -> Mono
                                    .when(userData.checkUserTypeAndPlatformType(
                                            dto.getPlatformType()), userData.checkUserStatus())
                                    .thenReturn(userData))
                    .flatMap(getSendMobile).flatMap(sendVerificationCodeFunction)
                    .map(mobile -> SendAuthCodeRspDto.builder()
                            .identificationNumber(DesensitizationUtils.desensitizePhone(mobile))
                            .build());
        });
    }

    @BusiCode
    @Override
    public Mono<Session> saveLoginNormal(Mono<LoginNormalReqDto> mono) {
        return mono.flatMap(dto -> {
            // 验证密码
            Function<UserDataEntity, Mono<UserDataEntity>> checkPassword =
                    userData -> userRootDomainService
                            .checkPassword(userData.getIdentifier(), dto.getLoginPassword())
                            .thenReturn(userData);

            // 获取用户商户信息
            LongFunction<Mono<CorpInfoIdentifierResDto>> getCorpInfoByUserIdFun = userId -> {
                UserIdReqDto userIdReqDto = new UserIdReqDto();
                userIdReqDto.setUserId(userId);
                return corpQueryService.findByUserId(Mono.just(userIdReqDto))
                        .filter(corpInfoIdentifierResDto -> CommonStatusEnum.ENABLE.getCode()
                                .equals(corpInfoIdentifierResDto.getCorpStatus()))
                        .switchIfEmpty(Mono.error(new XkAcctApplicationException(
                                SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
            };
            // 获取发送手机号
            Function<UserDataEntity, Mono<String>> getSendMobile = userData -> {
                if (UserTypeEnum.MANAGER.equals(userData.getUserType())) {
                    return dictObjectService.getSystemConfigValue(AcctDictEnum.SYS_MANAGER_PHONE);
                } else if (UserTypeEnum.MERCHANT_KAS.equals(userData.getUserType())) {
                    return getCorpInfoByUserIdFun.apply(userData.getUserId())
                            .map(CorpInfoIdentifierResDto::getContactPhone);
                } else {
                    return Mono.error(
                            new XkAcctApplicationException(SystemErrorEnum.UNSUPPORTED_OPERATION));
                }
            };
            // 校验 验证码
            Function<String, Mono<Void>> doCheckValidateCode = identificationNumber -> {
                if (StringHelper.isEmpty(dto.getAuthCode())) {
                    return Mono.error(new XkAcctApplicationException(
                            XkAcctApplicationErrorEnum.ACCOUNT_AUTH_CODE_ERROR));
                }
                CheckValidateCodeReqDto checkValidateReqDto = new CheckValidateCodeReqDto();
                checkValidateReqDto.create(dto.getAuthCode(), BusinessTypeEnum.XING_KA,
                        ValidateCodeBusinessTypeEnum.LOGIN_VALID, IdentifyTypeEnum.MOBILE,
                        identificationNumber, dto.getMobileCode());
                return validateCodeQueryService.checkValidateCode(Mono.just(checkValidateReqDto));
            };
            // 登录检查
            Function<UserDataEntity, Mono<UserDataEntity>> isCheckValidateCode =
                    userData -> userRootDomainService
                            .isValidateAuthCode(Mono.just(UserLoginLogEntity.builder()
                                    .loginSn(userData.getLastMrgLoginLogId())
                                    .createTime(JodaTimeUtil.getCurrentDateTime()).ip(dto.getIp())
                                    .deviceNumber(dto.getDeviceNumber())
                                    .platformType(dto.getPlatformType())
                                    .platformInfo(dto.getPlatformInfo()).mobile(dto.getMobile())
                                    .userId(dto.getCookieUserId()).mac(dto.getMac())
                                    .terminalVersions(dto.getTerminalVersions()).build()))
                            .flatMap(s -> {
                                if (Boolean.TRUE.equals(s)) {
                                    return getSendMobile.apply(userData)
                                            .flatMap(doCheckValidateCode);
                                }
                                return Mono.empty();
                            }).thenReturn(userData);
            // 校验状态和类型
            Function<UserDataEntity, Mono<UserDataEntity>> doCheckStatusAndType = userData -> Mono
                    .when(userData.checkUserTypeAndPlatformType(dto.getPlatformType()),
                            userData.checkUserStatus())
                    .thenReturn(userData);
            // 绑定session
            BiFunction<UserDataEntity, Long, Mono<Session>> bindSession =
                    (userData, loginLogId) -> {
                        BindSessionCommand bindSessionCommand = new BindSessionCommand();
                        bindSessionCommand.regLogBindSessionCommand(dto, userData, loginLogId);
                        dto.setLoginPassword(null);
                        if (UserTypeEnum.MERCHANT_KAS.equals(userData.getUserType())) {
                            return getCorpInfoByUserIdFun.apply(userData.getUserId())
                                    .flatMap(corpInfo -> {
                                        bindSessionCommand.setCorpId(corpInfo.getCorpInfoId());
                                        bindSessionCommand.setIsCorpAdmin(userData.getUserId()
                                                .equals(corpInfo.getAdminUserId()));
                                        return actionCommandDispatcher
                                                .executeCommand(Mono.just(bindSessionCommand),
                                                        BindSessionCommand.class, Session.class)
                                                .flatMap(session -> cacheObjectModifierService
                                                        .markUserObjectUpdatedMono(
                                                                bindSessionCommand.getUserId())
                                                        .thenReturn(session));
                                    });
                        }
                        return actionCommandDispatcher
                                .executeCommand(Mono.just(bindSessionCommand),
                                        BindSessionCommand.class, Session.class)
                                .flatMap(session -> cacheObjectModifierService
                                        .markUserObjectUpdatedMono(bindSessionCommand.getUserId())
                                        .thenReturn(session));
                    };
            // 创建事件
            BiFunction<UserDataEntity, Long, Mono<UserLoginEvent>> createEvent = (userData,
                    loginLogId) -> Mono.just(UserLoginEvent.builder()
                            .identifier(
                                    EventRoot.getCommonsDomainEventIdentifier(UserLoginEvent.class))
                            .userId(userData.getUserId())
                            .loginPlatformType(dto.getPlatformType() == null ? null
                                    : dto.getPlatformType().getValue())
                            .loginTime(JodaTimeUtil.getCurrentDateTime())
                            .sessionId(dto.getSessionId()).mobile(userData.getMobile())
                            .loginDetail(dto.getLoginDetail()).isRobot(dto.getIsRobot())
                            .mac(dto.getMac()).comeFrom(dto.getComeFrom())
                            .deviceNumber(dto.getDeviceNumber()).email(userData.getEmail())
                            .gps(dto.getGps()).appVersions(dto.getAppVersions())
                            .terminalVersions(dto.getTerminalVersions())
                            .platformInfo(dto.getPlatformInfo()).channelId(dto.getChannelId())
                            .tokenId(dto.getSessionId()).ip(dto.getIp()).ipCity(dto.getIpCity())
                            .ipCountry(dto.getIpCountry()).loginSn(loginLogId)
                            .createTime(DateHelper.now()).loginType(LoginTypeEnum.IDENTIFY.name())
                            .visitorUserId(dto.getCookieUserId())
                            .platformType(dto.getPlatformType().name()).country(dto.getIpCountry())
                            .city(dto.getIpCity()).build());
            // 发送登录事件
            Function<UserLoginEvent, Mono<Void>> userLoginEvent = event -> eventRootService
                    .publisheByMono(EventRoot.builder().isQueue(true).domainEvent(event).build())
                    .filter(s -> !s).flatMap(s -> Mono.error(
                            new ApplicationWrapperThrowable(SystemErrorEnum.PRODUCER_SEND_ERROR)));

            Function<UserDataEntity, Mono<UserDataEntity>> checkIfDelete =
                    userDataEntity -> dictObjectService
                            .getSystemConfigToInt(AcctDictEnum.DELETE_DELAY_DAYS)
                            .flatMap(userDataEntity::checkIfDelete);

            // 登录
            return actionQueryDispatcher
                    .executeQuery(dto, UserDataByIdentificationNumberQuery.class,
                            UserDataEntity.class)
                    .flatMap(checkIfDelete).flatMap(checkPassword)
                    .switchIfEmpty(
                            Mono.error(new XkAcctApplicationException(ACCOUNT_PASSWORD_ERROR)))
                    .flatMap(isCheckValidateCode).flatMap(doCheckStatusAndType)
                    .flatMap(userData -> userRootDomainService.generateUserLoginLogId()
                            .flatMap(loginLogId -> createEvent.apply(userData, loginLogId)
                                    .flatMap(userLoginEvent)
                                    .then(bindSession.apply(userData, loginLogId))));
        });
    }

    @BusiCode
    @Override
    public Mono<Session> saveRegLogin(Mono<RegLoginNormalReqDto> regLoginNormalReqDtoMono) {
        // 校验识别号
        Function<RegLoginNormalReqDto, Mono<Void>> checkIdentificationNumberFunction =
                regLoginNormalReqDto -> userRootDomainService
                        .checkIdentificationNumberV2(
                                RegisterTypeEnum.valueOf(regLoginNormalReqDto.getLoginType()),
                                regLoginNormalReqDto.getIdentificationNumber())
                        .filter(s -> !s).flatMap(s -> Mono.error(new ApplicationWrapperThrowable(
                                XkAcctApplicationErrorEnum.ACCOUNT_REGISTER_IDENTIFICATION_ERROR)));
        // 校验验证码
        Function<RegLoginNormalReqDto, Mono<Void>> checkVerificationCodeFunction =
                regLoginNormalReqDto -> {
                    if (StringHelper.isEmpty(regLoginNormalReqDto.getAuthCode())) {
                        return Mono.error(new XkAcctApplicationException(
                                XkAcctApplicationErrorEnum.ACCOUNT_AUTH_CODE_ERROR));
                    }
                    CheckValidateCodeReqDto checkValidateReqDto = new CheckValidateCodeReqDto();
                    switch (RegisterTypeEnum.valueOf(regLoginNormalReqDto.getLoginType())) {
                        case EMAIL -> checkValidateReqDto.create(regLoginNormalReqDto.getAuthCode(),
                                BusinessTypeEnum.XING_KA, ValidateCodeBusinessTypeEnum.EMAIL_LOGIN,
                                IdentifyTypeEnum.EMAIL,
                                regLoginNormalReqDto.getIdentificationNumber(), null);
                        case MOBILE -> checkValidateReqDto.create(
                                regLoginNormalReqDto.getAuthCode(), BusinessTypeEnum.XING_KA,
                                ValidateCodeBusinessTypeEnum.MOBILE_LOGIN, IdentifyTypeEnum.MOBILE,
                                regLoginNormalReqDto.getIdentificationNumber(),
                                regLoginNormalReqDto.getMobileCode());
                        default -> {
                            return Mono.error(new ApplicationWrapperThrowable(
                                    SystemErrorEnum.UNSUPPORTED_OPERATION));
                        }
                    }
                    return validateCodeQueryService
                            .checkValidateCode(Mono.just(checkValidateReqDto));
                };
        // 注册用户
        BiFunction<RegLoginNormalReqDto, Long, Mono<UserRoot>> userRegisterFunction =
                (regLoginNormalReqDto, userId) -> userRootDomainService.generateUserLoginLogId()
                        .flatMap(userLoginLogId -> {
                            // 创建命令
                            UserRegisterCommand command = UserRegisterCommand.builder().build();
                            command.createUserRegisterCommand(regLoginNormalReqDto, userId,
                                    userLoginLogId);
                            return actionCommandDispatcher.executeCommand(Mono.just(command),
                                    UserRegisterCommand.class, UserRoot.class);
                        })
                        // 发送注册事件
                        .flatMap(userRoot -> eventRootService.publisheByMono(EventRoot.builder()
                                .isQueue(true)
                                .domainEvent(UserRegisterEvent.builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                UserRegisterEvent.class))
                                        .id(userRoot.getIdentifier().id())
                                        .logidurl(regLoginNormalReqDto.getLogIdUrl()).build())
                                .build()).thenReturn(userRoot));
        // 绑定session,// 更新用户大对象
        Function<BindSessionCommand, Mono<Session>> bindSessionFunction =
                bindSessionCommand -> actionCommandDispatcher
                        .executeCommand(Mono.just(bindSessionCommand), BindSessionCommand.class,
                                Session.class)
                        .flatMap(session -> cacheObjectModifierService
                                .markUserObjectUpdatedMono(bindSessionCommand.getUserId())
                                .thenReturn(session));
        // 发送登录事件
        Function<UserLoginEvent, Mono<Void>> userLoginEventFunction = event -> eventRootService
                .publisheByMono(EventRoot.builder().isQueue(true).domainEvent(event).build())
                .filter(s -> !s).flatMap(s -> Mono.error(
                        new ApplicationWrapperThrowable(SystemErrorEnum.PRODUCER_SEND_ERROR)));
        // 获取用户公司信息
        LongFunction<Mono<CorpInfoIdentifierResDto>> getCorpInfoByUserIdFun = userId -> {
            UserIdReqDto userIdReqDto = new UserIdReqDto();
            userIdReqDto.setUserId(userId);
            return corpQueryService.findByUserId(Mono.just(userIdReqDto))
                    .filter(corpInfoIdentifierResDto -> CommonStatusEnum.ENABLE.getCode()
                            .equals(corpInfoIdentifierResDto.getCorpStatus()))
                    .switchIfEmpty(Mono.error(new XkAcctApplicationException(
                            XkAcctApplicationErrorEnum.ACCOUNT_LOGIN_USER_STATUS)));
        };
        Function<UserDataEntity, Mono<UserDataEntity>> checkIfDelete =
                userDataEntity -> dictObjectService
                        .getSystemConfigToInt(AcctDictEnum.DELETE_DELAY_DAYS)
                        .flatMap(userDataEntity::checkIfDelete);
        // 登录
        return regLoginNormalReqDtoMono.flatMap(dto -> actionQueryDispatcher
                .executeQuery(dto, UserDataByIdentificationNumberQuery.class, UserDataEntity.class)
                .flatMap(checkIfDelete).flatMap(userData -> {
                    if (RegisterTypeEnum.ACCOUNT.name().equals(dto.getLoginType())) {
                        return userRootDomainService
                                .checkPassword(userData.getIdentifier(), dto.getLoginPassword())
                                .then(userRootDomainService
                                        .isValidateAuthCode(Mono.just(UserLoginLogEntity.builder()
                                                .loginSn(userData.getLastLoginLogId())
                                                .createTime(JodaTimeUtil.getCurrentDateTime())
                                                .ip(dto.getIp()).deviceNumber(dto.getDeviceNumber())
                                                .platformType(dto.getPlatformType())
                                                .platformInfo(dto.getPlatformInfo())
                                                .mobile(dto.getMobile())
                                                .userId(dto.getCookieUserId()).mac(dto.getMac())
                                                .terminalVersions(dto.getTerminalVersions())
                                                .build())))
                                .filter(s -> s).thenReturn(userData);
                    }
                    return checkVerificationCodeFunction.apply(dto).thenReturn(userData);
                }).switchIfEmpty(Mono.defer(() -> {
                    if (RegisterTypeEnum.ACCOUNT.name().equals(dto.getLoginType())) {
                        return Mono.error(new XkAcctApplicationException(ACCOUNT_PASSWORD_ERROR));
                    }
                    return lockRootService
                            .acquireTransactionNonReentrantXLockMono(
                                    RedisLockObject.LOCKS_USER_REGIN, dto.getIdentificationNumber())
                            .then(checkIdentificationNumberFunction.apply(dto))
                            .then(checkVerificationCodeFunction.apply(dto))
                            .then(userRootDomainService.generateUserId())
                            .flatMap(userId -> userRegisterFunction.apply(dto, userId))
                            .map(UserRoot::getUserData);
                }))
                .flatMap(userData -> Mono
                        .when(userData.checkUserTypeAndPlatformType(dto.getPlatformType()),
                                userData.checkUserStatus())
                        .thenReturn(userData))
                .flatMap(userData -> userRootDomainService.generateUserLoginLogId()
                        .flatMap(loginLogId -> userLoginEventFunction
                                .apply(UserLoginEvent.builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                UserLoginEvent.class))
                                        .userId(userData.getUserId())
                                        .loginPlatformType(dto.getPlatformType() == null ? null
                                                : dto.getPlatformType().getValue())
                                        .loginTime(JodaTimeUtil.getCurrentDateTime())
                                        .sessionId(dto.getSessionId()).mobile(userData.getMobile())
                                        .loginDetail(dto.getLoginDetail()).isRobot(dto.getIsRobot())
                                        .mac(dto.getMac()).comeFrom(dto.getComeFrom())
                                        .deviceNumber(dto.getDeviceNumber())
                                        .email(userData.getEmail()).gps(dto.getGps())
                                        .appVersions(dto.getAppVersions())
                                        .terminalVersions(dto.getTerminalVersions())
                                        .platformInfo(dto.getPlatformInfo())
                                        .channelId(dto.getChannelId()).tokenId(dto.getSessionId())
                                        .ip(dto.getIp()).ipCity(dto.getIpCity())
                                        .ipCountry(dto.getIpCountry()).loginSn(loginLogId)
                                        .createTime(DateHelper.now())
                                        .loginType(LoginTypeEnum.IDENTIFY.name())
                                        .visitorUserId(dto.getCookieUserId())
                                        .platformType(dto.getPlatformType().name())
                                        .country(dto.getIpCountry()).city(dto.getIpCity()).build())
                                .thenReturn(loginLogId))
                        .flatMap(loginLogId -> {
                            BindSessionCommand bindSessionCommand = new BindSessionCommand();
                            bindSessionCommand.regLogBindSessionCommand(dto, userData, loginLogId);
                            dto.setLoginPassword(null);
                            if (UserTypeEnum.MERCHANT_KAS.equals(userData.getUserType())) {
                                return getCorpInfoByUserIdFun.apply(userData.getUserId())
                                        .flatMap(corpInfo -> {
                                            bindSessionCommand.setCorpId(corpInfo.getCorpInfoId());
                                            bindSessionCommand.setIsCorpAdmin(userData.getUserId()
                                                    .equals(corpInfo.getAdminUserId()));
                                            return bindSessionFunction.apply(bindSessionCommand);
                                        });
                            }
                            return bindSessionFunction.apply(bindSessionCommand);
                        })));
    }

    @Override
    @BusiCode
    public Mono<Session> saveAuthLogin(Mono<AuthLoginNormalReqDto> loginNormalBaseReq) {
        // 根据code获取用户信息
        Function<AuthLoginNormalReqDto, Mono<AuthUserInfoResDto>> getUserInfo =
                authLoginNormalReqDto -> {
                    log.info("saveAuthLogin 查询三方用户授权信息 req {}",
                            JSON.toJSONString(authLoginNormalReqDto));
                    return userAuthQueryService
                            .findUserInfo(Mono.just(AuthUserInfoReqDto.builder()
                                    .code(authLoginNormalReqDto.getCode())
                                    .channelType(authLoginNormalReqDto.getChannelType()).build()))
                            .doOnSuccess(authUserInfoResDto -> log.info(
                                    "saveAuthLogin 查询三方用户授权信息 res {}",
                                    JSON.toJSONString(authUserInfoResDto)))
                            .switchIfEmpty(
                                    Mono.error(new XkAcctApplicationException(APPLICATION_ERROR)));
                };
        // 根据授权信息查询用户
        BiFunction<AuthUserInfoResDto, AuthLoginNormalReqDto, Mono<Long>> getUserDataId =
                (authUserInfoResDto, authLoginNormalReqDto) -> {
                    return userRootDomainService
                            .getBindaccountByUnionId(authUserInfoResDto.getUnionid(),
                                    authLoginNormalReqDto.getChannelType())
                            .map(UserBindaccountEntity::getUserId);
                };
        // 新增授权绑定信息
        BiFunction<AuthUserInfoResDto, UserDataEntity, Mono<UserDataEntity>> addBingdaccount =
                (authUserInfoResDto, userDataEntity) -> {
                    return userRootDomainService.generateUserBindaccountId()
                            .flatMap(bindaccountId -> userRootDomainService.addBindaccount(
                                    UserBindaccountEntity.builder().bindaccountId(bindaccountId)
                                            .userId(userDataEntity.getUserId())
                                            .unionid(authUserInfoResDto.getUnionid())
                                            .channelType(authUserInfoResDto.getChannelType())
                                            .bindType(1).status(0).createTime(new Date())
                                            .nickname(authUserInfoResDto.getNickname())
                                            .picUrl(authUserInfoResDto.getHeadimgurl())
                                            .sex(authUserInfoResDto.getSex()).build()))
                            .thenReturn(userDataEntity);
                };
        // 注册
        BiFunction<AuthLoginNormalReqDto, Long, Mono<UserRoot>> userRegisterFunction =
                (authLoginNormalReqDto, userId) -> userRootDomainService.generateUserLoginLogId()
                        .flatMap(userLoginLogId -> {
                            // 创建命令
                            UserRegisterCommand command = UserRegisterCommand.builder().build();
                            command.createUserRegisterCommand(authLoginNormalReqDto, userId,
                                    userLoginLogId);
                            return actionCommandDispatcher.executeCommand(Mono.just(command),
                                    UserRegisterCommand.class, UserRoot.class);
                        })
                        // 发送注册事件
                        .flatMap(userRoot -> eventRootService.publisheByMono(EventRoot.builder()
                                .isQueue(true)
                                .domainEvent(UserRegisterEvent.builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                UserRegisterEvent.class))
                                        .id(userRoot.getIdentifier().id())
                                        .logidurl(authLoginNormalReqDto.getLogIdUrl()).build())
                                .build()).thenReturn(userRoot));
        // 发送登录事件
        Function<UserLoginEvent, Mono<Void>> userLoginEventFunction = event -> eventRootService
                .publisheByMono(EventRoot.builder().isQueue(true).domainEvent(event).build())
                .filter(s -> !s).flatMap(s -> Mono.error(
                        new ApplicationWrapperThrowable(SystemErrorEnum.PRODUCER_SEND_ERROR)));

        // 绑定session,// 更新用户大对象
        Function<BindSessionCommand, Mono<Session>> bindSessionFunction =
                bindSessionCommand -> actionCommandDispatcher
                        .executeCommand(Mono.just(bindSessionCommand), BindSessionCommand.class,
                                Session.class)
                        .flatMap(session -> cacheObjectModifierService
                                .markUserObjectUpdatedMono(bindSessionCommand.getUserId())
                                .thenReturn(session));
        // 将三方信息绑定到dto
        BiFunction<AuthLoginNormalReqDto, AuthUserInfoResDto, Mono<Void>> bindDto =
                (reqDto, userInfo) -> {
                    reqDto.setMobile(userInfo.getMobile());
                    return Mono.empty();
                };

        return loginNormalBaseReq.flatMap(dto -> {
            return getUserInfo.apply(dto).flatMap(authUserInfoResDto -> {
                return getUserDataId.apply(authUserInfoResDto, dto).flatMap(userDataId -> {
                    return userRootDomainService
                            .getUserRootById(
                                    Mono.just(LongIdentifier.builder().id(userDataId).build()))
                            .map(UserRoot::getUserData);
                }).switchIfEmpty(lockRootService
                        .acquireTransactionNonReentrantXLockMono(
                                RedisLockObject.LOCKS_USER_REGIN, dto.getCode())
                        .then(bindDto.apply(dto, authUserInfoResDto))
                        .then(userRootDomainService.generateUserId()
                                .flatMap(userId -> userRegisterFunction.apply(dto, userId)))
                        .map(UserRoot::getUserData)
                        .flatMap(userDataEntity -> addBingdaccount.apply(authUserInfoResDto,
                                userDataEntity)))
                        .flatMap(
                                userData -> Mono
                                        .when(userData.checkUserTypeAndPlatformType(
                                                dto.getPlatformType()), userData.checkUserStatus())
                                        .thenReturn(userData))
                        .flatMap(userData -> userRootDomainService.generateUserLoginLogId()
                                .flatMap(loginLogId -> userLoginEventFunction.apply(UserLoginEvent
                                        .builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                UserLoginEvent.class))
                                        .userId(userData.getUserId())
                                        .loginPlatformType(dto.getPlatformType() == null ? null
                                                : dto.getPlatformType().getValue())
                                        .loginTime(JodaTimeUtil.getCurrentDateTime())
                                        .sessionId(dto.getSessionId()).mobile(userData.getMobile())
                                        .loginDetail(dto.getLoginDetail()).isRobot(dto.getIsRobot())
                                        .mac(dto.getMac()).comeFrom(dto.getComeFrom())
                                        .deviceNumber(dto.getDeviceNumber())
                                        .email(userData.getEmail()).gps(dto.getGps())
                                        .appVersions(dto.getAppVersions())
                                        .terminalVersions(dto.getTerminalVersions())
                                        .platformInfo(dto.getPlatformInfo())
                                        .channelId(dto.getChannelId()).tokenId(dto.getSessionId())
                                        .ip(dto.getIp()).ipCity(dto.getIpCity())
                                        .ipCountry(dto.getIpCountry()).loginSn(loginLogId)
                                        .createTime(DateHelper.now())
                                        .loginType(LoginTypeEnum.IDENTIFY.name())
                                        .visitorUserId(dto.getCookieUserId())
                                        .platformType(dto.getPlatformType().name())
                                        .country(dto.getIpCountry()).city(dto.getIpCity()).build())
                                        .thenReturn(loginLogId))
                                .flatMap(loginLogId -> {
                                    BindSessionCommand bindSessionCommand =
                                            new BindSessionCommand();
                                    bindSessionCommand.regLogBindSessionCommand(dto, userData,
                                            loginLogId);
                                    return bindSessionFunction.apply(bindSessionCommand);
                                }));
            });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> saveLogout(Mono<RequireSessionDto> requireSessionMono) {
        return actionCommandDispatcher.executeCommand(requireSessionMono,
                UnbindSessionCommand.class);
    }

    @BusiCode
    @Override
    public Mono<Void> deleteUser(Mono<DeleteUserReqDto> mono) {
        return mono.flatMap(deleteUserReqDto -> ReadSynchronizationUtils.getSessionObjectMono()
                .flatMap(sessionObject -> {
                    UserDataIdQuery userDataIdQuery = new UserDataIdQuery();
                    userDataIdQuery.setUserId(sessionObject.getUserId());
                    return actionQueryDispatcher.executeQuery(Mono.just(userDataIdQuery),
                            UserDataIdQuery.class, UserDataEntity.class).flatMap(userDataEntity -> {
                                // 验证用户类型
                                if (!UserTypeEnum.USER.equals(userDataEntity.getUserType())) {
                                    return Mono.error(new XkAcctApplicationException(
                                            SystemErrorEnum.GEN_OPERATION_NOT_SUPPORT));
                                }
                                DeleteUserDataCommand command = new DeleteUserDataCommand();
                                command.setUserId(sessionObject.getUserId());

                                // 校验 验证码
                                Function<String, Mono<Void>> doCheckValidateCode =
                                        identificationNumber -> {
                                            if (StringHelper
                                                    .isEmpty(deleteUserReqDto.getAuthCode())) {
                                                return Mono.error(new XkAcctApplicationException(
                                                        XkAcctApplicationErrorEnum.ACCOUNT_AUTH_CODE_ERROR));
                                            }
                                            CheckValidateCodeReqDto checkValidateReqDto =
                                                    new CheckValidateCodeReqDto();
                                            checkValidateReqDto.create(
                                                    deleteUserReqDto.getAuthCode(),
                                                    BusinessTypeEnum.XING_KA,
                                                    ValidateCodeBusinessTypeEnum.DELETE_ACCT,
                                                    IdentifyTypeEnum.MOBILE, identificationNumber,
                                                    userDataEntity.getMobileCode());
                                            return validateCodeQueryService.checkValidateCode(
                                                    Mono.just(checkValidateReqDto));
                                        };

                                // 删除用户所有会话
                                Mono<Void> delSessionMono = sessionRootDomainService
                                        .unbindAllSession(userDataEntity.getUserId());
                                Mono<Void> delUserDataMono = Mono.defer(() -> {
                                    DeletedUserEvent deletedUserEvent = DeletedUserEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    DeletedUserEvent.class))
                                            .id(userDataEntity.getUserId()).build();
                                    EventRoot eventRoot = EventRoot.builder()
                                            .domainEvent(deletedUserEvent).isQueue(true).build();
                                    return eventRootService.publisheByMono(eventRoot).then();
                                });

                                // 更新用户大对象-标记删除
                                LongFunction<Mono<Void>> updateUserObjectCommand =
                                        cacheObjectModifierService::markUserObjectUpdatedMono;

                                // 删除三方授权信息
                                Mono<Void> deleteBindaccount = Mono.defer(()->{
                                    return userRootDomainService.deleteBinaccountByUserId(command.getUserId());
                                });

                                // 执行用户删除命令
                                return doCheckValidateCode.apply(userDataEntity.getMobile())
                                        .then(actionCommandDispatcher
                                                .executeCommand(Mono.just(command),
                                                        DeleteUserDataCommand.class)
                                                .then(delSessionMono).then(delUserDataMono))
                                        .then(deleteBindaccount)
                                        .then(updateUserObjectCommand
                                                .apply(userDataEntity.getUserId()));
                            });

                }));
    }

    @BusiCode
    @Override
    public Mono<Void> updateRestoreUser(Mono<RestoreUserReqDto> mono) {
        return mono.flatMap(dto -> {
            // 校验 验证码
            Supplier<Mono<Void>> doCheckValidateCode = () -> {
                if (StringHelper.isEmpty(dto.getAuthCode())) {
                    return Mono.error(new XkAcctApplicationException(
                            XkAcctApplicationErrorEnum.ACCOUNT_AUTH_CODE_ERROR));
                }
                CheckValidateCodeReqDto checkValidateReqDto = new CheckValidateCodeReqDto();
                checkValidateReqDto.create(dto.getAuthCode(), BusinessTypeEnum.XING_KA,
                        ValidateCodeBusinessTypeEnum.RESTORE_ACCT, IdentifyTypeEnum.MOBILE,
                        dto.getIdentificationNumber(), dto.getMobileCode());
                return validateCodeQueryService.checkValidateCode(Mono.just(checkValidateReqDto));
            };

            Mono<UserDataEntity> queryUser = actionQueryDispatcher
                    .executeQuery(dto, UserDataByIdentificationNumberQuery.class,
                            UserDataEntity.class)
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<UserDataEntity, Mono<UserDataEntity>> checkIfDeleted = userDataEntity -> {
                if (!CommonStatusEnum.ENABLE.getCode().equals(userDataEntity.getIsDelete())) {
                    return Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                }
                return Mono.just(userDataEntity);
            };

            Function<UserDataEntity, Mono<UserDataEntity>> restoreUser = userDataEntity -> {
                userDataEntity.setIsDelete(CommonStatusEnum.DISABLE.getCode());
                userDataEntity.setStatus(AccountStatusEnum.ACTIVE);
                return actionCommandDispatcher
                        .executeCommand(Mono.just(userDataEntity), EditUserDataCommand.class)
                        .thenReturn(userDataEntity);
            };

            Function<UserDataEntity, Mono<Void>> deleteCache =
                    userDataEntity -> userRootDomainService.deleteUserDelayCancelCache(
                            UserRoot.builder().identifier(userDataEntity.getIdentifier())
                                    .userData(userDataEntity).build());

            return doCheckValidateCode.get().then(
                    queryUser.flatMap(checkIfDeleted).flatMap(restoreUser).flatMap(deleteCache));
        });
    }
}

package com.xk.ewd.interfaces.dto.req.reconciliation;

import java.util.Date;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReconciliationDetailReq extends RequireSessionDtoPager {

    /**
     * 收款单据状态 1-正常 2-异常
     */
    private Integer receiptItemStatus;

    /**
     * 对账日期
     */
    private Date reconciliationDate;
}

package com.xk.ewd.interfaces.query.reconciliation;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationDetailReq;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationSearchPagerReq;

import reactor.core.publisher.Mono;

@HttpExchange("/ewd/reconciliation/query")
public interface ReconciliationQueryService {
    /**
     * 分页查询对账单
     * 
     * @param mono mono
     * @return Mono<Object>
     */
    @PostExchange("/search/pager")
    Mono<Pagination> searchPager(@RequestBody Mono<ReconciliationSearchPagerReq> mono);

    /**
     * 查询对账单详情
     * 
     * @param mono mono
     * @return Mono<ReconciliationDetailRsp>
     */
    @PostExchange("/detail")
    Mono<Pagination> detail(@RequestBody Mono<ReconciliationDetailReq> mono);
}

package com.xk.ewd.interfaces.query.reconciliation;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationItemUpdateReq;

import reactor.core.publisher.Mono;

/**
 * 对账单管理
 */
@HttpExchange("/ewd/reconciliation")
public interface ReconciliationService {
    /**
     * 对账单异常更正
     * 
     * @param mono mono
     * @return Mono<Object>
     */
    @PostExchange("/update")
    Mono<Void> update(@RequestBody Mono<ReconciliationItemUpdateReq> mono);
}

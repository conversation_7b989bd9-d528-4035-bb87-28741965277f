package com.xk.ewd.interfaces.api.reconciliation;

import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationDetailReq;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationSearchPagerReq;
import com.xk.ewd.interfaces.dto.rsp.reconciliation.ReconciliationDetailRsp;
import com.xk.ewd.interfaces.dto.rsp.reconciliation.ReconciliationPagerRsp;
import com.xk.ewd.interfaces.query.reconciliation.ReconciliationQueryService;

import reactor.core.publisher.Mono;

@Controller
@RequestMapping("/ewd/reconciliation/query")
public interface ReconciliationQueryDocService extends ReconciliationQueryService {

    /**
     * 分页查询对账单
     *
     * @param mono mono
     * @return Mono<Object>
     */
    @Override
    @PostMapping("/search/pager")
    Mono<Pagination> searchPager(@RequestBody Mono<ReconciliationSearchPagerReq> mono);

    /**
     * 分页查询对账单文档
     *
     * @param mono mono
     * @return Mono<Object>
     */
    @PostMapping("/search/pager/doc")
    Mono<List<ReconciliationPagerRsp>> searchPagerDoc(
            @RequestBody Mono<ReconciliationSearchPagerReq> mono);

    /**
     * 查询对账单分页详情
     *
     * @param mono mono
     * @return Mono<ReconciliationDetailRsp>
     */
    @Override
    @PostMapping("/detail")
    Mono<Pagination> detail(@RequestBody Mono<ReconciliationDetailReq> mono);

    /**
     * 查询对账单分页详情文档
     *
     * @param mono mono
     * @return Mono<ReconciliationDetailRsp>
     */
    @PostMapping("/detail/doc")
    Mono<List<ReconciliationDetailRsp>> detailDoc(@RequestBody Mono<ReconciliationDetailReq> mono);
}

package com.xk.ewd.interfaces.dto.req.reconciliation;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotNull;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReconciliationItemUpdateReq extends RequireSessionDto {

    /**
     * 对账条目id
     */
    @NotNull(message = "对账条目id不能为空")
    private Long reconciliationItemId;

    /**
     * 订正结果 1-跨天退款(三方未入账) 2-星卡系统缺失 3-第三方缺失
     */
    @NotNull(message = "订正结果不能为空")
    private Integer correctedResult;

    /**
     * 应收金额
     */
    private Long totalAmount;

    /**
     * 实收金额
     */
    private Long actualAmount;

    /**
     * 三方交易金额
     */
    private Long tpAmount;
}

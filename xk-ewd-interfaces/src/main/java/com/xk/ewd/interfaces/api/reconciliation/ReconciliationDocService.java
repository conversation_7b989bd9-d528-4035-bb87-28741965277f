package com.xk.ewd.interfaces.api.reconciliation;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationItemUpdateReq;
import com.xk.ewd.interfaces.query.reconciliation.ReconciliationService;

import reactor.core.publisher.Mono;

/**
 * 对账单管理
 */
@Controller
@RequestMapping("/ewd/reconciliation")
public interface ReconciliationDocService extends ReconciliationService {

    /**
     * 对账单异常更正
     *
     * @param mono mono
     * @return Mono<Object>
     */
    @Override
    @PostMapping("/update")
    Mono<Void> update(@RequestBody Mono<ReconciliationItemUpdateReq> mono);
}

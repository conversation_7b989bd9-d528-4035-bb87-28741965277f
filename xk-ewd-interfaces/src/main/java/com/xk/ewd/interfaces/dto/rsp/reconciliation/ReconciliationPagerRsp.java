package com.xk.ewd.interfaces.dto.rsp.reconciliation;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class ReconciliationPagerRsp implements Serializable {

    /**
     * 对账日期
     */
    private Date reconciliationDate;

    /**
     * 对账名称
     */
    private String reconciliationName;

    /**
     * 对账单据总数
     */
    private Integer reconciliationTotalCount;

    /**
     * 收款正常总数
     */
    private Integer reconciliationSuccessCount;

    /**
     * 收款异常总数
     */
    private Integer reconciliationFailCount;

    /**
     * 总实收金额
     */
    private Long reconciliationTotalAmount;

    /**
     * 总正常金额
     */
    private Long reconciliationSuccessAmount;

    /**
     * 总异常金额
     */
    private Long reconciliationFailAmount;
}

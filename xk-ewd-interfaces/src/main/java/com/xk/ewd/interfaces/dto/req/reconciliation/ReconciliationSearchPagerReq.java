package com.xk.ewd.interfaces.dto.req.reconciliation;

import java.util.Date;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReconciliationSearchPagerReq extends RequireSessionDtoPager {

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;
}

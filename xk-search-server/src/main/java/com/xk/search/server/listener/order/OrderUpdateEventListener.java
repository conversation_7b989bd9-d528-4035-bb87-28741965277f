package com.xk.search.server.listener.order;


import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.order.domain.event.order.OrderUpdateEvent;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@ConsumerListener
@RequiredArgsConstructor
public class OrderUpdateEventListener extends AbstractDispatchMessageListener<OrderUpdateEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(OrderUpdateEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(false).build();
        eventRootService.handler(eventRoot);
    }

}

package com.xk.search.server.listener.order;


import com.xk.search.domain.event.order.OrderUpdateCheckEvent;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.order.domain.event.order.OrderUpdateEvent;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@ConsumerListener
@RequiredArgsConstructor
public class OrderUpdateCheckEventListener extends AbstractDispatchMessageListener<OrderUpdateCheckEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(OrderUpdateCheckEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(false).build();
        eventRootService.handler(eventRoot);
    }

}

package com.xk.search.infrastructure.adapter.order;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.search.domain.dto.OrderUpdateDto;
import com.xk.search.domain.model.order.OrderSearchRoot;
import com.xk.search.domain.model.order.id.OrderSearchIdentifier;
import com.xk.search.domain.model.order.valobj.OrderIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchEntity;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.retrySearch.id.RetrySearchIdentifier;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.order.OrderSearchQueryRepository;
import com.xk.search.domain.service.order.OrderAdapterSearchService;
import com.xk.search.domain.service.order.OrderSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.OptTypeEnum;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderAdapterAdapterSearchServiceImpl implements OrderAdapterSearchService {

    private final OrderSearchQueryRepository orderSearchQueryRepository;

    private final OrderSearchRootService orderSearchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public Mono<Void> updateDocument(Mono<OrderUpdateDto> identifierMono) {
        return identifierMono.flatMap(mono ->
                orderSearchQueryRepository.getOrderById(Mono.just(StringIdentifier.builder().id(mono.getId()).build()))
                .flatMap(orderSearchRootService::updateDocument).onErrorResume(throwable -> {
                    log.error("订单更新文档失败：{}", throwable.getMessage());
                    return identifierMono.flatMap(
                            identifier -> retrySearchRootService.add(RetrySearchRoot.builder()
                                    .identifier(RetrySearchIdentifier.builder()
                                            .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT)
                                                    .id(identifier.getId()).build())
                                    .retrySearchEntity(RetrySearchEntity.builder()
                                                    .id(identifier.getId()).retryCount(1)
                                            .optType(OptTypeEnum.CREATE.getCode())
                                            .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                                    .getSearchChannelType().getType())
                                            .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT).build())
                                    .build()));
                        }));

    }

    @Override
    public Mono<Void> updateDocumentField(Mono<OrderSearchRoot> searchRootMono) {
        return searchRootMono.flatMap(orderSearchRootService::updateDocument);
    }

    @Override
    public Mono<Void> deleteDocument(Mono<StringIdentifier> identifierMono) {
        return identifierMono.flatMap(identifier -> orderSearchQueryRepository
                .getOrderById(identifierMono)
                .switchIfEmpty(searchIndexRootService
                        .getIndexList(SearchIndexRoot.builder().identifier(SearchIndexIdentifier
                                .builder().bizType(SearchBizTypeEnum.ORDER_MERCHANT.getCode())
                                .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                        .getSearchChannelType().getType())
                                .build())
                                .searchIndexEntity(
                                        SearchIndexEntity.builder()
                                                .bizType(SearchBizTypeEnum.ORDER_MERCHANT.getCode())
                                                .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                                        .getSearchChannelType().getType())
                                                .build())
                                .build())
                        .flatMap(searchIndexEntity -> orderSearchRootService.deleteDocument(
                                OrderSearchRoot.builder().identifier(OrderSearchIdentifier.builder()
                                        .orderNo(identifier.id())
                                        .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                                .getSearchChannelType())
                                        .build())
                                        .orderIndexValueObject(OrderIndexValueObject.builder()
                                                .productType(searchIndexEntity.getIdxType())
                                                .searchBizTypeEnum(SearchBizTypeEnum.ORDER_MERCHANT)
                                                .build())
                                        .build()))
                        .then(Mono.empty()))
                .onErrorResume(throwable -> {
                    log.error("订单删除文档失败：{}", throwable.getMessage());
                    return retrySearchRootService.add(RetrySearchRoot.builder()
                            .identifier(RetrySearchIdentifier.builder()
                                    .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT).id(identifier.id())
                                    .build())
                            .retrySearchEntity(RetrySearchEntity.builder().id(identifier.id())
                                    .retryCount(1).optType(OptTypeEnum.DELETE.getCode())
                                    .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH
                                            .getSearchChannelType().getType())
                                    .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT).build())
                            .build()).then(Mono.empty());
                }).then());
    }

}

package com.xk.acct.domain.service.user;

import java.util.List;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.IDomainService;
import com.xk.acct.domain.model.user.*;
import com.xk.acct.enums.user.RegisterTypeEnum;

import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: killer
 **/
public interface UserRootDomainService extends IDomainService<UserRoot> {

    /**
     * 根据用户唯一标识获取用户
     *
     * @param identificationNumberMono identificationNumber
     * @return Mono<Boolean>
     */
    Mono<UserDataEntity> getByIdentificationNumber(Mono<String> identificationNumberMono);

    /**
     * 校验用户唯一标识
     *
     * @param identificationNumberMono identificationNumber
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkIdentificationNumber(Mono<String> identificationNumberMono);

    /**
     * 校验用户唯一标识
     * 
     * @param registerType registerType
     * @param identificationNumber identificationNumber
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkIdentificationNumberV2(RegisterTypeEnum registerType,
            String identificationNumber);


    /**
     * 校验用户昵称
     *
     * @param nicknameMono nickname
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkNickname(Mono<String> nicknameMono);

    /**
     * @param registerType registerType
     * @param userType userType
     * @return Mono<Void>
     */
    Mono<Void> checkUserTypeAndPlatformType(PlatformTypeEnum registerType, UserTypeEnum userType);

    /**
     * 校验密码
     *
     * @param identifier identifier
     * @param password password
     * @return Mono<Void>
     */
    Mono<Void> checkPassword(LongIdentifier identifier, String password);

    /**
     * 是否校验验证码
     * 
     * @param userLoginLogEntityMono userLoginLogEntityMono
     * @return Mono<Boolean>
     */
    Mono<Boolean> isValidateAuthCode(Mono<UserLoginLogEntity> userLoginLogEntityMono);

    /**
     * 校验用户身份证号
     *
     * @param idCardMono idCard
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkIdCard(Mono<String> idCardMono);

    /**
     * 校验用户当前账户类型是否存在
     *
     * @param userPayAccountEntityMono userPayAccountEntityMono
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkUserPayAccountByUserIdAndType(
            Mono<UserPayAccountEntity> userPayAccountEntityMono);

    /**
     * 校验用户手机号绑定状态
     *
     * @param entityMono entityMono
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkMobileBindStatus(Mono<UserDataEntity> entityMono, Mono<String> mobileMono);

    /**
     * 校验用户邮箱绑定状态
     *
     * @param entityMono entityMono
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkEmailBindStatus(Mono<UserDataEntity> entityMono, Mono<String> emailMono);


    /**
     * 生成用户ID
     *
     * @return Mono<Long>
     */
    Mono<Long> generateUserId();

    /**
     * 生成用户登录日志ID
     *
     * @return Mono<Long>
     */
    Mono<Long> generateUserLoginLogId();

    /**
     * 生成用户支付账户ID
     *
     * @return Mono<Long>
     */
    Mono<Long> generateUserPayAccountId();
    /**
     * 生成三方id
     * @return Mono<Long>
     */
    Mono<Long> generateUserBindaccountId();

    /**
     * 保存用户数据
     *
     * @param userRootMono userRootMono
     * @return Mono<Void>
     */
    Mono<Void> saveUserRoot(Mono<UserRoot> userRootMono);

    /**
     * 获取用户数据
     *
     * @param userRootIdMono userRootIdMono
     * @return Mono<UserRoot>
     */
    Mono<UserRoot> getUserRootById(Mono<LongIdentifier> userRootIdMono);

    /**
     * 获取用户对象
     * 
     * @param longIdentifierMono longIdentifierMono
     * @return Mono<UserRoot>
     */
    Mono<UserRoot> getUserObject(Mono<LongIdentifier> longIdentifierMono);

    /**
     * 修改用户数据
     * @param userRootMono userRootMono
     * @return Mono<Void>
     */
    Mono<Void> updateUserRoot(Mono<UserRoot> userRootMono);

    /**
     * 根据id集合获取用户数据
     *
     * @param userIdsMono userIdsMono
     * @return Mono<UserRoot>
     */
    Flux<UserDataEntity> getUserByIds(Mono<List<LongIdentifier>> userIdsMono);

    /**
     * 根据用户id校验是否实名认证
     * @param identifierMono identifierMono
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkUserAuthByUserId(Mono<LongIdentifier> identifierMono);

    /**
     * 校验身份证合法性
     * @param idCardMono idCardMono
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkIdCardLegal(Mono<String> idCardMono);

    // 查询用户收藏浏览数量
    Mono<Long> queryUserViewCollectCount(Mono<UserRoot> userRootMono);

    // 验证用户是否已收藏 或 已浏览
    Mono<Boolean> checkUserViewCollect(Mono<UserRoot> userRootMono);

    Mono<Void> updateUserSecurity(Mono<UserRoot> userRootMono);


    Mono<UserBindaccountEntity> getBindaccountByUnionId(String unionid, Integer channelType);

    Mono<Void> addBindaccount(UserBindaccountEntity build);

    /**
     * 获取用户延迟注销缓存
     *
     * @param userRoot userRoot
     * @return Mono<T>
     */
    Mono<LongIdentifier> getUserDelayCancelCache(UserRoot userRoot);

    /**
     * 删除用户延迟注销缓存
     *
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteUserDelayCancelCache(UserRoot userRoot);

    /**
     * 删除用户延迟注销缓存队列
     * 
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteUserDelayCancelQueueCache(UserRoot userRoot);

    /**
     * 根据用户id删除用户绑定授权信息
     * @param userId userId
     * @return Mono<Void>
     */
    Mono<Void> deleteBinaccountByUserId(Long userId);
}

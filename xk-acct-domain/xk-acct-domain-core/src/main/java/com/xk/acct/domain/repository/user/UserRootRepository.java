package com.xk.acct.domain.repository.user;


import com.myco.mydata.domain.repository.I2Repository;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.acct.domain.model.user.UserBindaccountEntity;
import com.xk.acct.domain.model.user.UserRoot;

import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
public interface UserRootRepository extends I2Repository<UserRoot> {

    /**
     * 保存用户数据到历史表
     *
     * @param root root
     * @return Mono<Void>
     */
    Mono<Void> saveToBack(UserRoot root);

    /**
     * 逻辑删除用户数据
     *
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> removeLogically(UserRoot userRoot);

    /**
     * 添加用户支付账户
     */
    Mono<Void> saveUserPayAccount(UserRoot userRoot);

    /**
     * 修改用户支付账户
     */
    Mono<Void> updateUserPayAccount(UserRoot userRoot);

    // 添加收藏浏览记录
    Mono<Void> addUserViewsCollectCache(UserRoot userRoot);

    /**
     * 删除收藏浏览记录
     */
    Mono<Void> removeUserViewsCollectCache(UserRoot userRoot);

    /**
     * 删除首条记录 用于对集合长度有所限制 达到数量限制后 删除首条记录
     */
    Mono<Void> removeUserViewsCollectCacheFirst(UserRoot userRoot);

    /**
     * 删除全部收藏浏览记录
     *
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> deletedUserViewCollect(UserRoot userRoot);

    Mono<Void> updateUserSecurity(UserRoot userRoot);

    Mono<Void> saveUserSecurity(UserRoot userRoot);

    /**
     * 修改用户配置信息
     *
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> updateUserConfig(UserRoot userRoot);

    /**
     * 添加用户设备信息
     *
     * @param userRoot
     * @return
     */
    Mono<Void> saveUserDevice(UserRoot userRoot);

    /**
     * 修改用户设备信息
     *
     * @param userRoot
     * @return
     */
    Mono<Void> updateUserDevice(UserRoot userRoot);

    /**
     * 添加绑定信息
     * @param entity entity
     * @return Mono<Void>
     */
    Mono<Void> saveBindaccount(UserBindaccountEntity entity);

    /**
     * 添加到注销延迟队列
     *
     * @param userDataEntity userDataEntity
     * @return Mono<Void>
     */
    Mono<Void> addDelayCancelQueue(UserDataEntity userDataEntity);

    /**
     * 删除取消延迟缓存
     *
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteUserDelayCancelCache(UserRoot userRoot);

    /**
     * 删除取消延迟队列
     *
     * @param userRoot userRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteUserDelayCancelQueueCache(UserRoot userRoot);

    /**
     * 根据用户删除三方授权
     * @param entity entity
     * @return Mono<Void>
     */
    Mono<Void> deleteBinaccountByUserId(UserBindaccountEntity entity);
}

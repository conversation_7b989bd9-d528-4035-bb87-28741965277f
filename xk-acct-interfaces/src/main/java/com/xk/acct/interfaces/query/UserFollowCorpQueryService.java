package com.xk.acct.interfaces.query;

import java.util.List;
import java.util.Set;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.acct.interfaces.dto.req.follow.CorpFollowQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.SelectByUserQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryInnerReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryReqDto;
import com.xk.acct.interfaces.dto.req.user.UserFollowCorpReqDto;
import com.xk.acct.interfaces.dto.rsp.follow.CorpFollowNumberRspDtp;
import com.xk.acct.interfaces.dto.rsp.follow.UserCorpsAllRspDtp;
import com.xk.acct.interfaces.dto.rsp.follow.UserIsFollowCorpRspDto;

import reactor.core.publisher.Mono;

/**
 * 用户关注商户
 *
 * @author: killer
 **/
@HttpExchange("/acct/user/follow/corp/query")
public interface UserFollowCorpQueryService {

    /**
     * 查询关注商户列表
     *
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @PostExchange("/list")
    Mono<Pagination> list(@RequestBody Mono<UserFollowCorpQueryReqDto> dtoMono);

    /**
     * 内部接口 —— 查询关注商户列表
     *
     * @param dtoMono dtoMono
     * @return reactor.core.publisher.Mono<com.myco.framework.support.mybatis.Pagination>
     * <AUTHOR>
     * @date: 2025/8/20 19:51
     */
    @PostExchange("/inner/list")
    Mono<Pagination> listInner(@RequestBody Mono<UserFollowCorpQueryInnerReqDto> dtoMono);


    @PostExchange("/followNumber")
    Mono<CorpFollowNumberRspDtp> followNumber(@RequestBody Mono<CorpFollowQueryReqDto> dtoMono);

    @PostExchange("/user/corps")
    Mono<Set<Long>> userCorps(@RequestBody Mono<SelectByUserQueryReqDto> dtoMono);

    @PostExchange("/user/followStatus")
    Mono<UserIsFollowCorpRspDto> followStatus(@RequestBody Mono<UserFollowCorpReqDto> dtoMono);

    /**
     * 查询关注商户列表
     *
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @PostExchange("/user/corpsAll")
    Mono<List<UserCorpsAllRspDtp>> userCorpsAll(@RequestBody Mono<RequireSessionDto> dtoMono);
}

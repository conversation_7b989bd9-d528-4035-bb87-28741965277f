package com.xk.acct.interfaces.api;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.acct.interfaces.dto.req.follow.CorpFollowQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.SelectByUserQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryInnerReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryReqDto;
import com.xk.acct.interfaces.dto.req.user.UserFollowCorpReqDto;
import com.xk.acct.interfaces.dto.rsp.follow.CorpFollowNumberRspDtp;
import com.xk.acct.interfaces.dto.rsp.follow.UserCorpsAllRspDtp;
import com.xk.acct.interfaces.dto.rsp.follow.UserIsFollowCorpRspDto;
import com.xk.acct.interfaces.query.UserFollowCorpQueryService;

import reactor.core.publisher.Mono;

/**
 * 用户关注商户查询
 *
 * @author: killer
 **/
@Controller
@RequestMapping("/acct/user/follow/corp/query")
public interface UserFollowCorpQueryDocService extends UserFollowCorpQueryService {

    /**
     * 查询列表
     *
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/list")
    Mono<Pagination> list(@RequestBody Mono<UserFollowCorpQueryReqDto> dtoMono);

    /**
     * 内部接口 —— 查询关注商户列表
     *
     * @param dtoMono dtoMono
     * @return reactor.core.publisher.Mono<com.myco.framework.support.mybatis.Pagination>
     * <AUTHOR>
     * @date: 2025/8/20 19:51
     */
    @Override
    @PostMapping("/inner/list")
    Mono<Pagination> listInner(@RequestBody Mono<UserFollowCorpQueryInnerReqDto> dtoMono);

    /**
     * 查询商户粉丝数
     *
     * @param corpOptReqDtoMono corpOptReqDtoMono
     * @return
     */
    @Override
    @PostMapping("/followNumber")
    Mono<CorpFollowNumberRspDtp> followNumber(@RequestBody Mono<CorpFollowQueryReqDto> corpOptReqDtoMono);

    /**
     * 根据userId+商户返回 关注的商户列表
     *
     * @param dtoMono dtoMono
     * @return
     */
    @Override
    @PostMapping("/user/corps")
    Mono<Set<Long>> userCorps(@RequestBody Mono<SelectByUserQueryReqDto> dtoMono);

    @Override
    @PostMapping("/user/followStatus")
    Mono<UserIsFollowCorpRspDto> followStatus(@RequestBody Mono<UserFollowCorpReqDto> dtoMono);

    /**
     * 查询用户关注商户列表
     *
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/user/corpsAll")
    Mono<List<UserCorpsAllRspDtp>> userCorpsAll(@RequestBody Mono<RequireSessionDto> dtoMono);
}

package com.xk.acct.interfaces.dto.req.follow;

import com.myco.mydata.interfaces.dto.commons.pager.DefaultPagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 内部接口 —— 查询商户关注信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/20 19:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserFollowCorpQueryInnerReqDto extends DefaultPagination {

    private Long userId;

    private Long corpInfoId;

    private String userNick;
}

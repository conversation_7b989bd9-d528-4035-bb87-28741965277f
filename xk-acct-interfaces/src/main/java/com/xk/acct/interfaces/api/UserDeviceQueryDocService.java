package com.xk.acct.interfaces.api;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.acct.interfaces.dto.req.user.UserDevicePaperReqDto;
import com.xk.acct.interfaces.query.UserDeviceQueryService;

import reactor.core.publisher.Mono;

/**
 * 用户设备查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/11 17:34
 */
@Controller
@RequestMapping("/acct/user/device/query")
public interface UserDeviceQueryDocService extends UserDeviceQueryService {

    /**
     * 分页查询用户设备信息
     *
     * @param mono mono
     * @return reactor.core.publisher.Mono<com.myco.framework.support.mybatis.Pagination>
     * <AUTHOR>
     * @date: 2025/8/11 19:29
     */
    @PostMapping("/search")
    Mono<Pagination> searchUserDevice(@RequestBody Mono<UserDevicePaperReqDto> mono);
}

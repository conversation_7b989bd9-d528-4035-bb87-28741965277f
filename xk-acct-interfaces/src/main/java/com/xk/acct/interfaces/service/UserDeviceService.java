package com.xk.acct.interfaces.service;


import com.xk.acct.interfaces.dto.req.user.UserDeviceReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;


/**
 * 用户设备提交相关
 *
 * @author: limengda
 **/
@HttpExchange("/acct/user/device")
public interface UserDeviceService {

    /**
     * 用户设备上报
     *
     * @param userDeviceReqDtoMono 用户设备对象
     * @return Mono<Void>
     */
    @PostExchange("/addUserDevice")
    Mono<Void> addUserDevice(
            @RequestBody Mono<UserDeviceReqDto> userDeviceReqDtoMono);
}

package com.xk.acct.interfaces.api;

import com.xk.acct.interfaces.service.UserDeviceService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.acct.interfaces.dto.req.user.UserDeviceReqDto;

import reactor.core.publisher.Mono;

/**
 * 用户设备操作相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/11 17:31
 */
@Controller
@RequestMapping("/acct/user/device")
public interface UserDeviceDocService extends UserDeviceService {

    /**
     * 用户设备上报
     *
     * @param userDeviceReqDtoMono 用户设备对象
     * @return Mono<Void>
     */
    @PostMapping("/addUserDevice")
    Mono<Void> addUserDevice(
            @RequestBody Mono<UserDeviceReqDto> userDeviceReqDtoMono);
}

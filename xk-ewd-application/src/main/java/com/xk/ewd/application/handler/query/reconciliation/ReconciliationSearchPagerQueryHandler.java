package com.xk.ewd.application.handler.query.reconciliation;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.reconciliation.ReconciliationSearchPagerQuery;
import com.xk.ewd.application.dto.reconciliation.ReconciliationAppDto;
import com.xk.ewd.domain.repository.reconciliation.ReconciliationRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.reconciliation.ReconciliationPagerRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class ReconciliationSearchPagerQueryHandler
        implements IActionQueryHandler<ReconciliationSearchPagerQuery, Pagination> {

    private final Converter converter;
    private final ReconciliationRootQueryRepository reconciliationRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<ReconciliationSearchPagerQuery> mono) {
        return mono.flatMap(query -> {
            Pagination pagination = new Pagination();
            pagination.setOffset(query.getOffset());
            pagination.setLimit(query.getLimit());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));

            return reconciliationRootQueryRepository.selectByPage(pagination).collectList()
                    .map(v -> {
                        List<ReconciliationPagerRsp> convert =
                                converter.convert(converter.convert(v, ReconciliationAppDto.class),
                                        ReconciliationPagerRsp.class);
                        pagination.setRecords(convert);
                        return pagination;
                    });
        });
    }
}

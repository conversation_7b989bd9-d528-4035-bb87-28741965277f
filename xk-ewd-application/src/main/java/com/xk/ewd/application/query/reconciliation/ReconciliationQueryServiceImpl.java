package com.xk.ewd.application.query.reconciliation;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.ewd.application.action.query.reconciliation.ReconciliationDetailQuery;
import com.xk.ewd.application.action.query.reconciliation.ReconciliationSearchPagerQuery;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationDetailReq;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationSearchPagerReq;
import com.xk.ewd.interfaces.query.reconciliation.ReconciliationQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReconciliationQueryServiceImpl implements ReconciliationQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<ReconciliationSearchPagerReq> mono) {
        return queryDispatcher.executeQuery(mono, ReconciliationSearchPagerQuery.class,
                Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> detail(Mono<ReconciliationDetailReq> mono) {
        return queryDispatcher.executeQuery(mono, ReconciliationDetailQuery.class,
                Pagination.class);
    }
}

package com.xk.ewd.application.dto.app;

import java.util.Date;

import com.xk.ewd.domain.model.goods.entity.GoodsEwdEntity;
import com.xk.ewd.interfaces.dto.rsp.goods.CollectibleCardPagerResDto;
import com.xk.ewd.interfaces.dto.rsp.goods.MallPagerResDto;
import com.xk.ewd.interfaces.dto.rsp.goods.MaterialPagerResDto;
import com.xk.ewd.interfaces.dto.rsp.goods.MerchantPagerResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({
        @AutoMapper(target = CollectibleCardPagerResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = MaterialPagerResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = MallPagerResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = MerchantPagerResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = GoodsEwdEntity.class)})
public class GoodsEwdAppDto {

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型: 1-商城商品, 2-物料商品, 3-收藏卡,4-商家商品
     */
    private Integer goodsType;

    /**
     * 货币类型:1-人民币
     */
    private Integer currencyType;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 成本价
     */
    private Long costPrice;

    /**
     * 销售价
     */
    private Long salePrice;

    /**
     * 显示状态 1-显示 0-不显示
     */
    private Integer showStatus;

    /**
     * 上架状态枚举值 1-上架 2-下架 3-到期下架
     */
    private Integer listingStatus;

    /**
     * 上架时长(天)
     */
    private Integer listingDate;

    /**
     * 自动上架状态枚举值
     */
    private Integer autoListingStatus;

    /**
     * 最高价
     */
    private Long highestPrice;

    /**
     * 最低价
     */
    private Long lowestPrice;

    /**
     * 计划上架开始时间
     */
    private Date planUpTime;

    /**
     * 计划上架结束时间
     */
    private Date planDownTime;

    /**
     * 实际上架时间
     */
    private Date actualUpTime;

    /**
     * 实际下架时间
     */
    private Date actualDownTime;

    /**
     * 商品描述
     */
    private String goodsDescribe;

    /**
     * 剩余库存
     */
    private Long remainStock;

    /**
     * 总库存
     */
    private Long totalStock;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 板块类型 1-球星 2-动漫
     */
    private Integer blockType;

    /**
     * 所属系列
     */
    private String seriesName;

    /**
     * 商品类目id
     */
    private Long categoryId;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 商户id
     */
    private Long corpInfoId;

    /**
     * 商户名称
     */
    private String corpName;

    /**
     * 商户logo
     */
    private String corpLogo;

    /**
     * 被禁用状态 1-未禁用 0-已禁用
     */
    private Integer status;

    /**
     * 商品主图资源id
     */
    private Integer goodsResId;

    /**
     * 商品主图
     */
    private String goodsImage;

    /**
     * 收藏卡id
     */
    private Long collectibleCardId;

    /**
     * 收藏卡主图
     */
    private String collectibleImage;

    /**
     * 收藏卡名称
     */
    private String collectibleCardName;

    /**
     * 收藏卡单价
     */
    private Long collectibleCardUnitPrice;

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 销售状态:1-已销售,0-未销售
     */
    private Integer soldStatus;

    /**
     * 销售时间
     */
    private Date soldTime;

    /**
     * 售罄状态:1-售罄,0-未售罄
     */
    private Integer soldOutStatus;

    /**
     * 售罄时间
     */
    private Date soldOutTime;

    /**
     * 组齐状态:1-组齐,0-未组齐
     */
    private Integer groupStatus;

    /**
     * 组齐时间
     */
    private Date groupTime;

    /**
     * 公示状态:1-已公示,0-未公示
     */
    private Integer publicityStatus;

    /**
     * 公示时间
     */
    private Date publicityTime;

    /**
     * 录入状态:1-已录入 0-未录入
     */
    private Integer reportStatus;

    /**
     * 录入时间
     */
    private Date reportTime;

    /**
     * 完成状态:1-已完成,0-未完成
     */
    private Integer finishStatus;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 随机模式
     */
    private Integer randomType;

    /**
     * 审核状态1-审核中 2-已审核 3-未通过
     */
    private Integer auditStatus;

    /**
     * 回收状态
     */
    private Integer recycleStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 驳回备注
     */
    private String refuseRemark;

    /**
     * 审核人userId
     */
    private Long auditUserId;

    /**
     * 首购优惠状态
     */
    private Integer firstBuyDiscountStatus;

    /**
     * 首购优惠金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 剩余随机状态
     */
    private Integer remainRandomStatus;

    /**
     * 剩余随机金额
     */
    private Long remainRandomAmount;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;
}

package com.xk.ewd.application.action.command.reconciliation;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationItemEntity;
import com.xk.ewd.infrastructure.convertor.reconciliation.CorrectedResultEnumConvertor;
import com.xk.ewd.infrastructure.convertor.reconciliation.ReceiptItemStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = ReconciliationItemEntity.class, reverseConvertGenerate = false,
        uses = {ReceiptItemStatusEnumConvertor.class, CorrectedResultEnumConvertor.class})})
public class UpdateReconciliationItemCommand extends AbstractActionCommand {
    /**
     * 对账单据标识符
     */
    private Long reconciliationItemId;

    /**
     * 应收金额
     */
    private Long totalAmount;

    /**
     * 实收金额
     */
    private Long actualAmount;

    /**
     * 三方交易金额
     */
    private Long tpAmount;

    /**
     * 收款单据状态
     */
    private Integer receiptItemStatus;

    /**
     * 订正结果
     */
    private Integer correctedResult;

    /**
     * 更新人
     */
    @AutoMapping(targetClass = ReconciliationItemEntity.class, target = "updateValObj.updateId")
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMapping(targetClass = ReconciliationItemEntity.class, target = "updateValObj.updateTime")
    private Date updateTime;
}

package com.xk.ewd.application.handler.event.financial;

import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.application.action.command.reconciliation.CreateReconciliationItemCommand;
import com.xk.ewd.domain.event.financial.FinancialReconciliationPagerEvent;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.domain.service.financial.FinancialTransactionRootService;
import com.xk.ewd.domain.service.payment.PaymentRootService;
import com.xk.ewd.domain.service.reconciliation.ReconciliationRootService;
import com.xk.ewd.enums.reconciliation.ReceiptItemStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class FinancialReconciliationPagerEventHandler
        extends AbstractEventVerticle<FinancialReconciliationPagerEvent> {

    private final PaymentRootService paymentRootService;
    private final FinancialTransactionRootService financialTransactionRootService;
    private final ReconciliationRootService reconciliationRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public Mono<Void> handle(Mono<FinancialReconciliationPagerEvent> mono) {
        return mono.flatMap(event -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(event.getPageSize());
            pagination.setOffset((event.getPageNum() - 1) * event.getPageSize());
            if (event.getReconciliationDate() != null) {
                pagination.setCriteria(CollectionHelper.converBeanToMap(FinancialTransactionEntity
                        .builder().financialDate(event.getReconciliationDate()).build()));
            }

            CreateReconciliationItemCommand command =
                    CreateReconciliationItemCommand.builder().build();
            Function<CreateReconciliationItemCommand, Mono<Void>> createReconciliation =
                    result -> commandDispatcher.executeCommand(Mono.just(result),
                            CreateReconciliationItemCommand.class);
            Supplier<Mono<CreateReconciliationItemCommand>> doOnEmpty = () -> {
                log.error("未能找到第三方流水号{}对应的数据",
                        command.getFinancialTransactionId().getFinancialTransactionId());
                command.setReceiptItemStatus(ReceiptItemStatusEnum.ABNORMAL.getCode());
                return Mono.just(command);
            };

            return financialTransactionRootService.selectPager(pagination)
                    .flatMap(entity -> reconciliationRootService.generateId().flatMap(id -> {
                        command.setReconciliationItemId(id);
                        command.setFinancialTransactionId(entity.getIdentifier());
                        command.setPayNo(entity.getPayNo());
                        command.setTpAmount(entity.getPayAmount());
                        command.setPayDirection(entity.getPayDirection().getCode());
                        command.setPayType(entity.getPayType().getCode());
                        command.setPayAccount(entity.getPayAccount());
                        command.setPayCreateTime(entity.getPayCreateTime());
                        command.setCreateId(-1L);
                        command.setCreateTime(event.getReconciliationDate());
                        return paymentRootService.findByPayNo(entity.getPayNo())
                                .flatMap(paymentDetailEntity -> {
                                    if (!Objects.equals(paymentDetailEntity.getAmount(),
                                            entity.getPayAmount())) {
                                        String financialTransactionId =
                                                command.getFinancialTransactionId()
                                                        .getFinancialTransactionId();
                                        log.error("第三方流水号{}金额对应错误,系统流水金额{},三方流水金额{}",
                                                financialTransactionId,
                                                paymentDetailEntity.getAmount(),
                                                entity.getPayAmount());
                                        command.setReceiptItemStatus(
                                                ReceiptItemStatusEnum.ABNORMAL.getCode());
                                    } else {
                                        command.setReceiptItemStatus(
                                                ReceiptItemStatusEnum.NORMAL.getCode());
                                    }
                                    command.setActualAmount(paymentDetailEntity.getAmount());
                                    command.setTotalAmount(paymentDetailEntity.getAmount());
                                    command.setOrderNo(paymentDetailEntity.getOrderNo());
                                    command.setGoodsId(paymentDetailEntity.getGoodsId());
                                    command.setCorpId(paymentDetailEntity.getCorpId());
                                    return Mono.just(command);
                                }).switchIfEmpty(doOnEmpty.get()).flatMap(createReconciliation);
                    })).then();
        });
    }
}

package com.xk.ewd.application.dto.reconciliation;

import java.util.Date;

import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationItemEntity;
import com.xk.ewd.infrastructure.convertor.financial.FinancialPayDirectionEnumConvertor;
import com.xk.ewd.infrastructure.convertor.financial.PayTypeEnumConvertor;
import com.xk.ewd.infrastructure.convertor.reconciliation.CorrectedResultEnumConvertor;
import com.xk.ewd.infrastructure.convertor.reconciliation.ReceiptItemStatusEnumConvertor;
import com.xk.ewd.interfaces.dto.rsp.reconciliation.ReconciliationDetailRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = ReconciliationDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = ReconciliationItemEntity.class, convertGenerate = false,
                uses = {FinancialPayDirectionEnumConvertor.class, PayTypeEnumConvertor.class, ReceiptItemStatusEnumConvertor.class,
                        CorrectedResultEnumConvertor.class})})
public class ReconciliationItemAppDto {

    /**
     * 对账单据id
     */
    private Long reconciliationItemId;

    /**
     * 交易流水号
     */
    private String payNo;

    /**
     * 应收金额
     */
    private Long totalAmount;

    /**
     * 实收金额
     */
    private Long actualAmount;

    /**
     * 三方交易金额
     */
    private Long tpAmount;

    /**
     * 收支类型 1-收入 2-退款
     */
    private Integer payDirection;

    /**
     * 支付方式 1-银行卡 2-支付宝 3-微信
     */
    private Integer payType;

    /**
     * 第三方账户号
     */
    private String payAccount;

    /**
     * 第三方账号平台 1-支付宝 2-汇付
     */
    private Integer financialPlatformType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 交易时间
     */
    private Date payCreateTime;

    /**
     * 收款单据状态 1-正常 2-异常
     */
    private Integer receiptItemStatus;

    /**
     * 订正结果 1-跨天退款(三方未入账) 2-星卡系统缺失 3-第三方缺失
     */
    private Integer correctedResult;
}

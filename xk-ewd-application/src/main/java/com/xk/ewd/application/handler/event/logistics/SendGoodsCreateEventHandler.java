package com.xk.ewd.application.handler.event.logistics;


import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.enums.filetask.FileTaskBizStatusEnum;
import com.xk.enums.filetask.FileTaskStatusEnum;
import com.xk.ewd.domain.event.filetask.TaskOrderUpdateEvent;
import com.xk.ewd.domain.event.logistics.LogisticsSendGoodsSuccessEvent;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.domain.repository.logistics.LogisticsGoodsRootQueryRepository;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.application.action.command.log.CreateOrderLogCommand;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.service.logistics.LogisticsOrderAdapterService;
import com.xk.ewd.enums.order.OrderLogMsgEnum;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendGoodsCreateEventHandler extends AbstractEventVerticle<SendGoodsCreateEvent> {

    private final LogisticsOrderAdapterService logisticsOrderAdapterService;

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final LogisticsGoodsRootQueryRepository logisticsGoodsRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<SendGoodsCreateEvent> event) {
        return event.flatMap(createEvent -> logisticsOrderAdapterService
                .update(Mono.just(LogisticsOrderRoot.builder()
                        .identifier(LogisticsOrderIdentifier.builder()
                                .logisticsOrderId(createEvent.getLogisticsOrderId()).build())
                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                .logisticsOrderId(createEvent.getLogisticsOrderId())
                                .logisticsOrderStatus(
                                        createEvent.getLogisticsOrderStatus().getCode())
                                .logisticsCorpName(createEvent.getLogisticsCorpName())
                                .logisticsNo(createEvent.getLogisticsNo())
                                .createTime(createEvent.getCreateTime()).build())
                        .orderEntity(OrderEntity.builder().orderNo(createEvent.getOrderNo())
                                .sendGoodsTime(createEvent.getSendGoodsTime()).build())
                        .build()))
                .thenReturn(createEvent)).flatMap(sendGoodsCreateEvent -> {
                    if (sendGoodsCreateEvent.getLogisticsOrderType()
                            .equals(LogisticsOrderTypeEnum.MERCHANT) || sendGoodsCreateEvent
                            .getLogisticsOrderType().equals(LogisticsOrderTypeEnum.GIFT)) {
                        return logisticsGoodsRootQueryRepository
                                .selectGoodsInfoById(LongIdentifier.builder()
                                        .id(sendGoodsCreateEvent.getLogisticsOrderId()).build())
                                .flatMap(goodsId -> logisticsGoodsRootQueryRepository
                                        .queryGoodsSendSuccess(LogisticsOrderRoot.builder()
                                                .identifier(LogisticsOrderIdentifier.builder()
                                                        .logisticsOrderId(sendGoodsCreateEvent
                                                                .getLogisticsOrderId())
                                                        .build())
                                                .goodsValueObject(GoodsValueObject.builder()
                                                        .sendGoodsId(goodsId).build())
                                                .build())
                                        .flatMap(count -> {
                                            if (count == 0) {
                                                EventRoot eventRoot = EventRoot.builder()
                                                        .domainEvent(
                                                                LogisticsSendGoodsSuccessEvent
                                                                        .builder()
                                                                        .identifier(EventRoot
                                                                                .getCommonsDomainEventIdentifier(
                                                                                        LogisticsSendGoodsSuccessEvent.class))
                                                                        .goodsId(goodsId)
                                                                        .build())
                                                        .isQueue(true).build();
                                                return eventRootService
                                                        .publisheByMono(eventRoot)
                                                        .thenReturn(sendGoodsCreateEvent);
                                            }
                                            return Mono.just(sendGoodsCreateEvent);
                                        }));
                    }
                    return Mono.just(sendGoodsCreateEvent);
                })
                .flatMap(createEvent -> commandDispatcher
                        .executeCommand(Mono.just(new CreateOrderLogCommand()),
                                CreateOrderLogCommand.class, command -> {
                                    command.setOrderNo(createEvent.getOrderNo());
                                    command.setOrderLogMsgEnum(OrderLogMsgEnum.SHIPPED);
                                    command.setReason("发货");
                                    command.buildCreate(createEvent.getUserId());
                                    return command;
                                })
                        .onErrorResume(throwable -> {
                            log.error("处理发货事件异常", throwable);
                            return Mono.error(throwable);
                        }));
    }
}

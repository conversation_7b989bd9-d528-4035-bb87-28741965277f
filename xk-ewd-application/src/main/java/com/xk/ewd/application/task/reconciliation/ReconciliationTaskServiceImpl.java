package com.xk.ewd.application.task.reconciliation;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.ewd.domain.event.financial.FinancialReconciliationPagerEvent;
import com.xk.ewd.domain.event.payment.PaymentReconciliationPagerEvent;
import com.xk.ewd.domain.service.financial.FinancialTransactionRootService;
import com.xk.ewd.domain.service.payment.PaymentRootService;
import com.xk.ewd.interfaces.task.reconciliation.ReconciliationTaskService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReconciliationTaskServiceImpl implements ReconciliationTaskService {

    private static final Integer PAGE_SIZE = 100;
    private final PaymentRootService paymentRootService;
    private final FinancialTransactionRootService financialTransactionRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> createDailyReconciliation(Date date) {
        Mono<Integer> countPaymentDetail = paymentRootService.countCurrentDate(date);
        Mono<Integer> countTransaction = financialTransactionRootService.countCurrentDate(date);
        return countPaymentDetail.flatMapMany(totalCount -> {
            // 多算一页防止中途插入数据导致漏算
            int totalPages = (int) Math.ceil((double) totalCount / PAGE_SIZE) + 2;
            return Flux.range(1, totalPages).flatMap(page -> {
                EventRoot root = EventRoot.builder()
                        .domainEvent(PaymentReconciliationPagerEvent.builder()
                                .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                        PaymentReconciliationPagerEvent.class))
                                .pageNum(page).reconciliationDate(date).pageSize(PAGE_SIZE).build())
                        .isQueue(true).build();
                return eventRootService.publisheByMono(root);
            });
        }).then(countTransaction).flatMapMany(totalCount -> {
            // 多算一页防止中途插入数据导致漏算
            int totalPages = (int) Math.ceil((double) totalCount / PAGE_SIZE) + 2;
            return Flux.range(1, totalPages).flatMap(page -> {
                EventRoot root = EventRoot.builder()
                        .domainEvent(FinancialReconciliationPagerEvent.builder()
                                .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                        FinancialReconciliationPagerEvent.class))
                                .pageNum(page).reconciliationDate(date).pageSize(PAGE_SIZE).build())
                        .isQueue(true).build();
                return eventRootService.publisheByMono(root);
            });
        }).then();
    }
}

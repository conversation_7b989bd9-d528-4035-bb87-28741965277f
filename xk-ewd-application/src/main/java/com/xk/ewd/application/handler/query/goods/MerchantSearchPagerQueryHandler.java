package com.xk.ewd.application.handler.query.goods;

import java.util.Collections;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.goods.MerchantSearchPagerQuery;
import com.xk.ewd.application.dto.goods.GoodsEwdAppDto;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootQueryRepository;
import com.xk.ewd.enums.common.CommonSortEnum;
import com.xk.ewd.interfaces.dto.rsp.goods.BusinessResDto;
import com.xk.ewd.interfaces.dto.rsp.goods.FortuneBoxResDto;
import com.xk.ewd.interfaces.dto.rsp.goods.MerchantPagerResDto;
import com.xk.ewd.interfaces.dto.rsp.goods.NoneFortuneBoxResDto;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantSearchPagerQueryHandler
        implements IActionQueryHandler<MerchantSearchPagerQuery, Pagination> {

    private final GoodsEwdRootQueryRepository goodsEwdRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<MerchantSearchPagerQuery> mono) {
        return this.execute(mono, query -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            Map<String, Object> criteria = CollectionHelper.converBeanToMap(query);
            criteria.put("goodsType", GoodsTypeEnum.MERCHANT_PRODUCT.getCode());
            CommonSortEnum.getSort(criteria, query.getSort());
            pagination.setCriteria(criteria);
            return pagination;
        }, goodsEwdRootQueryRepository::searchPager, MerchantPagerResDto.class,
                (entity, resDtoClass) -> {
                    GoodsEwdAppDto appDto = converter.convert(entity, GoodsEwdAppDto.class);
                    MerchantPagerResDto convert = converter.convert(appDto, resDtoClass);
                    Long goodsResId = entity.getGoodsResId();
                    convert.setProductPicList(Collections.singletonList(BusinessResDto.builder()
                            .resId(goodsResId == null ? 0 : goodsResId.intValue())
                            .businessResType(BusinessResTypeEnum.PRODUCT_PICTURE.getCode())
                            .addr(entity.getGoodsImage()).build()));
                    if (ProductTypeEnum.FORTUNE_BOX.getCode().equals(entity.getProductType())) {
                        FortuneBoxResDto fortuneBoxResDto = new FortuneBoxResDto();
                        fortuneBoxResDto.setAmount(appDto.getLowestPrice());
                        fortuneBoxResDto.setCurrencyType(entity.getCurrencyType());
                        convert.setFortuneBoxResDto(fortuneBoxResDto);
                    } else {
                        NoneFortuneBoxResDto noneFortuneBoxResDto = new NoneFortuneBoxResDto();
                        noneFortuneBoxResDto.setRandomType(entity.getRandomType());
                        convert.setNoneFortuneBoxResDto(noneFortuneBoxResDto);
                    }
                    if (convert.getRemainStock() != null && convert.getTotalStock() != null) {
                        convert.setSoldStock(convert.getTotalStock() - convert.getRemainStock());
                    }
                    return convert;
                });
    }
}

package com.xk.ewd.application.action.command.reconciliation;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.ewd.domain.model.financial.identifier.FinancialTransactionIdentifier;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationItemEntity;
import com.xk.ewd.infrastructure.convertor.financial.FinancialPayDirectionEnumConvertor;
import com.xk.ewd.infrastructure.convertor.financial.PayTypeEnumConvertor;
import com.xk.ewd.infrastructure.convertor.reconciliation.CorrectedResultEnumConvertor;
import com.xk.ewd.infrastructure.convertor.reconciliation.ReceiptItemStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = ReconciliationItemEntity.class, reverseConvertGenerate = false,
        uses = {ReceiptItemStatusEnumConvertor.class, CorrectedResultEnumConvertor.class,
                FinancialPayDirectionEnumConvertor.class, PayTypeEnumConvertor.class})})
public class CreateReconciliationItemCommand extends AbstractActionCommand {
    /**
     * 对账单据标识符
     */
    private Long reconciliationItemId;

    /**
     * 三方流水标识符
     */
    private FinancialTransactionIdentifier financialTransactionId;

    /**
     * 星卡内部流水号
     */
    private String payNo;

    /**
     * 应收金额（单位：分）
     */
    private Long totalAmount;

    /**
     * 实收金额（单位：分）
     */
    private Long actualAmount;

    /**
     * 三方交易金额（单位：分）
     */
    private Long tpAmount;

    /**
     * 交易方向
     */
    private Integer payDirection;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 订单标识符
     */
    private String orderNo;

    /**
     * 商品标识符
     */
    private Long goodsId;

    /**
     * 商户标识符
     */
    private Long corpId;

    /**
     * 交易创建时间
     */
    private Date payCreateTime;

    /**
     * 收款单据状态
     */
    private Integer receiptItemStatus;

    /**
     * 订正结果
     */
    private Integer correctedResult;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;
}

package com.xk.ewd.application.handler.query.goods;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.enums.object.CorpCountType;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.acct.interfaces.dto.req.follow.CorpFollowQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.SelectByUserQueryReqDto;
import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import com.xk.domain.model.stock.valobj.StockRemainValObj;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.ewd.application.action.query.goods.SearchCorpMerchantQuery;
import com.xk.ewd.domain.model.goods.entity.GoodsEwdEntity;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.goods.SearchCorpMerchantRspDto;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.goods.ListingStatusEnum;
import com.xk.goods.enums.serial.ShowEnum;
import com.xk.infrastructure.cache.dao.user.UserBoughtGoodsDao;
import com.xk.infrastructure.cache.key.user.UserBoughtGoodsKey;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 搜索企业商户查询处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchCorpMerchantQueryHandler
        implements IActionQueryHandler<SearchCorpMerchantQuery, Pagination> {

    private final GoodsEwdRootQueryRepository goodsEwdRootQueryRepository;

    private final UserFollowCorpQueryService userFollowCorpQueryService;

    private final SelectorRootService selectorRootService;

    private final StockRootService stockRootService;

    private final UserBoughtGoodsDao userBoughtGoodsDao;

    private final Converter converter;
    Function<Long, CorpFollowQueryReqDto> queryFunc = corpInfoId -> {
        CorpFollowQueryReqDto reqDto =
                CorpFollowQueryReqDto.builder().corpInfoId(corpInfoId).build();
        reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
        return reqDto;
    };

    @Override
    public Mono<Pagination> execute(Mono<SearchCorpMerchantQuery> query) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .map(v -> v.getUserDataObjectEntity().getUserId()).switchIfEmpty(Mono.just(-1L))
                .flatMap(userId -> query
                        .doOnNext(q -> log.info("Executing corp merchant search for user: {}",
                                userId))
                        .flatMap(this::buildPagination)
                        .flatMap(pagination -> processCorpMerchantResults(pagination, userId,
                                query)));
    }

    /**
     * 构建分页对象
     */
    public Mono<Pagination> buildPagination(SearchCorpMerchantQuery searchQuery) {
        searchQuery.setGoodsType(GoodsTypeEnum.MERCHANT_PRODUCT.getCode());
        searchQuery.setSoldOutStatus(CommonStatusEnum.DISABLE.getCode());
        searchQuery.setFinishStatus(CommonStatusEnum.DISABLE.getCode());
        searchQuery.setListingStatus(ListingStatusEnum.UP.getCode());
        searchQuery.setShowStatus(ShowEnum.SHOW.getCode());

        Pagination pagination = new Pagination();
        pagination.setLimit(searchQuery.getLimit());
        pagination.setOffset(searchQuery.getOffset());
        pagination.setCriteria(CollectionHelper.converBeanToMap(searchQuery));

        return Mono.just(pagination);
    }

    /**
     * 获取用户关注的企业
     */
    public Mono<Set<Long>> getUserFollowedCorps(Long userId, Set<Long> corpIds) {
        if (userId == -1) {
            return Mono.just(new HashSet<>());
        }
        SelectByUserQueryReqDto request =
                SelectByUserQueryReqDto.builder().userId(userId).corpInfoIdList(corpIds).build();
        request.setSessionId(SessionRoot.getInternalDefaultSessionId());
        return userFollowCorpQueryService.userCorps(Mono.just(request));
    }

    /**
     * 处理企业商户搜索结果
     */
    public Mono<Pagination> processCorpMerchantResults(Pagination pagination, Long userId,
            Mono<SearchCorpMerchantQuery> query) {
        return goodsEwdRootQueryRepository.corpMerchantSearch(pagination).collectList()
                .flatMap(entities -> {
                    Set<Long> corpIds = entities.stream().map(GoodsEwdEntity::getCorpInfoId)
                            .collect(Collectors.toSet());

                    return getUserFollowedCorps(userId, corpIds)
                            .flatMap(followedCorps -> buildCorpMerchantResponses(pagination,
                                    entities, followedCorps, query))
                            .flatMap(this::enrichWithStockAndUserData);
                });
    }

    /**
     * 构建企业商户响应
     */
    public Mono<Pagination> buildCorpMerchantResponses(Pagination pagination,
            List<GoodsEwdEntity> entities, Set<Long> followedCorps,
            Mono<SearchCorpMerchantQuery> query) {
        return Flux.fromIterable(entities)
                .flatMap(entity -> buildSingleCorpMerchantResponse(entity, followedCorps, query))
                .collectList().map(responses -> {
                    pagination.setRecords(responses);
                    return pagination;
                });
    }

    /**
     * 构建单个企业商户响应
     */
    public Mono<SearchCorpMerchantRspDto> buildSingleCorpMerchantResponse(GoodsEwdEntity entity,
            Set<Long> followedCorps, Mono<SearchCorpMerchantQuery> query) {
        return userFollowCorpQueryService
                .followNumber(Mono.just(queryFunc.apply(entity.getCorpInfoId())))
                .flatMap(corpFollowNumberRspDtp -> selectorRootService
                        .getCorpObject(entity.getCorpInfoId())
                        .flatMap(corpObjectRoot -> getCorpGoods(entity.getCorpInfoId(), query)
                                .flatMap(goodsList -> Mono.just(SearchCorpMerchantRspDto.builder()
                                        .corpInfoId(entity.getCorpInfoId())
                                        .corpName(corpObjectRoot.getCorpInfoObjectEntity()
                                                .getCorpName())
                                        .followNumber(corpFollowNumberRspDtp.getNumber())
                                        .soldNumber(corpObjectRoot.getCorpCountMap()
                                                .getOrDefault(CorpCountType.GOODS, 0))
                                        .corpLogo(corpObjectRoot.getCorpInfoObjectEntity()
                                                .getCorpLogo())
                                        .isFollow(followedCorps.contains(entity.getCorpInfoId())
                                                ? CommonStatusEnum.ENABLE.getCode()
                                                : CommonStatusEnum.DISABLE.getCode())
                                        .goodsInfos(goodsList).build()))));

    }

    /**
     * 获取企业商品信息
     */
    public Mono<List<SearchCorpMerchantRspDto.GoodsInfo>> getCorpGoods(Long corpInfoId,
            Mono<SearchCorpMerchantQuery> query) {
        return query.flatMap(searchQuery -> {
            searchQuery.setGoodsType(GoodsTypeEnum.MERCHANT_PRODUCT.getCode());
            searchQuery.setCorpInfoId(corpInfoId);
            searchQuery.setSoldOutStatus(CommonStatusEnum.DISABLE.getCode());
            searchQuery.setFinishStatus(CommonStatusEnum.DISABLE.getCode());
            searchQuery.setListingStatus(ListingStatusEnum.UP.getCode());
            searchQuery.setShowStatus(ShowEnum.SHOW.getCode());

            Pagination pagination = new Pagination();
            pagination.setLimit(2); // 限制每个企业显示2个商品
            pagination.setOffset(0);
            pagination.setCriteria(CollectionHelper.converBeanToMap(searchQuery));

            return goodsEwdRootQueryRepository.corpMerchantGoodsSearch(pagination)
                    .flatMap(this::buildGoodsInfo).collectList();
        });
    }

    /**
     * 构建商品信息
     */
    public Mono<SearchCorpMerchantRspDto.GoodsInfo> buildGoodsInfo(GoodsEwdEntity goodsEntity) {
        return selectorRootService.getGoodsObject(goodsEntity.getGoodsId()).map(goodsObjectRoot -> {
            String goodsImages = extractGoodsImages(goodsObjectRoot);

            Long remainRandomAmount = 0L;
            Integer remainRandomStatus = 0;
            if (goodsObjectRoot.getGoodsInfo().getCardGoods() != null) {
                remainRandomAmount =
                        goodsObjectRoot.getGoodsInfo().getCardGoods().getRemainRandomAmount();
                remainRandomStatus =
                        goodsObjectRoot.getGoodsInfo().getCardGoods().getRemainRandomStatus();
            }

            return SearchCorpMerchantRspDto.GoodsInfo.builder().goodsId(goodsEntity.getGoodsId())
                    .collectibleCardId(goodsEntity.getCollectibleCardId())
                    .productType(goodsEntity.getProductType()).goodsImages(goodsImages)
                    .randomType(goodsEntity.getRandomType()).goodsName(goodsEntity.getGoodsName())
                    .collectibleCardName(goodsEntity.getCollectibleCardName())
                    .remainRandomAmount(remainRandomAmount).remainRandomStatus(remainRandomStatus)
                    .stockAmount(goodsObjectRoot.getGoodsInfo().getStock())
                    .amount(goodsObjectRoot.getGoodsInfo().getLowestPrice()).build();
        });
    }

    /**
     * 提取商品图片
     */
    public String extractGoodsImages(
            com.myco.mydata.domain.model.object.goods.GoodsObjectRoot goodsObjectRoot) {
        if (CollectionUtils.isEmpty(goodsObjectRoot.getResList())) {
            return "";
        }

        return goodsObjectRoot.getResList().stream()
                .filter(v -> BusinessResTypeEnum.PRODUCT_PICTURE.name().equals(v.getResType()))
                .findFirst().map(GoodsResValueObject::getResAddr).orElse("");
    }

    /**
     * 丰富库存和用户数据
     */
    public Mono<Pagination> enrichWithStockAndUserData(Pagination pagination) {
        List<SearchCorpMerchantRspDto> responses = pagination.getRecords();

        Map<StringIdentifier, SearchCorpMerchantRspDto.GoodsInfo> identifierMap = new HashMap<>();
        List<Long> goodsIds = new ArrayList<>();

        // 收集商品信息
        collectGoodsInfo(responses, identifierMap, goodsIds);

        return ReadSynchronizationUtils.getUserObjectMono(false)
                .map(v -> v.getUserDataObjectEntity().getUserId()).switchIfEmpty(Mono.just(-1L))
                .flatMap(userId -> getUserBoughtGoods(userId, goodsIds)
                        .flatMap(userBoughtBits -> enrichWithStock(identifierMap, userBoughtBits)
                                .thenReturn(pagination)));
    }

    /**
     * 收集商品信息
     */
    public void collectGoodsInfo(List<SearchCorpMerchantRspDto> responses,
            Map<StringIdentifier, SearchCorpMerchantRspDto.GoodsInfo> identifierMap,
            List<Long> goodsIds) {
        for (SearchCorpMerchantRspDto response : responses) {
            if (CollectionUtils.isNotEmpty(response.getGoodsInfos())) {
                response.getGoodsInfos().forEach(goodsInfo -> {
                    StringIdentifier identifier = StringIdentifier.builder()
                            .id(String.valueOf(goodsInfo.getGoodsId())).build();
                    identifierMap.put(identifier, goodsInfo);

                    // 处理随机剩余状态
                    if (CommonStatusEnum.ENABLE.getCode()
                            .equals(goodsInfo.getRemainRandomStatus())) {
                        goodsInfo.setAmount(goodsInfo.getRemainRandomAmount());
                    }

                    goodsIds.add(goodsInfo.getGoodsId());
                });
            }
        }
    }

    /**
     * 获取用户购买记录
     */
    public Mono<Map<Long, Boolean>> getUserBoughtGoods(Long userId, List<Long> goodsIds) {
        if (userId == -1L || CollectionUtils.isEmpty(goodsIds)) {
            return Mono.just(new HashMap<>());
        }

        return Mono.fromCallable(() -> {
            UserBoughtGoodsKey key = UserBoughtGoodsKey.builder().userId(userId).build();
            long[] goodsIdArray = goodsIds.stream().mapToLong(l -> l).toArray();
            return userBoughtGoodsDao.getBits(key, goodsIdArray);
        });
    }

    /**
     * 丰富库存信息
     */
    public Mono<Void> enrichWithStock(
            Map<StringIdentifier, SearchCorpMerchantRspDto.GoodsInfo> identifierMap,
            Map<Long, Boolean> userBoughtBits) {
        if (MapUtils.isEmpty(identifierMap)) {
            return Mono.empty();
        }

        StringIdentifier[] identifiers = identifierMap.keySet().toArray(new StringIdentifier[0]);

        return stockRootService.findRemainRealStock(StockBusinessTypeEnum.GOODS, identifiers)
                .doOnNext(stock -> processStockAndDiscount(stock, identifierMap, userBoughtBits))
                .then();
    }

    /**
     * 处理库存和折扣逻辑
     */
    private void processStockAndDiscount(StockRemainValObj stockValObj,
            Map<StringIdentifier, SearchCorpMerchantRspDto.GoodsInfo> identifierMap,
            Map<Long, Boolean> userBoughtBits) {
        if (stockValObj == null)
            return;

        // 根据实际的stock对象类型进行处理
        SearchCorpMerchantRspDto.GoodsInfo dto = identifierMap.get(stockValObj.getBusinessId());
        if (dto == null)
            return;

        dto.setResidueStockAmount(stockValObj.getRemainRealStock());
        applyFirstBuyDiscount(dto, userBoughtBits);
    }

    /**
     * 应用首购折扣
     */
    private void applyFirstBuyDiscount(SearchCorpMerchantRspDto.GoodsInfo dto,
            Map<Long, Boolean> userBoughtBits) {
        boolean isDiscountEnabled =
                CommonStatusEnum.ENABLE.getCode().equals(dto.getFirstBuyDiscountStatus());
        if (!isDiscountEnabled || dto.getFirstBuyDiscountAmount()== null) {
            return;
        }
        if (MapUtils.isEmpty(userBoughtBits)) {
            dto.setAmount(dto.getAmount() - dto.getFirstBuyDiscountAmount());
            return;
        }

        boolean isGoodsNotInBits = !userBoughtBits.containsKey(dto.getGoodsId())
                || !userBoughtBits.get(dto.getGoodsId());

        if (isGoodsNotInBits) {
            dto.setAmount(dto.getAmount() - dto.getFirstBuyDiscountAmount());
        }
    }
}

package com.xk.ewd.application.handler.event.financial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.xk.ewd.application.action.command.financialTransaction.CreateFinancialTransactionCommand;
import com.xk.ewd.domain.model.financial.FinancialTransactionRoot;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.domain.model.financial.identifier.FinancialTransactionIdentifier;
import com.xk.ewd.domain.repository.financial.FinancialTransactionRootQueryRepository;
import com.xk.ewd.domain.service.financial.FinancialTransactionRootService;
import com.xk.ewd.enums.financial.FinancialPlatformTypeEnum;
import com.xk.tp.domain.event.reconciled.CreateFinancialTransactionEvent;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreateFinancialTransactionEventHandler
        extends AbstractEventVerticle<CreateFinancialTransactionEvent> {

    private final FinancialTransactionRootService financialTransactionRootService;

    private final FinancialTransactionRootQueryRepository financialTransactionRootQueryRepository;

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CreateFinancialTransactionEvent> mono) {
        // 保存用户
        Mono<FinancialTransactionEntity> saveMono =
                financialTransactionRootService.generateId().flatMap(id -> {
                    return mono.flatMap(createFinancialTransactionEvent -> {
                        // 执行用户创建命令
                        return commandDispatcher
                                .executeCommand(Mono.just(new CreateFinancialTransactionCommand()),
                                        CreateFinancialTransactionCommand.class, command -> {
                                            command.buildCreate(-1L);
                                            command.setId(id);
                                            command.setPayPlatformTypeEnum(
                                                    PayPlatformTypeEnum.getEnumByValue(createFinancialTransactionEvent
                                                            .getPayPlatformType()));
                                            command.setFinancialTransactionId(
                                                    createFinancialTransactionEvent
                                                            .getFinancialTransactionId());
                                            command.setFinancialDate(createFinancialTransactionEvent
                                                    .getFinancialDate());
                                            command.setPayNo(
                                                    createFinancialTransactionEvent.getPayNo());
                                            command.setPayAmount(
                                                    createFinancialTransactionEvent.getPayAmount());
                                            command.setPayAccount(createFinancialTransactionEvent
                                                    .getPayAccount());
                                            command.setReceiveAccount(
                                                    createFinancialTransactionEvent
                                                            .getReceiveAccount());
                                            command.setPayType(
                                                    createFinancialTransactionEvent.getPayType());
                                            command.setPayDirection(createFinancialTransactionEvent
                                                    .getPayDirection());
                                            command.setPayCreateTime(createFinancialTransactionEvent
                                                    .getPayCreateTime());
                                            return command;
                                        }, CreateFinancialTransactionCommand.class)
                                .thenReturn(FinancialTransactionEntity.builder().build());
                    });
                });

        return mono
                .flatMap(createFinancialTransactionEvent -> financialTransactionRootQueryRepository
                        .selectByTransactionId(FinancialTransactionRoot.builder()
                                .identifier(FinancialTransactionIdentifier.builder()
                                        .financialTransactionId(createFinancialTransactionEvent
                                                .getFinancialTransactionId())
                                        .financialPlatformType(FinancialPlatformTypeEnum.getByCode(createFinancialTransactionEvent
                                                .getPayPlatformType()))
                                        .build())
                                .financialTransactionEntity(FinancialTransactionEntity.builder()
                                        .financialTransactionId(createFinancialTransactionEvent
                                                .getFinancialTransactionId())
                                        .financialPlatformType(FinancialPlatformTypeEnum.getByCode(createFinancialTransactionEvent
                                                .getPayPlatformType()))
                                        .build())
                                .build())
                        .switchIfEmpty(saveMono).then());
    }
}

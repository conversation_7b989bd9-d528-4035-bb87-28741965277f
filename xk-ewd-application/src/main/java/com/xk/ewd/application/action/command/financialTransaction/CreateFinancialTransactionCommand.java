package com.xk.ewd.application.action.command.financialTransaction;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;

import com.xk.domain.model.common.CreateValObj;
import com.xk.ewd.domain.model.financial.FinancialTransactionRoot;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.enums.financial.FinancialPlatformTypeEnum;
import com.xk.ewd.infrastructure.convertor.financial.FinancialPayDirectionEnumConvertor;
import com.xk.ewd.infrastructure.convertor.financial.PayTypeEnumConvertor;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;
import reactor.core.publisher.Mono;

@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = FinancialTransactionEntity.class, reverseConvertGenerate = false,uses = {PayTypeEnumConvertor.class, FinancialPayDirectionEnumConvertor.class})})
public class CreateFinancialTransactionCommand extends AbstractActionCommand {

    private Long id;

    private PayPlatformTypeEnum payPlatformTypeEnum;

    private String financialTransactionId;

    private Date financialDate;

    private String payNo;

    private Long payAmount;

    private String payAccount;

    private String receiveAccount;

    private Integer payType;

    private Integer payDirection;

    private Date payCreateTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    public void buildCreate(Long createId) {
        this.createId = createId;
        this.createTime = new Date();
    }

    public Mono<FinancialTransactionRoot> buildRoot(Converter converter) {
        FinancialTransactionEntity convert = converter.convert(this, FinancialTransactionEntity.class);
        convert.setFinancialPlatformType(FinancialPlatformTypeEnum.getByCode(payPlatformTypeEnum.getValue()));
        convert.setCreateValObj(CreateValObj.builder()
                        .createId(createId)
                        .createTime(createTime)
                .build());
        return Mono.just(FinancialTransactionRoot.builder().identifier(convert.getIdentifier())
                .financialTransactionEntity(convert).build());
    }
}

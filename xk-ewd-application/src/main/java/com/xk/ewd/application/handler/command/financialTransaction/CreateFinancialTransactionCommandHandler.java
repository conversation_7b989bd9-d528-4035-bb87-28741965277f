package com.xk.ewd.application.handler.command.financialTransaction;

import com.xk.ewd.application.action.command.financialTransaction.CreateFinancialTransactionCommand;
import com.xk.ewd.domain.repository.financial.FinancialTransactionRootRepository;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class CreateFinancialTransactionCommandHandler
        implements IActionCommandHandler<CreateFinancialTransactionCommand, Void> {

    private final Converter converter;
    private final FinancialTransactionRootRepository financialTransactionRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateFinancialTransactionCommand> mono) {
        return mono.flatMap(
                command -> command.buildRoot(converter).flatMap(financialTransactionRootRepository::save));
    }
}

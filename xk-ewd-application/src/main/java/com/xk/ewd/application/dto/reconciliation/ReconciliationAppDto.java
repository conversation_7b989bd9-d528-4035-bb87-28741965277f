package com.xk.ewd.application.dto.reconciliation;

import java.io.Serializable;
import java.util.Date;

import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationEntity;
import com.xk.ewd.interfaces.dto.rsp.reconciliation.ReconciliationPagerRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = ReconciliationPagerRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = ReconciliationEntity.class, convertGenerate = false)})
public class ReconciliationAppDto implements Serializable {

    /**
     * 对账日期
     */
    private Date reconciliationDate;

    /**
     * 对账名称
     */
    private String reconciliationName;

    /**
     * 对账单据总数
     */
    private Integer reconciliationTotalCount;

    /**
     * 收款正常总数
     */
    private Integer reconciliationSuccessCount;

    /**
     * 收款异常总数
     */
    private Integer reconciliationFailCount;

    /**
     * 总实收金额
     */
    private Long reconciliationTotalAmount;

    /**
     * 总正常金额
     */
    private Long reconciliationSuccessAmount;

    /**
     * 总异常金额
     */
    private Long reconciliationFailAmount;
}

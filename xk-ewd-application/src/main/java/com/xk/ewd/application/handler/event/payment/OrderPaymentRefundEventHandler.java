package com.xk.ewd.application.handler.event.payment;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.model.payment.PaymentRoot;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.domain.service.payment.PaymentRootService;
import com.xk.order.domain.event.payment.OrderPaymentRefundEvent;
import com.xk.order.enums.payment.PaymentPayTypeEnum;
import com.xk.order.enums.payment.RefundStatusEnum;
import com.xk.order.enums.payment.RefundTypeEnum;
import com.xk.order.interfaces.dto.req.order.OrderNoReq;
import com.xk.order.interfaces.dto.rsp.order.OrderGoodsInfoRsp;
import com.xk.order.interfaces.query.order.OrderQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaymentRefundEventHandler extends AbstractEventVerticle<OrderPaymentRefundEvent> {

    private final OrderQueryService orderQueryService;
    private final PaymentRootService paymentRootService;

    @Override
    public Mono<Void> handle(Mono<OrderPaymentRefundEvent> event) {

        return event.flatMap(orderPaymentRefundEvent ->{

            if (orderPaymentRefundEvent.isAutoRefund()){
                log.info("OrderPaymentRefundEventHandler 进行自动退款 {}",orderPaymentRefundEvent.getOrderNo());
                return Mono.empty();
            }

            log.info("OrderPaymentRefundEventHandler 接收创建退货单事件:{}",orderPaymentRefundEvent.getOrderNo());
            //获取订单信息
            return orderQueryService.searchByIdDetail(Mono.just(OrderNoReq.builder().orderNo(orderPaymentRefundEvent.getOrderNo()).build())).flatMap(orderSearchByIdDetailRsp->{
                // 商品信息
                String goodsId = orderSearchByIdDetailRsp
                        .getGoodsInfo().stream()
                        .map(OrderGoodsInfoRsp::getGoodsId)
                        .sorted().map(id -> id + "")
                        .collect(Collectors.joining(","));
                String goodsName = orderSearchByIdDetailRsp
                        .getGoodsInfo().stream()
                        .map(OrderGoodsInfoRsp::getGoodsName)
                        .sorted()
                        .collect(Collectors.joining(","));
                log.info("OrderPaymentRefundEventHandler 商品信息：{}，{}",goodsId,goodsName);
                //添加
                return paymentRootService.createRefund(PaymentRoot.builder()
                                .refundEntity(RefundEntity.builder()
                                        .paymentId(orderPaymentRefundEvent.getPaymentId())
                                        .orderNo(orderPaymentRefundEvent.getOrderNo())
                                        .payNo(orderPaymentRefundEvent.getPayNo())
                                        .userId(orderSearchByIdDetailRsp.getUserId())
                                        .username(orderSearchByIdDetailRsp.getUserNick())
                                        .refundType(Objects.nonNull(orderPaymentRefundEvent.getRefundType())?RefundTypeEnum.getByCode(orderPaymentRefundEvent.getRefundType()):null)
                                        .refundStatus(RefundStatusEnum.getByCode(orderPaymentRefundEvent.getRefundStatus()))
                                        .platformType(PlatformTypeEnum.getByValue(orderPaymentRefundEvent.getPlatformType()))
                                        .payType(Objects.nonNull(orderPaymentRefundEvent.getPayType())?PaymentPayTypeEnum.getByCode(orderPaymentRefundEvent.getPayType()):null)
                                        .payTime(orderPaymentRefundEvent.getPayTime())
                                        .refundTime(orderPaymentRefundEvent.getRefundTime())
                                        .goodsId(goodsId)
                                        .goodsName(goodsName)
                                        .corpId(orderSearchByIdDetailRsp.getCorpId())
                                        .corpName(orderSearchByIdDetailRsp.getCorpName())
                                        .amount(orderPaymentRefundEvent.getAmount())
                                        .remark(orderPaymentRefundEvent.getRemark())
                                        .orderType(orderSearchByIdDetailRsp.getOrderType())
                                        .build())
                        .build());
            });
        });
    }
    @Override
    public boolean isBlockExecute() {
        return true;
    }
}

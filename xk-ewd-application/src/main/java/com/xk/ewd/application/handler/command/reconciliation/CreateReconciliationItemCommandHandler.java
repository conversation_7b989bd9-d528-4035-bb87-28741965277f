package com.xk.ewd.application.handler.command.reconciliation;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.ewd.application.action.command.reconciliation.CreateReconciliationItemCommand;
import com.xk.ewd.domain.model.reconciliation.ReconciliationRoot;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationEntity;
import com.xk.ewd.domain.repository.reconciliation.ReconciliationRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class CreateReconciliationItemCommandHandler
        implements IActionCommandHandler<CreateReconciliationItemCommand, Void> {

    private final ReconciliationRootRepository reconciliationRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateReconciliationItemCommand> mono) {
        return execute(mono, ReconciliationEntity.class, converter::convert,
                entity -> ReconciliationRoot.builder().identifier(entity.getIdentifier())
                        .reconciliationEntity(entity).build(),
                reconciliationRootRepository::save);
    }
}

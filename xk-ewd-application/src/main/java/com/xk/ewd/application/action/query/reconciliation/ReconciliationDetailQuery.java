package com.xk.ewd.application.action.query.reconciliation;

import java.util.Date;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationSearchPagerReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = ReconciliationSearchPagerReq.class)})
public class ReconciliationDetailQuery extends PagerQuery implements IActionQuery {

    /**
     * 收款单据状态 1-正常 2-异常
     */
    private Integer receiptItemStatus;

    /**
     * 对账日期
     */
    private Date reconciliationDate;
}

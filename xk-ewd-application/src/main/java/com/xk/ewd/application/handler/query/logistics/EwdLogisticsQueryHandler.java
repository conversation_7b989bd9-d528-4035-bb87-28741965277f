package com.xk.ewd.application.handler.query.logistics;

import java.util.Map;
import java.util.function.Function;

import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.logistics.EwdLogisticsOrderQuery;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.ewd.enums.common.CommonSortEnum;
import com.xk.ewd.interfaces.dto.rsp.logistics.EwdLogisticsOrderRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class EwdLogisticsQueryHandler
        implements IActionQueryHandler<EwdLogisticsOrderQuery, Pagination> {

    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<EwdLogisticsOrderQuery> query) {
        Function<EwdLogisticsOrderQuery, Mono<Pagination>> getReq =
                q -> ReadSynchronizationUtils.getUserObjectMono(true).flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        q.setCorpId(userDataObjectEntity.getCorpId());
                    }

                    Pagination pagination = new Pagination();
                    pagination.setLimit(q.getLimit());
                    pagination.setOffset(q.getOffset());

                    Map<String, Object> criteria = CollectionHelper.converBeanToMap(q);
                    CommonSortEnum.getSort(criteria, q.getSort());
                    pagination.setCriteria(criteria);
                    return Mono.just(pagination);
                });

        return query.flatMap(orderQuery -> ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        orderQuery.setCorpId(userDataObjectEntity.getCorpId());
                    }
                    return Mono.just(orderQuery);
                }).flatMap(getReq)
                .flatMap(pagination -> logisticsOrderRootQueryRepository.selectList(pagination)
                        .flatMap(logisticsOrderRoot -> Mono.just(converter
                                .convert(logisticsOrderRoot, EwdLogisticsOrderRspDto.class)))
                        .collectList().flatMap(list -> {
                            pagination.setRecords(list);
                            return Mono.just(pagination);
                        })));
    }

}

package com.xk.ewd.application.handler.event.financialTransaction;

import java.util.Date;
import java.util.function.Function;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.xk.acct.enums.user.RegisterTypeEnum;
import com.xk.acct.enums.user.UserRegisterChannelEnum;
import com.xk.ewd.application.action.command.financialTransaction.CreateFinancialTransactionCommand;
import com.xk.ewd.application.action.command.log.CreateOrderLogCommand;
import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionEntity;
import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionRoot;
import com.xk.ewd.domain.model.financialTransaction.ids.FinancialTransactionIdentifier;
import com.xk.ewd.domain.repository.financialTransaction.FinancialTransactionRootQueryRepository;
import com.xk.ewd.domain.repository.financialTransaction.FinancialTransactionRootRepository;
import com.xk.ewd.domain.service.financialTransaction.FinancialTransactionRootService;
import com.xk.ewd.enums.order.OrderLogMsgEnum;
import com.xk.tp.domain.event.reconciled.CreateFinancialTransactionEvent;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.IntegerIdentifier;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.ewd.domain.model.goods.GoodsEwdRoot;
import com.xk.ewd.domain.repository.goods.GoodsEwdGatewayQueryRepository;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootRepository;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.goods.CreateGoodsEvent;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreateFinancialTransactionEventHandler
        extends AbstractEventVerticle<CreateFinancialTransactionEvent> {

    private final FinancialTransactionRootService financialTransactionRootService;

    private final FinancialTransactionRootQueryRepository financialTransactionRootQueryRepository;

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CreateFinancialTransactionEvent> mono) {
        // 保存用户
        Mono<FinancialTransactionEntity> saveMono =
                financialTransactionRootService.generateId().flatMap(id -> {
                    return mono.flatMap(createFinancialTransactionEvent -> {
                        // 执行用户创建命令
                        return commandDispatcher
                                .executeCommand(Mono.just(new CreateFinancialTransactionCommand()),
                                        CreateFinancialTransactionCommand.class, command -> {
                                            command.buildCreate(-1L);
                                            command.setId(id);
                                            command.setPayPlatformTypeEnum(
                                                    createFinancialTransactionEvent
                                                            .getPayPlatformTypeEnum());
                                            command.setFinancialTransactionId(
                                                    createFinancialTransactionEvent
                                                            .getFinancialTransactionId());
                                            command.setFinancialDate(createFinancialTransactionEvent
                                                    .getFinancialDate());
                                            command.setPayNo(
                                                    createFinancialTransactionEvent.getPayNo());
                                            command.setPayAmount(
                                                    createFinancialTransactionEvent.getPayAmount());
                                            command.setPayAccount(createFinancialTransactionEvent
                                                    .getPayAccount());
                                            command.setReceiveAccount(
                                                    createFinancialTransactionEvent
                                                            .getReceiveAccount());
                                            command.setPayType(
                                                    createFinancialTransactionEvent.getPayType());
                                            command.setPayDirection(createFinancialTransactionEvent
                                                    .getPayDirection());
                                            command.setPayCreateTime(createFinancialTransactionEvent
                                                    .getPayCreateTime());
                                            return command;
                                        }, CreateFinancialTransactionCommand.class)
                                .thenReturn(FinancialTransactionEntity.builder().build());
                    });
                });

        return mono
                .flatMap(createFinancialTransactionEvent -> financialTransactionRootQueryRepository
                        .selectByTransactionId(FinancialTransactionRoot.builder()
                                .identifier(FinancialTransactionIdentifier.builder()
                                        .financialTransactionId(createFinancialTransactionEvent
                                                .getFinancialTransactionId())
                                        .financialPlatformType(createFinancialTransactionEvent
                                                .getPayPlatformTypeEnum().getValue())
                                        .build())
                                .financialTransactionEntity(FinancialTransactionEntity.builder()
                                        .financialTransactionId(createFinancialTransactionEvent
                                                .getFinancialTransactionId())
                                        .financialPlatformType(createFinancialTransactionEvent
                                                .getPayPlatformTypeEnum().getValue())
                                        .build())
                                .build())
                        .switchIfEmpty(saveMono).then());
    }
}

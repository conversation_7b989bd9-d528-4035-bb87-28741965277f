package com.xk.ewd.application.service.reconciliation;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.ewd.application.action.command.reconciliation.UpdateReconciliationItemCommand;
import com.xk.ewd.enums.reconciliation.ReceiptItemStatusEnum;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationItemUpdateReq;
import com.xk.ewd.interfaces.query.reconciliation.ReconciliationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReconciliationServiceImpl implements ReconciliationService {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @BusiCode
    @Override
    public Mono<Void> update(Mono<ReconciliationItemUpdateReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> commandDispatcher
                .executeCommand(mono, UpdateReconciliationItemCommand.class, command -> {
                    command.setReceiptItemStatus(ReceiptItemStatusEnum.NORMAL.getCode());
                    command.setUpdateId(userId);
                    command.setUpdateTime(new Date());
                    return command;
                }));
    }
}

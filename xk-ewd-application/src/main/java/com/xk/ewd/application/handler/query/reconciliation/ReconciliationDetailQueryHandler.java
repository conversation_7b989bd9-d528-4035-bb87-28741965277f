package com.xk.ewd.application.handler.query.reconciliation;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.reconciliation.ReconciliationDetailQuery;
import com.xk.ewd.application.dto.reconciliation.ReconciliationItemAppDto;
import com.xk.ewd.domain.repository.reconciliation.ReconciliationRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.reconciliation.ReconciliationDetailRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class ReconciliationDetailQueryHandler
        implements IActionQueryHandler<ReconciliationDetailQuery, Pagination> {

    private final Converter converter;
    private final ReconciliationRootQueryRepository reconciliationRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<ReconciliationDetailQuery> mono) {
        return mono.flatMap(query -> {
            Pagination pagination = new Pagination();
            pagination.setOffset(query.getOffset());
            pagination.setLimit(query.getLimit());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));

            return reconciliationRootQueryRepository.selectItemByPage(pagination).collectList()
                    .map(v -> {
                        List<ReconciliationDetailRsp> convert = converter.convert(
                                converter.convert(v, ReconciliationItemAppDto.class),
                                ReconciliationDetailRsp.class);
                        pagination.setRecords(convert);
                        return pagination;
                    });
        });
    }
}

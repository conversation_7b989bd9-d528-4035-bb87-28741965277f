package com.xk.ewd.application.action.query.reconciliation;

import java.util.Date;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.reconciliation.ReconciliationSearchPagerReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = ReconciliationSearchPagerReq.class)})
public class ReconciliationSearchPagerQuery extends PagerQuery implements IActionQuery {

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;
}

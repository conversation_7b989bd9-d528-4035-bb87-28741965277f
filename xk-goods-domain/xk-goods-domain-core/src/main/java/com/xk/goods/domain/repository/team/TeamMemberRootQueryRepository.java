package com.xk.goods.domain.repository.team;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

public interface TeamMemberRootQueryRepository extends IQueryRepository {
    Flux<TeamMemberEntity> searchTeamMember(Pagination pagination);
    Flux<TeamMemberEntity> searchTeamMemberInCondition(Pagination pagination);
    Flux<TeamMemberEntity> searchAll();

    /**
     * Batch query team members by multiple conditions
     * @param conditions List of team member conditions
     * @return Flux of matching team members
     */
    Flux<TeamMemberEntity> batchSearchByConditions(List<TeamMemberEntity> conditions);

    /**
     * Batch query team members by member keys (optimized lookup)
     * @param memberKeys List of member keys in format "cnName|enName|type"
     * @return Map of member key to TeamMemberEntity
     */
    Mono<Map<String, TeamMemberEntity>> batchSearchByMemberKeys(List<String> memberKeys);
}

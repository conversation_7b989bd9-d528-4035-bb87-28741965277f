package com.xk.goods.domain.repository.serial;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.goods.domain.model.serialitem.SerialItemRoot;

import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SerialItemRootRepository extends IRepository<SerialItemRoot> {

    Mono<Void> removeBySerialGroupId(Long serialGroupId);

    /**
     * Batch save serial item roots
     * @param roots List of SerialItemRoot to save
     * @return Mono<Void>
     */
    Mono<Void> batchSave(List<SerialItemRoot> roots);
}

package com.xk.goods.domain.convertor.random;


import com.xk.goods.domain.model.random.id.DistributionItemIdentifier;

public class DistributionItemIdentifierConvertor {

    private DistributionItemIdentifierConvertor() {}

    public static DistributionItemIdentifier map(Long value) {
        if (value == null)
            return null;
        return DistributionItemIdentifier.builder().id(value).build();
    }

    public static Long map(DistributionItemIdentifier value) {
        if (value == null)
            return null;
        return value.id();
    }
}

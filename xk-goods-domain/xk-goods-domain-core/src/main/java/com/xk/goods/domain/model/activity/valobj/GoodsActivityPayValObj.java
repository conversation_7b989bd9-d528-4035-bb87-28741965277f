
package com.xk.goods.domain.model.activity.valobj;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.model.activity.id.GoodsActivityIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsActivityPayValObj {

    private LongIdentifier userId;

    private GoodsActivityIdentifier identifier;

    /**
     * 购买数量
     */
    private Integer buyCount;

    /**
     * 总金额
     */
    private Long totalAmount;

    /**
     * 满减状态
     */
    private CommonStatusEnum discountStatus = CommonStatusEnum.DISABLE;

    /**
     * 满减金额
     */
    private Long discountAmount = 0L;

    /**
     * 首购优惠状态
     */
    private CommonStatusEnum firstBuyDiscountStatus = CommonStatusEnum.DISABLE;

    /**
     * 首购优惠金额
     */
    private Long firstBuyDiscountAmount = 0L;

    /**
     * 优惠券状态
     */
    private CommonStatusEnum couponStatus = CommonStatusEnum.DISABLE;

    /**
     * 优惠券金额
     */
    private Long couponAmount = 0L;

    /**
     * 优惠券领取id
     */
    private LongIdentifier couponUserId;

    /**
     * 实付金额
     */
    private Long payAmount;
}

package com.xk.goods.domain.service.activity;

import com.xk.goods.domain.model.activity.valobj.GoodsActivityPayValObj;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityShowValObj;

import reactor.core.publisher.Mono;

public interface GoodsActivityRootService {

    /**
     * 根据价格获取展示价格
     * 
     * @return Mono<Long>
     */
    Mono<GoodsActivityShowValObj> getShowPrice(GoodsActivityShowValObj valObj);

    /**
     * 根据价格获取购买金额
     *
     * @return Mono<Long>
     */
    Mono<GoodsActivityPayValObj> getPayPrice(GoodsActivityPayValObj valObj);
}

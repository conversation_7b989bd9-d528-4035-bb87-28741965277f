package com.xk.goods.domain.service.activity.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.model.activity.GoodsActivityRoot;
import com.xk.goods.domain.model.activity.entity.GoodsActivityConfigEntity;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityPayValObj;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityShowValObj;
import com.xk.goods.domain.repository.activity.GoodsActivityRootQueryRepository;
import com.xk.goods.domain.service.activity.ActivityAdapterService;
import com.xk.goods.domain.service.activity.GoodsActivityRootService;
import com.xk.goods.enums.activity.ActivityTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsActivityRootServiceImpl implements GoodsActivityRootService {

    private final GoodsActivityRootQueryRepository goodsActivityRootQueryRepository;

    @BeansOfTypeToMap(value = ActivityAdapterService.class, methodName = "getActivityType")
    private Map<ActivityTypeEnum, ActivityAdapterService> activityAdapterServiceMap;

    @Override
    public Mono<GoodsActivityShowValObj> getShowPrice(GoodsActivityShowValObj valObj) {
        return goodsActivityRootQueryRepository.getRoot(valObj.getIdentifier())
                .flatMap(root -> processShowPriceActivities(root, valObj)).flatMap(obj -> {
                    if (obj.getShowAmount() < 0) {
                        obj.setShowAmount(0L);
                        return Mono.just(obj);
                    }
                    return Mono.just(obj);
                });
    }

    /**
     * 处理展示价格相关的活动
     */
    private Mono<GoodsActivityShowValObj> processShowPriceActivities(GoodsActivityRoot root,
            GoodsActivityShowValObj valObj) {

        Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap =
                root.getGoodsActivityConfigEntityMap();

        return Mono.just(valObj).flatMap(obj -> applyRemainRandomActivity(root, configMap, obj))
                .flatMap(obj -> applyFirstBuyShowActivity(root, configMap, obj));
    }

    /**
     * 应用随机剩余活动
     */
    private Mono<GoodsActivityShowValObj> applyRemainRandomActivity(GoodsActivityRoot root,
            Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap,
            GoodsActivityShowValObj valObj) {

        GoodsActivityConfigEntity remainRandom = configMap.get(ActivityTypeEnum.REMAIN_RANDOM);
        if (remainRandom != null && CommonStatusEnum.ENABLE.equals(remainRandom.getStatus())) {
            ActivityAdapterService adapter =
                    activityAdapterServiceMap.get(ActivityTypeEnum.REMAIN_RANDOM);
            if (adapter != null) {
                return adapter.calcShowAmount(root, valObj).thenReturn(valObj);
            }
        }
        return Mono.just(valObj);
    }

    /**
     * 应用首购展示活动
     */
    private Mono<GoodsActivityShowValObj> applyFirstBuyShowActivity(GoodsActivityRoot root,
            Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap,
            GoodsActivityShowValObj valObj) {

        GoodsActivityConfigEntity firstBuy = configMap.get(ActivityTypeEnum.FIRST_BUY);
        if (firstBuy != null && CommonStatusEnum.ENABLE.equals(firstBuy.getStatus())) {
            ActivityAdapterService adapter =
                    activityAdapterServiceMap.get(ActivityTypeEnum.FIRST_BUY);
            if (adapter != null) {
                return adapter.calcShowAmount(root, valObj).thenReturn(valObj);
            }
        }
        return Mono.just(valObj);
    }

    @Override
    public Mono<GoodsActivityPayValObj> getPayPrice(GoodsActivityPayValObj valObj) {
        return goodsActivityRootQueryRepository.getRoot(valObj.getIdentifier())
                .flatMap(root -> processPayPriceActivities(root, valObj)).flatMap(obj -> {
                    if (obj.getPayAmount() < 0) {
                        obj.setPayAmount(0L);
                        return Mono.just(obj);
                    }
                    return Mono.just(obj);
                });
    }

    /**
     * 处理支付价格相关的活动
     */
    private Mono<GoodsActivityPayValObj> processPayPriceActivities(GoodsActivityRoot root,
            GoodsActivityPayValObj valObj) {

        Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap =
                root.getGoodsActivityConfigEntityMap();

        return Mono.just(valObj).flatMap(obj -> applyFirstBuyPayActivity(root, configMap, obj))
                .flatMap(obj -> applyDiscountActivity(root, configMap, obj))
                .flatMap(obj -> applyCouponActivity(root, configMap, obj));
    }

    /**
     * 应用首购支付活动
     */
    private Mono<GoodsActivityPayValObj> applyFirstBuyPayActivity(GoodsActivityRoot root,
            Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap,
            GoodsActivityPayValObj valObj) {

        GoodsActivityConfigEntity firstBuy = configMap.get(ActivityTypeEnum.FIRST_BUY);
        if (firstBuy != null && CommonStatusEnum.ENABLE.equals(firstBuy.getStatus())) {
            ActivityAdapterService adapter =
                    activityAdapterServiceMap.get(ActivityTypeEnum.FIRST_BUY);
            if (adapter != null) {
                return adapter.calcPayAmount(root, valObj).thenReturn(valObj);
            }
        }
        return Mono.just(valObj);
    }

    /**
     * 应用折扣活动
     */
    private Mono<GoodsActivityPayValObj> applyDiscountActivity(GoodsActivityRoot root,
            Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap,
            GoodsActivityPayValObj valObj) {

        GoodsActivityConfigEntity discount = configMap.get(ActivityTypeEnum.DISCOUNT);
        if (discount != null && CommonStatusEnum.ENABLE.equals(discount.getStatus())) {
            ActivityAdapterService adapter =
                    activityAdapterServiceMap.get(ActivityTypeEnum.DISCOUNT);
            if (adapter != null) {
                return adapter.calcPayAmount(root, valObj).thenReturn(valObj);
            }
        }
        return Mono.just(valObj);
    }

    /**
     * 应用优惠券活动
     */
    private Mono<GoodsActivityPayValObj> applyCouponActivity(GoodsActivityRoot root,
            Map<ActivityTypeEnum, GoodsActivityConfigEntity> configMap,
            GoodsActivityPayValObj valObj) {
        if (valObj.getCouponUserId() == null) {
            return Mono.just(valObj);
        }

        GoodsActivityConfigEntity coupon = configMap.get(ActivityTypeEnum.COUPON);
        if (coupon != null && CommonStatusEnum.ENABLE.equals(coupon.getStatus())) {
            ActivityAdapterService adapter = activityAdapterServiceMap.get(ActivityTypeEnum.COUPON);
            if (adapter != null) {
                return adapter.calcPayAmount(root, valObj).thenReturn(valObj);
            }
        }
        return Mono.just(valObj);
    }
}

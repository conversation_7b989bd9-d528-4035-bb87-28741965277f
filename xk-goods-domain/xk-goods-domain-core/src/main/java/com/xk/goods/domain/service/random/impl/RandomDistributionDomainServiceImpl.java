package com.xk.goods.domain.service.random.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.xk.goods.domain.commons.XkGoodsDomainErrorEnum;
import com.xk.goods.domain.model.random.RandomDistributionRoot;
import com.xk.goods.domain.model.random.entity.DistributionItemEntity;
import com.xk.goods.domain.model.random.id.DistributionItemIdentifier;
import com.xk.goods.domain.model.random.id.RandomDistributionIdentifier;
import com.xk.goods.domain.repository.random.RandomDistributionRootQueryRepository;
import com.xk.goods.domain.repository.random.RandomDistributionRootRepository;
import com.xk.goods.domain.service.random.DistributionStrategy;
import com.xk.goods.domain.service.random.RandomDistributionDomainService;
import com.xk.goods.domain.support.XkGoodsDomainException;
import com.xk.goods.enums.random.DistributionStrategyType;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 随机分发领域服务实现 使用@BeansOfTypeToMap自动加载所有策略实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RandomDistributionDomainServiceImpl implements RandomDistributionDomainService {

    private final RandomDistributionRootQueryRepository randomDistributionRootQueryRepository;

    private final RandomDistributionRootRepository randomDistributionRootRepository;

    /**
     * 使用@BeansOfTypeToMap自动加载所有DistributionStrategy实现 key为策略的getStrategyType()返回值
     */
    @BeansOfTypeToMap(value = DistributionStrategy.class, methodName = "getStrategyType")
    private Map<DistributionStrategyType, DistributionStrategy> distributionStrategyMap;

    /**
     * 获取策略实现（内部验证策略是否存在）
     */
    private DistributionStrategy getStrategy(DistributionStrategyType distributionStrategyType) {
        return Objects.requireNonNull(distributionStrategyMap.get(distributionStrategyType));
    }

    @Override
    public Mono<RandomDistributionIdentifier> createRandomDistribute(
            Mono<RandomDistributionRoot> distributionRootMono) {
        return distributionRootMono.flatMap(distributionRoot -> {
            // 获取策略（内部验证策略是否存在）
            DistributionStrategy strategy;
            DistributionStrategyType distributionStrategyType =
                    distributionRoot.getDistributionEntity().getDistributionStrategyType();
            if (DistributionStrategyType.PSEUDO_RANDOM.equals(distributionStrategyType)) {
                if (distributionRoot.getStrategyByRatio()) {
                    strategy = getStrategy(
                            distributionRoot.getDistributionEntity().getDistributionStrategyType());
                } else {
                    strategy = getStrategy(DistributionStrategyType.SIMPLE_RANDOM);
                }
            } else {
                strategy = getStrategy(DistributionStrategyType.SIMPLE_RANDOM);
            }

            if (log.isInfoEnabled()) {
                log.debug("分发[{}]算法开始执行，策略: {}, 物品数量: {}, , 创建人: {}",
                        distributionRoot.getIdentifier().distributionId(),
                        strategy.getStrategyType().getName(),
                        distributionRoot.getInputItems().size(),
                        distributionRoot.getDistributionEntity().getCreatedBy());
            }
            // 执行分发算法
            return strategy.distribute(distributionRoot).flatMap(this::validateResult)
                    .flatMap(updatedRoot -> {
                        // 设置创建时间
                        updatedRoot.getDistributionEntity().setCreatedTime(new Date());
                        if (log.isInfoEnabled()) {
                            log.debug("分发[{}]算法执行完成，最终序列长度: {}, 可用数量: {}",
                                    updatedRoot.getIdentifier().distributionId(),
                                    updatedRoot.getFinalSequence().size(),
                                    distributionRoot.getDistributionEntity().getAvailableCount());
                        }
                        // 保存分发结果到Repository
                        return randomDistributionRootRepository.save(updatedRoot)
                                .thenReturn(updatedRoot.getIdentifier());
                    });
        }).onErrorMap(throwable -> {
            log.error("分发算法执行失败: {}", throwable.getMessage(), throwable);
            return new XkGoodsDomainException(
                    XkGoodsDomainErrorEnum.DISTRIBUTION_ALGORITHM_EXECUTION_FAILED, throwable);
        });
    }

    /**
     * 响应式验证聚合根
     */
    private Mono<RandomDistributionRoot> validateRoot(RandomDistributionRoot root) {
        return Mono.fromCallable(() -> {
            // root.validate();
            return root;
        }).onErrorMap(throwable -> {
            log.error("验证失败: {}", throwable.getMessage(), throwable);
            return new XkGoodsDomainException(
                    XkGoodsDomainErrorEnum.DISTRIBUTION_ALGORITHM_EXECUTION_FAILED, throwable);
        });
    }

    /**
     * 响应式验证结果
     */
    private Mono<RandomDistributionRoot> validateResult(RandomDistributionRoot root) {
        return Mono.fromCallable(() -> {
            // root.validateResult();
            return root;
        }).onErrorMap(throwable -> {
            log.error("分发结果验证失败: {}", throwable.getMessage(), throwable);
            return new XkGoodsDomainException(
                    XkGoodsDomainErrorEnum.DISTRIBUTION_ALGORITHM_EXECUTION_FAILED, throwable);
        });
    }

    @Override
    public Mono<RandomDistributionIdentifier> createSurplusRandom(
            Flux<RandomDistributionIdentifier> randomDistributionIdentifierFlux) {
        // 生成新的随机分发ID
        RandomDistributionIdentifier randomDistributionIdentifier =
                RandomDistributionRoot.generateId();
        return randomDistributionIdentifierFlux
                // 1. 通过getRoot获取每个RandomDistributionIdentifier对应的RandomDistributionRoot
                .flatMap(randomDistributionRootQueryRepository::getRoot)
                // 2. 使用reduce逐个收集和验证，构建完整的RandomDistributionRoot对象
                .reduce((accumulator, current) -> current.collectAndValidateRoot(accumulator,
                        randomDistributionIdentifier))
                .flatMap(aggregatedRoot -> {
                    List<DistributionItemEntity> inputItems = aggregatedRoot.getInputItems();
                    if (inputItems.isEmpty()) {
                        return Mono.error(new XkGoodsDomainException(
                                XkGoodsDomainErrorEnum.DISTRIBUTION_ALGORITHM_EXECUTION_FAILED,
                                "没有找到可用的随机分发项，无法进行剩余随机分发"));
                    }
                    log.info("开始执行剩余随机分发，可用物品数量: {}", inputItems.size());
                    // 使用简单随机策略进行分发
                    return this.createRandomDistribute(Mono.just(aggregatedRoot))
                            .thenReturn(randomDistributionIdentifier);
                });
    }

    @Override
    public Mono<Void> callbackDistributionItem(Mono<DistributionItemIdentifier> itemIdentifierMono,
            Function<Mono<DistributionItemIdentifier>, Mono<Boolean>> checkBizMono) {
        return checkBizMono.apply(itemIdentifierMono).flatMap(isBiz -> {
            if (Boolean.FALSE.equals(isBiz)) {
                return Mono.empty();
            }
            return randomDistributionRootQueryRepository.getDistributionItem(itemIdentifierMono)
                    .flatMap(distributionItemEntity -> randomDistributionRootRepository
                            .callbackDistributionItem(Mono.just(distributionItemEntity)));
        });
    }

    @Override
    public Mono<Void> callbackDistributionItemList(
            Flux<DistributionItemIdentifier> itemIdentifierFlux) {
        return Mono.empty();
    }

    @Override
    public Mono<DistributionItemEntity> getDistributionItem(
            Mono<RandomDistributionIdentifier> randomDistributionIdentifierMono) {
        return randomDistributionIdentifierMono
                .flatMap(randomDistributionRootQueryRepository::getRandomDistributionEntity)
                .flatMap(distributionEntity -> randomDistributionRootRepository
                        .distributionItem(Mono.just(distributionEntity)))
                .switchIfEmpty(Mono.error(new XkGoodsDomainException(
                        XkGoodsDomainErrorEnum.DISTRIBUTION_ALGORITHM_EXECUTION_FAILED,
                        "未找到指定的分发记录")))
                .doOnError(error -> log.error("获取分发项失败: {}", error.getMessage(), error));
    }

    @Override
    public Flux<DistributionItemEntity> getDistributionItem(
            Mono<RandomDistributionIdentifier> randomDistributionIdentifierMono, int count) {
        return randomDistributionIdentifierMono
                .flatMap(randomDistributionRootQueryRepository::getRandomDistributionEntity)
                .flatMapMany(distributionEntity -> randomDistributionRootRepository
                        .distributionItem(Mono.just(distributionEntity), count))
                .switchIfEmpty(Mono.error(new XkGoodsDomainException(
                        XkGoodsDomainErrorEnum.DISTRIBUTION_ALGORITHM_EXECUTION_FAILED,
                        "未找到指定的分发记录")))
                .doOnError(error -> log.error("获取分发项失败: {}", error.getMessage(), error));
    }

}

package com.xk.goods.domain.model.random;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections.CollectionUtils;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Identifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.support.DomainStaticBeanFactory;
import com.xk.goods.domain.commons.XkGoodsSequenceEnum;
import com.xk.goods.domain.convertor.random.DistributionItemIdentifierConvertor;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.random.entity.DistributionItemEntity;
import com.xk.goods.domain.model.random.entity.RandomDistributionEntity;
import com.xk.goods.domain.model.random.id.DistributionItemIdentifier;
import com.xk.goods.domain.model.random.id.RandomDistributionIdentifier;
import com.xk.goods.domain.model.random.valobj.AlgorithmConfig;
import com.xk.goods.domain.model.random.valobj.AlgorithmExecutionInfo;
import com.xk.goods.domain.model.random.valobj.RandomDistributionBizTypeEnum;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialTeamItemEntity;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.domain.model.specification.valobj.SpecificationGiftValObj;
import com.xk.goods.domain.support.XkGoodsDomainException;
import com.xk.goods.enums.random.DistributionItemStatusEnum;
import com.xk.goods.enums.random.DistributionItemType;
import com.xk.goods.enums.random.DistributionStrategyType;
import com.xk.goods.enums.random.SurplusRandomTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 随机分发聚合根
 *
 * <AUTHOR>
 */
@Getter
public class RandomDistributionRoot extends DomainRoot<RandomDistributionIdentifier> {
    /**
     * 分发上下文实体
     */
    private final RandomDistributionEntity distributionEntity;

    /**
     * 输入物品实体列表
     */
    private final List<DistributionItemEntity> inputItems;
    /**
     * 最终分发序列（包含完整的物品实体信息）
     */
    @Setter
    private List<DistributionItemEntity> finalSequence;

    /**
     * 算法执行信息（可选，用于调试）
     */
    @Setter
    private AlgorithmExecutionInfo executionInfo;

    /**
     * 构造函数
     */
    @Builder
    public RandomDistributionRoot(@NonNull RandomDistributionIdentifier identifier,
            @NonNull RandomDistributionEntity distributionEntity,
            @NonNull List<DistributionItemEntity> inputItems,
            List<DistributionItemEntity> finalSequence, AlgorithmExecutionInfo executionInfo) {
        super(identifier);
        this.distributionEntity = distributionEntity;
        this.inputItems = inputItems;
        this.finalSequence = finalSequence;
        this.executionInfo = executionInfo;
    }

    @Builder
    public RandomDistributionRoot(@NonNull RandomDistributionEntity distributionEntity,
            @NonNull List<DistributionItemEntity> inputItems,
            List<DistributionItemEntity> finalSequence, AlgorithmExecutionInfo executionInfo) {
        super(generateId());
        this.distributionEntity = distributionEntity;
        this.inputItems = inputItems;
        this.finalSequence = finalSequence;
        this.executionInfo = executionInfo;
    }

    /**
     * 生成随机分发ID
     * 
     * @return Serializable
     */
    public static RandomDistributionIdentifier generateId() {
        return RandomDistributionIdentifier.builder()
                .distributionId((Long) DomainStaticBeanFactory.getIdentifierGenerateService()
                        .generateIdentifier(IdentifierRoot.builder()
                                .identifier(XkGoodsSequenceEnum.RANDOM_DISTRIBUTION)
                                .type(IdentifierGenerateEnum.CACHE).build()))
                .build();
    }

    /**
     * 处理福盒随机分发
     *
     * @param specRoot specRoot
     * @param goodsObject goodsObject
     * @return Mono<Void>
     */
    public static Mono<RandomDistributionRoot> createFortuneRoot(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject) {
        Date offShelfTime = goodsObject.getGoodsInfo().getOffShelfTime();
        int availableCount = specRoot.size();
        int specialCount = 0;
        int totalCount = availableCount + specialCount;
        Date createdTime = new Date();
        RandomDistributionEntity entity = RandomDistributionEntity.builder()
                .distributionId(RandomDistributionRoot.generateId())
                .bizType(RandomDistributionBizTypeEnum.GOODS)
                .bizIdentifier(GoodsIdentifier.builder()
                        .goodsId(goodsObject.getGoodsInfo().getGoodsId()).build())
                .distributionStrategyType(DistributionStrategyType.SIMPLE_RANDOM)
                .surplusRandomTypeEnum(SurplusRandomTypeEnum.ITEM)
                .config(AlgorithmConfig.defaultSimpleAlgorithmConfig()).totalCount(totalCount)
                .specialCount(specialCount).availableCount(availableCount).createdBy("-1")
                .createdTime(createdTime).startTime(createdTime).endTime(offShelfTime).build();

        AtomicLong atomicLong = new AtomicLong(DomainStaticBeanFactory
                .getIdentifierGenerateService()
                .getByRange(IdentifierRoot.builder()
                        .identifier(XkGoodsSequenceEnum.RANDOM_DISTRIBUTION_ITEM)
                        .type(IdentifierGenerateEnum.CACHE).count((long) specRoot.size()).build())
                .getStart());
        return Flux.fromIterable(specRoot).flatMap(root -> {
            root.getSpecificationEntity()
                    .setDistributionId(entity.getDistributionId().distributionId());
            SpecificationEntity specificationEntity = root.getSpecificationEntity();
            return Mono.just(DistributionItemEntity.builder()
                    .itemIdentifier(
                            DistributionItemIdentifierConvertor.map(atomicLong.getAndIncrement()))
                    .distributionId(entity.getDistributionId())
                    .groupId(specificationEntity.getSpecificationId().toString())
                    .itemId(specificationEntity.getSpecificationId().toString())
                    .distributionItemType(DistributionItemType.NORMAL)
                    .distributionItemStatusEnum(DistributionItemStatusEnum.UNUSED).build());
        }).collectList()
                .map(inputItems -> RandomDistributionRoot.builder()
                        .identifier(entity.getDistributionId()).distributionEntity(entity)
                        .inputItems(inputItems).build());
    }

    /**
     * 处理非选队随机分发
     *
     * @param specRoot specRoot
     * @param goodsObject goodsObject
     * @return Mono<Void>
     */
    public static Flux<RandomDistributionRoot> createNoneTeamRoot(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject, List<SerialOriginalItemEntity> originalItemEntityList,
            List<SerialTeamItemEntity> teamEntityList) {
        Date offShelfTime = goodsObject.getGoodsInfo().getOffShelfTime();
        AtomicLong atomicLong =
                new AtomicLong(DomainStaticBeanFactory.getIdentifierGenerateService()
                        .getByRange(IdentifierRoot.builder()
                                .identifier(XkGoodsSequenceEnum.RANDOM_DISTRIBUTION_ITEM)
                                .type(IdentifierGenerateEnum.CACHE)
                                .count((long) originalItemEntityList.size() + teamEntityList.size())
                                .build())
                        .getStart());
        return Flux.fromIterable(specRoot).flatMap(root -> {
            SpecificationEntity specificationEntity = root.getSpecificationEntity();
            int specialCount = teamEntityList.size();
            int availableCount = originalItemEntityList.size();
            int totalCount = availableCount + specialCount;
            Date createdTime = new Date();
            RandomDistributionEntity entity = RandomDistributionEntity.builder()
                    .distributionId(RandomDistributionRoot.generateId())
                    .bizType(RandomDistributionBizTypeEnum.SPEC)
                    .bizIdentifier(specificationEntity.getIdentifier())
                    .distributionStrategyType(DistributionStrategyType.PSEUDO_RANDOM)
                    .config(AlgorithmConfig.forPseudoRandom()).totalCount(totalCount)
                    .surplusRandomTypeEnum(SurplusRandomTypeEnum.ITEM).specialCount(specialCount)
                    .availableCount(availableCount).createdBy("-1").createdTime(createdTime)
                    .startTime(createdTime).endTime(offShelfTime).build();
            root.getSpecificationEntity()
                    .setDistributionId(entity.getDistributionId().distributionId());

            List<DistributionItemEntity> list = new ArrayList<>();
            return Flux.fromIterable(originalItemEntityList)
                    .map(SerialOriginalItemEntity::getSerialItemId)
                    .map(itemId -> DistributionItemEntity.builder()
                            .itemIdentifier(DistributionItemIdentifierConvertor
                                    .map(atomicLong.getAndIncrement()))
                            .distributionId(entity.getDistributionId())
                            .groupId(specificationEntity.getSpecificationId().toString())
                            .itemId(itemId.toString())
                            .distributionItemType(DistributionItemType.NORMAL)
                            .distributionItemStatusEnum(DistributionItemStatusEnum.UNUSED).build())
                    .collectList().doOnSuccess(list::addAll)
                    .thenMany(Flux.fromIterable(teamEntityList))
                    .map(SerialTeamItemEntity::getSerialItemId)
                    .map(id -> DistributionItemEntity.builder()
                            .itemIdentifier(DistributionItemIdentifierConvertor
                                    .map(atomicLong.getAndIncrement()))
                            .distributionId(entity.getDistributionId())
                            .groupId(specificationEntity.getSpecificationId().toString())
                            .itemId(id.toString())
                            .distributionItemType(DistributionItemType.SPECIAL)
                            .distributionItemStatusEnum(DistributionItemStatusEnum.UNUSED).build())
                    .collectList().doOnSuccess(list::addAll).thenReturn(
                            RandomDistributionRoot.builder().identifier(entity.getDistributionId())
                                    .distributionEntity(entity).inputItems(list).build());
        });
    }

    /**
     * 处理选队随机分发
     *
     * @param specRoot specRoot
     * @param goodsObject goodsObject
     * @return Flux<Object>
     */
    public static Flux<RandomDistributionRoot> createTeamRoot(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject, List<SerialOriginalItemEntity> originalItemEntityList) {
        Date offShelfTime = goodsObject.getGoodsInfo().getOffShelfTime();
        Map<Long, List<SerialOriginalItemEntity>> itemMap = originalItemEntityList.stream()
                .collect(Collectors.groupingBy(SerialOriginalItemEntity::getSeriesCategoryId));
        AtomicLong atomicLong =
                new AtomicLong(DomainStaticBeanFactory.getIdentifierGenerateService()
                        .getByRange(IdentifierRoot.builder()
                                .identifier(XkGoodsSequenceEnum.RANDOM_DISTRIBUTION_ITEM)
                                .type(IdentifierGenerateEnum.CACHE)
                                .count((long) originalItemEntityList.size()).build())
                        .getStart());
        return Flux.fromIterable(specRoot).flatMap(root -> {
            SpecificationEntity specificationEntity = root.getSpecificationEntity();
            SpecificationGiftValObj specificationGiftValObj = root.getSpecificationGiftValObj();
            List<SerialOriginalItemEntity> itemList =
                    itemMap.get(specificationGiftValObj.getGiftBusinessIdList().getFirst());
            if (CollectionUtils.isEmpty(itemList)) {
                return Mono.empty();
            }
            int availableCount = itemList.size();
            int specialCount = 0;
            int totalCount = availableCount + specialCount;
            Date createdTime = new Date();
            RandomDistributionEntity entity = RandomDistributionEntity.builder()
                    .distributionId(RandomDistributionRoot.generateId())
                    .bizType(RandomDistributionBizTypeEnum.GOODS)
                    .bizIdentifier(GoodsIdentifier.builder()
                            .goodsId(goodsObject.getGoodsInfo().getGoodsId()).build())
                    .distributionStrategyType(DistributionStrategyType.SIMPLE_RANDOM)
                    .surplusRandomTypeEnum(SurplusRandomTypeEnum.GROUP)
                    .config(AlgorithmConfig.defaultSimpleAlgorithmConfig()).totalCount(totalCount)
                    .specialCount(specialCount).availableCount(availableCount).createdBy("-1")
                    .createdTime(createdTime).startTime(createdTime).endTime(offShelfTime).build();
            root.getSpecificationEntity()
                    .setDistributionId(entity.getDistributionId().distributionId());
            return Flux.fromIterable(itemList).map(SerialOriginalItemEntity::getSerialItemId)
                    .map(itemId -> DistributionItemEntity.builder()
                            .itemIdentifier(DistributionItemIdentifierConvertor
                                    .map(atomicLong.getAndIncrement()))
                            .distributionId(entity.getDistributionId())
                            .groupId(specificationEntity.getSpecificationId().toString())
                            .itemId(itemId.toString())
                            .distributionItemType(DistributionItemType.NORMAL)
                            .distributionItemStatusEnum(DistributionItemStatusEnum.UNUSED).build())
                    .collectList()
                    .map(inputItems -> RandomDistributionRoot.builder()
                            .identifier(entity.getDistributionId()).distributionEntity(entity)
                            .inputItems(inputItems).build());
        });
    }

    @Override
    public Validatable<RandomDistributionIdentifier> validate() throws XkGoodsDomainException {
        if (inputItems.isEmpty()) {
            throw new IllegalArgumentException("输入列表不能为空");
        }
        // 验证实体
        distributionEntity.validate();
        // 验证策略与配置的匹配性
        validateStrategyConfig();

        // 验证输入数据的一致性
        validateInputDataConsistency();
        return this;
    }

    /**
     * 验证策略与配置的匹配性
     */
    private void validateStrategyConfig() {
        DistributionStrategyType distributionStrategyType =
                distributionEntity.getDistributionStrategyType();
        AlgorithmConfig config = distributionEntity.getConfig();

        switch (distributionStrategyType) {
            case PSEUDO_RANDOM:
                config.validateForPseudoRandom();
                break;
            case SIMPLE_RANDOM:
                config.validateForSimpleRandom();
                break;
            default:
                throw new IllegalArgumentException("未知的策略类型: " + distributionStrategyType);
        }
    }

    /**
     * 验证分发结果
     */
    public void validateResult() {
        if (finalSequence == null || finalSequence.isEmpty()) {
            throw new IllegalArgumentException("最终序列不能为空");
        }
        // 验证序列长度是否与输入物品数量一致
        if (finalSequence.size() != inputItems.size()) {
            throw new IllegalArgumentException(
                    String.format("序列长度不匹配：期望 %d，实际 %d", inputItems.size(), finalSequence.size()));
        }
        // 验证序列中的物品是否都在输入物品中
        Set<DistributionItemIdentifier> inputItemIds = inputItems.stream()
                .map(DistributionItemEntity::getIdentifier).collect(Collectors.toSet());

        for (DistributionItemEntity item : finalSequence) {
            if (!inputItemIds.contains(item.getIdentifier())) {
                throw new IllegalArgumentException("序列中包含未知物品：" + item.getIdentifier());
            }
        }
        // 验证每个输入物品都在序列中
        Set<DistributionItemIdentifier> sequenceItemIds = finalSequence.stream()
                .map(DistributionItemEntity::getIdentifier).collect(Collectors.toSet());
        for (DistributionItemEntity inputItem : inputItems) {
            if (!sequenceItemIds.contains(inputItem.getIdentifier())) {
                throw new IllegalArgumentException("输入物品未在序列中：" + inputItem.getIdentifier());
            }
        }
    }

    /**
     * 验证输入数据的一致性（前置验证）
     */
    private void validateInputDataConsistency() {
        if (inputItems == null || inputItems.isEmpty()) {
            throw new IllegalArgumentException("输入物品列表不能为空");
        }

        // 验证总数量一致性
        int actualTotalCount = inputItems.size();
        int expectedTotalCount = distributionEntity.getTotalCount();
        if (actualTotalCount != expectedTotalCount) {
            throw new IllegalArgumentException(String.format("输入数据总数量不一致：实体记录 %d，实际输入 %d",
                    expectedTotalCount, actualTotalCount));
        }

    }

    /**
     * 检查是否已经分发
     *
     * @return Boolean
     */
    public Boolean checkDistributionResult() {
        return finalSequence != null && !finalSequence.isEmpty();
    }

    /**
     * 收集和验证RandomDistributionRoot的方法
     * 逐个验证bizType和bizIdentifier的一致性，并收集可用的DistributionItemEntity到inputItems
     */
    public RandomDistributionRoot collectAndValidateRoot(RandomDistributionRoot accumulator,
            RandomDistributionIdentifier randomDistributionIdentifier) {
        // 1. 验证业务一致性
        validateBizConsistency(accumulator);
        RandomDistributionEntity originalEntity = this.getDistributionEntity();

        // 3. 提取可用物品
        List<DistributionItemEntity> collectAvailableItems = Stream
                .of(accumulator.getInputItems(), extractUnusedItems(accumulator.getFinalSequence(),
                        randomDistributionIdentifier, originalEntity.getSurplusRandomTypeEnum()),
                        extractUnusedItems(this.getFinalSequence(), randomDistributionIdentifier,
                                originalEntity.getSurplusRandomTypeEnum()))
                .flatMap(List::stream).toList();

        // 4. 构建新的聚合根
        return RandomDistributionRoot.builder().identifier(randomDistributionIdentifier)
                .distributionEntity(createMergedDistributionEntity(randomDistributionIdentifier,
                        originalEntity, collectAvailableItems))
                .inputItems(collectAvailableItems).finalSequence(List.of()).build();
    }

    /**
     * 验证业务一致性
     *
     * @param accumulator accumulator
     */
    public void validateBizConsistency(RandomDistributionRoot accumulator) {
        RandomDistributionBizTypeEnum accumulatorBizType =
                accumulator.getDistributionEntity().getBizType();
        Identifier<?> accumulatorBizIdentifier =
                accumulator.getDistributionEntity().getBizIdentifier();
        RandomDistributionBizTypeEnum currentBizType = this.getDistributionEntity().getBizType();
        Identifier<?> currentBizIdentifier = this.getDistributionEntity().getBizIdentifier();

        if (!accumulatorBizType.equals(currentBizType)
                || !accumulatorBizIdentifier.equals(currentBizIdentifier)) {
            throw new IllegalArgumentException("只有相同业务才能一起进行剩余随机分发");
        }
    }

    /**
     * 提取未使用的DistributionItemEntity
     *
     * @param items items
     * @return List<DistributionItemEntity>
     */
    public List<DistributionItemEntity> extractUnusedItems(List<DistributionItemEntity> items,
            RandomDistributionIdentifier randomDistributionIdentifier,
            SurplusRandomTypeEnum surplusRandomTypeEnum) {
        return items.stream().filter(
                item -> DistributionItemStatusEnum.UNUSED.equals(item.distributionItemStatusEnum()))
                .map(item -> {
                    String itemId;
                    if (SurplusRandomTypeEnum.ITEM.equals(surplusRandomTypeEnum)) {
                        itemId = item.itemId();
                    } else {
                        itemId = item.groupId();
                    }
                    return DistributionItemEntity.builder()
                            .itemIdentifier(DistributionItemEntity.generateId())
                            .distributionId(randomDistributionIdentifier).groupId(item.groupId())
                            .itemId(itemId).distributionItemType(item.distributionItemType())
                            .distributionItemStatusEnum(DistributionItemStatusEnum.UNUSED).build();
                }).toList();
    }

    /**
     * 创建合并后的RandomDistributionEntity
     *
     * @param identifier identifier
     * @param originalEntity originalEntity
     * @param items items
     * @return RandomDistributionEntity
     */
    private RandomDistributionEntity createMergedDistributionEntity(
            RandomDistributionIdentifier identifier, RandomDistributionEntity originalEntity,
            List<DistributionItemEntity> items) {
        return RandomDistributionEntity.builder().distributionId(identifier)
                .totalCount(items.size()).availableCount(items.size()).specialCount(0)
                .bizType(originalEntity.getBizType())
                .bizIdentifier(originalEntity.getBizIdentifier())
                .distributionStrategyType(DistributionStrategyType.SIMPLE_RANDOM)
                .surplusRandomTypeEnum(originalEntity.getSurplusRandomTypeEnum())
                .config(AlgorithmConfig.defaultSimpleAlgorithmConfig())
                .createdBy(originalEntity.getCreatedBy()).createdTime(new Date())
                .startTime(originalEntity.getStartTime()).endTime(originalEntity.getEndTime())
                .build();
    }

    public boolean getStrategyByRatio() {
        AlgorithmConfig config = this.getDistributionEntity().getConfig();
        Float type1Ratio = config.getType1Ratio();
        // 计算期望的1类特殊物品数量
        int specialCount = this.getDistributionEntity().getSpecialCount();
        int totalCount = this.getDistributionEntity().getTotalCount();
        int expectedType1Count = Math.round(specialCount * type1Ratio);
        int expectedType2Count = specialCount - expectedType1Count;
        // 验证特殊物品数量是否合理
        if (specialCount > 0) {
            // 至少要有1个特殊物品才能进行比例验证
            if (expectedType1Count < 0 || expectedType2Count < 0) {
                return false;
            }
            // 验证总数量是否足够支持算法执行
            // 伪随机算法需要至少有普通物品来构建随机池
            int normalCount = totalCount - specialCount;
            if (normalCount <= 0) {
                return false;
            }
            // 验证区间配置的合理性
            if (expectedType2Count > 0) {
                int totalInterval = normalCount + expectedType1Count;
                Float minIntervalRatio = config.getMinIntervalRatio();
                Float maxIntervalRatio = config.getMaxIntervalRatio();
                int avgInterval = totalInterval / expectedType2Count;
                int minInterval = Math.max(1, Math.round(avgInterval * minIntervalRatio));
                int maxInterval = Math.round(avgInterval * maxIntervalRatio);

                // 验证最小区间是否可行
                if (minInterval * expectedType2Count > totalInterval) {
                    return false;
                }
                // 验证区间配置是否合理
                return maxInterval > 0;
            }
            return true;
        } else {
            return false;
        }
    }
}

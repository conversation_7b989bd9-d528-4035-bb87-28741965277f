package com.xk.ewd.infrastructure.repository.reconciliation;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.reconciliation.ReconciliationRoot;
import com.xk.ewd.domain.repository.reconciliation.ReconciliationRootRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class ReconciliationRootRepositoryImpl implements ReconciliationRootRepository {

    @Override
    public Mono<Void> save(ReconciliationRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(ReconciliationRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(ReconciliationRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(ReconciliationRoot root) {
        return null;
    }
}

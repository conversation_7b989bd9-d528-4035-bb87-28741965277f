package com.xk.ewd.infrastructure.adapter.logistics;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.domain.repository.logistics.LogisticsGoodsRootRepository;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderQueryRepository;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.ewd.domain.service.logistics.LogisticsOrderAdapterService;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsAdapterSearchServiceImpl implements LogisticsOrderAdapterService {

    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    private final LogisticsGoodsRootRepository logisticsGoodsRootRepository;

    private final LogisticsOrderQueryRepository logisticsOrderQueryRepository;

    @Override
    public Mono<Void> create(Mono<LogisticsOrderRoot> mono) {
        return mono.flatMap(root -> logisticsOrderQueryRepository
                .getLogisticsOrderById(Mono.just(
                        StringIdentifier.builder().id(root.getOrderEntity().getOrderNo()).build()))
                .flatMap(logisticsOrderRoot -> {
                    LogisticsOrderEntity source = logisticsOrderRoot.getLogisticsOrderEntity();
                    LogisticsOrderEntity target = root.getLogisticsOrderEntity();
                    source.setLogisticsOrderId(target.getLogisticsOrderId());
                    source.setLogisticsOrderType(target.getLogisticsOrderType());
                    source.setLogisticsCorpName(target.getLogisticsCorpName());
                    source.setLogisticsNo(target.getLogisticsNo());
                    source.setLogisticsOrderStatus(target.getLogisticsOrderStatus());
                    source.setCreateTime(target.getCreateTime());

                    GoodsValueObject goodsValueObject = logisticsOrderRoot.getGoodsValueObject();
                    if (target.getLogisticsOrderType()
                            .equals(LogisticsOrderTypeEnum.GIFT.getCode())) {
//                        List<JSONObject> array = JSONArray
//                                .parseArray(goodsValueObject.getGiftInfo(), JSONObject.class);
//                        if (CollectionUtils.isNotEmpty(array)) {
//                            List<String> goodsName = array.stream()
//                                    .map(x -> x.get("giftBusinessName").toString()).toList();
//                            List<String> goodsCount = array.stream().map(x -> "1").toList();
                            // List<String> productType = array.stream()
                            // .map(x -> x.get("productType").toString()).toList();
//                            goodsValueObject.setGoodsName(StringUtils.join(goodsName, ","));
//                            goodsValueObject.setGoodsCount(StringUtils.join(goodsCount, ","));
//                            goodsValueObject.setGiftAddrList(root.getOrderEntity().getGiftAddr());
                            // goodsValueObject.setProductType(StringUtils.join(productType, ","));
//                        }

                        OrderEntity orderEntity = logisticsOrderRoot.getOrderEntity();
                        orderEntity.setGiftReportId(root.getOrderEntity().getGiftReportId());
                        orderEntity.setGiftAddr(root.getOrderEntity().getGiftAddr());
                        orderEntity.setGiftName(root.getOrderEntity().getGiftName());
                    } else {
                        List<JSONObject> array = JSONArray
                                .parseArray(goodsValueObject.getGoodsInfo(), JSONObject.class);
                        if (CollectionUtils.isNotEmpty(array)) {
                            List<String> goodsName =
                                    array.stream().map(x -> x.get("goodsName").toString()).toList();
                            List<String> goodsCount =
                                    array.stream().map(x -> x.get("shipCount").toString()).toList();

                            goodsValueObject.setGoodsName(StringUtils.join(goodsName, ","));
                            goodsValueObject.setGoodsCount(StringUtils.join(goodsCount, ","));
                            if (target.getLogisticsOrderType()
                                    .equals(LogisticsOrderTypeEnum.MERCHANT.getCode())) {
                                List<String> productType = array.stream()
                                        .map(x -> x.get("productType").toString()).toList();
                                goodsValueObject.setProductType(StringUtils.join(productType, ","));
                            }
                        }
                    }
                    return Mono.just(logisticsOrderRoot);
                })
                .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository.save(logisticsOrderRoot)
                        .thenReturn(logisticsOrderRoot))
                .flatMap(logisticsGoodsRootRepository::save));
    }

    @Override
    public Mono<Void> update(Mono<LogisticsOrderRoot> mono) {
        return mono.flatMap(root -> logisticsOrderQueryRepository
                .getLogisticsOrderById(Mono.just(
                        StringIdentifier.builder().id(root.getOrderEntity().getOrderNo()).build()))
                .flatMap(logisticsOrderRoot -> {
                    LogisticsOrderEntity source = logisticsOrderRoot.getLogisticsOrderEntity();
                    LogisticsOrderEntity target = root.getLogisticsOrderEntity();
                    source.setLogisticsOrderId(target.getLogisticsOrderId());
                    source.setLogisticsCorpName(target.getLogisticsCorpName());
                    source.setLogisticsNo(target.getLogisticsNo());
                    source.setLogisticsOrderStatus(target.getLogisticsOrderStatus());
                    source.setCreateTime(target.getCreateTime());
                    OrderEntity orderEntity = logisticsOrderRoot.getOrderEntity();
                    orderEntity.setSendGoodsTime(root.getOrderEntity().getSendGoodsTime());
                    orderEntity.setOrderNo(root.getOrderEntity().getOrderNo());
                    return Mono.just(logisticsOrderRoot);
                })
                .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository
                        .update(logisticsOrderRoot).thenReturn(logisticsOrderRoot))
                .flatMap(logisticsGoodsRootRepository::update));

    }

    @Override
    public Mono<Void> delete(Mono<LogisticsOrderRoot> mono) {
        return mono
                .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository
                        .update(logisticsOrderRoot).thenReturn(logisticsOrderRoot))
                .flatMap(logisticsGoodsRootRepository::giftDelete);

    }

    @Override
    public Mono<Void> updateFiled(Mono<LogisticsOrderRoot> root) {
        return root
                .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository
                        .updateAddr(logisticsOrderRoot).thenReturn(logisticsOrderRoot))
                .flatMap(logisticsGoodsRootRepository::updateAddr);
    }

    @Override
    public Mono<Void> updateByOrderNo(Mono<LogisticsOrderRoot> root) {
        return root
                .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository
                        .updateByOrderNo(logisticsOrderRoot).thenReturn(logisticsOrderRoot))
                .flatMap(logisticsGoodsRootRepository::updateByOrderNo);
    }

}

package com.xk.ewd.infrastructure.convertor.settle;

import com.xk.ewd.enums.settle.SettleAuditEnum;

public class SettleAuditEnumConvertor {

    private SettleAuditEnumConvertor() {}

    public static SettleAuditEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return SettleAuditEnum.getByCode(code);
    }

    public static Integer map(SettleAuditEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

package com.xk.ewd.infrastructure.repository.logistics;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods;
import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.dto.logistics.AppSendGoodsDto;
import com.xk.ewd.domain.dto.logistics.PtsGoodsGroupDto;
import com.xk.ewd.domain.dto.logistics.PtsGroupDto;
import com.xk.ewd.domain.dto.logistics.SendGoodsDto;
import com.xk.ewd.domain.repository.logistics.LogisticsGoodsRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsGoodsMapper;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsGoodsRootQueryRepositoryImpl implements LogisticsGoodsRootQueryRepository {

    private final Converter converter;
    private final OLogisticsGoodsMapper oLogisticsGoodsMapper;

    @Override
    public Flux<PtsGoodsGroupDto> ptsGoodsGroup(Pagination pagination) {
        return search(pagination, oLogisticsGoodsMapper::ptsGoodsGroup, PtsGoodsGroupDto.class,
                converter::convert);
    }

    @Override
    public Flux<PtsGroupDto> ptsGroup(Pagination pagination) {
        return search(pagination, oLogisticsGoodsMapper::ptsGroup, PtsGroupDto.class,
                converter::convert);
    }

    @Override
    public Flux<AppSendGoodsDto> sendGoodsQuery(SendGoodsDto sendGoodsDto) {
        return find(sendGoodsDto, oLogisticsGoodsMapper::sendGoodsQuery, AppSendGoodsDto.class,
                converter::convert);
    }

    @Override
    public Flux<PtsGoodsGroupDto> ptsGoodsLiveGroup(Pagination pagination) {
        return search(pagination, oLogisticsGoodsMapper::ptsGoodsLiveGroup, PtsGoodsGroupDto.class,
                converter::convert);
    }

    @Override
    public Mono<Integer> queryGoodsSendSuccess(LogisticsOrderRoot logisticsGoodsRoot) {
        return Mono.just(oLogisticsGoodsMapper.selectBySendGoodsIdCount(OLogisticsGoods.builder()
                .sendGoodsId(logisticsGoodsRoot.getGoodsValueObject().getSendGoodsId())
                .build()));
    }

    @Override
    public Mono<Long> selectGoodsInfoById(LongIdentifier identifier) {
        OLogisticsGoods oLogisticsGoods = oLogisticsGoodsMapper.selectByLogisticsId(OLogisticsGoods.builder().logisticsOrderId(identifier.id()).build());
        return Mono.just(oLogisticsGoods.getSendGoodsId());
    }
}

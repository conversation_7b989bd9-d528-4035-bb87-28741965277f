package com.xk.ewd.infrastructure.convertor.reconciliation;

import com.xk.ewd.enums.reconciliation.ReconciliationAuditEnum;

public class ReconciliationAuditEnumConvertor {
    private ReconciliationAuditEnumConvertor() {}

    public static ReconciliationAuditEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return ReconciliationAuditEnum.getByCode(code);
    }

    public static Integer map(ReconciliationAuditEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

package com.xk.ewd.infrastructure.convertor.financial;

import com.xk.ewd.enums.financial.PayTypeEnum;

public class PayTypeEnumConvertor {
    private PayTypeEnumConvertor() {}

    public static PayTypeEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return PayTypeEnum.getByCode(code);
    }

    public static Integer map(PayTypeEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

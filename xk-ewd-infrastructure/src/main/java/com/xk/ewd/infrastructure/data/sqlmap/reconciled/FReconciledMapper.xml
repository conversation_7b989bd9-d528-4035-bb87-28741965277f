<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.reconciled.FReconciledMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.reconciled.FReconciled">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="financialTransactionId" column="financial_transaction_id" jdbcType="VARCHAR"/>
            <result property="financialPlatformType" column="financial_platform_type" jdbcType="INTEGER"/>
            <result property="financialDate" column="financial_date" jdbcType="DATE"/>
            <result property="payNo" column="pay_no" jdbcType="VARCHAR"/>
            <result property="payAmount" column="pay_amount" jdbcType="BIGINT"/>
            <result property="payAccount" column="pay_account" jdbcType="VARCHAR"/>
            <result property="receiveAccount" column="receive_account" jdbcType="VARCHAR"/>
            <result property="payType" column="pay_type" jdbcType="INTEGER"/>
            <result property="payDirection" column="pay_direction" jdbcType="INTEGER"/>
            <result property="payCreateTime" column="pay_create_time" jdbcType="TIMESTAMP"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,financial_transaction_id,financial_platform_type,
        financial_date,pay_no,pay_amount,
        pay_account,receive_account,pay_type,
        pay_direction,pay_create_time,create_id,
        create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.reconciled.FReconciled" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from f_reconciled
        where 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.reconciled.FReconciled">
        delete from f_reconciled
        where 
    </delete>
    <insert id="insert">
        insert into f_reconciled
        ( id,financial_transaction_id,financial_platform_type
        ,financial_date,pay_no,pay_amount
        ,pay_account,receive_account,pay_type
        ,pay_direction,pay_create_time,create_id
        ,create_time)
        values (#{id,jdbcType=BIGINT},#{financialTransactionId,jdbcType=VARCHAR},#{financialPlatformType,jdbcType=INTEGER}
        ,#{financialDate,jdbcType=DATE},#{payNo,jdbcType=VARCHAR},#{payAmount,jdbcType=BIGINT}
        ,#{payAccount,jdbcType=VARCHAR},#{receiveAccount,jdbcType=VARCHAR},#{payType,jdbcType=INTEGER}
        ,#{payDirection,jdbcType=INTEGER},#{payCreateTime,jdbcType=TIMESTAMP},#{createId,jdbcType=BIGINT}
        ,#{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective">
        insert into f_reconciled
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="financialTransactionId != null">financial_transaction_id,</if>
                <if test="financialPlatformType != null">financial_platform_type,</if>
                <if test="financialDate != null">financial_date,</if>
                <if test="payNo != null">pay_no,</if>
                <if test="payAmount != null">pay_amount,</if>
                <if test="payAccount != null">pay_account,</if>
                <if test="receiveAccount != null">receive_account,</if>
                <if test="payType != null">pay_type,</if>
                <if test="payDirection != null">pay_direction,</if>
                <if test="payCreateTime != null">pay_create_time,</if>
                <if test="createId != null">create_id,</if>
                <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="financialTransactionId != null">#{financialTransactionId,jdbcType=VARCHAR},</if>
                <if test="financialPlatformType != null">#{financialPlatformType,jdbcType=INTEGER},</if>
                <if test="financialDate != null">#{financialDate,jdbcType=DATE},</if>
                <if test="payNo != null">#{payNo,jdbcType=VARCHAR},</if>
                <if test="payAmount != null">#{payAmount,jdbcType=BIGINT},</if>
                <if test="payAccount != null">#{payAccount,jdbcType=VARCHAR},</if>
                <if test="receiveAccount != null">#{receiveAccount,jdbcType=VARCHAR},</if>
                <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
                <if test="payDirection != null">#{payDirection,jdbcType=INTEGER},</if>
                <if test="payCreateTime != null">#{payCreateTime,jdbcType=TIMESTAMP},</if>
                <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.ewd.infrastructure.data.po.reconciled.FReconciled">
        update f_reconciled
        <set>
                <if test="id != null">
                    id = #{id,jdbcType=BIGINT},
                </if>
                <if test="financialTransactionId != null">
                    financial_transaction_id = #{financialTransactionId,jdbcType=VARCHAR},
                </if>
                <if test="financialPlatformType != null">
                    financial_platform_type = #{financialPlatformType,jdbcType=INTEGER},
                </if>
                <if test="financialDate != null">
                    financial_date = #{financialDate,jdbcType=DATE},
                </if>
                <if test="payNo != null">
                    pay_no = #{payNo,jdbcType=VARCHAR},
                </if>
                <if test="payAmount != null">
                    pay_amount = #{payAmount,jdbcType=BIGINT},
                </if>
                <if test="payAccount != null">
                    pay_account = #{payAccount,jdbcType=VARCHAR},
                </if>
                <if test="receiveAccount != null">
                    receive_account = #{receiveAccount,jdbcType=VARCHAR},
                </if>
                <if test="payType != null">
                    pay_type = #{payType,jdbcType=INTEGER},
                </if>
                <if test="payDirection != null">
                    pay_direction = #{payDirection,jdbcType=INTEGER},
                </if>
                <if test="payCreateTime != null">
                    pay_create_time = #{payCreateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createId != null">
                    create_id = #{createId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where  
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.reconciled.FReconciled">
        update f_reconciled
        set 
            id =  #{id,jdbcType=BIGINT},
            financial_transaction_id =  #{financialTransactionId,jdbcType=VARCHAR},
            financial_platform_type =  #{financialPlatformType,jdbcType=INTEGER},
            financial_date =  #{financialDate,jdbcType=DATE},
            pay_no =  #{payNo,jdbcType=VARCHAR},
            pay_amount =  #{payAmount,jdbcType=BIGINT},
            pay_account =  #{payAccount,jdbcType=VARCHAR},
            receive_account =  #{receiveAccount,jdbcType=VARCHAR},
            pay_type =  #{payType,jdbcType=INTEGER},
            pay_direction =  #{payDirection,jdbcType=INTEGER},
            pay_create_time =  #{payCreateTime,jdbcType=TIMESTAMP},
            create_id =  #{createId,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP}
        where  
    </update>

    <select id="selectByTransactionId" parameterType="com.xk.ewd.infrastructure.data.po.reconciled.FReconciled" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from f_reconciled
        where financial_transaction_id = #{financialTransactionId,jdbcType=VARCHAR} and financial_platform_type =  #{financialPlatformType,jdbcType=INTEGER}
    </select>
</mapper>

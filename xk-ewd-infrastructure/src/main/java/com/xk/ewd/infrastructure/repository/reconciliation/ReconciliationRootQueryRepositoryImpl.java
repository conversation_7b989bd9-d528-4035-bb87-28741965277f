package com.xk.ewd.infrastructure.repository.reconciliation;

import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationEntity;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationItemEntity;
import com.xk.ewd.domain.repository.reconciliation.ReconciliationRootQueryRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@Repository
@RequiredArgsConstructor
public class ReconciliationRootQueryRepositoryImpl implements ReconciliationRootQueryRepository {

    @Override
    public Flux<ReconciliationEntity> selectByPage(Pagination pagination) {
        return null;
    }

    @Override
    public Flux<ReconciliationItemEntity> selectItemByPage(Pagination pagination) {
        return null;
    }
}

package com.xk.ewd.infrastructure.data.po.logistics;

import java.util.Date;

import com.xk.ewd.domain.dto.logistics.GiftCorpDto;
import com.xk.ewd.domain.model.logistics.entity.*;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName o_logistics_goods
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = CorpEntity.class, convertGenerate = false),
        @AutoMapper(target = LogisticsOrderEntity.class, convertGenerate = false),
        @AutoMapper(target = OrderAddrEntity.class, convertGenerate = false),
        @AutoMapper(target = OrderEntity.class, convertGenerate = false),
        @AutoMapper(target = UserEntity.class, convertGenerate = false),
        @AutoMapper(target = GiftCorpDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsValueObject.class, convertGenerate = false),})
public class OLogisticsGoods {
    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品id
     */
    private Long sendGoodsId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商商品图片
     */
    private String goodsImages;

    /**
     * 发货数量
     */
    private Integer shipCount;

    /**
     * 商品单价
     */
    private Long unitPrice;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 运费
     */
    private Long shippingFee;

    /**
     * 收货人手机号
     */
    private String receivingMobile;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 收货人名字
     */
    private String consigneeName;

    /**
     * 收货人昵称
     */
    private String userNick;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户地址id
     */
    private Long userAddressId;

    /**
     * 商户名
     */
    private String corpName;

    /**
     * 收货地址省市区
     */
    private String addressSite;

    /**
     * 收货详细地址
     */
    private String addressDetail;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 物流订单类型
     */
    private Integer logisticsOrderType;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 支付方式
     */
    private Integer payType;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 物流订单状态
     */
    private Integer logisticsOrderStatus;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 催发货状态
     */
    private Integer remindShippingStatus;

    /**
     * 用户logo
     */
    private String userLogo;

    /**
     * 商户logo
     */
    private String corpLogo;

    /**
     * 直播状态
     */
    private Integer liveStatus;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 发货时间
     */
    private Date sendGoodsTime;

    /**
     * 开始时间-查询用
     */
    private Date startTime;

    /**
     * 结束时间-查询用
     */
    private Date endTime;

    /**
     * 文件路径
     */
    private String filePath;
}

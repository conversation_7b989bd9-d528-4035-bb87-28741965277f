package com.xk.ewd.infrastructure.repository.financial;

import com.xk.ewd.domain.model.financial.FinancialTransactionRoot;
import com.xk.ewd.infrastructure.data.persistence.reconciled.FReconciledMapper;
import com.xk.ewd.infrastructure.data.po.reconciled.FReconciled;
import io.github.linpeilie.Converter;
import org.springframework.stereotype.Repository;

import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.domain.repository.financial.FinancialTransactionRootQueryRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class FinancialTransactionRootQueryRepositoryImpl
        implements FinancialTransactionRootQueryRepository {

    private final FReconciledMapper fReconciledMapper;

    private final Converter converter;

    @Override
    public Flux<FinancialTransactionEntity> selectCurrentDateAll(
            FinancialTransactionEntity entity) {
        return null;
    }

    @Override
    public Mono<FinancialTransactionEntity> selectByTransactionId(FinancialTransactionRoot root) {
        return this.get(root.getFinancialTransactionEntity(),
                e -> fReconciledMapper.selectByTransactionId(FReconciled.builder().financialTransactionId(e.getFinancialTransactionId()).financialPlatformType(e.getFinancialPlatformType().getCode()).build()),
                FinancialTransactionEntity.class, converter::convert);
    }
}

package com.xk.ewd.infrastructure.data.po.reconciled;

import java.util.Date;

import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.enums.financial.FinancialPayDirectionEnum;
import com.xk.ewd.enums.financial.FinancialPlatformTypeEnum;
import com.xk.ewd.enums.financial.PayTypeEnum;
import com.xk.ewd.infrastructure.convertor.financial.FinancialPayDirectionEnumConvertor;
import com.xk.ewd.infrastructure.convertor.financial.FinancialPlatformTypeEnumConvertor;
import com.xk.ewd.infrastructure.convertor.financial.PayTypeEnumConvertor;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName f_reconciled
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = FinancialTransactionEntity.class,uses =  {FinancialPlatformTypeEnumConvertor.class, PayTypeEnumConvertor.class, FinancialPayDirectionEnumConvertor.class}),
})
public class FReconciled {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 三方流水号
     */
    private String financialTransactionId;

    /**
     * 财务平台类型
     */
    private Integer financialPlatformType;

    /**
     * 账务日期
     */
    private Date financialDate;

    /**
     * 星卡内部流水号
     */
    private String payNo;

    /**
     * 交易金额（单位：分）
     */
    private Long payAmount;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 入账支付账号
     */
    private String receiveAccount;

    /**
     * 支付类型 1-银行卡 2-支付宝 3-微信支付
     */
    private Integer payType;

    /**
     * 交易方向 1-收款 2-付款
     */
    private Integer payDirection;

    /**
     * 交易创建时间
     */
    private Date payCreateTime;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = FinancialTransactionEntity.class, source = "createId",
            target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = FinancialTransactionEntity.class, source = "createTime",
            target = "createValObj.createTime")})
    private Date createTime;
}
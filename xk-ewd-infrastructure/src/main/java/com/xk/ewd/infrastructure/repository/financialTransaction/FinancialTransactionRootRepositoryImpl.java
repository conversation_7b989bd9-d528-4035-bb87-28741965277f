package com.xk.ewd.infrastructure.repository.financialTransaction;

import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionRoot;
import com.xk.ewd.domain.repository.financialTransaction.FinancialTransactionRootRepository;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.goods.GoodsEwdRoot;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootRepository;
import com.xk.ewd.infrastructure.data.persistence.goods.GGoodsMapper;
import com.xk.ewd.infrastructure.data.po.goods.GGoods;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class FinancialTransactionRootRepositoryImpl implements FinancialTransactionRootRepository {

    private final GGoodsMapper gGoodsMapper;

    private final Converter converter;


    @Override
    public Mono<Void> save(FinancialTransactionRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(FinancialTransactionRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(FinancialTransactionRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(FinancialTransactionRoot root) {
        return null;
    }
}

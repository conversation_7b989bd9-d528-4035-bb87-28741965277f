package com.xk.ewd.infrastructure.data.persistence.reconciled;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.infrastructure.data.po.reconciled.FReconciled;

/**
* <AUTHOR>
* @description 针对表【f_reconciled】的数据库操作Mapper
* @createDate 2025-08-19 16:20:20
* @Entity com.xk.ewd.infrastructure.data.po.reconciled.FReconciled
*/
@Repository
@Table("f_reconciled")
public interface FReconciledMapper {

    int deleteByPrimaryKey(FReconciled record);

    int insert(FReconciled record);

    int insertSelective(FReconciled record);

    FReconciled selectByPrimaryKey(FReconciled record);

    int updateByPrimaryKeySelective(FReconciled record);

    int updateByPrimaryKey(FReconciled record);

    FReconciled selectByTransactionId(FReconciled record);
}

package com.xk.ewd.infrastructure.convertor.financial;

import com.xk.ewd.enums.financial.FinancialPayDirectionEnum;

public class FinancialPayDirectionEnumConvertor {
    private FinancialPayDirectionEnumConvertor() {}

    public static FinancialPayDirectionEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return FinancialPayDirectionEnum.getByCode(code);
    }

    public static Integer map(FinancialPayDirectionEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

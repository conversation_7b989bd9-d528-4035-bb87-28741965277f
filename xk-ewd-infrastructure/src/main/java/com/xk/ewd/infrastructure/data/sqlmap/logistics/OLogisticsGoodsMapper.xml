<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsGoodsMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods">
        <result property="logisticsOrderId" column="logistics_order_id" jdbcType="BIGINT"/>
        <result property="goodsId" column="goods_id" jdbcType="BIGINT"/>
        <result property="sendGoodsId" column="send_goods_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="goodsImages" column="goods_images" jdbcType="VARCHAR"/>
        <result property="shipCount" column="ship_count" jdbcType="INTEGER"/>
        <result property="unitPrice" column="unit_price" jdbcType="BIGINT"/>
        <result property="unitType" column="unit_type" jdbcType="VARCHAR"/>
        <result property="productType" column="product_type" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="payNo" column="pay_no" jdbcType="VARCHAR"/>
        <result property="logisticsCorpName" column="logistics_corp_name" jdbcType="VARCHAR"/>
        <result property="logisticsNo" column="logistics_no" jdbcType="VARCHAR"/>
        <result property="shippingFee" column="shipping_fee" jdbcType="BIGINT"/>
        <result property="receivingMobile" column="receiving_mobile" jdbcType="VARCHAR"/>
        <result property="corpId" column="corp_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="consigneeName" column="consignee_name" jdbcType="VARCHAR"/>
        <result property="userNick" column="user_nick" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="userAddressId" column="user_address_id" jdbcType="BIGINT"/>
        <result property="corpName" column="corp_name" jdbcType="VARCHAR"/>
        <result property="addressSite" column="address_site" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="logisticsOrderType" column="logistics_order_type" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="payType" column="pay_type" jdbcType="INTEGER"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="logisticsOrderStatus" column="logistics_order_status" jdbcType="INTEGER"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="remindShippingStatus" column="remind_shipping_status" jdbcType="INTEGER"/>
        <result property="userLogo" column="user_logo" jdbcType="VARCHAR"/>
        <result property="corpLogo" column="corp_logo" jdbcType="VARCHAR"/>
        <result property="liveStatus" column="live_status" jdbcType="INTEGER"/>
        <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
        <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
        <result property="districtCode" column="district_code" jdbcType="VARCHAR"/>
        <result property="sendGoodsTime" column="send_goods_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        logistics_order_id
        ,goods_id,send_goods_id,create_time,
        goods_name,goods_images,ship_count,
        unit_price,unit_type,product_type,
        order_no,pay_no,logistics_corp_name,
        logistics_no,shipping_fee,receiving_mobile,
        corp_id,user_id,consignee_name,
        user_nick,mobile,user_address_id,
        corp_name,address_site,address_detail,
        order_type,logistics_order_type,deleted,
        pay_type,order_status,logistics_order_status,
        pay_time,remind_shipping_status,user_logo,
        corp_logo,live_status,province_code,
        city_code,district_code,send_goods_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_logistics_goods
        where
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods">
        delete
        from o_logistics_goods
        where
    </delete>
    <insert id="insert">
        insert into o_logistics_goods
        ( logistics_order_id, goods_id, send_goods_id, create_time
        , goods_name, goods_images, ship_count
        , unit_price, unit_type, product_type
        , order_no, pay_no, logistics_corp_name
        , logistics_no, shipping_fee, receiving_mobile
        , corp_id, user_id, consignee_name
        , user_nick, mobile, user_address_id
        , corp_name, address_site, address_detail
        , order_type, logistics_order_type, deleted
        , pay_type, order_status, logistics_order_status
        , pay_time, remind_shipping_status, user_logo
        , corp_logo, live_status, province_code
        , city_code, district_code, send_goods_time)
        values ( #{logisticsOrderId,jdbcType=BIGINT}, #{goodsId,jdbcType=BIGINT}, #{sendGoodsId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}
               , #{goodsName,jdbcType=VARCHAR}, #{goodsImages,jdbcType=VARCHAR}, #{shipCount,jdbcType=INTEGER}
               , #{unitPrice,jdbcType=BIGINT}, #{unitType,jdbcType=VARCHAR}, #{productType,jdbcType=INTEGER}
               , #{orderNo,jdbcType=VARCHAR}, #{payNo,jdbcType=VARCHAR}, #{logisticsCorpName,jdbcType=VARCHAR}
               , #{logisticsNo,jdbcType=VARCHAR}, #{shippingFee,jdbcType=BIGINT}, #{receivingMobile,jdbcType=VARCHAR}
               , #{corpId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{consigneeName,jdbcType=VARCHAR}
               , #{userNick,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{userAddressId,jdbcType=BIGINT}
               , #{corpName,jdbcType=VARCHAR}, #{addressSite,jdbcType=VARCHAR}, #{addressDetail,jdbcType=VARCHAR}
               , #{orderType,jdbcType=INTEGER}, #{logisticsOrderType,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}
               , #{payType,jdbcType=INTEGER}, #{orderStatus,jdbcType=INTEGER}, #{logisticsOrderStatus,jdbcType=INTEGER}
               , #{payTime,jdbcType=TIMESTAMP}, #{remindShippingStatus,jdbcType=INTEGER}, #{userLogo,jdbcType=VARCHAR}
               , #{corpLogo,jdbcType=VARCHAR}, #{liveStatus,jdbcType=INTEGER}, #{provinceCode,jdbcType=VARCHAR}
               , #{cityCode,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, #{sendGoodsTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective">
        insert into o_logistics_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logisticsOrderId != null">logistics_order_id,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="sendGoodsId != null">send_goods_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="goodsImages != null">goods_images,</if>
            <if test="shipCount != null">ship_count,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="unitType != null">unit_type,</if>
            <if test="productType != null">product_type,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="payNo != null">pay_no,</if>
            <if test="logisticsCorpName != null">logistics_corp_name,</if>
            <if test="logisticsNo != null">logistics_no,</if>
            <if test="shippingFee != null">shipping_fee,</if>
            <if test="receivingMobile != null">receiving_mobile,</if>
            <if test="corpId != null">corp_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="consigneeName != null">consignee_name,</if>
            <if test="userNick != null">user_nick,</if>
            <if test="mobile != null">mobile,</if>
            <if test="userAddressId != null">user_address_id,</if>
            <if test="corpName != null">corp_name,</if>
            <if test="addressSite != null">address_site,</if>
            <if test="addressDetail != null">address_detail,</if>
            <if test="orderType != null">order_type,</if>
            <if test="logisticsOrderType != null">logistics_order_type,</if>
            <if test="deleted != null">deleted,</if>
            <if test="payType != null">pay_type,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="logisticsOrderStatus != null">logistics_order_status,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="remindShippingStatus != null">remind_shipping_status,</if>
            <if test="userLogo != null">user_logo,</if>
            <if test="corpLogo != null">corp_logo,</if>
            <if test="liveStatus != null">live_status,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="districtCode != null">district_code,</if>
            <if test="sendGoodsTime != null">send_goods_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logisticsOrderId != null">#{logisticsOrderId,jdbcType=BIGINT},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=BIGINT},</if>
            <if test="sendGoodsId != null">#{sendGoodsId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsImages != null">#{goodsImages,jdbcType=VARCHAR},</if>
            <if test="shipCount != null">#{shipCount,jdbcType=INTEGER},</if>
            <if test="unitPrice != null">#{unitPrice,jdbcType=BIGINT},</if>
            <if test="unitType != null">#{unitType,jdbcType=VARCHAR},</if>
            <if test="productType != null">#{productType,jdbcType=INTEGER},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="payNo != null">#{payNo,jdbcType=VARCHAR},</if>
            <if test="logisticsCorpName != null">#{logisticsCorpName,jdbcType=VARCHAR},</if>
            <if test="logisticsNo != null">#{logisticsNo,jdbcType=VARCHAR},</if>
            <if test="shippingFee != null">#{shippingFee,jdbcType=BIGINT},</if>
            <if test="receivingMobile != null">#{receivingMobile,jdbcType=VARCHAR},</if>
            <if test="corpId != null">#{corpId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="consigneeName != null">#{consigneeName,jdbcType=VARCHAR},</if>
            <if test="userNick != null">#{userNick,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="userAddressId != null">#{userAddressId,jdbcType=BIGINT},</if>
            <if test="corpName != null">#{corpName,jdbcType=VARCHAR},</if>
            <if test="addressSite != null">#{addressSite,jdbcType=VARCHAR},</if>
            <if test="addressDetail != null">#{addressDetail,jdbcType=VARCHAR},</if>
            <if test="orderType != null">#{orderType,jdbcType=INTEGER},</if>
            <if test="logisticsOrderType != null">#{logisticsOrderType,jdbcType=INTEGER},</if>
            <if test="deleted != null">#{deleted,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=INTEGER},</if>
            <if test="logisticsOrderStatus != null">#{logisticsOrderStatus,jdbcType=INTEGER},</if>
            <if test="payTime != null">#{payTime,jdbcType=TIMESTAMP},</if>
            <if test="remindShippingStatus != null">#{remindShippingStatus,jdbcType=INTEGER},</if>
            <if test="userLogo != null">#{userLogo,jdbcType=VARCHAR},</if>
            <if test="corpLogo != null">#{corpLogo,jdbcType=VARCHAR},</if>
            <if test="liveStatus != null">#{liveStatus,jdbcType=INTEGER},</if>
            <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="districtCode != null">#{districtCode,jdbcType=VARCHAR},</if>
            <if test="sendGoodsTime != null">#{sendGoodsTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods">
        update o_logistics_goods
        <set>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsImages != null">
                goods_images = #{goodsImages,jdbcType=VARCHAR},
            </if>
            <if test="shipCount != null">
                ship_count = #{shipCount,jdbcType=INTEGER},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice,jdbcType=BIGINT},
            </if>
            <if test="unitType != null">
                unit_type = #{unitType,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCorpName != null">
                logistics_corp_name = #{logisticsCorpName,jdbcType=VARCHAR},
            </if>
            <if test="logisticsNo != null">
                logistics_no = #{logisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="shippingFee != null">
                shipping_fee = #{shippingFee,jdbcType=BIGINT},
            </if>
            <if test="receivingMobile != null">
                receiving_mobile = #{receivingMobile,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="consigneeName != null">
                consignee_name = #{consigneeName,jdbcType=VARCHAR},
            </if>
            <if test="userNick != null">
                user_nick = #{userNick,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userAddressId != null">
                user_address_id = #{userAddressId,jdbcType=BIGINT},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="addressSite != null">
                address_site = #{addressSite,jdbcType=VARCHAR},
            </if>
            <if test="addressDetail != null">
                address_detail = #{addressDetail,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="logisticsOrderType != null">
                logistics_order_type = #{logisticsOrderType,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="logisticsOrderStatus != null">
                logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remindShippingStatus != null">
                remind_shipping_status = #{remindShippingStatus,jdbcType=INTEGER},
            </if>
            <if test="userLogo != null">
                user_logo = #{userLogo,jdbcType=VARCHAR},
            </if>
            <if test="corpLogo != null">
                corp_logo = #{corpLogo,jdbcType=VARCHAR},
            </if>
            <if test="liveStatus != null">
                live_status = #{liveStatus,jdbcType=INTEGER},
            </if>
            <if test="provinceCode != null">
                province_code = #{provinceCode,jdbcType=VARCHAR},
                </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
                </if>
            <if test="districtCode != null">
                district_code = #{districtCode,jdbcType=VARCHAR},
                </if>
            <if test="sendGoodsTime != null">
                send_goods_time = #{sendGoodsTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        <where>
            <trim prefixOverrides="AND">
                <if test="logisticsOrderId != null">
                    AND logistics_order_id = #{logisticsOrderId,jdbcType=BIGINT}
                </if>
                <if test="liveStatus != null">
                    AND live_status = #{liveStatus,jdbcType=INTEGER}
                </if>
            </trim>
        </where>
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods">
        update o_logistics_goods
        set goods_name             = #{goodsName,jdbcType=VARCHAR},
            goods_images           = #{goodsImages,jdbcType=VARCHAR},
            ship_count             = #{shipCount,jdbcType=INTEGER},
            unit_price             = #{unitPrice,jdbcType=BIGINT},
            unit_type              = #{unitType,jdbcType=VARCHAR},
            product_type           = #{productType,jdbcType=INTEGER},
            order_no               = #{orderNo,jdbcType=VARCHAR},
            pay_no                 = #{payNo,jdbcType=VARCHAR},
            logistics_corp_name    = #{logisticsCorpName,jdbcType=VARCHAR},
            logistics_no           = #{logisticsNo,jdbcType=VARCHAR},
            shipping_fee           = #{shippingFee,jdbcType=BIGINT},
            receiving_mobile       = #{receivingMobile,jdbcType=VARCHAR},
            corp_id                = #{corpId,jdbcType=BIGINT},
            user_id                = #{userId,jdbcType=BIGINT},
            consignee_name         = #{consigneeName,jdbcType=VARCHAR},
            user_nick              = #{userNick,jdbcType=VARCHAR},
            mobile                 = #{mobile,jdbcType=VARCHAR},
            user_address_id        = #{userAddressId,jdbcType=BIGINT},
            corp_name              = #{corpName,jdbcType=VARCHAR},
            address_site           = #{addressSite,jdbcType=VARCHAR},
            address_detail         = #{addressDetail,jdbcType=VARCHAR},
            order_type             = #{orderType,jdbcType=INTEGER},
            logistics_order_type   = #{logisticsOrderType,jdbcType=INTEGER},
            deleted                = #{deleted,jdbcType=INTEGER},
            pay_type               = #{payType,jdbcType=INTEGER},
            order_status           = #{orderStatus,jdbcType=INTEGER},
            logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER},
            pay_time               = #{payTime,jdbcType=TIMESTAMP},
            remind_shipping_status = #{remindShippingStatus,jdbcType=INTEGER},
            user_logo              = #{userLogo,jdbcType=VARCHAR},
            corp_logo              = #{corpLogo,jdbcType=VARCHAR},
            live_status            = #{liveStatus,jdbcType=INTEGER},
            province_code          = #{provinceCode,jdbcType=VARCHAR},
            city_code              = #{cityCode,jdbcType=VARCHAR},
            district_code          = #{districtCode,jdbcType=VARCHAR},
            send_goods_time        = #{sendGoodsTime,jdbcType=TIMESTAMP}
        where goods_id = #{goodsId,jdbcType=BIGINT}
          and logistics_order_id = #{logisticsOrderId,jdbcType=BIGINT}
    </update>

    <select id="ptsGoodsGroup" resultType="com.xk.ewd.infrastructure.data.po.logistics.OPtsGoodsGroup">
        select
        goods_id as goodsId,min(goods_name) as goodsName,min(create_time) as createTime
        from o_logistics_goods
        <where>
            <trim prefixOverrides="AND">
                <if test="userId != null">
                    AND user_id = #{userId,jdbcType=BIGINT}
                </if>
                <if test="userNick != null and userNick != ''">
                    AND user_nick = #{userNick,jdbcType=VARCHAR}
                </if>
                <if test="consigneeName != null and consigneeName != ''">
                    AND consignee_name = #{consigneeName,jdbcType=VARCHAR}
                </if>
                <if test="mobile != null and mobile != ''">
                    AND receiving_mobile = #{mobile,jdbcType=VARCHAR}
                </if>
                <if test="logisticsOrderType != null">
                    AND logistics_order_type = #{logisticsOrderType,jdbcType=INTEGER}
                </if>
                <if test="addressId != null">
                    AND user_address_id = #{addressId,jdbcType=BIGINT}
                </if>
                <if test="remindShippingStatus != null">
                    AND remind_shipping_status = #{remindShippingStatus,jdbcType=INTEGER}
                </if>
                <if test="corpId != null">
                    AND corp_id = #{corpId,jdbcType=BIGINT}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    AND goods_name LIKE CONCAT('%', #{goodsName,jdbcType=VARCHAR}, '%')
                </if>
                <if test="deleted == null">
                    AND logistics_order_status = 1
                    AND create_time >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                    AND deleted = 0
                </if>
            </trim>
        </where>
        group by goods_id
        <if test="sort != null">
            order by #{sort}
            <if test="order == 'ASC'">
                ASC
            </if>
            <if test="order == 'DESC'">
                DESC
            </if>
        </if>
    </select>

    <select id="ptsGroup" resultType="com.xk.ewd.infrastructure.data.po.logistics.OPtsGroup">
        select
        user_id as userId ,min(user_nick) as userNick, min(user_logo) as userLogo, count(1) as countNum,max(create_time)
        as createTime
        from o_logistics_goods
        <where>
            <trim prefixOverrides="AND">
                <!-- 用户相关条件 -->
                <if test="userId != null">
                    AND user_id = #{userId,jdbcType=BIGINT}
                </if>
                <if test="userNick != null and userNick != ''">
                    AND user_nick = #{userNick,jdbcType=VARCHAR}
                </if>

                <!-- 收货人信息 -->
                <if test="consigneeName != null and consigneeName != ''">
                    AND consignee_name = #{consigneeName,jdbcType=VARCHAR}
                </if>
                <if test="mobile != null and mobile != ''">
                    AND mobile = #{mobile,jdbcType=VARCHAR}
                </if>

                <!-- 订单类型和状态 -->
                <if test="logisticsOrderType != null">
                    AND logistics_order_type = #{logisticsOrderType,jdbcType=INTEGER}
                </if>
                <if test="logisticsOrderStatus != null">
                    AND logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER}
                </if>
                <if test="remindShippingStatus != null">
                    AND remind_shipping_status = #{remindShippingStatus,jdbcType=INTEGER}
                </if>

                <!-- 地址和公司信息 -->
                <if test="addressId != null">
                    AND user_address_id = #{addressId,jdbcType=BIGINT}
                </if>
                <if test="corpId != null">
                    AND corp_id = #{corpId,jdbcType=BIGINT}
                </if>

                <!-- 商品条件 -->
                <if test="goodsIds != null and goodsIds.size() > 0">
                    AND goods_id IN
                    <foreach collection="goodsIds" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=BIGINT}
                    </foreach>
                </if>

                <!-- 默认条件（当deleted为null时应用） -->
                <if test="deleted == null">
                    AND logistics_order_status = 1
                    AND create_time >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                    AND logistics_order_type IN (3, 4)
                    AND deleted = 0
                </if>
            </trim>
        </where>
        group by user_id
        <if test="sort != null">
            order by #{sort}
            <if test="order == 'ASC'">
                ASC
            </if>
            <if test="order == 'DESC'">
                DESC
            </if>
        </if>
    </select>
    <select id="sendGoodsQuery" resultType="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDto">
        select
        goods_id as goodsId,min(goods_name) as goodsName,min(create_time) as createTime,sum(ship_count) as
        countNum,min(goods_images) as goodsImages
        from o_logistics_goods
        <where>
            <trim prefixOverrides="AND">
                <if test="corpId != null">
                    AND corp_id = #{corpId,jdbcType=BIGINT}
                </if>
                <if test="logisticsOrderIdList != null and logisticsOrderIdList.size() > 0">
                    AND logistics_order_id IN
                    <foreach collection="logisticsOrderIdList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=BIGINT}
                    </foreach>
                </if>
            </trim>
        </where>
        group by goods_id,logistics_order_type
    </select>
    <select id="ptsGoodsLiveGroup" resultType="com.xk.ewd.infrastructure.data.po.logistics.OPtsGoodsGroup">
        select
        goods_id as goodsId,min(live_status) liveStatus
        from o_logistics_goods
        <where>
            <trim prefixOverrides="AND">
                <if test="userId != null">
                    AND user_id = #{userId,jdbcType=BIGINT}
                </if>
                AND logistics_order_type = 3
                <if test="searchName != null">
                    AND (
                    goods_name like concat('%', #{searchName,jdbcType=BIGINT}, '%')
                    OR corp_name like concat('%', #{searchName,jdbcType=BIGINT}, '%')
                    OR collectible_card_name like concat('%', #{searchName,jdbcType=BIGINT}, '%')
                    )
                </if>
                <if test="deleted == null">
                    AND create_time >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                    AND deleted = 0
                </if>
            </trim>
        </where>
        group by goods_id order by liveStatus DESC
    </select>

    <insert id="export">
        SELECT content FROM (
        SELECT 1 AS sort_order, CONCAT('物流订单ID', ',', '订单编号', ',', '商品名称', ',', '发货数', ',', '物流公司名',
        ',', '物流单号', ',', '收货人姓名', ',', '收货人手机号', ',', '发货省市区', ',', '发货详细地址') AS content
        UNION ALL
        SELECT
        2 AS sort_order, CONCAT(
        '"', REPLACE(IFNULL(logistics_order_id,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(order_no,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(goods_name,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(ship_count,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(logistics_corp_name,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(logistics_no,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(consignee_name,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(receiving_mobile,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(address_site,''), '"', '""'), '",',
        '"', REPLACE(IFNULL(address_detail,''), '"', '""'), '",'
        ) AS content
        FROM o_logistics_goods
        where order_type = #{orderType,jdbcType=BIGINT}
        AND create_time >= #{startTime,jdbcType=TIMESTAMP}
        AND create_time  <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        <if test="corpId != null">
            AND corp_id = #{corpId,jdbcType=BIGINT}
        </if>
        AND logistics_order_status = 1
        AND deleted = 0 ) t order by sort_order
        INTO OUTFILE #{filePath,jdbcType=VARCHAR} FORMAT AS 'csv'
    </insert>

    <update id="updateByOrderNo"
            parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods">
        update o_logistics_goods
        <set>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsImages != null">
                goods_images = #{goodsImages,jdbcType=VARCHAR},
            </if>
            <if test="shipCount != null">
                ship_count = #{shipCount,jdbcType=INTEGER},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice,jdbcType=BIGINT},
            </if>
            <if test="unitType != null">
                unit_type = #{unitType,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=INTEGER},
            </if>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCorpName != null">
                logistics_corp_name = #{logisticsCorpName,jdbcType=VARCHAR},
            </if>
            <if test="logisticsNo != null">
                logistics_no = #{logisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="shippingFee != null">
                shipping_fee = #{shippingFee,jdbcType=BIGINT},
            </if>
            <if test="receivingMobile != null">
                receiving_mobile = #{receivingMobile,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="consigneeName != null">
                consignee_name = #{consigneeName,jdbcType=VARCHAR},
            </if>
            <if test="userNick != null">
                user_nick = #{userNick,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userAddressId != null">
                user_address_id = #{userAddressId,jdbcType=BIGINT},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="addressSite != null">
                address_site = #{addressSite,jdbcType=VARCHAR},
            </if>
            <if test="addressDetail != null">
                address_detail = #{addressDetail,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="logisticsOrderType != null">
                logistics_order_type = #{logisticsOrderType,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="logisticsOrderStatus != null">
                logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remindShippingStatus != null">
                remind_shipping_status = #{remindShippingStatus,jdbcType=INTEGER},
            </if>
            <if test="userLogo != null">
                user_logo = #{userLogo,jdbcType=VARCHAR},
            </if>
            <if test="corpLogo != null">
                corp_logo = #{corpLogo,jdbcType=VARCHAR},
            </if>
            <if test="liveStatus != null">
                live_status = #{liveStatus,jdbcType=INTEGER},
            </if>
            <if test="provinceCode != null">
                province_code = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                district_code = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="sendGoodsTime != null">
                send_goods_time = #{sendGoodsTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <trim prefixOverrides="AND">
                <if test="orderNo != null  and orderNo != ''">
                    AND order_no = #{orderNo,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </update>

    <select id="selectBySendGoodsIdCount" resultType="java.lang.Integer">
        select count(1) from o_logistics_goods where send_goods_id = #{sendGoodsId,jdbcType=BIGINT} and logistics_order_status = 1
    </select>

    <select id="selectByLogisticsId" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_logistics_goods
        where logistics_order_id = #{logisticsOrderId,jdbcType=BIGINT}
    </select>
</mapper>

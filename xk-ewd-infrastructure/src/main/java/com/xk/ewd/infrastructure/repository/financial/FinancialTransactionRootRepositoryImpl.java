package com.xk.ewd.infrastructure.repository.financial;

import com.xk.ewd.infrastructure.data.persistence.reconciled.FReconciledMapper;
import com.xk.ewd.infrastructure.data.po.reconciled.FReconciled;
import io.github.linpeilie.Converter;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.financial.FinancialTransactionRoot;
import com.xk.ewd.domain.repository.financial.FinancialTransactionRootRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class FinancialTransactionRootRepositoryImpl implements FinancialTransactionRootRepository {

    private final FReconciledMapper fReconciledMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(FinancialTransactionRoot root) {
        return save(root.getFinancialTransactionEntity(), FReconciled.class, converter::convert,
                fReconciledMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(FinancialTransactionRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(FinancialTransactionRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(FinancialTransactionRoot root) {
        return null;
    }
}

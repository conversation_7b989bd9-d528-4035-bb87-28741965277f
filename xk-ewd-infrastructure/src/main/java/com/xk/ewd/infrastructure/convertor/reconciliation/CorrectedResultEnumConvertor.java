package com.xk.ewd.infrastructure.convertor.reconciliation;

import com.xk.ewd.enums.reconciliation.CorrectedResultEnum;

public class CorrectedResultEnumConvertor {

    private CorrectedResultEnumConvertor() {}

    public static CorrectedResultEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return CorrectedResultEnum.getByCode(code);
    }

    public static Integer map(CorrectedResultEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

package com.xk.ewd.infrastructure.convertor.financial;

import com.xk.ewd.enums.financial.FinancialPlatformTypeEnum;

public class FinancialPlatformTypeEnumConvertor {
    private FinancialPlatformTypeEnumConvertor() {}

    public static FinancialPlatformTypeEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return FinancialPlatformTypeEnum.getByCode(code);
    }

    public static Integer map(FinancialPlatformTypeEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

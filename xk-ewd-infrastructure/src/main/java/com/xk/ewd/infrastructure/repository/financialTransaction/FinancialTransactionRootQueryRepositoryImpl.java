package com.xk.ewd.infrastructure.repository.financialTransaction;

import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionEntity;
import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionRoot;
import com.xk.ewd.domain.repository.financialTransaction.FinancialTransactionRootQueryRepository;
import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.model.goods.entity.GoodsEwdEntity;
import com.xk.ewd.domain.model.goods.valobj.StatusCountValObj;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.goods.GGoodsMapper;
import com.xk.ewd.infrastructure.data.po.goods.GGoods;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class FinancialTransactionRootQueryRepositoryImpl implements FinancialTransactionRootQueryRepository {

    private final GGoodsMapper gGoodsMapper;
    private final Converter converter;


    @Override
    public Mono<FinancialTransactionEntity> selectByTransactionId(FinancialTransactionRoot financialTransactionRoot) {
        return null;
    }
}

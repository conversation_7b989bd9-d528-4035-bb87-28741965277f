package com.xk.ewd.infrastructure.convertor.reconciliation;

import com.xk.ewd.enums.reconciliation.ReceiptItemStatusEnum;

public class ReceiptItemStatusEnumConvertor {

    private ReceiptItemStatusEnumConvertor() {}

    public static ReceiptItemStatusEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return ReceiptItemStatusEnum.getByCode(code);
    }

    public static Integer map(ReceiptItemStatusEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}

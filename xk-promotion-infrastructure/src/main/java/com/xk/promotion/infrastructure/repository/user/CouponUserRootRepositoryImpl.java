package com.xk.promotion.infrastructure.repository.user;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.infrastructure.data.persistence.coupon.PCouponGoodsMapper;
import com.xk.promotion.infrastructure.data.po.coupon.PCouponGoods;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.infrastructure.cache.dao.cancel.CancelListDao;
import com.xk.infrastructure.cache.key.cancel.CancelListKey;
import com.xk.infrastructure.commons.cancel.CancelTypeEnum;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.repository.user.CouponUserRootRepository;
import com.xk.promotion.infrastructure.data.persistence.coupon.PCouponUserMapper;
import com.xk.promotion.infrastructure.data.po.coupon.PCouponUser;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class CouponUserRootRepositoryImpl implements CouponUserRootRepository {

    private final Converter converter;

    private final PCouponUserMapper couponUserMapper;

    private final PCouponGoodsMapper couponGoodsMapper;

    private final CancelListDao cancelListDao;

    @Override
    public Mono<Void> save(CouponUserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getCouponUserEntity())
                        .flatMap(couponUserEntity -> save(couponUserEntity, PCouponUser.class,
                                converter::convert, couponUserMapper::insertSelective)))
                .then(Mono.justOrEmpty(root.getCouponGoodsEntity())
                        .flatMap(couponGoodsEntity -> save(couponGoodsEntity, PCouponGoods.class,
                                converter::convert, couponGoodsMapper::insertSelective)));
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(CouponUserRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(CouponUserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getCouponUserEntity())
                        .flatMap(couponUserEntity -> update(couponUserEntity, PCouponUser.class,
                                converter::convert,
                                couponUserMapper::updateByPrimaryKeySelective)));
    }

    @Override
    public Mono<Void> remove(CouponUserRoot root) {
        return null;
    }

    @Override
    public Mono<Void> addCouponUserExpireCache(CouponUserRoot root) {
        return Mono.fromRunnable(() -> {
            CouponUserEntity couponUserEntity = root.getCouponUserEntity();
            CancelListKey cancelListKey = getCanceKey(couponUserEntity);
            cancelListDao.addValue(cancelListKey, couponUserEntity.getCouponUserId().toString());
        });
    }

    @Override
    public Mono<Integer> updateCouponUserOverdue(CouponUserRoot couponUserRoot) {
        return update(couponUserRoot.getCouponUserEntity(), PCouponUser.class, converter::convert,
                couponUserMapper::updateCouponUserOverdue).thenReturn(1);
    }

    @Override
    public Mono<CouponUserIdentifier> getCouponUserExpireCache(CouponUserRoot root) {
        return Mono.fromCallable(() -> {
            CouponUserEntity couponUserEntity = root.getCouponUserEntity();
            CancelListKey cancelListKey = getCanceKey(couponUserEntity);
            String couponUserId = cancelListDao.rightPopValue(cancelListKey);
            if (StringUtils.isNotBlank(couponUserId)) {
                return CouponUserIdentifier.builder().couponUserId(Long.valueOf(couponUserId))
                        .build();
            }
            return null;
        }).switchIfEmpty(Mono.empty());
    }

    @Override
    public Mono<Void> deleteUserCancelQueue(CouponUserRoot root) {
        return Mono.fromRunnable(() -> {
            CouponUserEntity couponUserEntity = root.getCouponUserEntity();
            CancelListKey cancelListKey = getCanceKey(couponUserEntity);
            cancelListDao.delKey(cancelListKey);
        });
    }

    @Override
    public Mono<Void> deleteUserCancelData(CouponUserRoot root) {
        return Mono.fromRunnable(() -> {
            CouponUserEntity couponUserEntity = root.getCouponUserEntity();
            CancelListKey cancelListKey = getCanceKey(couponUserEntity);
            cancelListDao.removeRem(cancelListKey, 1L,
                    couponUserEntity.getCouponUserId().toString());
        });
    }

    @Override
    public Mono<Void> updateByCouponId(CouponUserRoot couponUserRoot) {
        return update(couponUserRoot.getCouponUserEntity(), PCouponUser.class, converter::convert,
                couponUserMapper::updateByCouponId);
    }

    private CancelListKey getCanceKey(CouponUserEntity couponUserEntity) {
        Date expireTime = couponUserEntity.getEndTime();
        LocalDateTime localDateTime =
                expireTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");

        return CancelListKey.builder().cancelType(CancelTypeEnum.COUPON_USER)
                .timeFormat(localDateTime.format(formatter)).build();
    }
}

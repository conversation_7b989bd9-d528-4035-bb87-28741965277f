<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.promotion.infrastructure.data.persistence.coupon.PCouponMapper">

    <resultMap id="BaseResultMap" type="com.xk.promotion.infrastructure.data.po.coupon.PCoupon">
        <id property="couponId" column="coupon_id" jdbcType="BIGINT"/>
        <result property="platformType" column="platform_type" jdbcType="TINYINT"/>
        <result property="couponType" column="coupon_type" jdbcType="TINYINT"/>
        <result property="scopeType" column="scope_type" jdbcType="TINYINT"/>
        <result property="corpId" column="corp_id" jdbcType="BIGINT"/>
        <result property="corpName" column="corp_name" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="discountAmount" column="discount_amount" jdbcType="BIGINT"/>
        <result property="thresholdAmount" column="threshold_amount" jdbcType="BIGINT"/>
        <result property="totalNum" column="total_num" jdbcType="INTEGER"/>
        <result property="receivedNum" column="received_num" jdbcType="INTEGER"/>
        <result property="usedNum" column="used_num" jdbcType="INTEGER"/>
        <result property="stockNum" column="stock_num" jdbcType="INTEGER"/>
        <result property="personNum" column="person_num" jdbcType="INTEGER"/>
        <result property="isShowHomepage" column="is_show_homepage" jdbcType="TINYINT"/>
        <result property="isShowCenter" column="is_show_center" jdbcType="TINYINT"/>
        <result property="showCenterDate" column="show_center_date" jdbcType="TIMESTAMP"/>
        <result property="periodType" column="period_type" jdbcType="TINYINT"/>
        <result property="periodNum" column="period_num" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="instruction" column="instruction" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="audit_status" jdbcType="TINYINT"/>
        <result property="weight" column="weight" jdbcType="INTEGER" />
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        coupon_id
        ,platform_type,coupon_type,
        scope_type,corp_id,corp_name,name,
        discount_amount,threshold_amount,total_num,
        received_num,used_num,stock_num,
        person_num,is_show_homepage,is_show_center,show_center_date,
        period_type,period_num,start_time,
        end_time,instruction,audit_status,weight,
        status,deleted,update_id,
        create_id,update_time,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCoupon"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon
        where coupon_id = #{couponId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCoupon">
        delete
        from p_coupon
        where coupon_id = #{couponId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="coupon_id" keyProperty="couponId"
            parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCoupon" useGeneratedKeys="true">
        insert into p_coupon
        ( coupon_id, platform_type, coupon_type
        , scope_type, corp_id, name
        , discount_amount, threshold_amount, total_num
        , received_num, used_num, stock_num
        , person_num, is_show_homepage, is_show_center,show_center_date
        , period_type, period_num, start_time
        , end_time, instruction, audit_status, weight
        , status, deleted, update_id
        , create_id, update_time, create_time)
        values ( #{couponId,jdbcType=BIGINT}, #{platformType,jdbcType=TINYINT}, #{couponType,jdbcType=TINYINT}
               , #{scopeType,jdbcType=TINYINT}, #{corpId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}
               , #{discountAmount,jdbcType=BIGINT}, #{thresholdAmount,jdbcType=BIGINT}, #{totalNum,jdbcType=INTEGER}
               , #{receivedNum,jdbcType=INTEGER}, #{usedNum,jdbcType=INTEGER}, #{stockNum,jdbcType=INTEGER}
               , #{personNum,jdbcType=INTEGER}, #{isShowHomepage,jdbcType=TINYINT}, #{isShowCenter,jdbcType=TINYINT},#{showCenterDate,jdbcType=TIMESTAMP}
               , #{periodType,jdbcType=TINYINT}, #{periodNum,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}
               , #{endTime,jdbcType=TIMESTAMP}, #{instruction,jdbcType=VARCHAR}, #{auditStatus,jdbcType=TINYINT},#{weight,jdbcType=INTEGER}
               , #{status,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, #{updateId,jdbcType=BIGINT}
               , #{createId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="coupon_id" keyProperty="couponId"
            parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCoupon" useGeneratedKeys="true">
        insert into p_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="couponId != null">coupon_id,</if>
            <if test="platformType != null">platform_type,</if>
            <if test="couponType != null">coupon_type,</if>
            <if test="scopeType != null">scope_type,</if>
            <if test="corpId != null">corp_id,</if>
            <if test="corpName != null">corp_name,</if>
            <if test="name != null">name,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="thresholdAmount != null">threshold_amount,</if>
            <if test="totalNum != null">total_num,</if>
            <if test="receivedNum != null">received_num,</if>
            <if test="usedNum != null">used_num,</if>
            <if test="stockNum != null">stock_num,</if>
            <if test="personNum != null">person_num,</if>
            <if test="isShowHomepage != null">is_show_homepage,</if>
            <if test="isShowCenter != null">is_show_center,</if>
            <if test="showCenterDate != null">show_center_date,</if>
            <if test="periodType != null">period_type,</if>
            <if test="periodNum != null">period_num,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="instruction != null">instruction,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="weight != null">weight,</if>
            <if test="status != null">status,</if>
            <if test="deleted != null">deleted,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="couponId != null">#{couponId,jdbcType=BIGINT},</if>
            <if test="platformType != null">#{platformType,jdbcType=TINYINT},</if>
            <if test="couponType != null">#{couponType,jdbcType=TINYINT},</if>
            <if test="scopeType != null">#{scopeType,jdbcType=TINYINT},</if>
            <if test="corpId != null">#{corpId,jdbcType=BIGINT},</if>
            <if test="corpName != null">#{corpName,jdbcType=VARCHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="discountAmount != null">#{discountAmount,jdbcType=BIGINT},</if>
            <if test="thresholdAmount != null">#{thresholdAmount,jdbcType=BIGINT},</if>
            <if test="totalNum != null">#{totalNum,jdbcType=INTEGER},</if>
            <if test="receivedNum != null">#{receivedNum,jdbcType=INTEGER},</if>
            <if test="usedNum != null">#{usedNum,jdbcType=INTEGER},</if>
            <if test="stockNum != null">#{stockNum,jdbcType=INTEGER},</if>
            <if test="personNum != null">#{personNum,jdbcType=INTEGER},</if>
            <if test="isShowHomepage != null">#{isShowHomepage,jdbcType=TINYINT},</if>
            <if test="isShowCenter != null">#{isShowCenter,jdbcType=TINYINT},</if>
            <if test="showCenterDate != null">#{showCenterDate,jdbcType=TIMESTAMP},</if>
            <if test="periodType != null">#{periodType,jdbcType=TINYINT},</if>
            <if test="periodNum != null">#{periodNum,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=TIMESTAMP},</if>
            <if test="endTime != null">#{endTime,jdbcType=TIMESTAMP},</if>
            <if test="instruction != null">#{instruction,jdbcType=VARCHAR},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="weight != null">#{weight,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCoupon">
        update p_coupon
        <set>
            <if test="platformType != null">
                platform_type = #{platformType,jdbcType=TINYINT},
            </if>
            <if test="couponType != null">
                coupon_type = #{couponType,jdbcType=TINYINT},
            </if>
            <if test="scopeType != null">
                scope_type = #{scopeType,jdbcType=TINYINT},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                discount_amount = #{discountAmount,jdbcType=BIGINT},
            </if>
            <if test="thresholdAmount != null">
                threshold_amount = #{thresholdAmount,jdbcType=BIGINT},
            </if>
            <if test="totalNum != null">
                total_num = #{totalNum,jdbcType=INTEGER},
            </if>
            <if test="receivedNum != null">
                received_num = #{receivedNum,jdbcType=INTEGER},
            </if>
            <if test="usedNum != null">
                used_num = #{usedNum,jdbcType=INTEGER},
            </if>
            <if test="stockNum != null">
                stock_num = #{stockNum,jdbcType=INTEGER},
            </if>
            <if test="personNum != null">
                person_num = #{personNum,jdbcType=INTEGER},
            </if>
            <if test="isShowHomepage != null">
                is_show_homepage = #{isShowHomepage,jdbcType=TINYINT},
            </if>
            <if test="isShowCenter != null">
                is_show_center = #{isShowCenter,jdbcType=TINYINT},
            </if>
            <if test="showCenterDate != null">
                show_center_date = #{showCenterDate,jdbcType=TIMESTAMP},
            </if>
            <if test="periodType != null">
                period_type = #{periodType,jdbcType=TINYINT},
            </if>
            <if test="periodNum != null">
                period_num = #{periodNum,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="instruction != null">
                instruction = #{instruction,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where coupon_id = #{couponId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCoupon">
        update p_coupon
        set platform_type    = #{platformType,jdbcType=TINYINT},
            coupon_type      = #{couponType,jdbcType=TINYINT},
            scope_type       = #{scopeType,jdbcType=TINYINT},
            corp_id          = #{corpId,jdbcType=BIGINT},
            name             = #{name,jdbcType=VARCHAR},
            discount_amount  = #{discountAmount,jdbcType=BIGINT},
            threshold_amount = #{thresholdAmount,jdbcType=BIGINT},
            total_num        = #{totalNum,jdbcType=INTEGER},
            received_num     = #{receivedNum,jdbcType=INTEGER},
            used_num         = #{usedNum,jdbcType=INTEGER},
            stock_num        = #{stockNum,jdbcType=INTEGER},
            person_num       = #{personNum,jdbcType=INTEGER},
            is_show_homepage = #{isShowHomepage,jdbcType=TINYINT},
            is_show_center   = #{isShowCenter,jdbcType=TINYINT},
            show_center_date   = #{showCenterDate,jdbcType=TIMESTAMP},
            period_type      = #{periodType,jdbcType=TINYINT},
            period_num       = #{periodNum,jdbcType=INTEGER},
            start_time       = #{startTime,jdbcType=TIMESTAMP},
            end_time         = #{endTime,jdbcType=TIMESTAMP},
            instruction      = #{instruction,jdbcType=VARCHAR},
            audit_status     = #{auditStatus,jdbcType=TINYINT},
            status           = #{status,jdbcType=TINYINT},
            deleted          = #{deleted,jdbcType=TINYINT},
            update_id        = #{updateId,jdbcType=BIGINT},
            create_id        = #{createId,jdbcType=BIGINT},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            create_time      = #{createTime,jdbcType=TIMESTAMP}
        where coupon_id = #{couponId,jdbcType=BIGINT}
    </update>


    <update id="updateByPrimaryKeyAndCorpIdSelective">
        update p_coupon
        <set>
            <if test="platformType != null">
                platform_type = #{platformType,jdbcType=TINYINT},
            </if>
            <if test="couponType != null">
                coupon_type = #{couponType,jdbcType=TINYINT},
            </if>
            <if test="scopeType != null">
                scope_type = #{scopeType,jdbcType=TINYINT},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="corpName != null">
                corp_id = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                discount_amount = #{discountAmount,jdbcType=BIGINT},
            </if>
            <if test="thresholdAmount != null">
                threshold_amount = #{thresholdAmount,jdbcType=BIGINT},
            </if>
            <if test="totalNum != null">
                total_num = #{totalNum,jdbcType=INTEGER},
            </if>
            <if test="receivedNum != null">
                received_num = #{receivedNum,jdbcType=INTEGER},
            </if>
            <if test="usedNum != null">
                used_num = #{usedNum,jdbcType=INTEGER},
            </if>
            <if test="personNum != null">
                person_num = #{personNum,jdbcType=INTEGER},
            </if>
            <if test="isShowHomepage != null">
                is_show_homepage = #{isShowHomepage,jdbcType=TINYINT},
            </if>
            <if test="isShowCenter != null">
                is_show_center = #{isShowCenter,jdbcType=TINYINT},
            </if>
            <if test="showCenterDate != null">
                show_center_date = #{showCenterDate,jdbcType=TIMESTAMP},
            </if>
            <if test="periodType != null">
                period_type = #{periodType,jdbcType=TINYINT},
            </if>
            <if test="periodNum != null">
                period_num = #{periodNum,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="instruction != null">
                instruction = #{instruction,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="true">
                and coupon_id = #{couponId,jdbcType=BIGINT}
            </if>
            <if test="corpId != null">
                and corp_id = #{corpId}
            </if>
        </where>
    </update>

    <select id="searchByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon
        <where>
            <if test="couponId != null">
                and coupon_id = #{couponId,jdbcType=BIGINT}
            </if>
            <if test="corpId != null">
                and corp_id = #{corpId}
            </if>
            <if test="corpName != null">
                and corp_name like concat('%',#{corpName},'%')
            </if>
            <if test="platformType != null">
                and platform_type = #{platformType}
            </if>
            <if test="name != null">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="couponType != null">
                and coupon_type = #{couponType,jdbcType=BIGINT}
            </if>
            <if test="scopeType != null">
                and scope_type = #{scopeType,jdbcType=BIGINT}
            </if>
            <if test="periodType != null">
                and period_type = #{periodType,jdbcType=BIGINT}
            </if>
            <if test="isShowCenter != null">
                and is_show_center = #{isShowCenter,jdbcType=BIGINT}
            </if>
            <if test="isShowHomepage != null">
                and is_show_homepage = #{isShowHomepage}
            </if>
            <if test="stockNum != null">
                and stock_num = #{stockNum}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="stockFlag != null">
                and stock_num &gt; 0
            </if>
            <if test="usedStatusList != null and usedStatusList.size() > 0">
                AND used_status IN
                <foreach collection="usedStatusList" item="usedStatus" index="index" separator="," open="(" close=")">
                    #{usedStatus}
                </foreach>
            </if>
            <if test="startTime != null">
                and create_time &gt; #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt; DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="startCreateTime != null">
                and create_time &gt; #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                and create_time &lt; #{endCreateTime}
            </if>
            <if test="true">
                and deleted = 0
            </if>
            <if test="(corpCouponIdList != null and corpCouponIdList.size() != 0) or (goodsCouponIdList != null and goodsCouponIdList.size() != 0) or (appCorpId != null)">
                and (scope_type = 1
                <if test="corpCouponIdList != null and corpCouponIdList.size() != 0">
                    or (scope_type = 2 and coupon_id in(
                        <foreach collection="corpCouponIdList" item="id" separator=",">
                            #{id}
                        </foreach>
                    ))
                </if>
                <if test="goodsCouponIdList != null and goodsCouponIdList.size() != 0">
                    or ((scope_type = 3 or scope_type = 12) and coupon_id in(
                    <foreach collection="goodsCouponIdList" item="id" separator=",">
                        #{id}
                    </foreach>
                    ))
                </if>
                <if test="appCorpId != null">
                    or (scope_type = 11 and corp_id = #{appCorpId})
                </if>
                )
            </if>
        </where>
        <choose>
            <when test="sort != null">
                order by ${sort} ${order}
            </when>
            <when test="sortFlag == 1">
                order by discount_amount desc, platform_type asc, coupon_id asc
            </when>
            <when test="sortFlag == 2">
                order by create_time desc,weight desc
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="countCoupon" resultType="java.lang.Integer">
        select count(1)
        from p_coupon
        <where>
            <if test="scopeType != null">
                and scope_type = #{scopeType}
            </if>
            <if test="corpId != null">
                and corp_id = #{corpId}
            </if>
            <if test="createStartTime != null">
                and create_time &gt; #{createStartTime}
            </if>
            <if test="createEndTime != null">
                and create_time &lt; #{createEndTime}
            </if>
            <if test="true">
                and deleted = 0
            </if>
        </where>
    </select>

    <select id="searchByEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon
        <where>
            <if test="name != null and name !=''">
                and `name` like concat("%",#{name},"%")
            </if>
            <if test="scopeType != null">
                and scope_type = #{scopeType}
            </if>
            <if test="corpId != null">
                and corp_id = #{corpId}
            </if>
            <if test="createStartTime != null">
                and create_time &gt; #{createStartTime}
            </if>
            <if test="createEndTime != null">
                and create_time &lt; #{createEndTime}
            </if>
            <if test="true">
                and deleted = 0
            </if>
            <if test="rankingFiltration">
                and scope_type != 13
            </if>
        </where>
    </select>

    <update id="updateCouponStock">
        update p_coupon
        set stock_num = #{stockNum,jdbcType=INTEGER}
        where coupon_id = #{couponId,jdbcType=BIGINT}
          and stock_num = #{oldStockNum,jdbcType=INTEGER}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.promotion.infrastructure.data.persistence.coupon.PCouponUserMapper">

    <resultMap id="BaseResultMap" type="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser">
        <id property="couponUserId" column="coupon_user_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="couponId" column="coupon_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="discountAmount" column="discount_amount" jdbcType="BIGINT"/>
        <result property="thresholdAmount" column="threshold_amount" jdbcType="BIGINT"/>
        <result property="instruction" column="instruction" jdbcType="VARCHAR"/>
        <result property="platformType" column="platform_type" jdbcType="TINYINT"/>
        <result property="couponType" column="coupon_type" jdbcType="TINYINT"/>
        <result property="scopeType" column="scope_type" jdbcType="TINYINT"/>
        <result property="corpId" column="corp_id" jdbcType="BIGINT"/>
        <result property="corpName" column="corp_name" jdbcType="VARCHAR" />
        <result property="receivedType" column="received_type" jdbcType="TINYINT"/>
        <result property="exchangeCode" column="exchange_code" jdbcType="VARCHAR"/>
        <result property="usedStatus" column="used_status" jdbcType="TINYINT"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="usedTime" column="used_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        coupon_user_id
        ,user_id,username,coupon_id,name,
        discount_amount,threshold_amount,instruction,
        platform_type,coupon_type,scope_type,
        corp_id,corp_name,received_type,exchange_code,
        used_status,start_time,end_time,
        used_time,status,update_id,
        create_id,update_time,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon_user
        where coupon_user_id = #{couponUserId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser">
        delete
        from p_coupon_user
        where coupon_user_id = #{couponUserId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="coupon_user_id" keyProperty="couponUserId"
            parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser" useGeneratedKeys="true">
        insert into p_coupon_user
        ( coupon_user_id,username, user_id, coupon_id,name
        , discount_amount,threshold_amount,instruction
        , platform_type, coupon_type, scope_type
        , corp_id, corp_name, received_type, exchange_code
        , used_status, start_time, end_time
        , used_time, status, update_id
        , create_id, update_time, create_time)
        values ( #{couponUserId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},#{username,jdbcType=VARCHAR}, #{couponId,jdbcType=BIGINT}
               , #{name,jdbcType=VARCHAR},#{discountAmount,jdbcType=BIGINT}, #{thresholdAmount,jdbcType=BIGINT}, #{instruction,jdbcType=VARBINARY}
               , #{platformType,jdbcType=TINYINT}, #{couponType,jdbcType=TINYINT}, #{scopeType,jdbcType=TINYINT}
               , #{corpId,jdbcType=BIGINT},#{corpName,jdbcType=VARBINARY}, #{receivedType,jdbcType=TINYINT}, #{exchangeCode,jdbcType=VARCHAR}
               , #{usedStatus,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}
               , #{usedTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{updateId,jdbcType=BIGINT}
               , #{createId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="coupon_user_id" keyProperty="couponUserId"
            parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser" useGeneratedKeys="true">
        insert into p_coupon_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="couponUserId != null">coupon_user_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="username != null">username,</if>
            <if test="couponId != null">coupon_id,</if>
            <if test="name != null">name,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="thresholdAmount != null">threshold_amount,</if>
            <if test="instruction != null">instruction,</if>
            <if test="platformType != null">platform_type,</if>
            <if test="couponType != null">coupon_type,</if>
            <if test="scopeType != null">scope_type,</if>
            <if test="corpId != null">corp_id,</if>
            <if test="corpName != null">corp_name,</if>
            <if test="receivedType != null">received_type,</if>
            <if test="exchangeCode != null">exchange_code,</if>
            <if test="usedStatus != null">used_status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="usedTime != null">used_time,</if>
            <if test="status != null">status,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="couponUserId != null">#{couponUserId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="couponId != null">#{couponId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="discountAmount != null">#{discountAmount,jdbcType=BIGINT},</if>
            <if test="thresholdAmount != null">#{thresholdAmount,jdbcType=BIGINT},</if>
            <if test="instruction != null">#{instruction,jdbcType=VARCHAR},</if>
            <if test="platformType != null">#{platformType,jdbcType=TINYINT},</if>
            <if test="couponType != null">#{couponType,jdbcType=TINYINT},</if>
            <if test="scopeType != null">#{scopeType,jdbcType=TINYINT},</if>
            <if test="corpId != null">#{corpId,jdbcType=BIGINT},</if>
            <if test="corpName != null">#{corpName,jdbcType=VARBINARY},</if>
            <if test="receivedType != null">#{receivedType,jdbcType=TINYINT},</if>
            <if test="exchangeCode != null">#{exchangeCode,jdbcType=VARCHAR},</if>
            <if test="usedStatus != null">#{usedStatus,jdbcType=TINYINT},</if>
            <if test="startTime != null">#{startTime,jdbcType=TIMESTAMP},</if>
            <if test="endTime != null">#{endTime,jdbcType=TIMESTAMP},</if>
            <if test="usedTime != null">#{usedTime,jdbcType=TIMESTAMP},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser">
        update p_coupon_user
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="couponId != null">
                coupon_id = #{couponId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                discount_amount = #{discountAmount,jdbcType=BIGINT},
            </if>
            <if test="thresholdAmount != null">
                threshold_amount = #{thresholdAmount,jdbcType=BIGINT},
            </if>
            <if test="platformType != null">
                platform_type = #{platformType,jdbcType=TINYINT},
            </if>
            <if test="couponType != null">
                coupon_type = #{couponType,jdbcType=TINYINT},
            </if>
            <if test="scopeType != null">
                scope_type = #{scopeType,jdbcType=TINYINT},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="receivedType != null">
                received_type = #{receivedType,jdbcType=TINYINT},
            </if>
            <if test="exchangeCode != null">
                exchange_code = #{exchangeCode,jdbcType=VARCHAR},
            </if>
            <if test="usedStatus != null">
                used_status = #{usedStatus,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="usedTime != null">
                used_time = #{usedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where coupon_user_id = #{couponUserId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser">
        update p_coupon_user
        set user_id       = #{userId,jdbcType=BIGINT},
            coupon_id     = #{couponId,jdbcType=BIGINT},
            name          = #{name,jdbcType=VARCHAR},
            threshold_amount = #{thresholdAmount,jdbcType=BIGINT},
            platform_type = #{platformType,jdbcType=TINYINT},
            coupon_type   = #{couponType,jdbcType=TINYINT},
            scope_type    = #{scopeType,jdbcType=TINYINT},
            corp_id       = #{corpId,jdbcType=BIGINT},
            received_type = #{receivedType,jdbcType=TINYINT},
            exchange_code = #{exchangeCode,jdbcType=VARCHAR},
            used_status   = #{usedStatus,jdbcType=TINYINT},
            start_time    = #{startTime,jdbcType=TIMESTAMP},
            end_time      = #{endTime,jdbcType=TIMESTAMP},
            used_time     = #{usedTime,jdbcType=TIMESTAMP},
            status        = #{status,jdbcType=TINYINT},
            update_id     = #{updateId,jdbcType=BIGINT},
            create_id     = #{createId,jdbcType=BIGINT},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            create_time   = #{createTime,jdbcType=TIMESTAMP}
        where coupon_user_id = #{couponUserId,jdbcType=BIGINT}
    </update>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon_user
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="couponId != null">
                and coupon_id = #{couponId}
            </if>
            <if test="scopeType != null">
                and scope_type = #{scopeType}
            </if>
            <if test="receivedType != null">
                and received_type = #{receivedType}
            </if>
            <if test="startTime != null">
                and create_time &gt; #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt; DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="usedStatusList != null and usedStatusList.size() > 0">
                and used_status IN
                <foreach collection="usedStatusList" item="usedStatus" index="index" separator="," open="(" close=")">
                    #{usedStatus}
                </foreach>
            </if>
            <if test="userIdList != null and userIdList.size() > 0">
                and user_id IN
                <foreach collection="userIdList" item="userId" index="index" separator="," open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="couponIdList != null and couponIdList.size() > 0">
                and coupon_id IN
                <foreach collection="couponIdList" item="couponId" index="index" separator="," open="(" close=")">
                    #{couponId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByConditionPager" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon_user
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="corpId != null">
                and corp_id = #{corpId}
            </if>
            <if test="usedStatusList != null and usedStatusList.size() > 0">
                and used_status IN
                <foreach collection="usedStatusList" item="usedStatus" index="index" separator="," open="(" close=")">
                    #{usedStatus}
                </foreach>
            </if>
            <if test="usedStatus != null">
                and used_status = #{usedStatus}
            </if>
            <if test="receivedType != null">
                and received_type = #{receivedType}
            </if>
            <if test="scopeType != null">
                and scope_type = #{scopeType}
            </if>
            <if test="couponId != null">
                and coupon_id = #{couponId}
            </if>
            <if test="couponName != null">
                and name like concat('%',#{couponName},'%')
            </if>
            <if test="platformType != null">
                and platform_type = #{platformType}
            </if>
            <if test="startTimeLeft != null">
                and start_time &gt; #{startTimeLeft}
            </if>
            <if test="startTimeRight != null">
                and start_time &lt; #{startTimeRight}
            </if>
            <if test="endTimeLeft != null">
                and end_time &gt; #{endTimeLeft}
            </if>
            <if test="endTimeRight != null">
                and end_time &lt; #{endTimeRight}
            </if>
            <if test="receivedTime != null">
                and create_time &gt; #{receivedTime}
            </if>
            <if test="startTime != null">
                and create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
            <if test="userName != null">
                and username like concat('%',#{userName},'%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="expiredFlag != null">
                and (status = 1 or used_status in(2,3))
            </if>
        </where>
        <if test="sort != null">
            order by ${sort} ${order}
        </if>
    </select>

    <select id="selectUsablePager" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon_user
        where
            user_id = #{userId}
        and (coupon_type = 1 or (coupon_type = 2 and threshold_amount &lt;=#{amount}))
        and (scope_type = 1
        <if test="gorpIdList != null and gorpIdList.size() != 0">
            or (scope_type = 2 and coupon_id in(
                <foreach collection="gorpIdList" item="id" separator=",">
                    #{id}
                </foreach>
            ))
        </if>
        <if test="goodsIdList != null and goodsIdList.size() != 0">
            or ((scope_type = 3 or scope_type = 12) and coupon_id in(
                <foreach collection="goodsIdList" item="id" separator=",">
                    #{id}
                </foreach>
            ))
        </if>
        <if test="corpId != null">
            or (scope_type = 11 and corp_id = #{corpId})
        </if>
        )
        and end_time &gt;= now()
        and start_time &lt;= now()
        and used_status= 1
        and status = 0
        order by platform_type desc,discount_amount desc
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from p_coupon_user
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="usedStatusList != null and usedStatusList.size() > 0">
                and used_status IN
                <foreach collection="usedStatusList" item="usedStatus" index="index" separator="," open="(" close=")">
                    #{usedStatus}
                </foreach>
            </if>
            <if test="couponId != null">
                and coupon_id = #{couponId}
            </if>
            <if test="platformType != null">
                and platform_type = #{platformType}
            </if>

        </where>
    </select>

    <select id="countNumByCorpId" resultType="java.lang.Integer">
        select count(1)
        from p_coupon_user
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="usedStatusList != null and usedStatusList.size() > 0">
                AND used_status IN
                <foreach collection="usedStatusList" item="usedStatus" index="index" separator="," open="(" close=")">
                    #{usedStatus}
                </foreach>
            </if>
            <if test="corpId != null">
                and corp_id = #{corpId}
            </if>
        </where>
    </select>

    <update id="updateCouponUserOverdue" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser">
        update p_coupon_user
        set used_status = 3
        where used_status = 1
        <if test="couponUserId != null">
            and coupon_user_id = #{couponUserId}
        </if>
        <if test="couponId != null">
            and coupon_id = #{couponId}
        </if>
    </update>
    
    <update id="updateByCouponId" parameterType="com.xk.promotion.infrastructure.data.po.coupon.PCouponUser">
        update p_coupon_user
        <set>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where coupon_id = #{couponId}
    </update>
</mapper>

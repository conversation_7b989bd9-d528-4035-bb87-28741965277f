package com.xk.acct.infrastructure.data.persistence.user;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.acct.infrastructure.data.po.user.AUserBindaccount;
import org.apache.ibatis.annotations.Param;

@Repository
@Table(value = "a_user_bindaccount")
public interface AUserBindaccountMapper {

    AUserBindaccount selectByUnionid(AUserBindaccount aUserBindaccount);

    int insertSelective(AUserBindaccount aUserBindaccount);

    int deleteByUserId(AUserBindaccount aUserBindaccount);
}

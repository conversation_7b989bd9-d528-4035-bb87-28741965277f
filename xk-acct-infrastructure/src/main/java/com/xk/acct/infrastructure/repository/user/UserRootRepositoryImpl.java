package com.xk.acct.infrastructure.repository.user;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import com.xk.acct.domain.model.user.UserBindaccountEntity;
import com.xk.acct.domain.model.user.UserDeviceEntity;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.myco.mydata.infrastructure.cache.key.SortedSetsCacheKeyValue;
import com.xk.acct.domain.model.user.UserCollectViewsEntity;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.acct.domain.model.user.UserDeviceEntity;
import com.xk.acct.domain.model.user.UserRoot;
import com.xk.acct.domain.repository.user.UserRootRepository;
import com.xk.acct.infrastructure.cache.dao.viewCollect.UserCollectViewsGoodsDao;
import com.xk.acct.infrastructure.cache.key.viewCollect.UserCollectViewsGoodsKey;
import com.xk.acct.infrastructure.data.persistence.user.*;
import com.xk.acct.infrastructure.data.po.user.*;
import com.xk.infrastructure.cache.dao.cancel.CancelListDao;
import com.xk.infrastructure.cache.key.cancel.CancelListKey;
import com.xk.infrastructure.commons.cancel.CancelTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Repository
@RequiredArgsConstructor
public class UserRootRepositoryImpl implements UserRootRepository {

    private final AUserDataMapper aUserDataMapper;

    private final AUserAddressMapper aUserAddressMapper;

    private final AUserRegisterMapper aUserRegisterMapper;

    private final AUserSecurityMapper aUserSecurityMapper;

    private final ALoginLogMapper aLoginLogMapper;

    private final Converter converter;

    private final AUserPayAccountMapper aUserOnlineAccountMapper;

    private final UserCollectViewsGoodsDao userCollectViewsGoodsDao;

    private final AUserConfigMapper aUserConfigMapper;

    private final AUserDeviceMapper aUserDeviceMapper;

    private final AUserBindaccountMapper aUserBindaccountMapper;

    private final CancelListDao cancelListDao;

    private final AUserDataBackMapper aUserDataBackMapper;

    @Override
    public Mono<Void> save(UserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getUserData())
                        .flatMap(userData -> save(userData, AUserData.class, converter::convert,
                                aUserDataMapper::insertSelective)))
                .then(Mono.justOrEmpty(root.getUserRegister()))
                .flatMap(userRegister -> save(userRegister, AUserRegister.class, converter::convert,
                        aUserRegisterMapper::insert))
                .then(Mono.justOrEmpty(root.getUserSecurity()))
                .flatMap(userSecurity -> save(userSecurity, AUserSecurity.class, converter::convert,
                        aUserSecurityMapper::insert))
                .then(Mono.justOrEmpty(root.getUserLoginLog()))
                .flatMap(userLogin -> save(userLogin, ALoginLog.class, converter::convert,
                        aLoginLogMapper::insert))
                .then(Mono.justOrEmpty(root.getUserAddressData()))
                .flatMap(userAddress -> save(userAddress, AUserAddress.class, converter::convert,
                        aUserAddressMapper::insertSelective))
                .then(Mono.justOrEmpty(root.getUserConfigEntities()))
                .flatMapMany(Flux::fromIterable).flatMap(entity -> save(entity, AUserConfig.class,
                        converter::convert, aUserConfigMapper::insertSelective))
                .then(); // 移除多余的右括号
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(UserRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(UserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getUserData())
                        .flatMap(userData -> update(userData, AUserData.class, converter::convert,
                                aUserDataMapper::updateByPrimaryKeySelective)))
                .then(Mono.justOrEmpty(root.getUserRegister()))
                .flatMap(userRegister -> save(userRegister, AUserRegister.class, converter::convert,
                        aUserRegisterMapper::updateByPrimaryKeySelective))
                .then(Mono.justOrEmpty(root.getUserSecurity()))
                .flatMap(userSecurity -> save(userSecurity, AUserSecurity.class, converter::convert,
                        aUserSecurityMapper::updateByPrimaryKeySelective))
                .then(Mono.justOrEmpty(root.getUserAddressData()))
                .flatMap(userAddress -> update(userAddress, AUserAddress.class, converter::convert,
                        aUserAddressMapper::updateByPrimaryKeySelective));
    }

    @Override
    public Mono<Void> remove(UserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getUserData())
                        .flatMap(userData -> remove(userData, AUserData.class, converter::convert,
                                aUserDataMapper::deleteByPrimaryKey)))
                .then(Mono.justOrEmpty(root.getUserRegister()))
                .flatMap(userRegister -> remove(userRegister, AUserRegister.class,
                        converter::convert, aUserRegisterMapper::deleteByPrimaryKey))
                .then(Mono.justOrEmpty(root.getUserSecurity()))
                .flatMap(userSecurity -> remove(userSecurity, AUserSecurity.class,
                        converter::convert, aUserSecurityMapper::deleteByPrimaryKey));
    }

    @Override
    public Mono<Void> updateAll(UserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getUserData())
                        .flatMap(userData -> update(userData, AUserData.class, converter::convert,
                                aUserDataMapper::updateByPrimaryKey)))
                .then(Mono.justOrEmpty(root.getUserRegister()))
                .flatMap(userRegister -> save(userRegister, AUserRegister.class, converter::convert,
                        aUserRegisterMapper::updateByPrimaryKey))
                .then(Mono.justOrEmpty(root.getUserSecurity()))
                .flatMap(userSecurity -> save(userSecurity, AUserSecurity.class, converter::convert,
                        aUserSecurityMapper::updateByPrimaryKey));
    }

    @Override
    public Mono<Long> removeRt(UserRoot root) {
        return null;
    }

    @Override
    public Mono<Void> saveToBack(UserRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getUserData())
                        .flatMap(userData -> update(userData, AUserDataBack.class,
                                converter::convert, aUserDataBackMapper::insertSelective)));
    }

    @Override
    public Mono<Void> removeLogically(UserRoot userRoot) {
        return Mono.justOrEmpty(userRoot)
                .flatMap(domain -> Mono.justOrEmpty(domain.getUserData())
                        .flatMap(userData -> remove(userData, AUserData.class, converter::convert,
                                aUserDataMapper::deleteByPrimaryKeyLogically)));
    }

    /**
     * 添加用户在线账户
     *
     * @param userRoot
     * @return
     */
    @Override
    public Mono<Void> saveUserPayAccount(UserRoot userRoot) {
        if (userRoot.getUserPayAccountEntities() == null) {
            return Mono.empty();
        }
        return Flux.fromIterable(userRoot.getUserPayAccountEntities())
                .flatMap(entity -> this.save(entity, AUserPayAccount.class, this.converter::convert,
                        aUserOnlineAccountMapper::insertSelective))
                .then();
    }

    /**
     * 修改用户在线账户
     *
     * @param userRoot
     * @return
     */
    @Override
    public Mono<Void> updateUserPayAccount(UserRoot userRoot) {
        if (userRoot.getUserPayAccountEntities() == null) {
            return Mono.empty();
        }
        return Flux.fromIterable(userRoot.getUserPayAccountEntities())
                .flatMap(entity -> this.update(entity, AUserPayAccount.class,
                        this.converter::convert,
                        aUserOnlineAccountMapper::updateByPrimaryKeySelective))
                .then();
    }

    @Override
    public Mono<Void> addUserViewsCollectCache(UserRoot userRoot) {
        return Mono.fromRunnable(() -> {
            List<UserCollectViewsEntity> userCollectViewsEntities =
                    userRoot.getUserCollectViewsEntities();
            UserCollectViewsEntity first = userCollectViewsEntities.getFirst();
            UserCollectViewsGoodsKey searchKey = UserCollectViewsGoodsKey.builder()
                    .userId(first.getUserId()).busiType(first.getBusiType().getCode())
                    .type(first.getType().getCode()).build();
            SortedSetsCacheKeyValue<String> cacheKeyValue = new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(first.getBusiId());
            cacheKeyValue.setScore((double) first.getCreateTime().getTime());
            userCollectViewsGoodsDao.addValue(searchKey, cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> removeUserViewsCollectCache(UserRoot userRoot) {
        return Mono.fromRunnable(() -> {
            List<UserCollectViewsEntity> userCollectViewsEntities =
                    userRoot.getUserCollectViewsEntities();
            UserCollectViewsEntity first = userCollectViewsEntities.getFirst();
            UserCollectViewsGoodsKey searchKey = UserCollectViewsGoodsKey.builder()
                    .userId(first.getUserId()).busiType(first.getBusiType().getCode())
                    .type(first.getType().getCode()).build();
            userCollectViewsGoodsDao.delValue(searchKey, first.getBusiId());
        });
    }

    @Override
    public Mono<Void> removeUserViewsCollectCacheFirst(UserRoot userRoot) {
        return Mono.fromRunnable(() -> {
            List<UserCollectViewsEntity> userCollectViewsEntities =
                    userRoot.getUserCollectViewsEntities();
            UserCollectViewsEntity first = userCollectViewsEntities.getFirst();
            UserCollectViewsGoodsKey searchKey = UserCollectViewsGoodsKey.builder()
                    .userId(first.getUserId()).busiType(first.getBusiType().getCode())
                    .type(first.getType().getCode()).build();
            userCollectViewsGoodsDao.delValueByRank(searchKey, 1L, 1L);
        });
    }

    @Override
    public Mono<Void> deletedUserViewCollect(UserRoot userRoot) {
        return Mono.fromRunnable(() -> {
            List<UserCollectViewsEntity> userCollectViewsEntities =
                    userRoot.getUserCollectViewsEntities();
            UserCollectViewsEntity first = userCollectViewsEntities.getFirst();
            UserCollectViewsGoodsKey searchKey = UserCollectViewsGoodsKey.builder()
                    .userId(first.getUserId()).busiType(first.getBusiType().getCode())
                    .type(first.getType().getCode()).build();
            userCollectViewsGoodsDao.delKey(searchKey);
        });
    }

    @Override
    public Mono<Void> updateUserSecurity(UserRoot userRoot) {
        return update(userRoot.getUserSecurity(), AUserSecurity.class, converter::convert,
                aUserSecurityMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> saveUserSecurity(UserRoot userRoot) {
        return save(userRoot.getUserSecurity(), AUserSecurity.class, converter::convert,
                aUserSecurityMapper::insertSelective);
    }

    @Override
    public Mono<Void> updateUserConfig(UserRoot userRoot) {
        if (userRoot.getUserConfigEntities() == null) {
            return Mono.empty();
        }
        return Flux.fromIterable(userRoot.getUserConfigEntities())
                .flatMap(entity -> this.update(entity, AUserConfig.class, this.converter::convert,
                        aUserConfigMapper::updateValueByKey))
                .then();
    }

    @Override
    public Mono<Void> addDelayCancelQueue(UserDataEntity userDataEntity) {
        return Mono.fromRunnable(() -> {
            LocalDateTime localDateTime = userDataEntity.getPlanDeleteTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime();
            DateTimeFormatter formatter =
                    DateTimeFormatter.ofPattern(UserRoot.Constant.TIME_PATTERN);
            CancelListKey cancelListKey =
                    CancelListKey.builder().timeFormat(localDateTime.format(formatter))
                            .cancelType(CancelTypeEnum.USER_DELAY).build();
            cancelListDao.addValue(cancelListKey, String.valueOf(userDataEntity.getUserId()));
        });
    }

    @Override
    public Mono<Void> saveUserDevice(UserRoot userRoot) {
        Optional<UserDeviceEntity> userDeviceOpt =
                Optional.ofNullable(userRoot).map(UserRoot::getUserDevice);
        if (userDeviceOpt.isEmpty()) {
            return Mono.empty();
        }

        return save(userDeviceOpt.get(), AUserDevice.class, converter::convert,
                aUserDeviceMapper::insertSelective);
    }

    @Override
    public Mono<Void> updateUserDevice(UserRoot userRoot) {
        return update(userRoot.getUserDevice(), AUserDevice.class, converter::convert,
                aUserDeviceMapper::updateByUserIdAndDeviceId);
    }

    @Override
    public Mono<Void> saveBindaccount(UserBindaccountEntity entity) {
        return this.save(entity, AUserBindaccount.class, converter::convert,
                aUserBindaccountMapper::insertSelective);
    }

    @Override
    public Mono<Void> deleteUserDelayCancelCache(UserRoot userRoot) {
        return Mono.fromRunnable(() -> {
            UserDataEntity userDataEntity = userRoot.getUserData();
            LocalDateTime localDateTime = userDataEntity.getPlanDeleteTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime();
            DateTimeFormatter formatter =
                    DateTimeFormatter.ofPattern(UserRoot.Constant.TIME_PATTERN);
            CancelListKey cancelListKey =
                    CancelListKey.builder().timeFormat(localDateTime.format(formatter))
                            .cancelType(CancelTypeEnum.USER_DELAY).build();
            cancelListDao.removeRem(cancelListKey, 1L, String.valueOf(userDataEntity.getUserId()));
        });
    }

    @Override
    public Mono<Void> deleteUserDelayCancelQueueCache(UserRoot userRoot) {
        return Mono.fromRunnable(() -> {
            UserDataEntity userDataEntity = userRoot.getUserData();
            LocalDateTime localDateTime = userDataEntity.getPlanDeleteTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime();
            DateTimeFormatter formatter =
                    DateTimeFormatter.ofPattern(UserRoot.Constant.TIME_PATTERN);
            CancelListKey cancelListKey =
                    CancelListKey.builder().timeFormat(localDateTime.format(formatter))
                            .cancelType(CancelTypeEnum.USER_DELAY).build();
            cancelListDao.delKey(cancelListKey);
        });
    }

    @Override
    public Mono<Void> deleteBinaccountByUserId(UserBindaccountEntity entity) {
        return this.update(entity, AUserBindaccount.class, converter::convert,
                aUserBindaccountMapper::deleteByUserId);
    }
}

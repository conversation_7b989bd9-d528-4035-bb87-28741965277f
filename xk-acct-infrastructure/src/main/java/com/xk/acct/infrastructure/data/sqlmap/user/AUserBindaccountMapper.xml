<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.acct.infrastructure.data.persistence.user.AUserBindaccountMapper">

    <resultMap id="BaseResultMap" type="com.xk.acct.infrastructure.data.po.user.AUserBindaccount">
        <id property="bindaccountId" column="bindaccount_id" />
        <result property="userId" column="user_id" />
        <result property="unionid" column="unionid" />
        <result property="channelType" column="channel_type"/>
        <result property="bindType" column="bind_type" />
        <result property="createTime" column="create_time" />
        <result property="unbindTime" column="unbind_time" />
        <result property="status" column="status" />
        <result property="nickname" column="nickname" />
        <result property="picUrl" column="pic_url" />
        <result property="sex" column="sex" />
    </resultMap>

    <sql id="Base_Column_List">
        bindaccount_id,user_id,unionid,channel_type,
        bind_type,create_time,unbind_time,status,nickname,pic_url,sex
    </sql>

    <select id="selectByUnionid" parameterType="com.xk.acct.infrastructure.data.po.user.AUserBindaccount" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            a_user_bindaccount
        where
            unionid = #{unionid}
        and channel_type = #{channelType}
        and status = 0
    </select>

    <insert id="insertSelective" keyColumn="bindaccount_id" keyProperty="bindaccountId"
            parameterType="com.xk.acct.infrastructure.data.po.user.AUserBindaccount" useGeneratedKeys="true">
        insert into a_user_bindaccount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bindaccountId != null">bindaccount_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="unionid != null">unionid,</if>
            <if test="channelType != null">channel_type,</if>
            <if test="bindType != null">bind_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="unbindTime != null">unbind_time,</if>
            <if test="status != null">status,</if>
            <if test="nickname != null">nickname,</if>
            <if test="picUrl != null">pic_url,</if>
            <if test="sex != null">sex,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bindaccountId != null">#{bindaccountId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="unionid != null">#{unionid,jdbcType=VARCHAR},</if>
            <if test="channelType != null">#{channelType,jdbcType=INTEGER},</if>
            <if test="bindType != null">#{bindType,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="unbindTime != null">#{unbindTime,jdbcType=TIMESTAMP},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="nickname != null">#{nickname,jdbcType=VARCHAR},</if>
            <if test="picUrl != null">#{picUrl,jdbcType=VARCHAR},</if>
            <if test="sex != null">#{sex,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="deleteByUserId" parameterType="com.xk.acct.infrastructure.data.po.user.AUserBindaccount">
        update a_user_bindaccount
            set status = 1
        where user_id = #{userId}
    </update>

</mapper>
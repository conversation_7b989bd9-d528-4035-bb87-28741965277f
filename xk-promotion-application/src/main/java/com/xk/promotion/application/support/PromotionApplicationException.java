package com.xk.promotion.application.support;

import com.xk.promotion.application.commons.PromotionApplicationErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;

/**
 * @author: killer
 **/
public class PromotionApplicationException extends ApplicationWrapperThrowable {

    public PromotionApplicationException(PromotionApplicationErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public PromotionApplicationException(PromotionApplicationErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public PromotionApplicationException(PromotionApplicationErrorEnum exceptionIdentifier,String msg) {
        super(exceptionIdentifier,msg);
    }

}

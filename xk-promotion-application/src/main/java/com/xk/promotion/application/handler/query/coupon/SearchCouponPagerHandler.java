package com.xk.promotion.application.handler.query.coupon;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.infrastructure.adapter.stock.CouponStockAdapterService;
import com.xk.promotion.application.action.query.coupon.SearchCouponPagerQuery;
import com.xk.promotion.domain.enums.coupon.CouponConstantEnum;
import com.xk.promotion.domain.enums.coupon.PlatformTypeEnum;
import com.xk.promotion.domain.enums.coupon.YesOrNotEnum;
import com.xk.promotion.domain.enums.exchange.ExchangeCodeTypeEnum;
import com.xk.promotion.domain.enums.exchange.ExchangeTypeEnum;
import com.xk.promotion.domain.model.coupon.entity.CouponEntity;
import com.xk.promotion.domain.model.exchange.entity.ExchangeCodeEntity;
import com.xk.promotion.domain.model.exchange.entity.ExchangeEntity;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SearchCouponPagerHandler
        implements IActionQueryHandler<SearchCouponPagerQuery, Pagination> {

    private final CouponRootQueryRepository couponRootQueryRepository;

    private final CouponUserRootQueryRepository couponUserRootQueryRepository;

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;

    private final CouponStockAdapterService couponStockAdapterService;

    private final SelectorRootService selectorRootService;

    @Override
    public Mono<Pagination> execute(Mono<SearchCouponPagerQuery> mono) {
        return mono.flatMap(query -> execute(Mono.just(query), q -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(q.getLimit());
            pagination.setOffset(q.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(q));
            return pagination;
        }, couponRootQueryRepository::searchCoupon, CouponEntity.class).flatMap(pagination -> {
            List<CouponEntity> couponEntityList = pagination.getRecords();

            // 使用 Flux 处理每个 CouponEntity
            return Flux.fromIterable(couponEntityList).flatMap(couponEntity -> {
                // 查询公共兑换码
                return couponStockAdapterService
                        .getRemainStock(StringIdentifier.builder()
                                .id(couponEntity.getCouponId().toString()).build())
                        .flatMap(stock -> exchangeRootQueryRepository
                                .findByBusinessIdAndType(ExchangeEntity.builder()
                                        .exchangeType(ExchangeTypeEnum.COUPON.getCode())
                                        .exchangeBusinessId(couponEntity.getCouponId().toString())
                                        .build())
                                .flatMap(exchangeEntity -> {
                                    // 查询公共兑换码
                                    return exchangeRootQueryRepository
                                            .findByExchangeIdAndType(ExchangeCodeEntity.builder()
                                                    .exchangeCodeType(
                                                            ExchangeCodeTypeEnum.COMMON.getCode())
                                                    .exchangeId(exchangeEntity.getExchangeId())
                                                    .build());
                                }).flatMap(exchangeCodeEntity -> {
                                    // 查询用户领取优惠券是否已达上限
                                    return couponUserRootQueryRepository
                                            .searchUserCoupon(CouponUserEntity.builder()
                                                    .couponId(couponEntity.getCouponId())
                                                    .userId(query.getUserId()).build())
                                            .count().flatMap(count -> {
                                                CouponRspDto couponRspDto = new CouponRspDto();
                                                BeanUtils.copyProperties(couponEntity,
                                                        couponRspDto);
                                                couponRspDto.setStockNum(
                                                        stock.getRemainRealStock().intValue());
                                                couponRspDto.setCommonCode(
                                                        exchangeCodeEntity.getExchangeCode());
                                                couponRspDto.setCommonCodeId(exchangeCodeEntity.getExchangeCodeId());
                                                couponRspDto.setCommonCodeStatus(exchangeCodeEntity.getStatus());
                                                couponRspDto.setUsedStatus(exchangeCodeEntity.getUsedStatus());

                                                // 设置是否已达领取上限
                                                setReceiveLimit(couponRspDto, count, couponEntity);

                                                // 设置商家名称和LOGO
                                                return setCorpInfo(couponRspDto, couponEntity);
                                            });
                                }).onErrorResume(throwable -> {
                                    // 如果查询兑换码失败，返回不带兑换码的 DTO
                                    return couponUserRootQueryRepository
                                            .searchUserCoupon(CouponUserEntity.builder()
                                                    .couponId(couponEntity.getCouponId())
                                                    .userId(query.getUserId()).build())
                                            .count().flatMap(count -> {
                                                CouponRspDto couponRspDto = new CouponRspDto();
                                                BeanUtils.copyProperties(couponEntity,
                                                        couponRspDto);
                                                couponRspDto.setStockNum(
                                                        stock.getRemainRealStock().intValue());

                                                // 设置是否已达领取上限
                                                setReceiveLimit(couponRspDto, count, couponEntity);

                                                // 设置商家名称和LOGO
                                                return setCorpInfo(couponRspDto, couponEntity);
                                            });
                                }));
            }).collectList().map(couponRspDtoList -> {
                //结果排序
                pagination.setRecords(couponRspDtoList);
                return pagination;
            });
        }));
    }

    /**
     * 设置用户领取优惠券是否已达上限
     */
    private void setReceiveLimit(CouponRspDto couponRspDto, Long count, CouponEntity couponEntity) {
        if (count >= couponEntity.getPersonNum()) {
            couponRspDto.setIsReceivedMax(YesOrNotEnum.YES.getCode());
        } else {
            couponRspDto.setIsReceivedMax(YesOrNotEnum.NOT.getCode());
        }
    }

    /**
     * 设置商家名称和LOGO
     */
    private Mono<CouponRspDto> setCorpInfo(CouponRspDto couponRspDto, CouponEntity couponEntity) {
        if (couponEntity.getPlatformType().equals(PlatformTypeEnum.MANAGER.getCode())) {
            // 运营平台的优惠券，设置官方LOGO
            couponRspDto.setCorpLogo(CouponConstantEnum.DEFAULT_CORP_LOGO.getDesc());
            couponRspDto.setCorpName(CouponConstantEnum.DEFAULT_CORP_NAME.getDesc());
            return Mono.just(couponRspDto);
        } else {
            // 商家平台的优惠券，设置商家LOGO
            return selectorRootService.getCorpObject(couponEntity.getCorpId())
                    .map(corpObjectRoot -> {
                        couponRspDto.setCorpName(corpObjectRoot.getCorpInfoObjectEntity().getCorpName());
                        couponRspDto.setCorpLogo(corpObjectRoot.getCorpInfoObjectEntity().getCorpLogo());
                        return couponRspDto;
                    });
        }
    }

}


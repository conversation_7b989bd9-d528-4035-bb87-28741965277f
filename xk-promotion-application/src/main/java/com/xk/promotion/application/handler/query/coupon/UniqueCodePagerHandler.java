package com.xk.promotion.application.handler.query.coupon;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.promotion.application.action.query.coupon.UniqueCodePagerQuery;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.domain.enums.exchange.ExchangeCodeTypeEnum;
import com.xk.promotion.domain.model.exchange.entity.ExchangeCodeEntity;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponCodeDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class UniqueCodePagerHandler
        implements IActionQueryHandler<UniqueCodePagerQuery, Pagination> {

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;
    private final SelectorRootService selectorRootService;

    @Override
    public Mono<Pagination> execute(Mono<UniqueCodePagerQuery> mono) {
        return mono.flatMap(uniqueCodePagerQuery -> {
            return execute(mono, q -> {
                Pagination pagination = new Pagination();
                pagination.setLimit(q.getLimit());
                pagination.setOffset(q.getOffset());
                ExchangeCodeEntity entity = ExchangeCodeEntity.builder()
                        .exchangeBusinessId(uniqueCodePagerQuery.getCouponId().toString())
                        .exchangeCodeType(ExchangeCodeTypeEnum.ONCE.getCode()).build();
                pagination.setCriteria(CollectionHelper.converBeanToMap(entity));
                return pagination;
            }, exchangeRootQueryRepository::findByBusinessIdAndTypePager, ExchangeCodeEntity.class)
                    .flatMap(pagination -> {
                        List<ExchangeCodeEntity> records = pagination.getRecords();
                        if (records.isEmpty()) {
                            return Mono.just(pagination);
                        }
                        // 使用 Flux 处理每个兑换码实体
                        return Flux.fromIterable(records).flatMap(exchangeCodeEntity -> {
                            // 如果有接收用户ID，则查询用户名
                            if (Objects.equals(exchangeCodeEntity.getUsedStatus(),
                                    UsedStatusEnum.USED.getCode())) {
                                return selectorRootService
                                        .getUserObject(exchangeCodeEntity.getReceivedUserId())
                                        .map(userObjectRoot -> {
                                            String userName = userObjectRoot
                                                    .getUserDataObjectEntity().getNickname();
                                            String mobile = userObjectRoot.getUserDataObjectEntity()
                                                    .getMobile();
                                            return CouponCodeDto.builder()
                                                    .exchangeId(exchangeCodeEntity.getExchangeCodeId())
                                                    .exchangeCode(
                                                            exchangeCodeEntity.getExchangeCode())
                                                    .receivedStatus(
                                                            exchangeCodeEntity.getUsedStatus())
                                                    .receivedUserId(
                                                            exchangeCodeEntity.getReceivedUserId())
                                                    .receivedUserName(userName)
                                                    .receivedMobile(mobile)
                                                    .status(exchangeCodeEntity.getStatus()).build();
                                        });
                            } else {
                                return Mono.just(CouponCodeDto.builder()
                                        .exchangeId(exchangeCodeEntity.getExchangeCodeId())
                                        .exchangeCode(exchangeCodeEntity.getExchangeCode())
                                        .receivedStatus(exchangeCodeEntity.getUsedStatus())
                                        .status(exchangeCodeEntity.getStatus()).build());
                            }
                        }).collectList().map(couponCodeDtoList -> {
                            pagination.setRecords(couponCodeDtoList);
                            return pagination;
                        });
                    });
        });
    }
}

package com.xk.promotion.application.handler.command.coupon;

import java.math.BigDecimal;
import java.util.*;

import com.xk.domain.service.stock.StockRootService;
import com.xk.promotion.domain.enums.coupon.*;
import com.xk.promotion.domain.enums.exchange.ExchangeCodeTypeEnum;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.enums.config.BusinessConfigGroupTypeEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.interfaces.dto.req.stock.StockReqDto;
import com.xk.goods.interfaces.service.stock.StockService;
import com.xk.promotion.application.action.command.coupon.SaveCouponCommand;
import com.xk.promotion.application.commons.PromotionApplicationErrorEnum;
import com.xk.promotion.application.support.PromotionApplicationException;
import com.xk.promotion.domain.enums.exchange.ExchangeTypeEnum;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.entity.CouponEntity;
import com.xk.promotion.domain.model.coupon.entity.CouponRelateCorpEntity;
import com.xk.promotion.domain.model.coupon.entity.CouponRelateGoodsEntity;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.exchange.ExchangeRoot;
import com.xk.promotion.domain.model.exchange.entity.ExchangeCodeEntity;
import com.xk.promotion.domain.model.exchange.entity.ExchangeEntity;
import com.xk.promotion.domain.model.exchange.id.ExchangeIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootRepository;
import com.xk.promotion.domain.service.exchange.ExchangeRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveCouponHandler implements IActionCommandHandler<SaveCouponCommand, Void> {

    private final CouponRootRepository couponRootRepository;

    private final CouponRootQueryRepository couponRootQueryRepository;

    private final ExchangeRootRepository exchangeRootRepository;

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;

    private final ExchangeRootService exchangeRootService;

    private final Converter converter;

    private final StockService stockService;

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;

    private final StockRootService stockRootService;

    @Override
    public Mono<Void> execute(Mono<SaveCouponCommand> mono) {
        return mono.flatMap(command -> {
            // 1、检查优惠券创建规则是否符合
            Mono<Void> checkMono = checkCouponCreationRules(command);

            // 2、 创建优惠券
            CouponEntity couponEntity = converter.convert(command, CouponEntity.class);
            couponEntity.setStockNum(couponEntity.getTotalNum());
            couponEntity.setWeight(0);
            couponEntity.setUpdateId(command.getUserId());
            couponEntity.setCreateId(command.getUserId());
            if (YesOrNotEnum.YES.getCode().equals(command.getIsShowCenter())){
                couponEntity.setShowCenterDate(new Date());
            }
            CouponIdentifier couponIdentifier =
                    CouponIdentifier.builder().couponId(couponEntity.getCouponId()).build();
            Mono<Void> createCoupnMono = couponRootRepository.save(CouponRoot.builder()
                    .identifier(couponIdentifier).couponEntity(couponEntity).build());

            // 3、 创建兑换实体
            Mono<Void> createExchangeMono = exchangeRootService.generateId().flatMap(exchangeId -> {
                ExchangeEntity exchangeEntity = new ExchangeEntity();
                exchangeEntity.setExchangeId(exchangeId);
                exchangeEntity.setExchangeType(ExchangeTypeEnum.COUPON.getCode());
                exchangeEntity.setExchangeBusinessId(couponEntity.getCouponId().toString());
                exchangeEntity.setStatus(StatusEnum.ENABLE.getValue());
                exchangeEntity.setUpdateId(command.getUserId());
                exchangeEntity.setCreateId(command.getUserId());

                ExchangeIdentifier exchangeIdentifier =
                        ExchangeIdentifier.builder().exchangeId(exchangeId).build();

                // 创建兑换码实体，使用唯一兑换码
                return exchangeRootService.generateCodeId().flatMap(codeId -> {
                    return generateUniqueExchangeCode().flatMap(uniqueCode -> {
                        ExchangeCodeEntity exchangeCodeEntity = new ExchangeCodeEntity();
                        exchangeCodeEntity.setExchangeCodeId(codeId);
                        exchangeCodeEntity.setExchangeId(exchangeId);
                        exchangeCodeEntity
                                .setExchangeBusinessId(couponEntity.getCouponId().toString());
                        exchangeCodeEntity.setExchangeCode(uniqueCode);
                        exchangeCodeEntity.setExchangeCodeType(ExchangeCodeTypeEnum.COMMON.getCode());
                        exchangeCodeEntity.setUsedStatus(UsedStatusEnum.NOT_USED.getCode());
                        exchangeCodeEntity.setStatus(StatusEnum.ENABLE.getValue());
                        exchangeCodeEntity.setUpdateId(command.getUserId());
                        exchangeCodeEntity.setCreateId(command.getUserId());

                        ExchangeRoot exchangeRoot = ExchangeRoot.builder()
                                .identifier(exchangeIdentifier).exchangeEntity(exchangeEntity)
                                .exchangeCodeEntity(exchangeCodeEntity).build();

                        // 先保存兑换实体，再保存兑换码实体
                        return exchangeRootRepository.save(exchangeRoot);
                    });

                });
            });

            // 4、如果是指定商家或者商品，则需要插入响应记录
            Mono<Void> createScopeGoodsMono = Mono.defer(() -> {
                // 全平台通过
                if (command.getScopeType().equals(ScopeTypeEnum.ALL_PLATFORM.getCode())) {
                    return Mono.empty();
                }

                // 指定商家
                if (command.getScopeType().equals(ScopeTypeEnum.SPECIAL_CORP.getCode())
                        && command.getCorpIdList() != null && !command.getCorpIdList().isEmpty()) {
                    List<CouponRelateCorpEntity> couponRelateCorpEntityList = new ArrayList<>();
                    for (SaveCouponCommand.IdAndName idAndName : command.getCorpIdList()) {
                        CouponRelateCorpEntity couponRelateCorpEntity =
                                new CouponRelateCorpEntity();
                        couponRelateCorpEntity.setCouponId(couponEntity.getCouponId());
                        couponRelateCorpEntity.setCorpId(idAndName.getId());
                        couponRelateCorpEntity.setCorpName(idAndName.getName());
                        couponRelateCorpEntity.setStatus(StatusEnum.ENABLE.getValue());
                        couponRelateCorpEntity.setCreateId(command.getUserId());
                        couponRelateCorpEntity.setCreateTime(new Date());
                        couponRelateCorpEntityList.add(couponRelateCorpEntity);
                    }

                    return couponRootRepository.saveCouponCorpList(CouponRoot.builder()
                            .identifier(couponIdentifier)
                            .couponRelateCorpEntityList(couponRelateCorpEntityList).build());
                }

                // 指定商品
                if ((command.getScopeType().equals(ScopeTypeEnum.SPECIAL_GOODS.getCode())
                        || command.getScopeType().equals(ScopeTypeEnum.CORP_SPECIAL.getCode()))// 商家指定券
                        && command.getGoodsIdList() != null
                        && !command.getGoodsIdList().isEmpty()) {
                    List<CouponRelateGoodsEntity> couponRelateGoodsEntityList = new ArrayList<>();
                    for (SaveCouponCommand.IdAndName idAndName : command.getGoodsIdList()) {
                        CouponRelateGoodsEntity couponRelateGoodsEntity =
                                new CouponRelateGoodsEntity();
                        couponRelateGoodsEntity.setCouponId(couponEntity.getCouponId());
                        couponRelateGoodsEntity.setGoodsId(idAndName.getId());
                        couponRelateGoodsEntity.setGoodsName(idAndName.getName());
                        couponRelateGoodsEntity.setStatus(StatusEnum.ENABLE.getValue());
                        couponRelateGoodsEntity.setCreateId(command.getUserId());
                        couponRelateGoodsEntity.setCreateTime(new Date());
                        couponRelateGoodsEntityList.add(couponRelateGoodsEntity);
                    }

                    return couponRootRepository.saveCouponGoodsList(CouponRoot.builder()
                            .identifier(couponIdentifier)
                            .couponRelateGoodsEntityList(couponRelateGoodsEntityList).build());
                }

                return Mono.empty();
            });

            // 5、创建总库存和剩余库存
            StockReqDto stockReqDto = new StockReqDto();
            stockReqDto.setStockBusinessType(StockBusinessTypeEnum.COUPON.getCode());
            stockReqDto.setBusinessIdentifier(couponEntity.getCouponId().toString());
            stockReqDto.setTotalRealStock(couponEntity.getTotalNum().longValue());
            stockReqDto.setTotalVirtualStock(couponEntity.getTotalNum().longValue());
            stockReqDto.setCreateId(command.getUserId());
            Mono<Void> createStockMono = stockService.create(Mono.just(stockReqDto));

            // 6、优惠券添加缓存
            Mono<Void> addCouponExpireCacheMono = Mono.defer(() -> {
                if (PeriodTypeEnum.TIME_SCOPE.getCode().equals(couponEntity.getPeriodType())) {
                    return couponRootRepository.addCouponExpireCache(CouponRoot.builder()
                            .identifier(couponIdentifier).couponEntity(couponEntity).build());
                } else {
                    return Mono.empty();
                }
            });

            // 串联所有操作：先创建优惠券，再创建兑换相关数据
            return checkMono.then(createCoupnMono).then(createExchangeMono)
                    .then(createScopeGoodsMono).then(createStockMono)
                    .then(addCouponExpireCacheMono);
        });
    }

    /**
     * 递归生成唯一的兑换码
     *
     * @return 唯一的兑换码
     */
    private Mono<String> generateUniqueExchangeCode() {
        return exchangeRootService.generateUniqueCode()
                .flatMap(code -> exchangeRootQueryRepository
                        .findByCode(ExchangeCodeEntity.builder().exchangeCode(code).build())
                        .flatMap(existingEntity -> {
                            // 如果存在，递归调用生成新的兑换码
                            log.info("兑换码 {} 已存在，重新生成", code);
                            return generateUniqueExchangeCode();
                        }).switchIfEmpty(Mono.just(code)) // 如果不存在，返回当前兑换码
                );
    }

    /**
     * 检查优惠券创建规则
     * 
     * @param command 保存优惠券命令
     * @return Mono<Void>
     */
    private Mono<Void> checkCouponCreationRules(SaveCouponCommand command) {
        return businessConfigRootQueryRepository
                .findList(BusinessConfigEntity.builder()
                        .businessType(BusinessTypeEnum.XING_KA.getValue())
                        .groupType(BusinessConfigGroupTypeEnum.COUPON.getCode())
                        .groupId("0").build())
                .collectList().flatMap(businessConfigList -> {
                    // 遍历所有配置项，检查对应的规则
                    for (BusinessConfigEntity businessConfigEntity : businessConfigList) {
                        String key = businessConfigEntity.getKey();
                        String value = businessConfigEntity.getVal();

                        // 无门槛优惠券单条金额上限
                        if (CouponConfigKeyEnum.COMMON_PER_COUPON_MAX_AMOUNT.getCode().equals(key)
                                && command.getCouponType()
                                        .equals(CouponTypeEnum.NO_THRESHOLD.getCode())) {
                            if (command.getDiscountAmount() > Long.parseLong(value)) {
                                double amount = new BigDecimal(value).divide(new BigDecimal("1000000")).doubleValue();
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.COMMON_PER_COUPON_MAX_AMOUNT,String.format("无门槛单张金额最大%.2f元",amount)));
                            }
                        }
                        // 有门槛优惠券单条金额上限
                        else if (CouponConfigKeyEnum.THRESHOLD_PER_COUPON_MAX_AMOUNT.getCode()
                                .equals(key)
                                && command.getCouponType()
                                        .equals(CouponTypeEnum.THRESHOLD.getCode())) {
                            if (command.getDiscountAmount() > Long.parseLong(value)) {
                                double amount = new BigDecimal(value).divide(new BigDecimal("1000000")).doubleValue();
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.THRESHOLD_PER_COUPON_MAX_AMOUNT,String.format("有门槛单张金额最大%.2f元",amount)));
                            }
                        }
                        // 有门槛满减优惠券占比
                        else if (CouponConfigKeyEnum.THRESHOLD_RATIO.getCode().equals(key)
                                && command.getCouponType()
                                        .equals(CouponTypeEnum.THRESHOLD.getCode())) {
                            long ratio = (command.getDiscountAmount() * 100)
                                    / command.getThresholdAmount();
                            if (ratio > Long.parseLong(value)) {
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.THRESHOLD_RATIO,String.format("优惠券金额不可超过门槛金额的%s%%",value)));
                            }
                        }
                        // 单条优惠券金额总和
                        else if ( !ScopeTypeEnum.RANKING_LIST.getCode().equals(command.getScopeType()) //榜单券不判断
                                && CouponConfigKeyEnum.PER_COUPON_MAX_AMOUNT.getCode().equals(key)) {
                            long perCouponMaxAmount =
                                    command.getDiscountAmount() * command.getTotalNum();
                            if (perCouponMaxAmount > Long.parseLong(value)) {
                                long amount = new BigDecimal(value).divide(new BigDecimal("1000000")).longValue();
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.PER_COUPON_MAX_AMOUNT,String.format("优惠券总金额上限%s元",amount)));
                            }
                        }
                    }
                    if (ScopeTypeEnum.RANKING_LIST.getCode().equals(command.getScopeType())){
                        return Mono.empty();
                    }else {
                        // 检查单日优惠券限制
                        return checkDailyCouponLimits(command, businessConfigList);
                    }
                });
    }

    /**
     * 检查单日优惠券限制
     * 
     * @param command 保存优惠券命令
     * @param businessConfigList 业务配置列表
     * @return Mono<Void>
     */
    private Mono<Void> checkDailyCouponLimits(SaveCouponCommand command,
            List<BusinessConfigEntity> businessConfigList) {
        CouponEntity couponEntity = new CouponEntity();
        couponEntity.setCorpId(command.getCorpId());
        couponEntity.setCreateStartTime(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH));
        couponEntity.setCreateEndTime(new Date());
        couponEntity.setRankingFiltration(true);

        return couponRootQueryRepository.searchCoupon(couponEntity).collectList()
                .flatMap(couponEntityList -> {
                    for (BusinessConfigEntity businessConfigEntity : businessConfigList) {
                        String key = businessConfigEntity.getKey();
                        String value = businessConfigEntity.getVal();

                        // 单日优惠券总金额上限
                        if (CouponConfigKeyEnum.DAILY_COUPON_MAX_AMOUNT.getCode().equals(key)) {
                            long dayTotalAmount = couponEntityList.stream().mapToLong(
                                    coupon -> coupon.getDiscountAmount() * coupon.getTotalNum())
                                    .sum();
                            long currentAmount = command.getDiscountAmount() * command.getTotalNum();
                            long amount = currentAmount + dayTotalAmount;
                            log.info("SaveCouponHandler amount {} value {}", amount, value);
                            if (amount > Long.parseLong(value)) {
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.DAILY_COUPON_MAX_AMOUNT));
                            }
                        }
                    }

                    // 检查单日优惠券总条数上限
                    for (BusinessConfigEntity businessConfigEntity : businessConfigList) {
                        String key = businessConfigEntity.getKey();
                        String value = businessConfigEntity.getVal();

                        if (CouponConfigKeyEnum.DAILY_COUPON_MAX_NUM.getCode().equals(key)
                                && couponEntityList.size() >= Integer.parseInt(value)) {
                            return Mono.error(new PromotionApplicationException(
                                    PromotionApplicationErrorEnum.DAILY_COUPON_MAX_NUM));
                        }

                    }
                    return Mono.empty();
                });
    }
}

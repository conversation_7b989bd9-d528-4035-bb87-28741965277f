package com.xk.promotion.application.action.query.coupon;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.promotion.interfaces.dto.req.CouponAllReceivedReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CouponAllReceivedReqDto.class, convertGenerate = false),})
public class SearchAllReceivedPagerQuery extends PagerQuery implements IActionQuery {
    /**
     * 使用状态：（1未使用; 2已使用；3已过期）
     */
    private Integer usedStatus;

    /**
     * 领取开始时间
     */
    private Date startTime;

    /**
     * 领取结束时间
     */
    private Date endTime;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 用户ID
     */
    private Long userId;

    private List<Long> userIdList;

    /**
     * 领取类型（1手动领取；2兑换码兑换；3平台下发；4商户下发；）
     */
    private Integer receivedType;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    private Integer scopeType;

    /**
     * 优惠券ID
     */
    private Long couponId;

    private List<Long> couponIdList;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 商户id
     */
    private Long corpId;
}


package com.xk.promotion.application.handler.query.coupon;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.promotion.application.action.query.coupon.CommonCodeQuery;
import com.xk.promotion.application.commons.PromotionApplicationErrorEnum;
import com.xk.promotion.application.support.PromotionApplicationException;
import com.xk.promotion.domain.enums.exchange.ExchangeCodeTypeEnum;
import com.xk.promotion.domain.model.exchange.entity.ExchangeCodeEntity;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponCommonCodeRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CommonCodeHandler
        implements IActionQueryHandler<CommonCodeQuery, CouponCommonCodeRspDto> {

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;

    @Override
    public Mono<CouponCommonCodeRspDto> execute(Mono<CommonCodeQuery> mono) {
        return mono.flatMap(query -> exchangeRootQueryRepository
                .findByBusinessIdAndType(ExchangeCodeEntity.builder()
                        .exchangeBusinessId(query.getCouponId().toString())
                        .exchangeCodeType(ExchangeCodeTypeEnum.COMMON.getCode()).build())
                .collectList()
                .flatMap(exchangeCodeEntityList -> {
                    if(exchangeCodeEntityList.isEmpty()) {
                        return Mono.error(new PromotionApplicationException(
                                PromotionApplicationErrorEnum.INNER_COUPON_CODE_NOT_FOUND));
                    }

                    CouponCommonCodeRspDto rspDto = new CouponCommonCodeRspDto();
                    rspDto.setCommonCode(exchangeCodeEntityList.getFirst().getExchangeCode());
                    rspDto.setStatus(exchangeCodeEntityList.getFirst().getStatus());
                    rspDto.setExchangeCodeId(exchangeCodeEntityList.getFirst().getExchangeCodeId());
                    return Mono.just(rspDto);
                }));
    }
}


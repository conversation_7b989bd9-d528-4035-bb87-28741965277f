package com.xk.promotion.application.query.coupon;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.promotion.application.action.query.coupon.*;
import com.xk.promotion.domain.enums.coupon.ScopeTypeEnum;
import com.xk.promotion.interfaces.dto.req.*;
import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.promotion.domain.enums.coupon.YesOrNotEnum;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;
import com.xk.promotion.interfaces.query.coupon.CouponQueryUserService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponQueryUserServiceImpl implements CouponQueryUserService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final SelectorRootService selectorRootService;

    @BusiCode
    @Override
    public Mono<Pagination> searchCouponCenter(Mono<CouponPlatformReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> reqMono.flatMap(dto -> actionQueryDispatcher
                        .executeQuery(reqMono, SearchCouponPagerQuery.class, query -> {
                            query.setIsShowCenter(YesOrNotEnum.YES.getCode());// 去掉就是商家的
                            query.setStatus(StatusEnum.ENABLE.getValue());
                            query.setUserId(userId);
                            query.setStockFlag(1);
                            query.setSortFlag(2);
                            return query;
                        }, Pagination.class)));
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchCorpCoupon(Mono<CouponCorpIdReqDto> reqMono) {
        // scope_type是通用的也要查询
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> reqMono.flatMap(dto -> {
            return actionQueryDispatcher
                    .executeQuery(reqMono, SearchCouponCorpIdsQuery.class, List.class)
                    .flatMap(ids -> {
                        return actionQueryDispatcher.executeQuery(reqMono,
                                SearchCouponPagerQuery.class, query -> {
                                    if (CollectionUtils.isEmpty(ids)) {
                                        query.setCorpCouponIdList(List.of(-1L));
                                    } else {
                                        query.setCorpCouponIdList(ids);
                                    }
                                    query.setIsShowHomepage(YesOrNotEnum.YES.getCode());
                                    query.setAppCorpId(query.getCorpId());
                                    query.setCorpId(null);
                                    query.setUserId(userId);
                                    query.setStockFlag(1);
                                    query.setSortFlag(1);
                                    query.setStatus(StatusEnum.ENABLE.getValue());
                                    return query;
                                }, Pagination.class);
                    });

        }));
    }

    // @BusiCode
    // @Override
    // public Mono<Pagination> searchGoodsCoupon(Mono<CouponGoodsReqDto> reqMono) {
    // return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> reqMono.flatMap(dto ->
    // actionQueryDispatcher
    // .executeQuery(reqMono, SearchCouponGoodsIdsQuery.class, List.class)
    // .flatMap(ids -> {
    // return selectorRootService.getGoodsObject(dto.getGoodsId())
    // .flatMap(goodsObjectRoot -> {
    // return actionQueryDispatcher.executeQuery(reqMono,
    // SearchCouponPagerQuery.class, query -> {
    // if (CollectionUtils.isEmpty(ids)) {
    // query.setGoodsCouponIdList(List.of(-1L));
    // } else {
    // query.setGoodsCouponIdList(ids);
    // }
    // query.setAppCorpId(goodsObjectRoot
    // .getCorpObjectIdentifier().corpId());
    // query.setUserId(userId);
    // query.setStockFlag(1);
    // query.setSortFlag(1);
    // query.setStatus(StatusEnum.ENABLE.getValue());
    // query.setIsShowHomepage(YesOrNotEnum.YES.getCode());
    // return query;
    // }, Pagination.class);
    // });
    // })));
    // }

    @BusiCode
    @Override
    public Mono<Pagination> searchGoodsCoupon(Mono<CouponGoodsReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> reqMono.flatMap(dto -> {
            return selectorRootService.getGoodsObject(dto.getGoodsId()).flatMap(goodsObjectRoot -> {
                return actionQueryDispatcher
                        .executeQuery(reqMono, SearchCouponGoodsIdsQuery.class, List.class)
                        .flatMap(goodsCouponIds -> {
                            Long corpId = goodsObjectRoot.getCorpObjectIdentifier().corpId();
                            return actionQueryDispatcher
                                    .executeQuery(
                                            Mono.just(SearchCouponCorpIdsQuery.builder()
                                                    .corpId(corpId).build()),
                                            SearchCouponCorpIdsQuery.class, List.class)
                                    .flatMap(corpCouponIds -> {
                                        return actionQueryDispatcher.executeQuery(reqMono,
                                                SearchCouponPagerQuery.class, query -> {
                                                    if (CollectionUtils.isEmpty(goodsCouponIds)) {
                                                        query.setGoodsCouponIdList(List.of(-1L));
                                                    } else {
                                                        query.setGoodsCouponIdList(goodsCouponIds);
                                                    }
                                                    if (CollectionUtils.isEmpty(corpCouponIds)) {
                                                        query.setCorpCouponIdList(List.of(-1L));
                                                    } else {
                                                        query.setCorpCouponIdList(corpCouponIds);
                                                    }
                                                    query.setAppCorpId(corpId);
                                                    query.setUserId(userId);
                                                    query.setStockFlag(1);
                                                    query.setSortFlag(1);
                                                    query.setStatus(StatusEnum.ENABLE.getValue());
                                                    query.setIsShowHomepage(
                                                            YesOrNotEnum.YES.getCode());
                                                    return query;
                                                }, Pagination.class);
                                    });
                        });
            });
        }));
    }

    @BusiCode
    @Override
    public Mono<Pagination> corpSearchCorpCoupon(Mono<CouponCorpIdReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(
                userId -> selectorRootService.getUserObject(userId).flatMap(userObjectRoot -> {
                    return reqMono.flatMap(dto -> {
                        return actionQueryDispatcher
                                .executeQuery(reqMono, SearchCouponCorpIdsQuery.class, List.class)
                                .flatMap(ids -> {
                                    return actionQueryDispatcher.executeQuery(reqMono,
                                            SearchCouponPagerQuery.class, query -> {
                                                if (CollectionUtils.isEmpty(ids)) {
                                                    query.setCorpCouponIdList(List.of(-1L));
                                                } else {
                                                    query.setCorpCouponIdList(ids);
                                                }
                                                query.setAppCorpId(userObjectRoot
                                                        .getUserDataObjectEntity().getCorpId());
                                                query.setIsShowHomepage(YesOrNotEnum.YES.getCode());
                                                query.setUserId(userId);
                                                query.setSort("create_time");
                                                query.setOrder(
                                                        SystemConstant.BusinessOrderType.DESC);
                                                return query;
                                            }, Pagination.class);
                                });

                    });
                }));
    }

    @BusiCode
    @Override
    public Mono<Pagination> corpSearchRankingCoupon(Mono<RankingCouponReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(
                userId -> selectorRootService.getUserObject(userId).flatMap(userObjectRoot -> {
                    return reqMono.flatMap(dto -> {
                        return actionQueryDispatcher.executeQuery(reqMono,
                                SearchCouponPagerQuery.class, query -> {
                                    query.setSort("create_time");
                                    query.setOrder(SystemConstant.BusinessOrderType.DESC);
                                    query.setScopeType(ScopeTypeEnum.RANKING_LIST.getCode());
                                    query.setCorpId(
                                            userObjectRoot.getUserDataObjectEntity().getCorpId());
                                    return query;
                                }, Pagination.class);
                    });
                }));
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchReceived(Mono<CouponReceivedReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> actionQueryDispatcher
                .executeQuery(reqMono, CouponReceivedPagerQuery.class, query -> {
                    query.setUserId(userId);
                    query.setSort("create_time");
                    query.setOrder(SystemConstant.BusinessOrderType.DESC);
                    return query;
                }, Pagination.class));
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchUsable(Mono<CouponUsableReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> actionQueryDispatcher
                .executeQuery(reqMono, CouponUsablePagerQuery.class, query -> {
                    query.setUserId(userId);
                    return query;
                }, Pagination.class));
    }

    @BusiCode
    @Override
    public Mono<CouponDetailRspDto> searchCouponByCode(Mono<CouponExchangeReqDto> reqMono) {
        return reqMono.flatMap(dto -> actionQueryDispatcher.executeQuery(reqMono,
                CouponByCodeQuery.class, CouponDetailRspDto.class));
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchCorps(Mono<CouponReceivedReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> reqMono.flatMap(dto -> actionQueryDispatcher
                        .executeQuery(reqMono, CouponCorpsQuery.class, query -> {
                            query.setUserId(userId);
                            return query;
                        }, Pagination.class)));
    }
}

package com.xk.promotion.application.handler.event.coupon;

import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.promotion.domain.enums.coupon.PeriodTypeEnum;
import com.xk.promotion.domain.event.coupon.CouponUserUsedEvent;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponGoodsEntity;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.domain.service.coupon.CouponUserRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class CouponUserUsedHandler extends AbstractEventVerticle<CouponUserUsedEvent> {

    private final CouponUserRootService couponUserRootService;
    private final CouponUserRootQueryRepository couponUserRootQueryRepository;
    private final CouponRootQueryRepository couponRootQueryRepository;

    @Override
    public Mono<Void> handle(Mono<CouponUserUsedEvent> event) {
        return event.flatMap(couponUserUsedEvent -> {
            log.info("CouponUserUsedHandler 开始执行：{}，{}", couponUserUsedEvent.getCouponUserId(),
                    couponUserUsedEvent.getGoodsId());
            return couponUserRootQueryRepository
                    .searchByCouponUserId(LongIdentifier.builder()
                            .id(couponUserUsedEvent.getCouponUserId()).build())
                    .flatMap(couponUserEntity -> {
                        // 添加商品优惠券记录
                        Mono<Void> saveCouponGoodsMono = couponUserRootService
                                .generateCouponGoodsId().flatMap(couponGoodsId -> {
                                    return couponUserRootService.saveCouponGoods(CouponUserRoot
                                            .builder()
                                            .identifier(CouponUserIdentifier.builder()
                                                    .couponUserId(couponUserEntity
                                                            .getCouponUserId())
                                                    .build())
                                            .couponGoodsEntity(CouponGoodsEntity.builder()
                                                    .couponGoodsId(couponGoodsId)
                                                    .goodsId(couponUserUsedEvent.getGoodsId())
                                                    .couponUserId(
                                                            couponUserUsedEvent.getCouponUserId())
                                                    .couponId(couponUserEntity.getCouponId())
                                                    .status(0)
                                                    .createId(couponUserEntity.getCreateId())
                                                    .createTime(new Date()).build())
                                            .build());
                                });
                        // 查询优惠券
                        return couponRootQueryRepository
                                .findById(LongIdentifier.builder()
                                        .id(couponUserEntity.getCouponId()).build())
                                .flatMap(couponEntity -> {
//                                    if (Objects.equals(PeriodTypeEnum.AFTER_RECEIVE.getCode(),
//                                            couponEntity.getPeriodType())) {
//
//                                        CouponUserRoot couponUserRoot = CouponUserRoot.builder()
//                                                .identifier(CouponUserIdentifier.builder()
//                                                        .couponUserId(couponUserEntity
//                                                                .getCouponUserId())
//                                                        .build())
//                                                .couponUserEntity(CouponUserEntity.builder()
//                                                        .couponUserId(couponUserUsedEvent
//                                                                .getCouponUserId())
//                                                        .endTime(couponUserEntity
//                                                                .getEndTime())
//                                                        .build())
//                                                .build();
//                                        // 从缓存中去除
//                                        return saveCouponGoodsMono.then(couponUserRootService
//                                                .deleteUserCancelData(couponUserRoot));
//                                    }
                                    return saveCouponGoodsMono;
                                });
                    });
        });
    }
}

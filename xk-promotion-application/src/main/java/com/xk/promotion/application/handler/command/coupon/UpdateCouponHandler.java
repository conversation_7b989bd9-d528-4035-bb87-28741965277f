package com.xk.promotion.application.handler.command.coupon;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.promotion.application.action.command.coupon.UpdateCouponCommand;
import com.xk.promotion.domain.enums.coupon.AuditStatusEnum;
import com.xk.promotion.domain.enums.coupon.PlatformTypeEnum;
import com.xk.promotion.domain.enums.coupon.YesOrNotEnum;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.entity.CouponEntity;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateCouponHandler implements IActionCommandHandler<UpdateCouponCommand, Void> {

    private final CouponRootRepository couponRootRepository;
    private final CouponRootQueryRepository couponRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateCouponCommand> mono) {
        return mono.flatMap(command -> {
            CouponEntity entity = converter.convert(command, CouponEntity.class);
            entity.setUpdateId(command.getUserId());
            Date date = new Date();
            entity.setUpdateTime(date);
            if (YesOrNotEnum.YES.getCode().equals(command.getIsShowCenter())) {
                entity.setShowCenterDate(date);
            }else {
                //重置申请状态
                entity.setAuditStatus(0);
            }
            CouponIdentifier identifier = CouponIdentifier.builder().couponId(entity.getCouponId()).build();
            return couponRootRepository.update(CouponRoot.builder().identifier(identifier).couponEntity(entity).build());
        });
    }
}

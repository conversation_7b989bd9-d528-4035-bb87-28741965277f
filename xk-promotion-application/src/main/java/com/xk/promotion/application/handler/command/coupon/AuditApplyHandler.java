package com.xk.promotion.application.handler.command.coupon;

import java.util.Date;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.promotion.application.action.command.coupon.AuditApplyCommand;
import com.xk.promotion.domain.enums.coupon.AuditStatusEnum;
import com.xk.promotion.domain.enums.coupon.YesOrNotEnum;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.entity.CouponApplyEntity;
import com.xk.promotion.domain.model.coupon.entity.CouponEntity;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuditApplyHandler implements IActionCommandHandler<AuditApplyCommand, Void> {

    private final CouponRootRepository couponRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<AuditApplyCommand> mono) {
        return mono.flatMap(command -> {
            CouponApplyEntity applyEntity = converter.convert(command, CouponApplyEntity.class);
            applyEntity.setUpdateId(command.getUserId());
            applyEntity.setUpdateTime(new Date());
            CouponIdentifier identifier =
                    CouponIdentifier.builder().couponId(command.getCouponId()).build();

            // 如果审核通过
            if (Objects.equals(command.getAuditStatus(), AuditStatusEnum.PASS.getCode())) {
                // 先更新优惠券状态，再更新申请状态
                CouponEntity couponEntity = new CouponEntity();
                couponEntity.setCouponId(applyEntity.getCouponId());
                couponEntity.setIsShowCenter(YesOrNotEnum.YES.getCode());
                couponEntity.setAuditStatus(AuditStatusEnum.PASS.getCode());
                couponEntity.setShowCenterDate(new Date());

                CouponRoot couponRoot = CouponRoot.builder().identifier(identifier)
                        .couponEntity(couponEntity).couponApplyEntity(applyEntity).build();
                return couponRootRepository.update(couponRoot);
            } else {
                CouponEntity couponEntity = CouponEntity.builder().couponId(command.getCouponId())
                        .auditStatus(0).updateId(command.getUserId())
                        .updateTime(new Date()).build();

                // 审核不通过，只更新申请状态
                CouponRoot couponRoot = CouponRoot.builder().identifier(identifier)
                        .couponEntity(couponEntity).couponApplyEntity(applyEntity).build();
                return couponRootRepository.update(couponRoot);
            }
        });
    }
}

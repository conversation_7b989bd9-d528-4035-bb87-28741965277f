package com.xk.promotion.application.commons;

import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * application错误码
 * 13000-13999
 */
@Getter
@AllArgsConstructor
public enum PromotionApplicationErrorEnum implements ExceptionIdentifier {

    APPLICATION_ERROR(13000, "application错误"),
    COUPON_NOT_FOUND(13001, "优惠券不存在"),
    COUPON_CODE_NOT_FOUND(13002, "输入的兑换码无法使用"),
    COUPON_CODE_USED(13003, "优惠券已失效"),
    COUPON_RECEIVED_MAX(13004, "已达领取上限"),
    COUPON_NUM_MAX(13005, "慢了一步，优惠券已被抢完"),
    COUPON_RECEIVED(13006, "优惠券已有人领取，不能删除"),
    INNER_COUPON_CODE_NOT_FOUND(13007, "兑换码不存在"),
    STOCK_DEDUCT_ERROR(13008, "库存扣减失败"),
    STOCK_NOT_ENOUGH(13009, "库存不足"),
    COMMON_PER_COUPON_MAX_AMOUNT(13010, "超出无门槛优惠券单条金额上限"),
    THRESHOLD_PER_COUPON_MAX_AMOUNT(13011, "超出有门槛优惠券单条金额上限"),
    THRESHOLD_RATIO(13012, "超出有门槛满减优惠券占比"),
    DAILY_COUPON_MAX_NUM(13013, "今日创建的优惠券条数已达上限"),
    DAILY_COUPON_MAX_AMOUNT(13014, "今日创建的优惠券金额已达上限"),
    PER_COUPON_MAX_AMOUNT(13015, "超出单条优惠券金额总和"),
    PER_GOODS_COUPON_MAX_NUM(13016, "当前商品已达使用商家优惠券上限，无法使用优惠券抵扣"),
    COUPON_NOT_USER(13017, "该优惠券不属于此用户"),
    COUPON_RECEIVE_TOAST(13018,"慢了一步，优惠券已被抢完了"),
    COUPON_EXCHANGE_CODE_USED(13019, "兑换码已失效"),
    COUPON_EXCHANGE_NUM_MAX(13005, "兑换码已被兑换了"),
    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}

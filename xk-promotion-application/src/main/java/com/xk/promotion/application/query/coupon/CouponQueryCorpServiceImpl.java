package com.xk.promotion.application.query.coupon;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.promotion.interfaces.dto.req.CouponAllReceivedReqDto;
import com.xk.promotion.interfaces.dto.rsp.CouponRuleRspDto;
import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.promotion.application.action.query.coupon.*;
import com.xk.promotion.domain.enums.coupon.PlatformTypeEnum;
import com.xk.promotion.interfaces.dto.req.CouponIdReqDto;
import com.xk.promotion.interfaces.dto.req.CouponPagerReqDto;
import com.xk.promotion.interfaces.dto.req.CouponReceivedReqDto;
import com.xk.promotion.interfaces.dto.rsp.CouponCommonCodeRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponUniqueCodeRspDto;
import com.xk.promotion.interfaces.query.coupon.CouponQueryCorpService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponQueryCorpServiceImpl implements CouponQueryCorpService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final SelectorRootService selectorRootService;

    @BusiCode
    @Override
    public Mono<Pagination> search(Mono<CouponPagerReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> selectorRootService.getUserObject(userId)
                        .flatMap(userObjectRoot -> actionQueryDispatcher.executeQuery(reqMono,
                                SearchCouponPagerQuery.class, query -> {
                                    query.setPlatformType(PlatformTypeEnum.CORP.getCode());
                                    query.setCorpId(
                                            userObjectRoot.getUserDataObjectEntity().getCorpId());
                                    return query;
                                }, Pagination.class)));
    }

    @BusiCode
    @Override
    public Mono<CouponDetailRspDto> detail(Mono<CouponIdReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> selectorRootService.getUserObject(userId)
                        .flatMap(userObjectRoot -> actionQueryDispatcher.executeQuery(reqMono,
                                CouponDetailByIdQuery.class, query -> {
                                    query.setPlatformType(PlatformTypeEnum.CORP.getCode());
                                    query.setCorpId(
                                            userObjectRoot.getUserDataObjectEntity().getCorpId());
                                    return query;
                                }, CouponDetailRspDto.class)));
    }


    @BusiCode
    @Override
    public Mono<Pagination> searchReceived(Mono<CouponReceivedReqDto> reqMono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> selectorRootService.getUserObject(userId)
                        .flatMap(userObjectRoot -> actionQueryDispatcher.executeQuery(reqMono,
                                CouponReceivedPagerQuery.class, query -> {
                                    query.setPlatformType(PlatformTypeEnum.CORP.getCode());
                                    query.setCorpId(
                                            userObjectRoot.getUserDataObjectEntity().getCorpId());
                                    return query;
                                }, Pagination.class)));
    }

    @BusiCode
    @Override
    public Mono<CouponCommonCodeRspDto> searchCommonCode(Mono<CouponIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, CommonCodeQuery.class,
                CouponCommonCodeRspDto.class);
    }

    @BusiCode
    @Override
    public Mono<CouponUniqueCodeRspDto> searchUniqueCode(Mono<CouponIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, UniqueCodeQuery.class,
                CouponUniqueCodeRspDto.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchUniqueCodePager(Mono<CouponIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, UniqueCodePagerQuery.class,
                Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Long> searchRemainNum(Mono<CouponIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, RemainNumQuery.class, Long.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchCouponCorps(Mono<CouponIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, CouponRelateCorpQuery.class,
                Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchCouponGoods(Mono<CouponIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, CouponRelateGoodsQuery.class,
                Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchCouponReceived(Mono<CouponAllReceivedReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(
                userId -> selectorRootService.getUserObject(userId).flatMap(userObjectRoot -> {
                    return actionQueryDispatcher.executeQuery(mono,
                            SearchAllReceivedPagerQuery.class, query -> {
                                query.setCorpId(
                                        userObjectRoot.getUserDataObjectEntity().getCorpId());
                                return query;
                            }, Pagination.class);
                }));
    }

    @BusiCode
    @Override
    public Mono<CouponRuleRspDto> searchRule(Mono<RequireSessionDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(
                userId -> selectorRootService.getUserObject(userId).flatMap(userObjectRoot -> {
                    return actionQueryDispatcher.executeQuery(mono, CouponRuleDetailQuery.class,
                            q -> {
                                q.setCorpId(userObjectRoot.getUserDataObjectEntity().getCorpId());
                                return q;
                            }, CouponRuleRspDto.class);
                }));
    }

    @BusiCode
    @Override
    public Mono<CouponRuleRspDto> searchInstruction(Mono<RequireSessionDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, CouponInstructionQuery.class,
                CouponRuleRspDto.class);
    }
}

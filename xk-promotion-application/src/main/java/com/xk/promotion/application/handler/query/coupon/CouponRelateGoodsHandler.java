package com.xk.promotion.application.handler.query.coupon;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.goods.ListingStatusEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.promotion.application.action.query.coupon.CouponRelateGoodsQuery;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponRelateGoodsRspDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class CouponRelateGoodsHandler
        implements IActionQueryHandler<CouponRelateGoodsQuery, Pagination> {

    private final CouponRootQueryRepository couponRootQueryRepository;
    private final SelectorRootService selectorRootService;

    @Override
    public Mono<Pagination> execute(Mono<CouponRelateGoodsQuery> query) {
        return query.flatMap(couponRelateGoodsQuery -> {
            // 创建分页
            Pagination pagination = new Pagination();
            pagination.setLimit(couponRelateGoodsQuery.getLimit());
            pagination.setOffset(couponRelateGoodsQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(couponRelateGoodsQuery));
            return couponRootQueryRepository.findCouponRelateGoodsPage(pagination).collectList()
                    .flatMap(list -> {
                        return Flux.fromIterable(list).flatMap(entity -> {
                            return selectorRootService.getGoodsObject(entity.getGoodsId())
                                    .flatMap(goodsObjectRoot -> {
                                        //商品类型
                                        ProductTypeEnum productTypeEnum = ProductTypeEnum.valueOf(goodsObjectRoot.getGoodsInfo().getCardGoods()
                                                .getCardGoodsType());
                                        //上下架状态
                                        Integer goodsStatus = switch (ListingStatusEnum.getByCode(goodsObjectRoot.getGoodsInfo().getListingStatus())){
                                            case ListingStatusEnum.UP -> 1;
                                            case ListingStatusEnum.DOWN -> 2;
                                            case ListingStatusEnum.TIME_OUT_DOWN -> 2;
                                            default -> 0;
                                        };
                                        //判断有无商户
                                        if (Objects.nonNull(goodsObjectRoot.getCorpObjectIdentifier().corpId())){
                                            return selectorRootService.getCorpObject(goodsObjectRoot.getCorpObjectIdentifier().corpId()).map(corpObjectRoot -> {

                                                return CouponRelateGoodsRspDto.builder()
                                                        .goodsId(entity.getGoodsId())
                                                        .goodsName(goodsObjectRoot.getGoodsInfo()
                                                                .getGoodsName())
                                                        .goodsType(productTypeEnum.getCode())
                                                        .goodsStatus(goodsStatus)
                                                        .corpId(goodsObjectRoot.getCorpObjectIdentifier().corpId())
                                                        .corpName(corpObjectRoot.getCorpInfoObjectEntity().getCorpName())
                                                        .corpStatus(corpObjectRoot.getCorpInfoObjectEntity().getCorpStatus())
                                                        .couponId(entity.getCouponId())
                                                        .status(entity.getStatus())
                                                        .createId(entity.getCreateId())
                                                        .createTime(entity.getCreateTime()).build();
                                            });
                                        }else {
                                            return Mono.just(CouponRelateGoodsRspDto.builder()
                                                    .goodsId(entity.getGoodsId())
                                                    .goodsName(goodsObjectRoot.getGoodsInfo()
                                                            .getGoodsName())
                                                    .goodsType(productTypeEnum.getCode())
                                                    .goodsStatus(goodsStatus)
                                                    .couponId(entity.getCouponId())
                                                    .status(entity.getStatus())
                                                    .createId(entity.getCreateId())
                                                    .createTime(entity.getCreateTime()).build());
                                        }

                                    });
                        }).collectList().map(rspList -> {
                            pagination.setRecords(rspList);
                            return pagination;
                        });
                    });
        });
    }
}

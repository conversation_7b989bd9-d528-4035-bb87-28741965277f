package com.xk.promotion.application.handler.command.coupon;

import java.util.Date;
import java.util.Objects;

import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.interfaces.dto.req.CouponSendDto;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.promotion.application.action.command.coupon.SendCouponCommand;
import com.xk.promotion.application.commons.PromotionApplicationErrorEnum;
import com.xk.promotion.application.support.PromotionApplicationException;
import com.xk.promotion.domain.enums.coupon.PeriodTypeEnum;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootRepository;
import com.xk.promotion.domain.service.coupon.CouponUserRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendCouponHandler implements IActionCommandHandler<SendCouponCommand, Void> {

    private final CouponRootRepository couponRootRepository;

    private final CouponRootQueryRepository couponRootQueryRepository;

    private final CouponUserRootRepository couponUserRootRepository;

    private final CouponUserRootService couponUserRootService;

    private final SelectorRootService selectorRootService;

    private final StockRootService stockRootService;

    @Override
    public Mono<Void> execute(Mono<SendCouponCommand> mono) {
        return mono.flatMap(command -> couponRootQueryRepository
                .findById(LongIdentifier.builder().id(command.getCouponId()).build())
                .switchIfEmpty(Mono.error(new PromotionApplicationException(
                        PromotionApplicationErrorEnum.COUPON_NOT_FOUND)))
                .flatMap(couponEntity -> Flux.fromIterable(command.getCouponSendDtoList())
                        .flatMap(couponSendDto ->

                        // 查询用户信息
                        selectorRootService.getUserObject(couponSendDto.getUserId())
                                .flatMap(userObjectRoot -> {
                                    // 为每个用户创建指定数量的优惠券
                                    return Flux.range(0, couponSendDto.getCouponNum())
                                            .flatMap(i -> couponUserRootService.generateId()
                                                    .flatMap(couponUserId -> {
                                                        CouponUserEntity couponUserEntity =
                                                                new CouponUserEntity();
                                                        BeanUtils.copyProperties(couponEntity,
                                                                couponUserEntity);
                                                        Date today = new Date();
                                                        if (Objects.equals(
                                                                PeriodTypeEnum.TIME_SCOPE.getCode(),
                                                                couponEntity.getPeriodType())) {
                                                            couponUserEntity.setStartTime(
                                                                    couponEntity.getStartTime());
                                                            couponUserEntity.setEndTime(
                                                                    couponEntity.getEndTime());
                                                        } else {
                                                            couponUserEntity.setStartTime(today);
                                                            couponUserEntity.setEndTime(DateUtils
                                                                    .addDays(today, couponEntity
                                                                            .getPeriodNum()));
                                                        }
                                                        couponUserEntity.setUserId(
                                                                couponSendDto.getUserId());
                                                        couponUserEntity.setUsername(userObjectRoot
                                                                .getUserDataObjectEntity()
                                                                .getNickname());
                                                        couponUserEntity
                                                                .setCouponUserId(couponUserId);
                                                        couponUserEntity
                                                                .setCorpId(command.getCorpId());
                                                        couponUserEntity.setReceivedType(
                                                                command.getReceivedType());
                                                        couponUserEntity
                                                                .setUpdateId(command.getUserId());
                                                        couponUserEntity
                                                                .setCreateId(command.getUserId());
                                                        couponUserEntity.setCreateTime(today);
                                                        couponUserEntity.setUpdateTime(today);
                                                        CouponUserIdentifier identifier =
                                                                CouponUserIdentifier.builder()
                                                                        .couponUserId(
                                                                                couponUserEntity
                                                                                        .getCouponUserId())
                                                                        .build();
                                                        CouponUserRoot couponUserRoot =
                                                                CouponUserRoot.builder()
                                                                        .identifier(identifier)
                                                                        .couponUserEntity(
                                                                                couponUserEntity)
                                                                        .build();
                                                        // 添加用户优惠券
                                                        Mono<Void> saveMono =
                                                                couponUserRootRepository
                                                                        .save(couponUserRoot);
                                                        // 更新优惠券库存
                                                        Mono<Void> updateCouponMono =
                                                                Mono.defer(() -> {
                                                                    couponEntity.setReceivedNum(
                                                                            couponEntity
                                                                                    .getReceivedNum()
                                                                                    + 1);
                                                                    couponEntity.setStockNum(
                                                                            couponEntity
                                                                                    .getStockNum()
                                                                                    - 1);
                                                                    return couponRootRepository
                                                                            .update(CouponRoot
                                                                                    .builder()
                                                                                    .identifier(
                                                                                            CouponIdentifier
                                                                                                    .builder()
                                                                                                    .couponId(
                                                                                                            couponEntity
                                                                                                                    .getCouponId())
                                                                                                    .build())
                                                                                    .couponEntity(
                                                                                            couponEntity)
                                                                                    .build());
                                                                });
                                                        // 将用户优惠券加入到缓存中，过期自动失效
                                                        if (PeriodTypeEnum.AFTER_RECEIVE.getCode()
                                                                .equals(couponEntity
                                                                        .getPeriodType())) {
                                                            return saveMono.then(updateCouponMono)
                                                                    .then(couponUserRootRepository
                                                                            .addCouponUserExpireCache(
                                                                                    couponUserRoot));
                                                        } else {
                                                            return saveMono.then(updateCouponMono);
                                                        }
                                                    }))
                                            .then();
                                })


                        ).then(deductionStock(command))));
    }

    /**
     * 扣减库存
     * 
     * @param command command
     * @return Mono<Void>
     */
    private Mono<Void> deductionStock(SendCouponCommand command) {
        // 计算数量
        int sum =
                command.getCouponSendDtoList().stream().mapToInt(CouponSendDto::getCouponNum).sum();
        return stockRootService
                .deductionStock(
                        StringIdentifier.builder().id(command.getCouponId().toString()).build(),
                        sum, StockBusinessTypeEnum.COUPON)
                .doOnSuccess(b -> log.info("SendCouponHandler 扣减库存 {}", b)).flatMap(b -> {
                    if (!b) {
                        return Mono.error(new PromotionApplicationException(
                                PromotionApplicationErrorEnum.STOCK_NOT_ENOUGH));
                    }
                    return Mono.just(b);
                }).then();
    }
}

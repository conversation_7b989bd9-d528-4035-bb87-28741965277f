package com.xk.promotion.application.handler.query.coupon;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.infrastructure.adapter.stock.CouponStockAdapterService;
import com.xk.promotion.application.action.query.coupon.UniqueCodeQuery;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.domain.enums.exchange.ExchangeCodeTypeEnum;
import com.xk.promotion.domain.model.exchange.entity.ExchangeCodeEntity;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponCodeDto;
import com.xk.promotion.interfaces.dto.rsp.CouponUniqueCodeRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class UniqueCodeHandler
        implements IActionQueryHandler<UniqueCodeQuery, CouponUniqueCodeRspDto> {

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;

    private final SelectorRootService selectorRootService;

    private final CouponRootQueryRepository couponRootQueryRepository;

    private final CouponStockAdapterService couponStockAdapterService;

    @Override
    public Mono<CouponUniqueCodeRspDto> execute(Mono<UniqueCodeQuery> mono) {
        return mono
                .flatMap(
                        query -> couponStockAdapterService
                                .getRemainStock(StringIdentifier.builder()
                                        .id(query.getCouponId().toString()).build())
                                .flatMap(stockValObj -> exchangeRootQueryRepository
                                        .findByBusinessIdAndType(ExchangeCodeEntity.builder()
                                                .exchangeBusinessId(query.getCouponId().toString())
                                                .exchangeCodeType(
                                                        ExchangeCodeTypeEnum.ONCE.getCode())
                                                .build())
                                        .collectList().flatMap(exchangeCodeEntityList -> {
                                            CouponUniqueCodeRspDto rspDto =
                                                    new CouponUniqueCodeRspDto();
                                            rspDto.setRemainNum(
                                                    stockValObj.getRemainRealStock().intValue());
                                            if (exchangeCodeEntityList.isEmpty()) {
                                                return Mono.just(rspDto);
                                            }

                                            // 使用 Flux 处理每个兑换码实体
                                            return Flux.fromIterable(exchangeCodeEntityList)
                                                    .flatMap(exchangeCodeEntity -> {
                                                        // 如果有接收用户ID，则查询用户名
                                                        if (Objects.equals(
                                                                exchangeCodeEntity.getUsedStatus(),
                                                                UsedStatusEnum.USED.getCode())) {
                                                            return selectorRootService
                                                                    .getUserObject(
                                                                            exchangeCodeEntity
                                                                                    .getReceivedUserId())
                                                                    .map(userObjectRoot -> {
                                                                        String userName =
                                                                                userObjectRoot
                                                                                        .getUserDataObjectEntity()
                                                                                        .getNickname();
                                                                        return CouponCodeDto
                                                                                .builder()
                                                                                .exchangeId(
                                                                                        exchangeCodeEntity
                                                                                                .getExchangeCodeId())
                                                                                .exchangeCode(
                                                                                        exchangeCodeEntity
                                                                                                .getExchangeCode())
                                                                                .receivedStatus(
                                                                                        exchangeCodeEntity
                                                                                                .getUsedStatus())
                                                                                .receivedUserId(
                                                                                        exchangeCodeEntity
                                                                                                .getReceivedUserId())
                                                                                .receivedUserName(
                                                                                        userName)
                                                                                .status(exchangeCodeEntity
                                                                                        .getStatus())
                                                                                .build();
                                                                    });
                                                        } else {
                                                            return Mono.just(CouponCodeDto.builder()
                                                                            .exchangeId(exchangeCodeEntity.getExchangeCodeId())
                                                                    .exchangeCode(exchangeCodeEntity
                                                                            .getExchangeCode())
                                                                    .receivedStatus(
                                                                            exchangeCodeEntity
                                                                                    .getUsedStatus())
                                                                    .status(exchangeCodeEntity
                                                                            .getStatus())
                                                                    .build());
                                                        }
                                                    }).collectList().map(couponCodeDtoList -> {
                                                        rspDto.setCouponCodeDtoList(
                                                                couponCodeDtoList);
                                                        return rspDto;
                                                    });
                                        })));
    }
}


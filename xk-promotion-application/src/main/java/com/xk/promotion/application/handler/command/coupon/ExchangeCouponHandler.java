package com.xk.promotion.application.handler.command.coupon;

import java.util.Date;
import java.util.Objects;
import java.util.function.Supplier;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.domain.enums.commons.DeleteFlagEnum;
import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.promotion.domain.service.exchange.ExchangeRootService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.promotion.application.action.command.coupon.ExchangeCouponCommand;
import com.xk.promotion.application.commons.PromotionApplicationErrorEnum;
import com.xk.promotion.application.support.PromotionApplicationException;
import com.xk.promotion.domain.enums.coupon.AuditStatusEnum;
import com.xk.promotion.domain.enums.coupon.ExchangeStatusEnum;
import com.xk.promotion.domain.enums.coupon.PeriodTypeEnum;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.domain.enums.exchange.ExchangeCodeTypeEnum;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.exchange.ExchangeRoot;
import com.xk.promotion.domain.model.exchange.entity.ExchangeCodeEntity;
import com.xk.promotion.domain.model.exchange.entity.ExchangeRecordEntity;
import com.xk.promotion.domain.model.exchange.id.ExchangeIdentifier;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExchangeCouponHandler implements IActionCommandHandler<ExchangeCouponCommand, Void> {

    private final CouponRootRepository couponRootRepository;

    private final CouponRootQueryRepository couponRootQueryRepository;

    private final CouponUserRootRepository couponUserRootRepository;

    private final CouponUserRootQueryRepository couponUserRootQueryRepository;

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;

    private final ExchangeRootRepository exchangeRootRepository;

    private final StockRootService stockRootService;

    private final ExchangeRootService exchangeRootService;

    @Override
    public Mono<Void> execute(Mono<ExchangeCouponCommand> mono) {
        return mono.flatMap(command -> exchangeRootQueryRepository
                .findByCode(ExchangeCodeEntity.builder().exchangeCode(command.getExchangeCode())
                        .build())
                .switchIfEmpty(Mono.error(new PromotionApplicationException(
                        PromotionApplicationErrorEnum.COUPON_CODE_NOT_FOUND)))
                .flatMap(exchangeCodeEntity -> {
                    command.setCouponId(Long.valueOf(exchangeCodeEntity.getExchangeBusinessId()));

                    return couponRootQueryRepository
                            .findById(LongIdentifier.builder().id(command.getCouponId()).build())
                            .switchIfEmpty(Mono.error(new PromotionApplicationException(
                                    PromotionApplicationErrorEnum.COUPON_NOT_FOUND)))
                            .flatMap(couponEntity -> {
                                log.info("ExchangeCouponHandler couponEntity {}",
                                        JSON.toJSONString(couponEntity));
                                log.info("ExchangeCouponHandler exchangeCodeEntity {}",
                                        JSON.toJSONString(exchangeCodeEntity));

                                // 1、校验
                                Supplier<Mono<Void>> checkMono = () -> {
                                    log.info("ExchangeCouponHandler check");

                                    // 检查优惠券是否已审核通过
                                    // if (!Objects.equals(couponEntity.getAuditStatus(),
                                    // AuditStatusEnum.PASS.getCode())) {
                                    // return Mono.error(new PromotionApplicationException(
                                    // PromotionApplicationErrorEnum.COUPON_NOT_FOUND));
                                    // }
                                    // 检查是否存在
                                    if (DeleteFlagEnum.DELETED.getValue() == couponEntity
                                            .getDeleted()) {
                                        return Mono.error(new PromotionApplicationException(
                                                PromotionApplicationErrorEnum.COUPON_EXCHANGE_CODE_USED));
                                    }
                                    // 检查是否启用
                                    if (StatusEnum.DISABLE.getValue() == couponEntity.getStatus()) {
                                        return Mono.error(new PromotionApplicationException(
                                                PromotionApplicationErrorEnum.COUPON_EXCHANGE_CODE_USED));
                                    }

                                    // 如果是公共兑换码，检查优惠券库存
                                    if (ExchangeCodeTypeEnum.COMMON.getCode()
                                            .equals(exchangeCodeEntity.getExchangeCodeType())
                                            && couponEntity.getStockNum() <= 0) {
                                        return Mono.error(new PromotionApplicationException(
                                                PromotionApplicationErrorEnum.COUPON_EXCHANGE_NUM_MAX));
                                    }

                                    // 检查一次性兑换码是否可用
                                    boolean isUsed = ExchangeCodeTypeEnum.ONCE.getCode()
                                            .equals(exchangeCodeEntity.getExchangeCodeType())
                                            && !Objects.equals(exchangeCodeEntity.getUsedStatus(),
                                                    UsedStatusEnum.NOT_USED.getCode());

                                    // 检查优惠券使用日期是否过了
                                    boolean isOverdue = PeriodTypeEnum.TIME_SCOPE.getCode()
                                            .equals(couponEntity.getPeriodType())
                                            && couponEntity.getEndTime().before(new Date());
                                    if (isUsed || isOverdue) {
                                        return Mono.error(new PromotionApplicationException(
                                                PromotionApplicationErrorEnum.COUPON_EXCHANGE_CODE_USED));
                                    }

                                    // 已达领取上限
                                    return couponUserRootQueryRepository
                                            .searchUserCoupon(CouponUserEntity.builder()
                                                    .couponId(command.getCouponId())
                                                    .userId(command.getUserId()).build())
                                            .count().flatMap(count -> {
                                                if (count >= couponEntity.getPersonNum()) {
                                                    return Mono.error(
                                                            new PromotionApplicationException(
                                                                    PromotionApplicationErrorEnum.COUPON_RECEIVED_MAX));
                                                }
                                                return Mono.empty();
                                            });
                                };

                                // 2、保存优惠券
                                Mono<Void> saveCouponMono = Mono.defer(() -> {
                                    log.info("ExchangeCouponHandler saveCoupon");
                                    CouponUserEntity couponUserEntity = new CouponUserEntity();
                                    BeanUtils.copyProperties(couponEntity, couponUserEntity);
                                    BeanUtils.copyProperties(command, couponUserEntity);

                                    Date today = new Date();
                                    if (Objects.equals(PeriodTypeEnum.TIME_SCOPE.getCode(),
                                            couponEntity.getPeriodType())) {
                                        couponUserEntity.setStartTime(couponEntity.getStartTime());
                                        couponUserEntity.setEndTime(couponEntity.getEndTime());
                                    } else {
                                        couponUserEntity.setStartTime(today);
                                        couponUserEntity.setEndTime(DateUtils.addDays(today,
                                                couponEntity.getPeriodNum()));
                                    }

                                    couponUserEntity.setUpdateId(command.getUserId());
                                    couponUserEntity.setCreateId(command.getUserId());
                                    couponUserEntity.setCreateTime(today);
                                    couponUserEntity.setUpdateTime(today);
                                    couponUserEntity.setExchangeCode(command.getExchangeCode());
                                    CouponUserIdentifier identifier = CouponUserIdentifier.builder()
                                            .couponUserId(couponUserEntity.getCouponUserId())
                                            .build();

                                    CouponUserRoot couponUserRoot =
                                            CouponUserRoot.builder().identifier(identifier)
                                                    .couponUserEntity(couponUserEntity).build();

                                    // 保存用户优惠券
                                    Mono<Void> saveMono =
                                            couponUserRootRepository.save(couponUserRoot);

                                    // 将用户优惠券加入到缓存中，过期自动失效
                                    if (PeriodTypeEnum.AFTER_RECEIVE.getCode()
                                            .equals(couponEntity.getPeriodType())) {
                                        return saveMono.then(couponUserRootRepository
                                                .addCouponUserExpireCache(couponUserRoot));
                                    } else {
                                        return saveMono;
                                    }
                                });

                                // 3、保存插入记录
                                Mono<Void> saveExchangeRecordMono = Mono.defer(() -> {
                                    log.info("ExchangeCouponHandler saveExchangeRecord");
                                    return exchangeRootService.generateRecordId()
                                            .flatMap(recordId -> {
                                                ExchangeRecordEntity exchangeRecordEntity =
                                                        new ExchangeRecordEntity();
                                                exchangeRecordEntity.setExchangeRecordId(recordId);
                                                exchangeRecordEntity.setExchangeCodeId(
                                                        exchangeCodeEntity.getExchangeCodeId());
                                                exchangeRecordEntity.setExchangeCode(
                                                        exchangeCodeEntity.getExchangeCode());
                                                exchangeRecordEntity.setExchangeStatus(
                                                        ExchangeStatusEnum.SUCCESS.getCode());
                                                exchangeRecordEntity.setRemark("兑换成功");
                                                exchangeRecordEntity
                                                        .setCreateId(command.getUserId());
                                                exchangeRecordEntity.setCreateTime(new Date());
                                                ExchangeIdentifier identifier =
                                                        ExchangeIdentifier.builder()
                                                                .exchangeId(exchangeCodeEntity
                                                                        .getExchangeCodeId())
                                                                .build();
                                                return exchangeRootRepository.save(ExchangeRoot
                                                        .builder().identifier(identifier)
                                                        .exchangeRecordEntity(exchangeRecordEntity)
                                                        .build());
                                            });
                                });

                                // 一次性兑换码需要修改状态
                                Mono<Void> updateExchangeCodeStatus = null;
                                if (ExchangeCodeTypeEnum.ONCE.getCode()
                                        .equals(exchangeCodeEntity.getExchangeCodeType())) {
                                    updateExchangeCodeStatus = Mono.defer(()->{
                                        log.info("ExchangeCouponHandler updateExchangeCodeStatus");
                                        return exchangeRootRepository.update(ExchangeRoot.builder()
                                                .identifier(ExchangeIdentifier.builder()
                                                        .exchangeId(-1L).build())
                                                .exchangeCodeEntity(ExchangeCodeEntity.builder()
                                                        .exchangeCodeId(exchangeCodeEntity
                                                                .getExchangeCodeId())
                                                        .usedStatus(
                                                                UsedStatusEnum.USED.getCode())
                                                        .receivedUserId(command.getUserId())
                                                        .updateId(command.getUserId())
                                                        .updateTime(new Date()).build())
                                                .build());
                                    });
                                } else {
                                    updateExchangeCodeStatus = Mono.empty();
                                }

                                // 如果是公共兑换码需要更新库存
                                Mono<Boolean> deductionStockMono = null;
                                if (ExchangeCodeTypeEnum.COMMON.getCode()
                                        .equals(exchangeCodeEntity.getExchangeCodeType())) {
                                    deductionStockMono = Mono.defer(() -> {
                                        log.info("ExchangeCouponHandler deductionStock");
                                        return stockRootService
                                                .deductionStock(
                                                        StringIdentifier.builder()
                                                                .id(couponEntity.getCouponId()
                                                                        .toString())
                                                                .build(),
                                                        1L, StockBusinessTypeEnum.COUPON)
                                                .flatMap(b -> {
                                                    if (!b) {
                                                        return Mono.error(
                                                                new PromotionApplicationException(
                                                                        PromotionApplicationErrorEnum.STOCK_NOT_ENOUGH));
                                                    }
                                                    return Mono.just(b);
                                                });
                                    });
                                } else {
                                    deductionStockMono = Mono.empty();
                                }

                                // 4、更新优惠券领取数量
                                Mono<Void> updateCouponMono = Mono.defer(() -> {
                                    log.info("ExchangeCouponHandler updateCoupon");
                                    couponEntity.setReceivedNum(couponEntity.getReceivedNum() + 1);
                                    //公共兑换码更新库存
                                    if (ExchangeCodeTypeEnum.COMMON.getCode()
                                            .equals(exchangeCodeEntity.getExchangeCodeType())) {
                                        couponEntity.setStockNum(couponEntity.getStockNum() - 1);
                                    }

                                    CouponIdentifier identifier = CouponIdentifier.builder()
                                            .couponId(couponEntity.getCouponId()).build();
                                    return couponRootRepository
                                            .update(CouponRoot.builder().identifier(identifier)
                                                    .couponEntity(couponEntity).build());
                                });

                                return checkMono.get().then(saveCouponMono)
                                        .then(saveExchangeRecordMono).then(updateCouponMono)
                                        .then(deductionStockMono).then(updateExchangeCodeStatus)
                                        .then();

                            });

                }));
    }
}

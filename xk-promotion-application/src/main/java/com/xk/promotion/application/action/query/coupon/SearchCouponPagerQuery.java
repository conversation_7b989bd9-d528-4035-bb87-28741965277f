package com.xk.promotion.application.action.query.coupon;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.promotion.interfaces.dto.req.*;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = CouponPagerReqDto.class, convertGenerate = false),
        @AutoMapper(target = CouponPlatformReqDto.class, convertGenerate = false),
        @AutoMapper(target = CouponCorpIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = CouponGoodsReqDto.class, convertGenerate = false),
        @AutoMapper(target = SearchCouponReqDto.class,convertGenerate = false),
        @AutoMapper(target = RankingCouponReqDto.class,convertGenerate = false),
@AutoMapper(target = CouponReceivedReqDto.class, convertGenerate = false),})
public class SearchCouponPagerQuery extends PagerQuery implements IActionQuery {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券所属平台 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    private Integer scopeType;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 优惠券状态（0：不可用，1：可用）
     */
    private Integer status;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商户id（app端）
     */
    private Long appCorpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 创建开始时间
     */
    private Date startTime;

    /**
     * 创建结束时间
     */
    private Date endTime;

    /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 库存
     */
    private Integer stockNum;

    /**
     * 使用状态：（1未使用; 2已使用；3已过期）
     */
    private List<Integer> usedStatusList;

    /**
     * 商户关联优惠券id集合
     */
    private List<Long> corpCouponIdList;

    /**
     * 商品关联优惠券id集合
     */
    private List<Long> goodsCouponIdList;


    /**
     * 排序方式 1默认排序 2权重排序
     */
    private Integer sortType;


    private Date startCreateTime;
    private Date endCreateTime;

    /**
     * 库存标识
     */
    private Integer stockFlag;

    /**
     * 排序标识
     */
    private Integer sortFlag;

}


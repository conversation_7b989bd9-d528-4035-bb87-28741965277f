package com.xk.promotion.application.handler.event.coupon;

import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.promotion.domain.event.coupon.CouponCancelEvent;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.entity.CouponEntity;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.service.coupon.CouponRootService;
import com.xk.promotion.domain.service.coupon.CouponUserRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CouponCancelHandler extends AbstractEventVerticle<CouponCancelEvent> {

    // private final LockRootService lockRootService;
    private final CouponUserRootService couponUserRootService;
    private final CouponRootRepository cuponRootRepository;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CouponCancelEvent> event) {
        return event.flatMap(couponCancelEvent -> {
            log.info("CouponCancelHandler 开始执行：{}", couponCancelEvent.getCouponId());
            CouponUserRoot couponUserRoot = CouponUserRoot.builder()
                    .identifier(CouponUserIdentifier.builder().couponUserId(-1L).build())
                    .couponUserEntity(CouponUserEntity.builder()
                            .couponId(couponCancelEvent.getCouponId()).build())
                    .build();
            CouponRoot couponRoot = CouponRoot.builder()
                    .identifier(CouponIdentifier.builder().couponId(couponCancelEvent.getCouponId())
                            .build())
                    .couponEntity(CouponEntity.builder().couponId(couponCancelEvent.getCouponId())
                            .status(StatusEnum.DISABLE.getValue()).build())
                    .build();

            return couponUserRootService.updateCouponUserOverdue(couponUserRoot)
                    .then(cuponRootRepository.update(couponRoot));
        });
    }
}

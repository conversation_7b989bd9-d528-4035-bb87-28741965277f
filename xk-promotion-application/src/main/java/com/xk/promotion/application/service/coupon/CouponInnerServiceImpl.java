package com.xk.promotion.application.service.coupon;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootRepository;
import com.xk.promotion.interfaces.dto.req.UpdateUsedStatusReqDto;
import com.xk.promotion.interfaces.service.coupon.CouponInnerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponInnerServiceImpl implements CouponInnerService {

    private final LockRootService lockRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final CouponUserRootQueryRepository couponUserRootQueryRepository;
    private final CouponUserRootRepository couponUserRootRepository;


    @BusiCode
    @Override
    public Mono<Boolean> updateUsedStatus(Mono<UpdateUsedStatusReqDto> mono) {

        return mono.flatMap(updateUsedStatusReqDto -> {

            //
            return lockRootService
                    .acquireTransactionObjectLockMono(ZookeeperLockObject.LOCKS_COUPON_USED,
                            updateUsedStatusReqDto.getCouponUserId())
                    .flatMap(b -> {
                        // 查询
                        return couponUserRootQueryRepository
                                .searchByCouponUserId(LongIdentifier.builder()
                                        .id(updateUsedStatusReqDto.getCouponUserId()).build())
                                .flatMap(couponUserEntity -> {
                                    if (UsedStatusEnum.OVERDUE.getCode().equals(couponUserEntity.getUsedStatus())) {
                                        return Mono.just(false);
                                    }
                                    CouponUserRoot couponUserRoot = CouponUserRoot.builder()
                                            .identifier(CouponUserIdentifier.builder()
                                                    .couponUserId(updateUsedStatusReqDto
                                                            .getCouponUserId())
                                                    .build())
                                            .couponUserEntity(CouponUserEntity.builder()
                                                    .couponUserId(updateUsedStatusReqDto
                                                            .getCouponUserId())
                                                    .usedStatus(
                                                            updateUsedStatusReqDto.getUsedStatus())
                                                    .usedTime(new Date())
                                                    .build())
                                            .build();
                                    if (UsedStatusEnum.NOT_USED.getCode()
                                            .equals(updateUsedStatusReqDto.getUsedStatus())
                                            && !UsedStatusEnum.OVERDUE.getCode()
                                                    .equals(couponUserEntity.getUsedStatus())) {
                                        // 还原
                                        return couponUserRootRepository.update(couponUserRoot)
                                                .thenReturn(true);
                                    }
                                    if (UsedStatusEnum.NOT_USED.getCode()
                                            .equals(couponUserEntity.getUsedStatus())) {
                                        // 修改
                                        return couponUserRootRepository.update(couponUserRoot)
                                                .thenReturn(true);
                                    } else {
                                        return Mono.just(false);
                                    }
                                });
                    });

        });
    }
}

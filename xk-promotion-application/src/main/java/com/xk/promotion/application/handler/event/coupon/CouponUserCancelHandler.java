package com.xk.promotion.application.handler.event.coupon;

import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.model.stock.valobj.StockRemainValObj;
import com.xk.infrastructure.adapter.stock.CouponStockAdapterService;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.domain.event.coupon.CouponCancelEvent;
import com.xk.promotion.domain.event.coupon.CouponUserCancelEvent;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.entity.CouponEntity;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.domain.service.coupon.CouponUserRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class CouponUserCancelHandler extends AbstractEventVerticle<CouponUserCancelEvent> {

    // private final LockRootService lockRootService;
    private final CouponUserRootService couponUserRootService;
    private final CouponUserRootQueryRepository couponUserRootQueryRepository;
    private final CouponRootRepository couponRootRepository;
    private final CouponRootQueryRepository couponRootQueryRepository;
    private final CouponStockAdapterService couponStockAdapterService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CouponUserCancelEvent> event) {
        return event.flatMap(couponUserCancelEvent -> {
            log.info("CouponUserCancelHandler 开始执行：{}", couponUserCancelEvent.getCouponUserId());
            CouponUserRoot couponUserRoot = CouponUserRoot.builder()
                    .identifier(CouponUserIdentifier.builder()
                            .couponUserId(couponUserCancelEvent.getCouponUserId()).build())
                    .couponUserEntity(CouponUserEntity.builder()
                            .couponUserId(couponUserCancelEvent.getCouponUserId()).build())
                    .build();

            return couponUserRootQueryRepository
                    .searchByCouponUserId(LongIdentifier.builder()
                            .id(couponUserCancelEvent.getCouponUserId()).build())
                    .flatMap(couponUserEntity -> {
                        Long couponId = couponUserEntity.getCouponId();

                        CouponRoot couponRoot = CouponRoot.builder()
                                .identifier(CouponIdentifier.builder().couponId(couponId).build())
                                .couponEntity(CouponEntity.builder().couponId(couponId)
                                        .status(StatusEnum.DISABLE.getValue()).build())
                                .build();
                        CouponUserEntity searchEntity =
                                CouponUserEntity.builder().couponId(couponId)
                                        .usedStatusList(List.of(UsedStatusEnum.NOT_USED.getCode())).build();

                        return couponUserRootService.updateCouponUserOverdue(couponUserRoot)
                                .flatMap( i ->Mono.zip(
                                        couponStockAdapterService
                                                .getRemainStock(StringIdentifier.builder()
                                                        .id(couponId.toString()).build()),
                                        couponUserRootQueryRepository.searchUserCoupon(searchEntity)
                                                .collectList()))
                                .flatMap(tuple -> {
                                    StockRemainValObj stock = tuple.getT1();
                                    List<CouponUserEntity> couponUserEntityList = tuple.getT2();
                                    log.info("CouponUserCancelHandler 禁用判断 {}，{}",
                                            stock.getRemainRealStock(),
                                            couponUserEntityList.size());

                                    Boolean b1 = stock.getRemainRealStock().intValue() <= 0;
                                    Boolean b2 = CollectionUtils.isEmpty(couponUserEntityList);

                                    if (b1 && b2) {
                                        log.info("CouponUserCancelHandler 禁用优惠券 {}", couponId);
                                        return couponRootRepository.update(couponRoot);
                                    }
                                    return Mono.empty();
                                });
                    });
        });
    }
}

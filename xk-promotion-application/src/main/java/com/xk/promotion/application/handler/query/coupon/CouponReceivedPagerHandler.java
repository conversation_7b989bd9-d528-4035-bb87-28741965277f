package com.xk.promotion.application.handler.query.coupon;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.xk.promotion.domain.enums.coupon.*;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.promotion.application.action.query.coupon.CouponReceivedPagerQuery;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponUserRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CouponReceivedPagerHandler
        implements IActionQueryHandler<CouponReceivedPagerQuery, Pagination> {

    private final CouponUserRootQueryRepository couponUserRootQueryRepository;

    private final SelectorRootService selectorRootService;

    @Override
    public Mono<Pagination> execute(Mono<CouponReceivedPagerQuery> mono) {
        return execute(mono, query -> {
            // 构造查询条件
            List<Integer> usedStatusList = new ArrayList<>();

            if (query.getIsHistory() != null) {
                // 1、如果是可用优惠券
                if (YesOrNotEnum.NOT.getCode().equals(query.getIsHistory())) {
                    usedStatusList.add(UsedStatusEnum.NOT_USED.getCode());
                    query.setUsedStatusList(usedStatusList);
                    Date today = new Date();
                    query.setEndTimeLeft(today);
                    query.setStatus(StatusEnum.ENABLE.getValue());
                    // 1、未生效
                    if (TimeTypeEnum.INVALID.getCode().equals(query.getTimeType())) {
                        query.setStartTimeLeft(today);
                        // 2、新到券
                    } else if (TimeTypeEnum.NEW.getCode().equals(query.getTimeType())) {
                        query.setReceivedTime(DateUtils.addDays(new Date(), -3));
                        // 3、快到期
                    } else if (TimeTypeEnum.ALMOST_EXPIRED.getCode().equals(query.getTimeType())) {
                        query.setEndTimeRight(DateUtils.addDays(new Date(), 2));
                        // 4、即将过期
                    } else if (TimeTypeEnum.SOON_EXPIRED.getCode().equals(query.getTimeType())) {
                        query.setEndTimeRight(DateUtils.addDays(new Date(), 1));
                    }
                } else {
                    if (Objects.nonNull(query.getExpiredType())){
                        if (ExpireTypeEnum.DISABLE.getCode().equals(query.getExpiredType())) {
                            query.setStatus(StatusEnum.DISABLE.getValue());
                        } else if (ExpireTypeEnum.OVERDUE.getCode().equals(query.getExpiredType())){
                            usedStatusList.add(UsedStatusEnum.OVERDUE.getCode());
                        }else if (ExpireTypeEnum.USED.getCode().equals(query.getExpiredType())){
                            usedStatusList.add(UsedStatusEnum.USED.getCode());
                        }
                        query.setUsedStatusList(usedStatusList);
                    }else {
//                        query.setStatus(StatusEnum.DISABLE.getValue());
//                        usedStatusList.add(UsedStatusEnum.OVERDUE.getCode());
//                        usedStatusList.add(UsedStatusEnum.USED.getCode());
                        //失效标识
                        query.setExpiredFlag(1);
                    }

                }
            }

            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));
            return pagination;
        }, couponUserRootQueryRepository::searchUserCoupon, CouponUserEntity.class)
                .flatMap(pagination -> {
                    List<CouponUserEntity> couponUserEntityList = pagination.getRecords();

                    // 使用 Flux 处理每个 CouponUserEntity
                    return Flux.fromIterable(couponUserEntityList).flatMap(couponUserEntity -> {
                        CouponUserRspDto couponUserRspDto = new CouponUserRspDto();
                        BeanUtils.copyProperties(couponUserEntity, couponUserRspDto);
                        //设置优惠券到期类型
                        TimeTypeEnum timeTypeEnum = getTimeType(couponUserEntity);
                        couponUserRspDto.setTimeType(Objects.nonNull(timeTypeEnum)?timeTypeEnum.getCode():null);
                        //设置优惠券过期类型
                        ExpireTypeEnum expireTypeEnum = getExpireType(couponUserEntity);
                        couponUserRspDto.setExpiredType(Objects.nonNull(expireTypeEnum)?expireTypeEnum.getCode():null);
                        // 异步获取用户信息
                        return selectorRootService.getUserObject(couponUserEntity.getUserId())
                                .flatMap(userObjectRoot -> {
                                    couponUserRspDto.setMobile(
                                            userObjectRoot.getUserDataObjectEntity().getMobile());
                                    //设置商家
                                    return setCorpInfo(couponUserRspDto,couponUserEntity);
                                    // 如果查询用户失败，返回不带手机号的DTO
                                }).onErrorReturn(couponUserRspDto);
                    }).collectList().map(couponUserRspDtoList -> {
                        pagination.setRecords(couponUserRspDtoList);
                        return pagination;
                    });
                });
    }

    private ExpireTypeEnum getExpireType(CouponUserEntity couponUserEntity) {
        //已失效
        if (StatusEnum.DISABLE.getValue() == couponUserEntity.getStatus()){
            return ExpireTypeEnum.DISABLE;
        }
        //已过期
        if (UsedStatusEnum.OVERDUE.getCode().equals(couponUserEntity.getUsedStatus())){
            return ExpireTypeEnum.OVERDUE;
        }
        //已过期
        if (UsedStatusEnum.USED.getCode().equals(couponUserEntity.getUsedStatus())){
            return ExpireTypeEnum.USED;
        }
        return null;
    }

    private TimeTypeEnum getTimeType(CouponUserEntity couponUserEntity) {
        Date startTime = couponUserEntity.getStartTime();
        Date endTime = couponUserEntity.getEndTime();
        Date createTime = couponUserEntity.getCreateTime();
        Date localTime = new Date();
        //未生效
        if (localTime.before(startTime)) {
            return TimeTypeEnum.INVALID;
        }
        //即将过期
        if (localTime.before(endTime)
                && endTime.after(DateUtils.addDays(localTime, -1))){
            return TimeTypeEnum.SOON_EXPIRED;
        }
        //快到期
        if (localTime.before(endTime)
                && endTime.after(DateUtils.addDays(localTime, -2))){
            return TimeTypeEnum.ALMOST_EXPIRED;
        }
        //新到券
        if (createTime.after(DateUtils.addDays(localTime, -3))){
            return TimeTypeEnum.NEW;
        }
        return null;
    }
    /**
     * 设置商家名称和LOGO
     */
    private Mono<CouponUserRspDto> setCorpInfo(CouponUserRspDto couponUserRspDto , CouponUserEntity couponUserEntity) {
        if (couponUserEntity.getPlatformType().equals(PlatformTypeEnum.MANAGER.getCode())) {
            // 运营平台的优惠券，设置官方LOGO
            couponUserRspDto.setCorpLogo(CouponConstantEnum.DEFAULT_CORP_LOGO.getDesc());
            couponUserRspDto.setCorpName(CouponConstantEnum.DEFAULT_CORP_NAME.getDesc());
            return Mono.just(couponUserRspDto);
        } else {
            // 商家平台的优惠券，设置商家LOGO
            return selectorRootService.getCorpObject(couponUserEntity.getCorpId())
                    .map(corpObjectRoot -> {
                        couponUserRspDto.setCorpName(corpObjectRoot.getCorpInfoObjectEntity().getCorpName());
                        couponUserRspDto.setCorpLogo(corpObjectRoot.getCorpInfoObjectEntity().getCorpLogo());
                        return couponUserRspDto;
                    });
        }
    }
}


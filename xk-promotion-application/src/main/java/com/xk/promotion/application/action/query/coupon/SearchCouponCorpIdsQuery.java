package com.xk.promotion.application.action.query.coupon;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.promotion.interfaces.dto.req.CouponCorpIdReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@AutoMappers({
        @AutoMapper(target = CouponCorpIdReqDto.class)
})
public class SearchCouponCorpIdsQuery implements IActionQuery {

    /**
     * 商户id
     */
    private Long corpId;
}

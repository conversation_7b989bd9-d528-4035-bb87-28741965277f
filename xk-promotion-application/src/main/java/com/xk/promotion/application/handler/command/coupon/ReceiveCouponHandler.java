package com.xk.promotion.application.handler.command.coupon;

import java.util.Date;
import java.util.Objects;

import com.myco.mydata.domain.enums.commons.DeleteFlagEnum;
import com.myco.mydata.domain.enums.commons.StatusEnum;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.promotion.application.action.command.coupon.ReceiveCouponCommand;
import com.xk.promotion.application.commons.PromotionApplicationErrorEnum;
import com.xk.promotion.application.support.PromotionApplicationException;
import com.xk.promotion.domain.enums.coupon.AuditStatusEnum;
import com.xk.promotion.domain.enums.coupon.PeriodTypeEnum;
import com.xk.promotion.domain.model.coupon.CouponRoot;
import com.xk.promotion.domain.model.coupon.id.CouponIdentifier;
import com.xk.promotion.domain.model.user.CouponUserRoot;
import com.xk.promotion.domain.model.user.entity.CouponUserEntity;
import com.xk.promotion.domain.model.user.id.CouponUserIdentifier;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.domain.repository.coupon.CouponRootRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootQueryRepository;
import com.xk.promotion.domain.repository.exchange.ExchangeRootRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootQueryRepository;
import com.xk.promotion.domain.repository.user.CouponUserRootRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReceiveCouponHandler implements IActionCommandHandler<ReceiveCouponCommand, Void> {

    private final CouponRootRepository couponRootRepository;

    private final CouponRootQueryRepository couponRootQueryRepository;

    private final CouponUserRootRepository couponUserRootRepository;

    private final CouponUserRootQueryRepository couponUserRootQueryRepository;

    private final ExchangeRootQueryRepository exchangeRootQueryRepository;

    private final ExchangeRootRepository exchangeRootRepository;

    private final StockRootService stockRootService;

    @Override
    public Mono<Void> execute(Mono<ReceiveCouponCommand> mono) {
        return mono.flatMap(command -> {
            return couponRootQueryRepository
                    .findById(LongIdentifier.builder().id(command.getCouponId()).build())
                    .switchIfEmpty(Mono.error(new PromotionApplicationException(
                            PromotionApplicationErrorEnum.COUPON_NOT_FOUND)))
                    .flatMap(couponEntity -> {

                        // 1、校验
                        Mono<Void> checkMono = Mono.defer(() -> {
                            // 检查优惠券是否已审核通过
//                            if (!Objects.equals(couponEntity.getAuditStatus(),
//                                    AuditStatusEnum.PASS.getCode())) {
//                                return Mono.error(new PromotionApplicationException(
//                                        PromotionApplicationErrorEnum.COUPON_NOT_FOUND));
//                            }
                            //检查是否存在
                            if (DeleteFlagEnum.DELETED.getValue() == couponEntity.getDeleted()){
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.COUPON_RECEIVE_TOAST));
                            }
                            //检查是否启用
                            if (StatusEnum.DISABLE.getValue() == couponEntity.getStatus()) {
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.COUPON_RECEIVE_TOAST));
                            }

                            // 检查优惠券是否已领完【库存是否为0】
                            if (couponEntity.getStockNum() <= 0) {
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.COUPON_RECEIVE_TOAST));
                            }

                            // 检查优惠券使用日期是否过了
//                            if (couponEntity.getEndTime().before(new Date())) {
//                                return Mono.error(new PromotionApplicationException(
//                                        PromotionApplicationErrorEnum.COUPON_CODE_USED));
//                            }

                            // 已达领取上限
                            return couponUserRootQueryRepository
                                    .searchUserCoupon(CouponUserEntity.builder()
                                            .couponId(command.getCouponId())
                                            .userId(command.getUserId()).build())
                                    .count().flatMap(count -> {
                                        if (count >= couponEntity.getPersonNum()) {
                                            return Mono.error(new PromotionApplicationException(
                                                    PromotionApplicationErrorEnum.COUPON_RECEIVED_MAX));
                                        }
                                        return Mono.empty();
                                    });
                        });

                        // 2、保存优惠券
                        Mono<Void> saveCouponMono = Mono.defer(() -> {
                            CouponUserEntity couponUserEntity = new CouponUserEntity();
                            BeanUtils.copyProperties(couponEntity, couponUserEntity);
                            BeanUtils.copyProperties(command, couponUserEntity);
                            Date today = new Date();
                            if (Objects.equals(PeriodTypeEnum.TIME_SCOPE.getCode(),
                                    couponEntity.getPeriodType())) {
                                couponUserEntity.setStartTime(couponEntity.getStartTime());
                                couponUserEntity.setEndTime(couponEntity.getEndTime());
                            } else {

                                couponUserEntity.setStartTime(today);
                                couponUserEntity.setEndTime(
                                        DateUtils.addDays(today, couponEntity.getPeriodNum()));
                            }

                            couponUserEntity.setUpdateId(command.getUserId());
                            couponUserEntity.setCreateId(command.getUserId());
                            couponUserEntity.setCreateTime(today);
                            couponUserEntity.setUpdateTime(today);
                            CouponUserIdentifier identifier = CouponUserIdentifier.builder()
                                    .couponUserId(couponUserEntity.getCouponUserId()).build();
                            CouponUserRoot couponUserRoot =
                                    CouponUserRoot.builder().identifier(identifier)
                                            .couponUserEntity(couponUserEntity).build();

                            // 保存用户优惠券
                            Mono<Void> saveMono = couponUserRootRepository.save(couponUserRoot);

                            // 将用户优惠券加入到缓存中，过期自动失效
                            if (PeriodTypeEnum.AFTER_RECEIVE.getCode()
                                    .equals(couponEntity.getPeriodType())) {
                                return saveMono.then(couponUserRootRepository
                                        .addCouponUserExpireCache(couponUserRoot));
                            } else {
                                return saveMono;
                            }
                        });

                        // 3、更新优惠券领取数量
                        Mono<Void> updateCouponMono = Mono.defer(() -> {
                            couponEntity.setReceivedNum(couponEntity.getReceivedNum() + 1);
                            couponEntity.setStockNum(couponEntity.getStockNum() - 1);
                            CouponIdentifier identifier = CouponIdentifier.builder()
                                    .couponId(couponEntity.getCouponId()).build();
                            return couponRootRepository.update(CouponRoot.builder()
                                    .identifier(identifier).couponEntity(couponEntity).build());
                        });

                        //4. 更新库存
                        Mono<Boolean> deductionStockMono = stockRootService.deductionStock(StringIdentifier.builder().id(couponEntity.getCouponId().toString()).build(),1L, StockBusinessTypeEnum.COUPON).flatMap(b->{
                            if (!b) {
                                return Mono.error(new PromotionApplicationException(
                                        PromotionApplicationErrorEnum.STOCK_NOT_ENOUGH));
                            }
                            return Mono.just(b);
                        });

                        return checkMono.then(saveCouponMono).then(updateCouponMono).then(deductionStockMono).then();
                    });

        });
    }
}

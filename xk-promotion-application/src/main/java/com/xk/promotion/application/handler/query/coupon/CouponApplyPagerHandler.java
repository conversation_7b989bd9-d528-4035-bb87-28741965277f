package com.xk.promotion.application.handler.query.coupon;

import java.util.ArrayList;
import java.util.List;

import com.myco.mydata.domain.model.LongIdentifier;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.promotion.application.action.query.coupon.CouponApplyPagerQuery;
import com.xk.promotion.domain.model.coupon.entity.CouponApplyEntity;
import com.xk.promotion.domain.repository.coupon.CouponRootQueryRepository;
import com.xk.promotion.interfaces.dto.rsp.CouponApplyRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CouponApplyPagerHandler
        implements IActionQueryHandler<CouponApplyPagerQuery, Pagination> {

    private final CouponRootQueryRepository couponRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<CouponApplyPagerQuery> mono) {
        return execute(mono, query -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));
            return pagination;
        }, couponRootQueryRepository::findCouponApply, CouponApplyEntity.class)
                .flatMap(pagination -> {
                    List<CouponApplyEntity> records = pagination.getRecords();
                    if (records.isEmpty()) {
                        return Mono.just(pagination);
                    }
                    return Flux.fromIterable(records).flatMap(couponApplyEntity -> {
                        return couponRootQueryRepository
                                .findById(LongIdentifier.builder()
                                        .id(couponApplyEntity.getCouponId()).build())
                                .flatMap(couponEntity -> {
                                    CouponApplyRspDto couponApplyRspDto = new CouponApplyRspDto();
//                                    BeanUtils.copyProperties(couponEntity, couponApplyRspDto);
                                    BeanUtils.copyProperties(couponApplyEntity, couponApplyRspDto);
                                    couponApplyRspDto.setPeriodType(couponEntity.getPeriodType());
                                    couponApplyRspDto.setStatus(couponEntity.getStatus());
                                    couponApplyRspDto.setInstruction(couponEntity.getInstruction());
                                    couponApplyRspDto.setStartTime(couponEntity.getStartTime());
                                    couponApplyRspDto.setEndTime(couponEntity.getEndTime());
                                    couponApplyRspDto.setPeriodNum(couponEntity.getPeriodNum());
                                    couponApplyRspDto.setPersonNum(couponEntity.getPersonNum());
                                    couponApplyRspDto.setTotalNum(couponEntity.getTotalNum());
                                    couponApplyRspDto.setStockNum(couponEntity.getStockNum());
                                    couponApplyRspDto.setCouponType(couponEntity.getCouponType());
                                    couponApplyRspDto.setThresholdAmount(couponEntity.getThresholdAmount());
                                    couponApplyRspDto.setDiscountAmount(couponEntity.getDiscountAmount());
                                    return Mono.just(couponApplyRspDto);
                                });
                    }).collectList().map(couponApplyRspDtos -> {
                        pagination.setRecords(couponApplyRspDtos);
                        return pagination;
                    });
                });
    }
}


package com.xk.tp.interfaces.dto.res.userauth;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthUserInfoResDto extends RequireSessionDto {

    private String openid;
    private String nickname;
    private String sex;
    private String province;
    private String city;
    private String country;
    private String headimgurl;
    private String unionid;
    private Integer channelType;
    private String mobile;
}

package com.xk.tp.interfaces.dto.req.recordingTask;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data

@AllArgsConstructor
@NoArgsConstructor
public class CreateRecordingReqDto extends RequireSessionDto {

    /**
     * 直播平台类型
     */
    private Integer livePlatformType;

    /**
     * 直播间id
     */
    private String liveId;

    /**
     * 0-字符类型 1-数值类型
     */
    private Integer liveUserType;

    /**
     * 发起人
     */
    private String creator;

    /**
     * 视频的宽度值，单位为像素，默认值360。 PS: 不能超过1920，与height的乘积不能超过 1920*1080
     */
    private Long width;

    /**
     * 视频的高度值，单位为像素，默认值640。 PS：不能超过1920，与width的乘积不能超过1920*1080。
     */
    private Long height;

    @Builder
    public CreateRecordingReqDto(String sessionId, Integer livePlatformType, String liveId,
            Integer liveUserType, String creator, Long width, Long height) {
        super(sessionId);
        this.livePlatformType = livePlatformType;
        this.liveId = liveId;
        this.liveUserType = liveUserType;
        this.creator = creator;
        this.width = width;
        this.height = height;
    }
}

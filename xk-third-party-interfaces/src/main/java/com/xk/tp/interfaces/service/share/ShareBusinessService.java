package com.xk.tp.interfaces.service.share;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.xk.tp.interfaces.dto.req.share.ShareBusinessReqDto;
import com.xk.tp.interfaces.dto.res.share.ShareBusinessResDto;

import reactor.core.publisher.Mono;

/**
 * 业务分享
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 9:47
 */
@HttpExchange("/tp/share")
public interface ShareBusinessService {

    /**
     * 创建业务分享配置
     *
     * @param mono 业务分享响应dto
     * @return reactor.core.publisher.Mono<com.xk.tp.interfaces.dto.req.share.ShareBusinessResDto>
     * <AUTHOR>
     * @date: 2025/8/8 10:02
     */
    @PostExchange("/create/business/config")
    Mono<ShareBusinessResDto> createBusinessConfig(@RequestBody Mono<ShareBusinessReqDto> mono);
}

package com.xk.tp.interfaces.dto.req.live;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignReqDto extends RequireSessionDto {

    /**
     * 直播平台 100-腾讯
     */
    private Integer livePlatformType;

    /**
     * 用户id
     */
    private String userId;

    @Builder
    public SignReqDto(String sessionId, Integer livePlatformType, String userId) {
        super(sessionId);
        this.livePlatformType = livePlatformType;
        this.userId = userId;
    }

}

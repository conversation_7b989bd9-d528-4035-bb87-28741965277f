package com.xk.tp.interfaces.dto.req.recordingTask;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data

@AllArgsConstructor
@NoArgsConstructor
public class DeleteRecordingReqDto extends RequireSessionDto {

    private Integer livePlatformType;

    private String liveId;

    @Builder
    public DeleteRecordingReqDto(String sessionId, Integer livePlatformType, String liveId) {
        super(sessionId);
        this.livePlatformType = livePlatformType;
        this.liveId = liveId;
    }

}

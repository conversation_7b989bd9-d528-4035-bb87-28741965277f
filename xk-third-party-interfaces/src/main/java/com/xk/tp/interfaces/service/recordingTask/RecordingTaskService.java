package com.xk.tp.interfaces.service.recordingTask;

import com.xk.tp.interfaces.dto.res.recordingTask.CreateTaskRecordingRspDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.tp.interfaces.dto.req.recordingTask.CreateRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DeleteRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DescribeRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.UpdateRecordingReqDto;

import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@HttpExchange("/tp/live")
public interface RecordingTaskService extends IApplicationService {

    @PostExchange("/create/cloud")
    Mono<CreateTaskRecordingRspDto> createCloudRecording(@RequestBody Mono<CreateRecordingReqDto> dtoMono);

    @PostExchange("/delete/cloud")
    Mono<Void> deleteCloudRecording(@RequestBody Mono<DeleteRecordingReqDto> dtoMono);

    @PostExchange("/describe/cloud")
    Mono<Void> describeCloudRecording(@RequestBody Mono<DescribeRecordingReqDto> dtoMono);

    @PostExchange("/modify/cloud")
    Mono<Void> modifyCloudRecording(@RequestBody Mono<UpdateRecordingReqDto> dtoMono);
}

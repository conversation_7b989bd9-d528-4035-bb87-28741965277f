package com.xk.tp.interfaces.dto.req.im;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data

@AllArgsConstructor
@NoArgsConstructor
public class ImSignReqDto extends RequireSessionDto {

    /**
     * 直播平台 100-腾讯
     */
    private Integer imPlatformType;

    /**
     * 用户id
     */
    private String userId;

    @Builder
    public ImSignReqDto(String sessionId, Integer imPlatformType, String userId) {
        super(sessionId);
        this.imPlatformType = imPlatformType;
        this.userId = userId;
    }
}

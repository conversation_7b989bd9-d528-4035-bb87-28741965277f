package com.xk.tp.interfaces.api.recordingTask;

import com.xk.tp.interfaces.dto.res.recordingTask.CreateTaskRecordingRspDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xk.tp.interfaces.dto.req.recordingTask.CreateRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DeleteRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.DescribeRecordingReqDto;
import com.xk.tp.interfaces.dto.req.recordingTask.UpdateRecordingReqDto;
import com.xk.tp.interfaces.service.recordingTask.RecordingTaskService;

import reactor.core.publisher.Mono;

/**
 * 录播
 *
 * <AUTHOR> date 2024/07/26
 */
@RestController
@RequestMapping("/tp/recording")
public interface RecordingTaskDocTaskService extends RecordingTaskService {

    /**
     * 开始录播
     * 
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @PostMapping("/create/cloud")
    Mono<CreateTaskRecordingRspDto> createCloudRecording(@RequestBody Mono<CreateRecordingReqDto> dtoMono);

    /**
     * 停止录播
     * 
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @PostMapping("/delete/cloud")
    Mono<Void> deleteCloudRecording(@RequestBody Mono<DeleteRecordingReqDto> dtoMono);

    /**
     * 查询录播
     * 
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @PostMapping("/describe/cloud")
    Mono<Void> describeCloudRecording(@RequestBody Mono<DescribeRecordingReqDto> dtoMono);

    /**
     * 更新录播
     * 
     * @param dtoMono dtoMono
     * @return Mono<Void>
     */
    @PostMapping("/modify/cloud")
    Mono<Void> modifyCloudRecording(@RequestBody Mono<UpdateRecordingReqDto> dtoMono);


}

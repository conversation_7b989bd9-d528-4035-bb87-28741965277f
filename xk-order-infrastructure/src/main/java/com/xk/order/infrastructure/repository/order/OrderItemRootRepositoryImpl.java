package com.xk.order.infrastructure.repository.order;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.myco.mydata.infrastructure.cache.key.SortedSetsCacheKeyValue;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.infrastructure.cache.dao.merchant.FortunePositionDao;
import com.xk.infrastructure.cache.dao.merchant.MerchantOrderDao;
import com.xk.infrastructure.cache.dao.merchant.MerchantProductUserDao;
import com.xk.infrastructure.cache.dao.merchant.MerchantUserAmountDao;
import com.xk.infrastructure.cache.dao.order.OrderAnonymousDao;
import com.xk.infrastructure.cache.dao.user.UserBoughtGoodsDao;
import com.xk.infrastructure.cache.key.merchant.FortunePositionKey;
import com.xk.infrastructure.cache.key.merchant.MerchantOrderKey;
import com.xk.infrastructure.cache.key.merchant.MerchantProductUserKey;
import com.xk.infrastructure.cache.key.merchant.MerchantUserAmountKey;
import com.xk.infrastructure.cache.key.order.OrderAnonymousKey;
import com.xk.infrastructure.cache.key.user.UserBoughtGoodsKey;
import com.xk.infrastructure.cache.po.merchant.MerchantOrderPo;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.repository.order.OrderItemRootRepository;
import com.xk.order.enums.order.FortuneLockStatusEnum;
import com.xk.order.infrastructure.cache.dao.order.FortuneSpecificationLockDao;
import com.xk.order.infrastructure.cache.dao.order.UserGoodsBuyCountDao;
import com.xk.order.infrastructure.cache.dao.order.UserGoodsBuyTimeDao;
import com.xk.order.infrastructure.cache.key.order.OrderUserBuyCountKey;
import com.xk.order.infrastructure.cache.key.order.OrderUserBuyTimeKey;
import com.xk.order.infrastructure.cache.key.order.SpecificationLockKey;
import com.xk.order.infrastructure.cache.po.order.FortuneSpecificationLockPo;
import com.xk.order.infrastructure.data.persistence.order.OOrderGiftMapper;
import com.xk.order.infrastructure.data.persistence.order.OOrderItemMapper;
import com.xk.order.infrastructure.data.po.order.OOrderGift;
import com.xk.order.infrastructure.data.po.order.OOrderItem;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderItemRootRepositoryImpl implements OrderItemRootRepository {

    private final OOrderItemMapper orderItemMapper;
    private final OOrderGiftMapper orderGiftMapper;
    private final FortuneSpecificationLockDao fortuneSpecificationLockDao;
    private final FortunePositionDao fortunePositionDao;
    private final MerchantOrderDao merchantOrderDao;
    private final MerchantProductUserDao merchantProductUserDao;
    private final MerchantUserAmountDao merchantUserAmountDao;
    private final UserGoodsBuyTimeDao userGoodsBuyTimeDao;
    private final UserGoodsBuyCountDao userGoodsBuyCountDao;
    private final UserBoughtGoodsDao userBoughtGoodsDao;
    private final OrderAnonymousDao orderAnonymousDao;
    private final Converter converter;

    @Override
    public Mono<Void> save(OrderItemRoot root) {
        return Mono.justOrEmpty(root.getOrderItemEntity())
                .flatMap(entity -> save(entity, OOrderItem.class, converter::convert,
                        orderItemMapper::insertSelective))
                .then(Mono.justOrEmpty(root.getOrderGiftEntityList()))
                .flatMap(this::saveBatchOrderGift).then();
    }

    /**
     * 分批保存订单赠品到数据库
     *
     * @param list 订单赠品列表
     * @return Mono<Void>
     */
    private Mono<Void> saveBatchOrderGift(List<OrderGiftEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.debug("订单赠品结果为空，跳过保存");
            return Mono.empty();
        }

        int totalSize = list.size();
        int batchSize = getBatchSize(totalSize);

        log.info("开始分批保存分发结果，总数量: {}, 批次大小: {}, 预计批次数: {}", totalSize, batchSize,
                (totalSize + batchSize - 1) / batchSize);

        // 使用 concatMap 确保顺序执行，任何批次失败立即停止整个操作
        return Flux.range(0, (totalSize + batchSize - 1) / batchSize).concatMap(batchIndex -> {
            int startIndex = batchIndex * batchSize;
            int endIndex = Math.min(startIndex + batchSize, totalSize);

            List<OrderGiftEntity> batchEntity = list.subList(startIndex, endIndex);

            return Mono.fromCallable(() -> {
                long batchStartTime = System.currentTimeMillis();

                log.debug("批次 {} 开始处理，数量: {}", batchIndex + 1, batchEntity.size());

                // 构建当前批次的PO对象
                List<OOrderGift> batchPOList = converter.convert(batchEntity, OOrderGift.class);

                // 批量插入当前批次
                int insertCount = orderGiftMapper.batchInsert(batchPOList);

                long batchEndTime = System.currentTimeMillis();
                log.debug("批次 {} 保存完成，插入数量: {}, 耗时: {}ms", batchIndex + 1, insertCount,
                        batchEndTime - batchStartTime);

                return batchIndex + 1; // 返回已完成的批次号
            }).doOnError(ex -> log.error("批次 {} 保存失败，停止后续批次执行", batchIndex + 1, ex));
        }).then().doOnSuccess(unused -> log.info("所有批次保存完成，总数量: {}", totalSize))
                .doOnError(ex -> log.error("分批保存失败，操作已终止", ex));
    }

    /**
     * 根据数据量动态计算批次大小，控制虚拟线程数量在合理范围内 针对7000-10000数据量场景优化
     *
     * @param totalSize 总数据量
     * @return 批次大小
     */
    private int getBatchSize(int totalSize) {
        // 最大虚拟线程数限制
        final int MAX_VIRTUAL_THREADS = 50;
        // 最小批次大小
        final int MIN_BATCH_SIZE = 100;
        // 最大批次大小
        final int MAX_BATCH_SIZE = 5000;

        if (totalSize <= 100) {
            return totalSize; // 极小数据量，不分批
        }

        // 基于总数据量计算理想批次大小
        int idealBatchSize;
        if (totalSize <= 500) {
            idealBatchSize = 100; // 小数据量，每批100个
        } else if (totalSize <= 2000) {
            idealBatchSize = 200; // 较小数据量，每批200个
        } else if (totalSize <= 5000) {
            idealBatchSize = 500; // 中小数据量，每批500个
        } else if (totalSize <= 8000) {
            idealBatchSize = 800; // 常用数据量范围，每批800个，约8-10批次
        } else if (totalSize <= 12000) {
            idealBatchSize = 1000; // 常用数据量范围，每批1000个，约7-12批次
        } else if (totalSize <= 30000) {
            idealBatchSize = 1500; // 较大数据量，每批1500个
        } else if (totalSize <= 50000) {
            idealBatchSize = 2000; // 大数据量，每批2000个
        } else {
            idealBatchSize = 3000; // 超大数据量，每批3000个
        }

        // 计算预期的虚拟线程数量
        int expectedThreadCount = (totalSize + idealBatchSize - 1) / idealBatchSize;

        // 如果虚拟线程数量超过限制，调整批次大小
        if (expectedThreadCount > MAX_VIRTUAL_THREADS) {
            idealBatchSize = (totalSize + MAX_VIRTUAL_THREADS - 1) / MAX_VIRTUAL_THREADS;
        }

        // 确保批次大小在合理范围内
        return Math.clamp(idealBatchSize, MIN_BATCH_SIZE, MAX_BATCH_SIZE);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(OrderItemRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(OrderItemRoot root) {
        return Mono.justOrEmpty(root.getOrderItemEntity())
                .flatMap(entity -> update(entity, OOrderItem.class, converter::convert,
                        orderItemMapper::updateByPrimaryKeySelective))
                .then(Mono.justOrEmpty(root.getOrderGiftEntityList()))
                .flatMapMany(Flux::fromIterable).flatMap(gift -> update(gift, OOrderGift.class,
                        converter::convert, orderGiftMapper::updateByPrimaryKeySelective))
                .then();
    }

    @Override
    public Mono<Void> remove(OrderItemRoot root) {
        return Mono.justOrEmpty(root.getOrderItemEntity())
                .flatMap(entity -> remove(entity, OOrderItem.class, converter::convert,
                        orderItemMapper::deleteByPrimaryKey))
                .then(Mono.justOrEmpty(root.getOrderGiftEntityList()))
                .flatMapMany(Flux::fromIterable).flatMap(gift -> remove(gift, OOrderGift.class,
                        converter::convert, orderGiftMapper::deleteByPrimaryKey))
                .then();
    }

    @Override
    public Mono<Void> addMerchantProductUserCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> {
            SortedSetsCacheKeyValue<Long> cacheKeyValue = new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(valObj.getUserId());
            cacheKeyValue.setScore((double) new Date().getTime());
            merchantProductUserDao.addValue(
                    MerchantProductUserKey.builder().goodsId(valObj.getGoodsId()).build(),
                    cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> addMerchantOrderCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> {
            MerchantOrderPo po = MerchantOrderPo.builder().orderNo(valObj.getOrderNo())
                    .count(valObj.getBuyCount()).amount(valObj.getAmount())
                    .userId(valObj.getUserId()).build();
            SortedSetsCacheKeyValue<MerchantOrderPo> cacheKeyValue =
                    new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(po);
            cacheKeyValue.setScore((double) new Date().getTime());
            merchantOrderDao.addValue(
                    MerchantOrderKey.builder().goodsId(valObj.getGoodsId()).build(), cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> addFortuneSpecificationLock(OrderItemLockValObj valObj) {
        return Mono.fromRunnable(() -> {
            SpecificationLockKey key = SpecificationLockKey.builder().goodsId(valObj.getGoodsId())
                    .itemPosition(valObj.getPosition()).build();
            fortuneSpecificationLockDao.addValue(key, FortuneSpecificationLockPo.builder()
                    .userId(valObj.getUserId()).lockStatus(valObj.getLockStatus()).build());
        });
    }

    @Override
    public Mono<Void> addUserGoodsBuyCountCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> {
            OrderUserBuyCountKey key =
                    OrderUserBuyCountKey.builder().goodsId(valObj.getGoodsId()).build();
            SortedSetsCacheKeyValue<Long> cacheKeyValue = new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(valObj.getUserId());
            cacheKeyValue.setScore((double) valObj.getBuyCount());
            userGoodsBuyCountDao.incrValue(key, cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> removeUserGoodsBuyCountCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> {
            OrderUserBuyCountKey key =
                    OrderUserBuyCountKey.builder().goodsId(valObj.getGoodsId()).build();
            SortedSetsCacheKeyValue<Long> cacheKeyValue = new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(valObj.getUserId());
            cacheKeyValue.setScore((double) -valObj.getBuyCount());
            userGoodsBuyCountDao.incrValue(key, cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> addUserGoodsBuyTimeCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> {
            OrderUserBuyTimeKey key = OrderUserBuyTimeKey.builder().goodsId(valObj.getGoodsId())
                    .userId(valObj.getUserId()).build();
            Date date = new Date();
            SortedSetsCacheKeyValue<Integer> cacheKeyValue = new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(valObj.getBuyCount());
            cacheKeyValue.setScore((double) date.getTime());
            userGoodsBuyTimeDao.addValue(key, cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> addMerchantUserAmountCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> {
            SortedSetsCacheKeyValue<Long> cacheKeyValue = new SortedSetsCacheKeyValue<>();
            cacheKeyValue.setValue(valObj.getUserId());
            cacheKeyValue.setScore(valObj.getAmount().doubleValue());
            merchantUserAmountDao.incrValue(
                    MerchantUserAmountKey.builder().goodsId(valObj.getGoodsId()).build(),
                    cacheKeyValue);
        });
    }

    @Override
    public Mono<Void> addUserBoughtCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> userBoughtGoodsDao.setBit(
                UserBoughtGoodsKey.builder().userId(valObj.getUserId()).build(),
                valObj.getGoodsId(), true));
    }

    @Override
    public Mono<Void> removeUserBoughtCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> userBoughtGoodsDao.setBit(
                UserBoughtGoodsKey.builder().userId(valObj.getUserId()).build(),
                valObj.getGoodsId(), false));
    }

    @Override
    public Mono<Void> removeSpecificationLocked(OrderItemLockValObj valObj) {
        return Mono.fromRunnable(() -> {
            SpecificationLockKey key = SpecificationLockKey.builder().goodsId(valObj.getGoodsId())
                    .itemPosition(valObj.getPosition()).build();
            FortuneSpecificationLockPo value = fortuneSpecificationLockDao
                    .getValue(SpecificationLockKey.builder().itemPosition(valObj.getPosition())
                            .goodsId(valObj.getGoodsId()).build());
            if (value == null || !Objects.equals(value.getUserId(), valObj.getUserId())) {
                return;
            }
            // 如果已经下单不去进行福盒解锁操作
            if (FortuneLockStatusEnum.SELECTABLE.equals(valObj.getLockStatus())
                    && FortuneLockStatusEnum.BUY_LOCKED.equals(value.getLockStatus())) {
                log.info("商品id:{},位置:{}已下单,不进行福盒解锁操作", valObj.getGoodsId(), valObj.getPosition());
                return;
            }
            fortuneSpecificationLockDao.delKey(key);
        });
    }

    @Override
    public Mono<Void> addFortunePositionCache(OrderCacheValObj valObj) {
        return Mono.fromRunnable(() -> fortunePositionDao.addValue(
                FortunePositionKey.builder().goodsId(valObj.getGoodsId())
                        .itemPosition(valObj.getItemPosition()).build(),
                valObj.getSpecificationId()));
    }

    @Override
    public Mono<Void> addAnonymousStatusCache(OrderCacheValObj valObj) {
        return Mono
                .fromCallable(() -> orderAnonymousDao.setBit(OrderAnonymousKey.builder().build(),
                        valObj.getOrderNo(),
                        CommonStatusEnum.ENABLE.getCode().equals(valObj.getAnonymousStatus())))
                .then();
    }
}

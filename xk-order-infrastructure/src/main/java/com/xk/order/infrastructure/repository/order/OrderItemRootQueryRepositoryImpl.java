package com.xk.order.infrastructure.repository.order;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.infrastructure.cache.dao.merchant.MerchantUserAmountDao;
import com.xk.infrastructure.cache.key.merchant.MerchantUserAmountKey;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.order.id.OrderItemIdentifier;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.infrastructure.cache.dao.order.FortuneSpecificationLockDao;
import com.xk.order.infrastructure.cache.dao.order.UserGoodsBuyCountDao;
import com.xk.order.infrastructure.cache.dao.order.UserGoodsBuyTimeDao;
import com.xk.order.infrastructure.cache.key.order.OrderUserBuyCountKey;
import com.xk.order.infrastructure.cache.key.order.OrderUserBuyTimeKey;
import com.xk.order.infrastructure.cache.key.order.SpecificationLockKey;
import com.xk.order.infrastructure.cache.po.order.FortuneSpecificationLockPo;
import com.xk.order.infrastructure.data.persistence.order.OOrderGiftMapper;
import com.xk.order.infrastructure.data.persistence.order.OOrderItemMapper;
import com.xk.order.infrastructure.data.po.order.OOrderGift;
import com.xk.order.infrastructure.data.po.order.OOrderItem;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderItemRootQueryRepositoryImpl implements OrderItemRootQueryRepository {

    private final FortuneSpecificationLockDao fortuneSpecificationLockDao;
    private final OOrderItemMapper oOrderItemMapper;
    private final UserGoodsBuyCountDao userGoodsBuyCountDao;
    private final UserGoodsBuyTimeDao userGoodsBuyTimeDao;
    private final MerchantUserAmountDao merchantUserAmountDao;
    private final Converter converter;
    private final OOrderGiftMapper oOrderGiftMapper;

    @Override
    public Mono<OrderItemLockValObj> querySpecificationLocked(OrderItemLockValObj valObj) {
        return Mono.fromCallable(() -> {
            FortuneSpecificationLockPo value = fortuneSpecificationLockDao
                    .getValue(SpecificationLockKey.builder().itemPosition(valObj.getPosition())
                            .goodsId(valObj.getGoodsId()).build());
            if (value != null) {
                return OrderItemLockValObj.builder().userId(value.getUserId())
                        .lockStatus(value.getLockStatus()).position(valObj.getPosition())
                        .goodsId(valObj.getGoodsId()).build();
            }
            return OrderItemLockValObj.builder().position(valObj.getPosition())
                    .goodsId(valObj.getGoodsId()).build();
        });
    }

    @Override
    public Flux<OrderItemRoot> searchRootByOrderNo(OrderIdentifier identifier) {
        return Mono
                .defer(() -> Mono.zip(
                        find(identifier,
                                id -> oOrderItemMapper.selectByOrderNo(
                                        OOrderItem.builder().orderNo(id.getOrderNo()).build()),
                                OrderItemEntity.class, converter::convert).collectList(),
                        find(identifier,
                                id -> oOrderGiftMapper.selectByOrderNo(
                                        OOrderGift.builder().orderNo(id.getOrderNo()).build()),
                                OrderGiftEntity.class, converter::convert).collectList()))
                .flatMapMany(tuple -> {
                    List<OrderItemEntity> orderItemEntityList = tuple.getT1();
                    Map<OrderItemIdentifier, List<OrderGiftEntity>> orderGiftEntityMap =
                            tuple.getT2().stream().collect(
                                    Collectors.groupingBy(OrderGiftEntity::getOrderItemId));
                    return Flux.fromIterable(orderItemEntityList)
                            .map(entity -> OrderItemRoot.builder()
                                    .identifier(entity.getIdentifier()).orderItemEntity(entity)
                                    .orderGiftEntityList(
                                            orderGiftEntityMap.get(entity.getIdentifier()))
                                    .build());
                });
    }

    @Override
    public Flux<OrderItemEntity> searchEntityByOrderNo(OrderIdentifier identifier) {
        return find(identifier,
                id -> oOrderItemMapper
                        .selectByOrderNo(OOrderItem.builder().orderNo(id.getOrderNo()).build()),
                OrderItemEntity.class, converter::convert);
    }

    @Override
    public Mono<Long> getUserBuyCount(OrderCacheValObj cacheValObj) {
        return Mono.fromCallable(() -> {
            OrderUserBuyCountKey key =
                    OrderUserBuyCountKey.builder().goodsId(cacheValObj.getGoodsId()).build();
            Double value = userGoodsBuyCountDao.getValue(key, cacheValObj.getUserId());
            return value == null ? 0L : value.longValue();
        });
    }

    @Override
    public Mono<Long> getUserBuyTimeCount(OrderCacheValObj cacheValObj) {
        return Mono.fromCallable(() -> {
            OrderUserBuyTimeKey key = OrderUserBuyTimeKey.builder()
                    .goodsId(cacheValObj.getGoodsId()).userId(cacheValObj.getUserId()).build();
            Date date = new Date();
            List<Integer> values = userGoodsBuyTimeDao.getValues(key,
                    DateUtils.addHours(date, -cacheValObj.getLimitTimeInterval()).getTime(),
                    date.getTime());
            if (CollectionUtils.isEmpty(values)) {
                return 0L;
            }
            return values.stream().mapToLong(Integer::longValue).sum();
        });
    }

    @Override
    public Mono<OrderGiftEntity> findGiftEntityByGoodsIdAndBusinessId(OrderGiftEntity entity) {
        return get(entity,
                data -> oOrderGiftMapper
                        .selectByGoodsIdAndBusinessId(converter.convert(data, OOrderGift.class)),
                OrderGiftEntity.class, converter::convert);
    }

    @Override
    public Flux<OrderGiftEntity> findGiftEntityByGoodsId(OrderGiftEntity entity) {
        return find(entity,
                data -> oOrderGiftMapper.selectByGoodsId(converter.convert(data, OOrderGift.class)),
                OrderGiftEntity.class, converter::convert);
    }

    @Override
    public Flux<OrderGiftEntity> searchGiftByPage(Pagination pagination) {
        return this.search(pagination, oOrderGiftMapper::selectByPage, OrderGiftEntity.class,
                converter::convert);
    }

    @Override
    public Mono<Long> getUserBuyAmount(OrderCacheValObj cacheValObj) {
        return Mono.fromCallable(() -> {
            MerchantUserAmountKey key =
                    MerchantUserAmountKey.builder().goodsId(cacheValObj.getGoodsId()).build();
            Double value = merchantUserAmountDao.getValue(key, cacheValObj.getUserId());
            return value == null ? 0L : value.longValue();
        });
    }

    @Override
    public Flux<OrderCacheValObj> getUserBuyAmount(OrderCacheValObj cacheValObj, Integer count) {
        return Mono.fromCallable(() -> {
            MerchantUserAmountKey key =
                    MerchantUserAmountKey.builder().goodsId(cacheValObj.getGoodsId()).build();
            return merchantUserAmountDao.getValues(key, 0L, (long) count, true);
        }).flatMapMany(Flux::fromIterable).map(keyValue -> OrderCacheValObj.builder()
                .userId(keyValue.getValue()).amount(keyValue.getScore().longValue()).build());
    }

    @Override
    public Flux<OrderGiftEntity> searchGiftEntityByOrderNo(OrderIdentifier identifier) {
        return find(identifier,
                id -> oOrderGiftMapper
                        .selectByOrderNo(OOrderGift.builder().orderNo(id.getOrderNo()).build()),
                OrderGiftEntity.class, converter::convert);
    }

    @Override
    public Flux<OrderGiftEntity> searchLimitGiftEntityByOrderNo(OrderIdentifier identifier) {
        return find(identifier,
                id -> oOrderGiftMapper.selectLimitByOrderNo(
                        OOrderGift.builder().orderNo(id.getOrderNo()).build()),
                OrderGiftEntity.class, converter::convert);
    }

    @Override
    public Mono<Long> searchGiftByCount(OrderGiftEntity entity) {
        return Mono.just(
                oOrderGiftMapper.searchGiftByCount(OOrderGift.builder().goodsId(entity.getGoodsId())
                        .createId(entity.getCreateValObj().getCreateId()).build()));
    }
}

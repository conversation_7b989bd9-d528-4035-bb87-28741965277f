package com.xk.order.infrastructure.data.persistence.order;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.order.infrastructure.data.po.order.OOrderGift;

/**
 * <AUTHOR>
 * @description 针对表【o_order_gift(订单赠品表)】的数据库操作Mapper
 * @createDate 2025-08-01 18:10:30
 * @Entity com.xk.order.infrastructure.data.po.order.OOrderGift
 */
@Repository
@Table("o_order_gift")
public interface OOrderGiftMapper {

    int deleteByPrimaryKey(OOrderGift record);

    int insert(OOrderGift record);

    int insertSelective(OOrderGift record);

    OOrderGift selectByPrimaryKey(OOrderGift record);

    int updateByPrimaryKeySelective(OOrderGift record);

    int updateByPrimaryKey(OOrderGift record);

    OOrderGift selectByGoodsIdAndBusinessId(OOrderGift record);

    List<OOrderGift> selectByGoodsId(OOrderGift record);

    List<OOrderGift> selectByPage(Pagination pagination);

    List<OOrderGift> selectByOrderNo(OOrderGift record);

    List<OOrderGift> selectLimitByOrderNo(OOrderGift record);

    Long searchGiftByCount(OOrderGift record);

    int batchInsert(List<OOrderGift> records);
}

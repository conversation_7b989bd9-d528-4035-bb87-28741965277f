package com.xk.order.infrastructure.cache.key.payment;

import com.myco.mydata.infrastructure.cache.key.AbstractCacheKey;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class RefundRepetitionKey extends AbstractCacheKey {
    private String orderNo;
    private String payNo;

    @Override
    public String getKey(Serializable... keys) {
        return super.getKey(orderNo+"-"+payNo);
    }

    @Override
    public Boolean isExpires() {
        return Boolean.TRUE;
    }

    @Override
    public Long getSeconds(Long ttl) {
        return 10 * 60L;
    }
}

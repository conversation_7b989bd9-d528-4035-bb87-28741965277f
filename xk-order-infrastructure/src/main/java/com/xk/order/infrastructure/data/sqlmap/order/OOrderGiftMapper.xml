<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.order.infrastructure.data.persistence.order.OOrderGiftMapper">

    <resultMap id="BaseResultMap" type="com.xk.order.infrastructure.data.po.order.OOrderGift">
        <id property="orderGiftId" column="order_gift_id" jdbcType="BIGINT"/>
        <result property="orderId" column="order_id" jdbcType="BIGINT"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="goodsId" column="goods_id" jdbcType="BIGINT"/>
        <result property="corpId" column="corp_id" jdbcType="BIGINT"/>
        <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
        <result property="specificationId" column="specification_id" jdbcType="BIGINT"/>
        <result property="businessGroupId" column="business_group_id" jdbcType="BIGINT"/>
        <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
        <result property="giftPrizeStatus" column="gift_prize_status" jdbcType="TINYINT"/>
        <result property="giftBusinessType" column="gift_business_type" jdbcType="TINYINT"/>
        <result property="giftBusinessId" column="gift_business_id" jdbcType="BIGINT"/>
        <result property="giftBusinessName" column="gift_business_name" jdbcType="VARCHAR"/>
        <result property="giftGroupAddr" column="gift_group_addr" jdbcType="VARCHAR"/>
        <result property="giftShowAddr" column="gift_show_addr" jdbcType="VARCHAR"/>
        <result property="giftAddr" column="gift_addr" jdbcType="VARCHAR"/>
        <result property="color" column="color" jdbcType="VARCHAR"/>
        <result property="limitEdition" column="limit_edition" jdbcType="VARCHAR"/>
        <result property="distributionId" column="distribution_id" jdbcType="BIGINT"/>
        <result property="randomItemId" column="random_item_id" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_gift_id
        ,order_id,order_no,
        goods_id,corp_id,order_item_id,
        specification_id,business_group_id,category_name,
        gift_prize_status,gift_business_type,gift_business_id,
        gift_business_name,gift_group_addr,gift_show_addr,
        gift_addr,color,limit_edition,
        distribution_id,random_item_id,deleted,
        create_id,create_time,update_id,
        update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.order.infrastructure.data.po.order.OOrderGift"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_gift
        where order_gift_id = #{orderGiftId,jdbcType=BIGINT}
    </select>
    <select id="selectByGoodsIdAndBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_gift
        where goods_id = #{goodsId,jdbcType=BIGINT} AND gift_business_id = #{giftBusinessId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.xk.order.infrastructure.data.po.order.OOrderGift">
        delete
        from o_order_gift
        where order_gift_id = #{orderGiftId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="order_gift_id" keyProperty="orderGiftId"
            parameterType="com.xk.order.infrastructure.data.po.order.OOrderGift" useGeneratedKeys="true">
        insert into o_order_gift
        ( order_gift_id, order_id, order_no
        , goods_id, corp_id, order_item_id
        , specification_id, business_group_id, category_name
        , gift_prize_status, gift_business_type, gift_business_id
        , gift_business_name, gift_group_addr, gift_show_addr
        , gift_addr, color, limit_edition
        , distribution_id, random_item_id, deleted
        , create_id, create_time, update_id
        , update_time)
        values ( #{orderGiftId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}
               , #{goodsId,jdbcType=BIGINT}, #{corpId,jdbcType=BIGINT}, #{orderItemId,jdbcType=BIGINT}
               , #{specificationId,jdbcType=BIGINT}, #{businessGroupId,jdbcType=BIGINT}
               , #{categoryName,jdbcType=VARCHAR}
               , #{giftPrizeStatus,jdbcType=TINYINT}, #{giftBusinessType,jdbcType=TINYINT}
               , #{giftBusinessId,jdbcType=BIGINT}
               , #{giftBusinessName,jdbcType=VARCHAR}, #{giftGroupAddr,jdbcType=VARCHAR}
               , #{giftShowAddr,jdbcType=VARCHAR}
               , #{giftAddr,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, #{limitEdition,jdbcType=VARCHAR}
               , #{distributionId,jdbcType=BIGINT}, #{randomItemId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}
               , #{createId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=BIGINT}
               , #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="order_gift_id" keyProperty="orderGiftId"
            parameterType="com.xk.order.infrastructure.data.po.order.OOrderGift" useGeneratedKeys="true">
        insert into o_order_gift
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderGiftId != null">order_gift_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="corpId != null">corp_id,</if>
            <if test="orderItemId != null">order_item_id,</if>
            <if test="specificationId != null">specification_id,</if>
            <if test="businessGroupId != null">business_group_id,</if>
            <if test="categoryName != null">category_name,</if>
            <if test="giftPrizeStatus != null">gift_prize_status,</if>
            <if test="giftBusinessType != null">gift_business_type,</if>
            <if test="giftBusinessId != null">gift_business_id,</if>
            <if test="giftBusinessName != null">gift_business_name,</if>
            <if test="giftGroupAddr != null">gift_group_addr,</if>
            <if test="giftShowAddr != null">gift_show_addr,</if>
            <if test="giftAddr != null">gift_addr,</if>
            <if test="color != null">color,</if>
            <if test="limitEdition != null">limit_edition,</if>
            <if test="distributionId != null">distribution_id,</if>
            <if test="randomItemId != null">random_item_id,</if>
            <if test="deleted != null">deleted,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderGiftId != null">#{orderGiftId,jdbcType=BIGINT},</if>
            <if test="orderId != null">#{orderId,jdbcType=BIGINT},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=BIGINT},</if>
            <if test="corpId != null">#{corpId,jdbcType=BIGINT},</if>
            <if test="orderItemId != null">#{orderItemId,jdbcType=BIGINT},</if>
            <if test="specificationId != null">#{specificationId,jdbcType=BIGINT},</if>
            <if test="businessGroupId != null">#{businessGroupId,jdbcType=BIGINT},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="giftPrizeStatus != null">#{giftPrizeStatus,jdbcType=TINYINT},</if>
            <if test="giftBusinessType != null">#{giftBusinessType,jdbcType=TINYINT},</if>
            <if test="giftBusinessId != null">#{giftBusinessId,jdbcType=BIGINT},</if>
            <if test="giftBusinessName != null">#{giftBusinessName,jdbcType=VARCHAR},</if>
            <if test="giftGroupAddr != null">#{giftGroupAddr,jdbcType=VARCHAR},</if>
            <if test="giftShowAddr != null">#{giftShowAddr,jdbcType=VARCHAR},</if>
            <if test="giftAddr != null">#{giftAddr,jdbcType=VARCHAR},</if>
            <if test="color != null">#{color,jdbcType=VARCHAR},</if>
            <if test="limitEdition != null">#{limitEdition,jdbcType=VARCHAR},</if>
            <if test="distributionId != null">#{distributionId,jdbcType=BIGINT},</if>
            <if test="randomItemId != null">#{randomItemId,jdbcType=BIGINT},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.order.infrastructure.data.po.order.OOrderGift">
        update o_order_gift
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="orderItemId != null">
                order_item_id = #{orderItemId,jdbcType=BIGINT},
            </if>
            <if test="specificationId != null">
                specification_id = #{specificationId,jdbcType=BIGINT},
            </if>
            <if test="businessGroupId != null">
                business_group_id = #{businessGroupId,jdbcType=BIGINT},
            </if>
            <if test="categoryName != null">
                category_name = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="giftPrizeStatus != null">
                gift_prize_status = #{giftPrizeStatus,jdbcType=TINYINT},
            </if>
            <if test="giftBusinessType != null">
                gift_business_type = #{giftBusinessType,jdbcType=TINYINT},
            </if>
            <if test="giftBusinessId != null">
                gift_business_id = #{giftBusinessId,jdbcType=BIGINT},
            </if>
            <if test="giftBusinessName != null">
                gift_business_name = #{giftBusinessName,jdbcType=VARCHAR},
            </if>
            <if test="giftGroupAddr != null">
                gift_group_addr = #{giftGroupAddr,jdbcType=VARCHAR},
            </if>
            <if test="giftShowAddr != null">
                gift_show_addr = #{giftShowAddr,jdbcType=VARCHAR},
            </if>
            <if test="giftAddr != null">
                gift_addr = #{giftAddr,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="limitEdition != null">
                limit_edition = #{limitEdition,jdbcType=VARCHAR},
            </if>
            <if test="distributionId != null">
                distribution_id = #{distributionId,jdbcType=BIGINT},
            </if>
            <if test="randomItemId != null">
                random_item_id = #{randomItemId,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_gift_id = #{orderGiftId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.order.infrastructure.data.po.order.OOrderGift">
        update o_order_gift
        set order_id           = #{orderId,jdbcType=BIGINT},
            order_no           = #{orderNo,jdbcType=VARCHAR},
            goods_id           = #{goodsId,jdbcType=BIGINT},
            corp_id            = #{corpId,jdbcType=BIGINT},
            order_item_id      = #{orderItemId,jdbcType=BIGINT},
            specification_id   = #{specificationId,jdbcType=BIGINT},
            business_group_id  = #{businessGroupId,jdbcType=BIGINT},
            category_name      = #{categoryName,jdbcType=VARCHAR},
            gift_prize_status  = #{giftPrizeStatus,jdbcType=TINYINT},
            gift_business_type = #{giftBusinessType,jdbcType=TINYINT},
            gift_business_id   = #{giftBusinessId,jdbcType=BIGINT},
            gift_business_name = #{giftBusinessName,jdbcType=VARCHAR},
            gift_group_addr    = #{giftGroupAddr,jdbcType=VARCHAR},
            gift_show_addr     = #{giftShowAddr,jdbcType=VARCHAR},
            gift_addr          = #{giftAddr,jdbcType=VARCHAR},
            color              = #{color,jdbcType=VARCHAR},
            limit_edition      = #{limitEdition,jdbcType=VARCHAR},
            distribution_id    = #{distributionId,jdbcType=BIGINT},
            random_item_id     = #{randomItemId,jdbcType=BIGINT},
            deleted            = #{deleted,jdbcType=TINYINT},
            create_id          = #{createId,jdbcType=BIGINT},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_id          = #{updateId,jdbcType=BIGINT},
            update_time        = #{updateTime,jdbcType=TIMESTAMP}
        where order_gift_id = #{orderGiftId,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into o_order_gift
        ( order_gift_id, order_id, order_no
        , goods_id, corp_id, order_item_id
        , specification_id, business_group_id, category_name
        , gift_prize_status, gift_business_type, gift_business_id
        , gift_business_name, gift_group_addr, gift_show_addr
        , gift_addr, color, limit_edition
        , distribution_id, random_item_id, deleted
        , create_id, create_time, update_id
        , update_time) values
        <foreach collection="records" item="item" separator=",">
            (
            #{item.orderGiftId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=VARCHAR}
            , #{item.goodsId,jdbcType=BIGINT}, #{item.corpId,jdbcType=BIGINT}, #{item.orderItemId,jdbcType=BIGINT}
            , #{item.specificationId,jdbcType=BIGINT}, #{item.businessGroupId,jdbcType=BIGINT}
            , #{item.categoryName,jdbcType=VARCHAR}
            , #{item.giftPrizeStatus,jdbcType=TINYINT}, #{item.giftBusinessType,jdbcType=TINYINT}
            , #{item.giftBusinessId,jdbcType=BIGINT}
            , #{item.giftBusinessName,jdbcType=VARCHAR}, #{item.giftGroupAddr,jdbcType=VARCHAR}
            , #{item.giftShowAddr,jdbcType=VARCHAR}
            , #{item.giftAddr,jdbcType=VARCHAR}, #{item.color,jdbcType=VARCHAR}, #{item.limitEdition,jdbcType=VARCHAR}
            , #{item.distributionId,jdbcType=BIGINT}, #{item.randomItemId,jdbcType=BIGINT},
            #{item.deleted,jdbcType=TINYINT}
            , #{item.createId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateId,jdbcType=BIGINT}
            , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="selectByPage" parameterType="com.myco.framework.support.mybatis.Pagination" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_gift
        <where>
            create_id = #{userId}
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="goodsId != null">
                AND goods_id = #{goodsId}
            </if>
            <if test="giftBusinessName != null and giftBusinessName != ''">
                AND gift_business_name LIKE CONCAT('%', #{giftBusinessName}, '%')
            </if>
            <if test="giftPrizeStatus != null">
                AND gift_prize_status = #{giftPrizeStatus}
            </if>
            <if test="onlyLimitEdition != null and onlyLimitEdition == 1">
                AND limit_edition != '无限编'
            </if>
        </where>
    </select>
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_gift
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>
    <select id="selectLimitByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_gift
        where order_no = #{orderNo,jdbcType=VARCHAR}
        LIMIT 4
    </select>
    <select id="selectByGoodsId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_gift
        where goods_id = #{goodsId,jdbcType=BIGINT}
    </select>
    <select id="searchGiftByCount" resultType="java.lang.Long">
        select count(1)
        from o_order_gift
        where goods_id = #{goodsId}
          and create_id = #{createId}
    </select>
</mapper>

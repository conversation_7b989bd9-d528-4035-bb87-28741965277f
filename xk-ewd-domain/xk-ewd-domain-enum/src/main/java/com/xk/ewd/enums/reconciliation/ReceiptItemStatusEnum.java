package com.xk.ewd.enums.reconciliation;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
@Getter
@AllArgsConstructor
public enum ReceiptItemStatusEnum {

    NORMAL(1,"正常"),
    ABNORMAL(2,"异常"),  ;

    private final Integer code;
    private final String msg;


    private static final Map<Integer, ReceiptItemStatusEnum> MAP = EnumUtil.getEnumMap(ReceiptItemStatusEnum.class,
     ReceiptItemStatusEnum::getCode);

    public static ReceiptItemStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

package com.xk.ewd.enums.financial;

import java.util.Map;

import com.myco.mydata.domain.enums.util.EnumUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 领域枚举示例
 */
@Getter
@AllArgsConstructor
public enum PayTypeEnum {

    BANK_CARD(1, "银行卡"),
    ALIPAY(2, "支付宝"),
    WECHAT_PAY(3,"微信支付");

    private static final Map<Integer, PayTypeEnum> MAP =
            EnumUtil.getEnumMap(PayTypeEnum.class, PayTypeEnum::getCode);
    private final int code;
    private final String desc;

    public static PayTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

package com.xk.ewd.enums.reconciliation;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
/**
 * 订单消息枚举
 */
@Getter
@AllArgsConstructor
public enum CorrectedResultEnum {

    CROSS_DAY_REFUND(1, "退款未到账(跨天交易)"),
    ONLY_TP_EXIST(2, "星卡系统缺失"),
    ONLY_SYSTEM_EXIST(3, "第三方缺失")  ;

    private final Integer code;
    private final String msg;


    private static final Map<Integer, CorrectedResultEnum> MAP = EnumUtil.getEnumMap(CorrectedResultEnum.class, CorrectedResultEnum::getCode);

    public static CorrectedResultEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

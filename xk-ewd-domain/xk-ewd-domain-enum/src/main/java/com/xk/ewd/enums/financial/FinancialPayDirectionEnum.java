package com.xk.ewd.enums.financial;

import java.util.Map;

import com.myco.mydata.domain.enums.util.EnumUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 领域枚举示例
 */
@Getter
@AllArgsConstructor
public enum FinancialPayDirectionEnum {

    INCOME(1, "入账"),
    PAYOUT(2, "出账");

    private static final Map<Integer, FinancialPayDirectionEnum> MAP =
            EnumUtil.getEnumMap(FinancialPayDirectionEnum.class, FinancialPayDirectionEnum::getCode);
    private final int code;
    private final String desc;

    public static FinancialPayDirectionEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

package com.xk.ewd.enums.financial;

import java.util.Map;

import com.myco.mydata.domain.enums.util.EnumUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FinancialPlatformTypeEnum {

    ALIPAY(301,"支付宝"),
    HUI_FU(305,"汇付");

    private static final Map<Integer, FinancialPlatformTypeEnum> MAP =
            EnumUtil.getEnumMap(FinancialPlatformTypeEnum.class, FinancialPlatformTypeEnum::getCode);
    private final int code;
    private final String desc;

    public static FinancialPlatformTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

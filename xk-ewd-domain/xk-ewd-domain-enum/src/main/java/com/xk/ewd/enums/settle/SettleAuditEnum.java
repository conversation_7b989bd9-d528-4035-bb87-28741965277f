package com.xk.ewd.enums.settle;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
@Getter
@AllArgsConstructor
public enum SettleAuditEnum {

    PENDING(1,"待审核"),
    APPROVED(2,"审核通过"),
    FROZEN(3,"冻结");

    private final Integer code;
    private final String msg;


    private static final Map<Integer, SettleAuditEnum> MAP = EnumUtil.getEnumMap(SettleAuditEnum.class,
     SettleAuditEnum::getCode);

    public static SettleAuditEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

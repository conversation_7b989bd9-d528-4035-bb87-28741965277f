package com.xk.ewd.enums.reconciliation;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
@Getter
@AllArgsConstructor
public enum ReconciliationAuditEnum {

    PENDING(1,"待审核"),
    APPROVED(2,"审核通过"),  ;

    private final Integer code;
    private final String msg;


    private static final Map<Integer, ReconciliationAuditEnum> MAP = EnumUtil.getEnumMap(ReconciliationAuditEnum.class,
     ReconciliationAuditEnum::getCode);

    public static ReconciliationAuditEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}

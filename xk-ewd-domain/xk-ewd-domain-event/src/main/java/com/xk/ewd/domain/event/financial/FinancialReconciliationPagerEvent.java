package com.xk.ewd.domain.event.financial;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_EWD, domainName = DomainNameEnum.EWD)
public class FinancialReconciliationPagerEvent extends AbstractCommonsDomainEvent
        implements Serializable {

    /**
     * 页码
     */
    private final Integer pageNum;

    /**
     * 页面数量
     */
    private final Integer pageSize;

    /**
     * 对账日期
     */
    private final Date reconciliationDate;

    @Builder
    public FinancialReconciliationPagerEvent(@NonNull Long identifier, Map<String, Object> context,
            Integer pageNum, Integer pageSize, Date reconciliationDate) {
        super(identifier, context);
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.reconciliationDate = reconciliationDate;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}

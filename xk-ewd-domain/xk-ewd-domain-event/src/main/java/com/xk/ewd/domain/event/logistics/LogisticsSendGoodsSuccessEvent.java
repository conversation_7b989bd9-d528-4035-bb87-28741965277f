package com.xk.ewd.domain.event.logistics;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_EWD, domainName = DomainNameEnum.EWD)
public class LogisticsSendGoodsSuccessEvent extends AbstractCommonsDomainEvent implements Serializable {

    /**
     * 商品id
     */
    private final Long goodsId;


    @Builder
    public LogisticsSendGoodsSuccessEvent(@NonNull Long identifier, Map<String, Object> context, Long goodsId) {
        super(identifier, context);
        this.goodsId = goodsId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}

package com.xk.ewd.domain.model.settle.identifier;

import java.util.Date;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Getter
@Builder
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SettleIdentifier implements Identifier<SettleIdentifier> {

    private final Date settleDate;

    @Override
    public @NonNull SettleIdentifier getIdentifier() {
        return this;
    }
}

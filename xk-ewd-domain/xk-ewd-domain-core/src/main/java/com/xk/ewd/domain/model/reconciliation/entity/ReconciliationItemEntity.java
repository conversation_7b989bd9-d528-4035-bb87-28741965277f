package com.xk.ewd.domain.model.reconciliation.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.domain.model.common.CreateValObj;
import com.xk.domain.model.common.UpdateValObj;
import com.xk.ewd.domain.model.financial.identifier.FinancialTransactionIdentifier;
import com.xk.ewd.domain.model.reconciliation.id.ReconciliationItemIdentifier;
import com.xk.ewd.enums.financial.FinancialPayDirectionEnum;
import com.xk.ewd.enums.financial.PayTypeEnum;
import com.xk.ewd.enums.reconciliation.CorrectedResultEnum;
import com.xk.ewd.enums.reconciliation.ReceiptItemStatusEnum;

import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReconciliationItemEntity implements Entity<ReconciliationItemIdentifier> {

    /**
     * 对账单据标识符
     */
    private Long reconciliationItemId;

    /**
     * 三方流水标识符
     */
    private FinancialTransactionIdentifier financialTransactionId;

    /**
     * 星卡内部流水号
     */
    private String payNo;

    /**
     * 应收金额
     */
    private Long totalAmount;

    /**
     * 实收金额
     */
    private Long actualAmount;

    /**
     * 三方交易金额
     */
    private Long tpAmount;

    /**
     * 交易方向
     */
    private FinancialPayDirectionEnum payDirection;

    /**
     * 支付类型
     */
    private PayTypeEnum payType;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 订单标识符
     */
    private String orderNo;

    /**
     * 商品标识符
     */
    private Long goodsId;

    /**
     * 商户标识符
     */
    private Long corpId;

    /**
     * 交易创建时间
     */
    private Date payCreateTime;

    /**
     * 收款单据状态
     */
    private ReceiptItemStatusEnum receiptItemStatus;

    /**
     * 订正结果
     */
    private CorrectedResultEnum correctedResult;

    /**
     * 备注
     */
    private String remark;

    private CreateValObj createValObj;

    private UpdateValObj updateValObj;

    @Override
    public @NonNull ReconciliationItemIdentifier getIdentifier() {
        return ReconciliationItemIdentifier.builder().reconciliationItemId(reconciliationItemId)
                .build();
    }

    @Override
    public Validatable<ReconciliationItemIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

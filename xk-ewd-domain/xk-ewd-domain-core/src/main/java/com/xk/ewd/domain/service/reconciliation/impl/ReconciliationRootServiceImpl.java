package com.xk.ewd.domain.service.reconciliation.impl;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.ewd.domain.service.reconciliation.ReconciliationRootService;
import com.xk.ewd.domain.support.EwdSequenceEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class ReconciliationRootServiceImpl implements ReconciliationRootService {

    private final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Long> generateId() {
        IdentifierRoot identifierRoot =
                IdentifierRoot.builder().identifier(EwdSequenceEnum.RECONCILIATION_ITEM)
                        .type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) identifierGenerateService.generateIdentifier(identifierRoot));
    }
}

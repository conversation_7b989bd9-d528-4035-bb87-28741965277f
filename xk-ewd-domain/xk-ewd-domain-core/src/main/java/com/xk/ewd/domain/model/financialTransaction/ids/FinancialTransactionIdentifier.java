package com.xk.ewd.domain.model.financialTransaction.ids;

import com.myco.mydata.domain.model.Identifier;

import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinancialTransactionIdentifier implements Identifier<FinancialTransactionIdentifier> {

    /**
     * 交易流水号
     */
    private String financialTransactionId;

    /**
     * 财务平台类型
     */
    private Integer financialPlatformType;

    @NonNull
    @Override
    public FinancialTransactionIdentifier getIdentifier() {
        return FinancialTransactionIdentifier.builder().financialTransactionId(financialTransactionId).financialPlatformType(financialPlatformType).build();
    }

}

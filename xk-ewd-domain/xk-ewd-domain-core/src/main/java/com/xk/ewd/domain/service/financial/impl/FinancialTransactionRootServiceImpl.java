package com.xk.ewd.domain.service.financial.impl;

import java.util.Date;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.ewd.domain.support.EwdSequenceEnum;
import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.domain.repository.financial.FinancialTransactionRootQueryRepository;
import com.xk.ewd.domain.service.financial.FinancialTransactionRootService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class FinancialTransactionRootServiceImpl implements FinancialTransactionRootService {

    private final IdentifierGenerateService identifierGenerateService;

    private final FinancialTransactionRootQueryRepository financialTransactionRootQueryRepository;

    @Override
    public Mono<Long> generateId() {
        return Mono.just((Long) identifierGenerateService
                .generateIdentifier(IdentifierRoot.builder().identifier(EwdSequenceEnum.EWD_RECONCILED)
                        .type(IdentifierGenerateEnum.CACHE).build()));
    }

    @Override
    public Mono<Integer> countCurrentDate(Date date) {
        return null;
    }

    @Override
    public Flux<FinancialTransactionEntity> selectPager(Pagination pagination) {
        return null;
    }

    @Override
    public Mono<FinancialTransactionEntity> findByPayNo(String payNo) {
        return null;
    }
}

package com.xk.ewd.domain.model.financial.identifier;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.enums.financial.FinancialPlatformTypeEnum;

import lombok.*;

@Getter
@Builder
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class FinancialTransactionIdentifier implements Identifier<FinancialTransactionIdentifier> {

    /**
     * 三方流水号
     */
    private final String financialTransactionId;

    /**
     * 财务平台类型
     */
    private final FinancialPlatformTypeEnum financialPlatformType;

    @Override
    public @NonNull FinancialTransactionIdentifier getIdentifier() {
        return this;
    }
}

package com.xk.ewd.domain.model.logistics.valobj;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息
 * 
 * <AUTHOR>
 * @Date 2024/8/6 14:29
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsValueObject {

    /**
     * 商品信息
     */
    private String goodsInfo;

    /**
     * 赠品信息
     */
    private String giftInfo;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 商品购买数
     */
    private String goodsCount;

    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 商品id
     */
    private Long sendGoodsId;

    /**
     * 直播状态
     */
    private Integer liveStatus;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 商品类型
     */
    private List<String> giftAddrList;

}

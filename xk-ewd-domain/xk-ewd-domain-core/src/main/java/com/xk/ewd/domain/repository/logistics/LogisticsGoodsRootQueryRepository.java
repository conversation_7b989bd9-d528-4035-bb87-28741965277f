package com.xk.ewd.domain.repository.logistics;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.dto.logistics.AppSendGoodsDto;
import com.xk.ewd.domain.dto.logistics.PtsGoodsGroupDto;
import com.xk.ewd.domain.dto.logistics.PtsGroupDto;
import com.xk.ewd.domain.dto.logistics.SendGoodsDto;

import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface LogisticsGoodsRootQueryRepository extends IQueryRepository {

    Flux<PtsGoodsGroupDto> ptsGoodsGroup(Pagination pagination);

    Flux<PtsGroupDto> ptsGroup(Pagination pagination);

    Flux<AppSendGoodsDto> sendGoodsQuery(SendGoodsDto sendGoodsDto);

    Flux<PtsGoodsGroupDto> ptsGoodsLiveGroup(Pagination pagination);

    Mono<Integer> queryGoodsSendSuccess(LogisticsOrderRoot logisticsGoodsRoot);

    Mono<Long> selectGoodsInfoById(LongIdentifier longIdentifier);
}

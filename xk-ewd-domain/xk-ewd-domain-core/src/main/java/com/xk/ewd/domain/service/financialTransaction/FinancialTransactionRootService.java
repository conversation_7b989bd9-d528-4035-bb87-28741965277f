package com.xk.ewd.domain.service.financialTransaction;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionRoot;
import com.xk.ewd.domain.model.goods.GoodsEwdRoot;
import com.xk.ewd.domain.model.goods.entity.GoodsEwdEntity;
import com.xk.ewd.domain.model.goods.valobj.StatusCountValObj;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface FinancialTransactionRootService extends IDomainService<FinancialTransactionRoot> {

    Mono<Long> generateId();

}

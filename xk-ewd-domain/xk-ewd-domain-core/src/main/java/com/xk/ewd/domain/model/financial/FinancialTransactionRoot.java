package com.xk.ewd.domain.model.financial;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;
import com.xk.ewd.domain.model.financial.identifier.FinancialTransactionIdentifier;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class FinancialTransactionRoot extends DomainRoot<FinancialTransactionIdentifier> {

    private final FinancialTransactionEntity financialTransactionEntity;

    @Builder
    public FinancialTransactionRoot(@NonNull FinancialTransactionIdentifier identifier,
            FinancialTransactionEntity financialTransactionEntity) {
        super(identifier);
        this.financialTransactionEntity = financialTransactionEntity;
    }

    @Override
    public Validatable<FinancialTransactionIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

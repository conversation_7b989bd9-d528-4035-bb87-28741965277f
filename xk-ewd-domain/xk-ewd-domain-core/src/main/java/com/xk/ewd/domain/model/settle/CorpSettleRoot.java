package com.xk.ewd.domain.model.settle;

import java.util.List;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.settle.entity.CorpSettleEntity;
import com.xk.ewd.domain.model.settle.entity.GoodsSettleEntity;
import com.xk.ewd.domain.model.settle.identifier.CorpSettleIdentifier;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class CorpSettleRoot extends DomainRoot<CorpSettleIdentifier> {

    private final CorpSettleEntity corpSettleEntity;
    private final List<GoodsSettleEntity> goodsSettleEntityList;

    @Builder
    public CorpSettleRoot(@NonNull CorpSettleIdentifier identifier,
            CorpSettleEntity corpSettleEntity, List<GoodsSettleEntity> goodsSettleEntityList) {
        super(identifier);
        this.corpSettleEntity = corpSettleEntity;
        this.goodsSettleEntityList = goodsSettleEntityList;
    }

    @Override
    public Validatable<CorpSettleIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

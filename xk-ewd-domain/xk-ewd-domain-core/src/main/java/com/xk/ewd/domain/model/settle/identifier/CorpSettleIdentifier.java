package com.xk.ewd.domain.model.settle.identifier;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Getter
@Builder
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CorpSettleIdentifier implements Identifier<CorpSettleIdentifier> {

    private final SettleIdentifier settleIdentifier;

    private final Long corpId;

    @Override
    public @NonNull CorpSettleIdentifier getIdentifier() {
        return this;
    }
}

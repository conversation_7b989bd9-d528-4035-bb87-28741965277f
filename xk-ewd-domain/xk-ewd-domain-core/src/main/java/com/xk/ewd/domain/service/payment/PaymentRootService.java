package com.xk.ewd.domain.service.payment;


import java.util.Date;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.service.IDomainService;
import com.xk.ewd.domain.model.payment.PaymentRoot;
import com.xk.ewd.domain.model.payment.entity.PaymentDetailEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface PaymentRootService extends IDomainService<PaymentRoot> {

    Mono<Void> createRefund(PaymentRoot root);

    Mono<Void> updateRefund(PaymentRoot root);

    /**
     * 统计当日所有流水总数
     * 
     * @param date date
     * @return Mono<Integer>
     */
    Mono<Integer> countCurrentDate(Date date);

    /**
     * 根据支付单号查询流水
     * 
     * @param payNo payNo
     * @return Mono<PaymentDetailEntity>
     */
    Mono<PaymentDetailEntity> findByPayNo(String payNo);

    /**
     * 分页查询流水数据
     * 
     * @param pagination pagination
     * @return Flux<PaymentDetailEntity>
     */
    Flux<PaymentDetailEntity> selectPager(Pagination pagination);
}

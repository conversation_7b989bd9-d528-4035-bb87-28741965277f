package com.xk.ewd.domain.model.settle.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.common.CreateValObj;
import com.xk.ewd.domain.model.settle.identifier.GoodsSettleIdentifier;
import com.xk.ewd.domain.model.settle.identifier.SettleIdentifier;

import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsSettleEntity implements Identifier<GoodsSettleIdentifier> {

    /**
     * 结算日期
     */
    private Date settleDate;

    /**
     * 商户标识符
     */
    private Long corpId;

    /**
     * 商品标识符
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 产品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 销售额
     */
    private Long saleAmount;

    /**
     * 扣减金额
     */
    private Long discountAmount;

    /**
     * 平台优惠金额
     */
    private Long platformCouponAmount;

    /**
     * 商品入账金额
     */
    private Long goodsSettleAmount;

    /**
     * 交易手续费配置（百分比）
     */
    private BigDecimal transactionFee;

    /**
     * 交易手续费金额
     */
    private Long transactionFeeAmount;

    /**
     * 商品收益金额
     */
    private Long goodsEarningAmount;

    /**
     * 商品完成发货时间
     */
    private Date sendGoodsFinishDate;

    private CreateValObj createValObj;

    @Override
    public @NonNull GoodsSettleIdentifier getIdentifier() {
        return GoodsSettleIdentifier.builder()
                .settleIdentifier(SettleIdentifier.builder().settleDate(settleDate).build())
                .goodsId(goodsId).build();
    }
}

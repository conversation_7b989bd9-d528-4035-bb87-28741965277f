package com.xk.ewd.domain.model.financial.ids;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinancialIdentifier implements Identifier<FinancialIdentifier> {

    /**
     * 交易流水号
     */
    private String financialTransactionId;

    /**
     * 财务平台类型
     */
    private Integer financialPlatformType;

    @NonNull
    @Override
    public FinancialIdentifier getIdentifier() {
        return FinancialIdentifier.builder().financialTransactionId(financialTransactionId).financialPlatformType(financialPlatformType).build();
    }

}

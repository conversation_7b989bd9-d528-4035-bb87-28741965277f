package com.xk.ewd.domain.model.settle.identifier;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Getter
@Builder
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GoodsSettleIdentifier implements Identifier<GoodsSettleIdentifier> {

    private final SettleIdentifier settleIdentifier;

    private final Long goodsId;

    @Override
    public @NonNull GoodsSettleIdentifier getIdentifier() {
        return this;
    }
}

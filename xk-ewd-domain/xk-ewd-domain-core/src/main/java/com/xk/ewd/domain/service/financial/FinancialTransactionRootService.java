package com.xk.ewd.domain.service.financial;

import java.util.Date;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface FinancialTransactionRootService {

    Mono<Long> generateId();

    /**
     * 统计当日所有流水总数
     * 
     * @param date date
     * @return Mono<Integer>
     */
    Mono<Integer> countCurrentDate(Date date);

    /**
     * 分页查询财务流水
     * 
     * @param pagination pagination
     * @return Flux<FinancialTransactionEntity>
     */
    Flux<FinancialTransactionEntity> selectPager(Pagination pagination);

    /**
     * 根据订单号查询
     * 
     * @param payNo payNo
     * @return Mono<FinancialTransactionEntity>
     */
    Mono<FinancialTransactionEntity> findByPayNo(String payNo);
}

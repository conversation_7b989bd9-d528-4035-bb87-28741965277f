package com.xk.ewd.domain.model.settle.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.common.CreateValObj;
import com.xk.ewd.domain.model.settle.identifier.SettleIdentifier;
import com.xk.ewd.domain.model.settle.valobj.SettleAuditValObj;

import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettleEntity implements Identifier<SettleIdentifier> {

    /**
     * 清算日期
     */
    private Date settleDate;

    /**
     * 结算总金额
     */
    private Long settleTotalAmount;

    /**
     * 结算审核值对象
     */
    private SettleAuditValObj settleAuditValObj;

    private CreateValObj createValObj;

    @Override
    public @NonNull SettleIdentifier getIdentifier() {
        return SettleIdentifier.builder().settleDate(settleDate).build();
    }
}

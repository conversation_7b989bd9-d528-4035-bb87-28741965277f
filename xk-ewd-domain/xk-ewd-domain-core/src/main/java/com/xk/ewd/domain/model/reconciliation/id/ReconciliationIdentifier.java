package com.xk.ewd.domain.model.reconciliation.id;

import java.util.Date;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Getter
@Builder
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ReconciliationIdentifier implements Identifier<ReconciliationIdentifier> {

    /**
     * 对账日期
     */
    private final Date reconciliationDate;

    @Override
    public @NonNull ReconciliationIdentifier getIdentifier() {
        return this;
    }
}

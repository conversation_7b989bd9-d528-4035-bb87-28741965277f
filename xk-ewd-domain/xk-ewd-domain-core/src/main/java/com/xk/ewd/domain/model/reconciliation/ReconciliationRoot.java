package com.xk.ewd.domain.model.reconciliation;

import java.util.List;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationEntity;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationItemEntity;
import com.xk.ewd.domain.model.reconciliation.id.ReconciliationIdentifier;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReconciliationRoot extends DomainRoot<ReconciliationIdentifier> {

    private final ReconciliationEntity reconciliationEntity;

    private final List<ReconciliationItemEntity> reconciliationItemEntityList;

    @Builder
    public ReconciliationRoot(@NonNull ReconciliationIdentifier identifier,
            ReconciliationEntity reconciliationEntity,
            List<ReconciliationItemEntity> reconciliationItemEntityList) {
        super(identifier);
        this.reconciliationEntity = reconciliationEntity;
        this.reconciliationItemEntityList = reconciliationItemEntityList;
    }

    @Override
    public Validatable<ReconciliationIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

    public static final class Constant {
        public static final String DAILY_PATTERN = "yyyyMMdd";

        private Constant() {}
    }
}

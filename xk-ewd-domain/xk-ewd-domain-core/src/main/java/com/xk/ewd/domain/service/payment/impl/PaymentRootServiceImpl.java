package com.xk.ewd.domain.service.payment.impl;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.ewd.domain.model.payment.PaymentRoot;
import com.xk.ewd.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.domain.repository.payment.PaymentRootQueryRepository;
import com.xk.ewd.domain.repository.payment.PaymentRootRepository;
import com.xk.ewd.domain.service.payment.PaymentRootService;
import com.xk.ewd.domain.support.EwdSequenceEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class PaymentRootServiceImpl implements PaymentRootService {

    private final PaymentRootRepository paymentRootRepository;
    private final PaymentRootQueryRepository paymentRootQueryRepository;
    private final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Void> createRefund(PaymentRoot root) {
        RefundEntity refundEntity = root.getRefundEntity();
        refundEntity.setCreateTime(new Date());
        refundEntity.setDeleted(CommonStatusEnum.DISABLE);
        return paymentRootRepository.insertRefund(refundEntity);
    }

    @Override
    public Mono<Void> updateRefund(PaymentRoot root) {
        return paymentRootRepository.updateRefund(root.getRefundEntity());
    }

    @Override
    public Mono<Integer> countCurrentDate(Date date) {
        return paymentRootQueryRepository
                .countCurrentDate(PaymentDetailEntity.builder().createTime(date).build());
    }

    @Override
    public Mono<PaymentDetailEntity> findByPayNo(String payNo) {
        return paymentRootQueryRepository
                .findByPayNo(PaymentDetailEntity.builder().payNo(payNo).build());
    }

    @Override
    public Flux<PaymentDetailEntity> selectPager(Pagination pagination) {
        return null;
    }

    private Mono<Long> generateId(EwdSequenceEnum orderSequence) {
        return Mono.just((Long) identifierGenerateService.generateIdentifier(IdentifierRoot
                .builder().identifier(orderSequence).type(IdentifierGenerateEnum.CACHE).build()));
    }
}

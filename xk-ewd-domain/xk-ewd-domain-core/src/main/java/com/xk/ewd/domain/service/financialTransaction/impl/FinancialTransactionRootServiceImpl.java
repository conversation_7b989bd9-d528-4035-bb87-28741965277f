package com.xk.ewd.domain.service.financialTransaction.impl;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.ewd.domain.service.financialTransaction.FinancialTransactionRootService;
import com.xk.ewd.domain.support.EwdSequenceEnum;
import org.springframework.stereotype.Service;

import com.xk.ewd.domain.model.goods.entity.GoodsEwdEntity;
import com.xk.ewd.domain.model.goods.valobj.StatusCountValObj;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootQueryRepository;
import com.xk.ewd.domain.service.goods.GoodsEwdRootService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/6 20:20
 */
@Service
@RequiredArgsConstructor
public class FinancialTransactionRootServiceImpl implements FinancialTransactionRootService {

    private final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Long> generateId() {
        return Mono.just((Long) identifierGenerateService
                .generateIdentifier(IdentifierRoot.builder().identifier(EwdSequenceEnum.EWD_RECONCILED)
                        .type(IdentifierGenerateEnum.CACHE).build()));
    }
}

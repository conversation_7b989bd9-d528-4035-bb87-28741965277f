package com.xk.ewd.domain.model.settle.valobj;

import java.util.Date;

import com.xk.ewd.enums.settle.SettleAuditEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettleAuditValObj {

    /**
     * 审核状态
     */
    private SettleAuditEnum auditStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核用户名
     */
    private String auditUserName;
}

package com.xk.ewd.domain.repository.financialTransaction;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionEntity;
import com.xk.ewd.domain.model.financialTransaction.FinancialTransactionRoot;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface FinancialTransactionRootQueryRepository extends IQueryRepository {
    /**
     * 根据交易id查询交易记录
     *
     * @param financialTransactionRoot financialTransactionRoot
     * @return Flux<FinancialTransactionRoot>
     */
    Mono<FinancialTransactionEntity> selectByTransactionId(FinancialTransactionRoot financialTransactionRoot);
}

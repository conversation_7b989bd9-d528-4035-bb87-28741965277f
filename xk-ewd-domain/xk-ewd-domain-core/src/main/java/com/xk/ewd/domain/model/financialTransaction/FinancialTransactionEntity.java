package com.xk.ewd.domain.model.financialTransaction;

import java.time.LocalDate;
import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.domain.model.common.CreateValObj;
import com.xk.ewd.domain.model.financialTransaction.ids.FinancialTransactionIdentifier;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinancialTransactionEntity implements Entity<FinancialTransactionIdentifier> {

    /**
     * 交易流水号
     */
    private String financialTransactionId;

    /**
     * 财务平台类型
     */
    private Integer financialPlatformType;

    /**
     * 账务日期
     */
    private Date financialDate;

    /**
     * 星卡内部流水号
     */
    private String payNo;

    /**
     * 交易金额
     */
    private Long payAmount;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 入账支付账号
     */
    private String receiveAccount;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 交易方向
     */
    private Integer payDirection;

    /**
     * 交易创建时间
     */
    private Date payCreateTime;

    /**
     * 创建值对象
     */
    private CreateValObj createValObj;

    @Override
    public @NonNull FinancialTransactionIdentifier getIdentifier() {
        return FinancialTransactionIdentifier.builder().financialTransactionId(financialTransactionId).financialPlatformType(financialPlatformType).build();
    }

    @Override
    public Validatable<FinancialTransactionIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}

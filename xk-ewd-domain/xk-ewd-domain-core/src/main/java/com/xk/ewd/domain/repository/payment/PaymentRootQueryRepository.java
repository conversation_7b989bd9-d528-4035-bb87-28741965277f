package com.xk.ewd.domain.repository.payment;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface PaymentRootQueryRepository extends IQueryRepository {

    /**
     * 分页查询退款订单
     * 
     * @param pagination pagination
     * @return Flux<RefundEntity>
     */
    Flux<RefundEntity> searchRefundByPage(Pagination pagination);

    /**
     * 统计当日所有流水总数
     * 
     * @param entity entity
     * @return Mono<Integer>
     */
    Mono<Integer> countCurrentDate(PaymentDetailEntity entity);

    /**
     * 根据支付单号查询流水
     * 
     * @param entity entity
     * @return Mono<PaymentDetailEntity>
     */
    Mono<PaymentDetailEntity> findByPayNo(PaymentDetailEntity entity);
}

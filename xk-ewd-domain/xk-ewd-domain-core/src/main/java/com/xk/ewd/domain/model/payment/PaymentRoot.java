package com.xk.ewd.domain.model.payment;

import java.util.List;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.domain.model.payment.id.PaymentIdentifier;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class PaymentRoot extends DomainRoot<PaymentIdentifier> {

    private final RefundEntity refundEntity;

    private final List<PaymentDetailEntity> paymentDetailEntityList;

    @Builder
    public PaymentRoot(@NonNull PaymentIdentifier identifier, RefundEntity refundEntity,
            List<PaymentDetailEntity> paymentDetailEntityList) {
        super(identifier);
        this.refundEntity = refundEntity;
        this.paymentDetailEntityList = paymentDetailEntityList;
    }

    @Override
    public Validatable<PaymentIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

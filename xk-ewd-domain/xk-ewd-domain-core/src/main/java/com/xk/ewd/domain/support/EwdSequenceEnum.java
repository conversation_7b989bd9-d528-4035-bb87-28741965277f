package com.xk.ewd.domain.support;

import com.myco.mydata.domain.model.identifier.SequenceIdentifier;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum EwdSequenceEnum implements SequenceIdentifier {

    EWD_REFUND("O_REFUND","o_refund","ORefundMapper"),
    RECONCILIATION_ITEM("O_RECONCILIATION_ITEM","o_reconciliation_item","OReconciliationItemMapper"),
    EWD_RECONCILED("F_RECONCILED", "f_reconciled", "FReconciledMapper");

    private final String table;
    private final String pk;
    private final String className;

    @Override
    public @NonNull String getName() {
        return this.name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return this.name();
    }

    @Override
    public String getTable() {
        return this.table;
    }

    public String getPk() {
        return this.pk;
    }

    public String getClassName() {
        return this.className;
    }

}

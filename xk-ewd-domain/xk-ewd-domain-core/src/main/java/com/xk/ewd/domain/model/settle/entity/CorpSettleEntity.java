package com.xk.ewd.domain.model.settle.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.common.CreateValObj;
import com.xk.ewd.domain.model.settle.identifier.CorpSettleIdentifier;
import com.xk.ewd.domain.model.settle.identifier.SettleIdentifier;

import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CorpSettleEntity implements Identifier<CorpSettleIdentifier> {

    /**
     * 结算日期
     */
    private Date settleDate;

    /**
     * 商户标识符
     */
    private Long corpId;

    /**
     * 商户名称
     */
    private String corpName;

    /**
     * 商户结算金额（单位：分）
     */
    private Long corpSettleAmount;

    /**
     * 商户状态
     */
    private Integer corpStatus;

    /**
     * 福盒手续费(%)
     */
    private BigDecimal fortuneBoxTransactionFee;

    /**
     * 边锋盒子手续费(%)
     */
    private BigDecimal edgeBoxTransactionFee;

    /**
     * 搓卡密手续费(%)
     */
    private BigDecimal rubbedCardPackTransactionFee;

    /**
     * 原盒手续费(%)
     */
    private BigDecimal originalBoxTransactionFee;

    /**
     * 预计入账时间
     */
    private Date expectedDepositTime;

    private CreateValObj createValObj;

    @Override
    public @NonNull CorpSettleIdentifier getIdentifier() {
        return CorpSettleIdentifier.builder()
                .settleIdentifier(SettleIdentifier.builder().settleDate(settleDate).build())
                .corpId(corpId).build();
    }
}

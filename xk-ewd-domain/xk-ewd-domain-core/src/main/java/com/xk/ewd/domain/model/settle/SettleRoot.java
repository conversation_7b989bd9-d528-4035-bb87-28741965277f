package com.xk.ewd.domain.model.settle;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.settle.entity.SettleEntity;
import com.xk.ewd.domain.model.settle.identifier.SettleIdentifier;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettleRoot extends DomainRoot<SettleIdentifier> {

    private final SettleEntity settleEntity;

    @Builder
    public SettleRoot(@NonNull SettleIdentifier identifier, SettleEntity settleEntity) {
        super(identifier);
        this.settleEntity = settleEntity;
    }

    @Override
    public Validatable<SettleIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

package com.xk.ewd.domain.model.reconciliation.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Getter
@Builder
//@RequiredArgsConstructor
//@EqualsAndHashCode(callSuper = false)
public class ReconciliationItemIdentifier implements Identifier<ReconciliationItemIdentifier> {

    /**
     * 对账条目id
     */
    private Long reconciliationItemId;

    @Override
    public @NonNull ReconciliationItemIdentifier getIdentifier() {
        return this;
    }
}

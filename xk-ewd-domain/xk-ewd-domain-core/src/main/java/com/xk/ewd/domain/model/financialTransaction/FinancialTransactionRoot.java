package com.xk.ewd.domain.model.financialTransaction;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.financialTransaction.ids.FinancialTransactionIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class FinancialTransactionRoot extends DomainRoot<FinancialTransactionIdentifier> {

    private ReconciledPayPlatformTypeEntity reconciledPayPlatformTypeEntity;
    private FinancialTransactionEntity financialTransactionEntity;

    @Builder
    public FinancialTransactionRoot(FinancialTransactionIdentifier identifier, FinancialTransactionEntity financialTransactionEntity, ReconciledPayPlatformTypeEntity reconciledPayPlatformTypeEntity) {
        super(identifier);
        this.financialTransactionEntity = financialTransactionEntity;
        this.reconciledPayPlatformTypeEntity = reconciledPayPlatformTypeEntity;
    }

    @Override
    public Validatable<FinancialTransactionIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}

package com.xk.ewd.domain.repository.reconciliation;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationEntity;
import com.xk.ewd.domain.model.reconciliation.entity.ReconciliationItemEntity;

import reactor.core.publisher.Flux;

public interface ReconciliationRootQueryRepository extends IQueryRepository {

    /**
     * 分页查询所有对账数据
     * 
     * @param pagination pagination
     * @return Flux<ReconciliationEntity>
     */
    Flux<ReconciliationEntity> selectByPage(Pagination pagination);

    /**
     * 分页查询所有对账条目数据
     * 
     * @param pagination pagination
     * @return Flux<ReconciliationItemEntity>
     */
    Flux<ReconciliationItemEntity> selectItemByPage(Pagination pagination);
}

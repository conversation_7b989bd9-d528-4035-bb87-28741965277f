package com.xk.ewd.domain.model.financial.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.domain.model.common.CreateValObj;
import com.xk.ewd.domain.model.financial.identifier.FinancialTransactionIdentifier;
import com.xk.ewd.enums.financial.FinancialPayDirectionEnum;
import com.xk.ewd.enums.financial.FinancialPlatformTypeEnum;
import com.xk.ewd.enums.financial.PayTypeEnum;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinancialTransactionEntity implements Entity<FinancialTransactionIdentifier> {

    private Long id;

    /**
     * 三方流水号
     */
    private String financialTransactionId;

    /**
     * 财务平台类型
     */
    private FinancialPlatformTypeEnum financialPlatformType;

    /**
     * 账务日期
     */
    private Date financialDate;

    /**
     * 星卡内部流水号
     */
    private String payNo;

    /**
     * 交易金额（单位：分）
     */
    private Long payAmount;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 入账客户号
     */
    private String receiveAccount;

    /**
     * 支付类型
     */
    private PayTypeEnum payType;

    /**
     * 交易方向
     */
    private FinancialPayDirectionEnum payDirection;

    /**
     * 交易创建时间
     */
    private Date payCreateTime;

    private CreateValObj createValObj;

    @Override
    public @NonNull FinancialTransactionIdentifier getIdentifier() {
        return FinancialTransactionIdentifier.builder()
                .financialTransactionId(financialTransactionId)
                .financialPlatformType(financialPlatformType).build();
    }

    @Override
    public Validatable<FinancialTransactionIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

package com.xk.ewd.domain.model.reconciliation.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.reconciliation.id.ReconciliationIdentifier;

import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReconciliationEntity implements Entity<ReconciliationIdentifier> {

    /**
     * 对账日期
     */
    private Date reconciliationDate;

    /**
     * 对账名称
     */
    private String reconciliationName;

    /**
     * 对账单据总数
     */
    private Integer reconciliationTotalCount;

    /**
     * 收款正常总数
     */
    private Integer reconciliationSuccessCount;

    /**
     * 收款异常总数
     */
    private Integer reconciliationFailCount;

    /**
     * 总实收金额
     */
    private Long reconciliationTotalAmount;

    /**
     * 总正常金额
     */
    private Long reconciliationSuccessAmount;

    /**
     * 总异常金额
     */
    private Long reconciliationFailAmount;

    @Override
    public @NonNull ReconciliationIdentifier getIdentifier() {
        return ReconciliationIdentifier.builder().reconciliationDate(reconciliationDate).build();
    }

    @Override
    public Validatable<ReconciliationIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}

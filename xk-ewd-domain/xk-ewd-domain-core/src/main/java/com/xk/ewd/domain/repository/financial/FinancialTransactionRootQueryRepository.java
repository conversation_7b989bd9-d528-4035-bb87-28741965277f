package com.xk.ewd.domain.repository.financial;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.model.financial.FinancialTransactionRoot;
import com.xk.ewd.domain.model.financial.entity.FinancialTransactionEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface FinancialTransactionRootQueryRepository extends IQueryRepository {

    /**
     * 根据日期查出当日所有
     * 
     * @param entity entity
     * @return Flux<FinancialTransactionEntity>
     */
    Flux<FinancialTransactionEntity> selectCurrentDateAll(FinancialTransactionEntity entity);

    /**
     * 根据交易id查询交易记录
     *
     * @param financialRoot financialTransactionRoot
     * @return Flux<FinancialTransactionRoot>
     */
    Mono<FinancialTransactionEntity> selectByTransactionId(FinancialTransactionRoot financialRoot);
}

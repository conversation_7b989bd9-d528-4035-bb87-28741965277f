package com.xk.order.domain.repository.order;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface OrderItemRootQueryRepository extends IQueryRepository {

    /**
     * 查询规格是否锁定状态
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<OrderItemLockValObj> querySpecificationLocked(OrderItemLockValObj valObj);

    /**
     * 根据订单号查询所有条目
     *
     * @param identifier identifier
     * @return Flux<OrderItemRoot>
     */
    Flux<OrderItemRoot> searchRootByOrderNo(OrderIdentifier identifier);

    /**
     * 根据订单号查询所有条目
     * 
     * @param identifier identifier
     * @return Flux<OrderItemRoot>
     */
    Flux<OrderItemEntity> searchEntityByOrderNo(OrderIdentifier identifier);

    /**
     * 查询用户购买数量
     * 
     * @param cacheValObj cacheValObj
     * @return Mono<Long>
     */
    Mono<Long> getUserBuyCount(OrderCacheValObj cacheValObj);

    /**
     * 查询指定时间间隔用户购买次数
     *
     * @param cacheValObj cacheValObj
     * @return Mono<Long>
     */
    Mono<Long> getUserBuyTimeCount(OrderCacheValObj cacheValObj);

    /**
     * 根据商品id和赠品业务id查找赠品
     *
     * @param entity entity
     * @return Mono<OrderItemEntity>
     */
    Mono<OrderGiftEntity> findGiftEntityByGoodsIdAndBusinessId(OrderGiftEntity entity);

    /**
     * 根据商品id查找所有赠品
     *
     * @param entity entity
     * @return Mono<OrderItemEntity>
     */
    Flux<OrderGiftEntity> findGiftEntityByGoodsId(OrderGiftEntity entity);

    /**
     * 分页查询订单赠品
     *
     * @param pagination pagination
     * @return Flux<OrderGiftEntity>
     */
    Flux<OrderGiftEntity> searchGiftByPage(Pagination pagination);

    /**
     * 查询用户购买金额
     *
     * @param cacheValObj cacheValObj
     * @return Mono<Long>
     */
    Mono<Long> getUserBuyAmount(OrderCacheValObj cacheValObj);

    /**
     * 获取商品榜单指定数量的用户
     *
     * @param cacheValObj 商品id 用户id
     * @return 用户id和总购买金额
     */
    Flux<OrderCacheValObj> getUserBuyAmount(OrderCacheValObj cacheValObj, Integer count);

    /**
     * 根据订单号查询赠品
     * 
     * @param identifier identifier
     * @return Mono<OrderGiftEntity>
     */
    Flux<OrderGiftEntity> searchGiftEntityByOrderNo(OrderIdentifier identifier);

    /**
     * 根据订单号查询赠品(4条)
     * 
     * @param identifier identifier
     * @return Flux<OrderGiftEntity>
     */
    Flux<OrderGiftEntity> searchLimitGiftEntityByOrderNo(OrderIdentifier identifier);

    /**
     * 根据商品id 统计数量
     * 
     * @param entity entity
     * @return Mono<Long>
     */
    Mono<Long> searchGiftByCount(OrderGiftEntity entity);
}

package com.xk.order.domain.service.order.impl;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.order.domain.commons.XkOrderDomainErrorEnum;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.domain.repository.order.OrderItemRootRepository;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.support.OrderSequenceEnum;
import com.xk.order.domain.support.XkOrderDomainException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderItemRootServiceImpl implements OrderItemRootService {

    private final IdentifierGenerateService identifierGenerateService;
    private final OrderItemRootRepository orderItemRootRepository;
    private final OrderItemRootQueryRepository orderItemRootQueryRepository;

    @Override
    public Mono<Long> generateId() {
        return Mono.just((Long) identifierGenerateService.generateIdentifier(
                IdentifierRoot.builder().identifier(OrderSequenceEnum.O_ORDER_ITEM)
                        .type(IdentifierGenerateEnum.CACHE).build()));
    }

    @Override
    public AtomicLong generateGiftId(Long count) {
        return new AtomicLong(
                identifierGenerateService
                        .getByRange(
                                IdentifierRoot.builder().identifier(OrderSequenceEnum.O_ORDER_GIFT)
                                        .type(IdentifierGenerateEnum.CACHE).count(count).build())
                        .getStart());
    }

    @Override
    public Mono<Void> saveRoot(Mono<OrderItemRoot> mono) {
        return mono.flatMap(root -> root.initDefault().then(orderItemRootRepository.save(root)));
    }

    @Override
    public Flux<OrderItemRoot> searchRootByOrderNo(OrderIdentifier identifier) {
        return orderItemRootQueryRepository.searchRootByOrderNo(identifier);
    }

    @Override
    public Flux<OrderItemEntity> searchEntityByOrderNo(OrderIdentifier identifier) {
        return orderItemRootQueryRepository.searchEntityByOrderNo(identifier);
    }

    @Override
    public Flux<OrderGiftEntity> searchGiftEntityByOrderNo(OrderIdentifier identifier) {
        return orderItemRootQueryRepository.searchGiftEntityByOrderNo(identifier);
    }

    @Override
    public Flux<OrderGiftEntity> searchLimitGiftEntityByOrderNo(OrderIdentifier identifier) {
        return orderItemRootQueryRepository.searchLimitGiftEntityByOrderNo(identifier);
    }

    @Override
    public Mono<OrderGiftEntity> findGiftEntityByGoodsIdAndBusinessId(OrderGiftEntity entity) {
        return orderItemRootQueryRepository.findGiftEntityByGoodsIdAndBusinessId(entity);
    }

    @Override
    public Mono<Void> addFortunePositionCache(OrderCacheValObj valObj) {
        return orderItemRootRepository.addFortunePositionCache(valObj);
    }

    @Override
    public Mono<OrderItemLockValObj> querySpecificationLocked(OrderItemLockValObj valObj) {
        return orderItemRootQueryRepository.querySpecificationLocked(valObj);
    }

    @Override
    public Mono<Void> checkSpecificationLocked(OrderItemLockValObj valObj) {
        return orderItemRootQueryRepository.querySpecificationLocked(valObj).flatMap(lockObj -> {
            if (Boolean.TRUE.equals(valObj.getHoldLock())) {
                if (lockObj == null || !Objects.equals(lockObj.getUserId(), valObj.getUserId())) {
                    return Mono.error(new XkOrderDomainException(
                            XkOrderDomainErrorEnum.SPECIFICATION_HAS_EXPIRE));
                }
            } else {
                if (lockObj != null && lockObj.getUserId() != null
                        && !Objects.equals(lockObj.getUserId(), valObj.getUserId())) {
                    return Mono.error(new XkOrderDomainException(
                            XkOrderDomainErrorEnum.SPECIFICATION_HAS_LOCKED));
                }
            }
            return Mono.empty();
        });
    }

    @Override
    public Mono<Void> removeSpecificationLocked(OrderItemLockValObj valObj) {
        return orderItemRootRepository.removeSpecificationLocked(valObj);
    }

    @Override
    public Mono<Void> addSpecificationLock(OrderItemLockValObj valObj) {
        return orderItemRootRepository.addFortuneSpecificationLock(valObj);
    }

    @Override
    public Mono<Void> addOrderPaidCache(OrderCacheValObj valObj) {
        return orderItemRootRepository.addMerchantProductUserCache(valObj)
                .then(orderItemRootRepository.addMerchantOrderCache(valObj))
                .then(orderItemRootRepository.addMerchantUserAmountCache(valObj));
    }

    @Override
    public Mono<Void> addOrderBuyCache(OrderCacheValObj valObj) {
        return orderItemRootRepository.addUserGoodsBuyCountCache(valObj)
                .then(orderItemRootRepository.addUserBoughtCache(valObj))
                .then(orderItemRootRepository.addAnonymousStatusCache(valObj));
    }

    @Override
    public Mono<Void> removeOrderBuyCache(OrderCacheValObj valObj) {
        return orderItemRootRepository.removeUserGoodsBuyCountCache(valObj)
                .then(orderItemRootQueryRepository.getUserBuyCount(valObj)
                        .filter(v -> Objects.equals(v, 0L))
                        .flatMap(v -> orderItemRootRepository.removeUserBoughtCache(valObj)));
    }

    @Override
    public Mono<Void> addUserGoodsBuyTimeCache(OrderCacheValObj valObj) {
        return orderItemRootRepository.addUserGoodsBuyTimeCache(valObj);
    }

    @Override
    public Mono<Long> getUserBuyCount(OrderCacheValObj cacheValObj) {
        return orderItemRootQueryRepository.getUserBuyCount(cacheValObj);
    }

    @Override
    public Mono<Long> getUserBuyTimeCount(OrderCacheValObj cacheValObj) {
        return orderItemRootQueryRepository.getUserBuyTimeCount(cacheValObj);
    }

    @Override
    public Mono<Long> getUserBuyAmount(OrderCacheValObj cacheValObj) {
        return orderItemRootQueryRepository.getUserBuyAmount(cacheValObj);
    }

    @Override
    public Flux<OrderCacheValObj> getUserBuyAmount(OrderCacheValObj cacheValObj, Integer count) {
        return orderItemRootQueryRepository.getUserBuyAmount(cacheValObj, count);
    }
}

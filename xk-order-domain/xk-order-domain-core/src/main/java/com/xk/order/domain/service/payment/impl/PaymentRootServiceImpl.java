package com.xk.order.domain.service.payment.impl;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.domain.event.payment.RefundStatusChangeEvent;
import com.xk.order.domain.event.payment.RefundSuccessEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.repository.payment.PaymentRootQueryRepository;
import com.xk.order.domain.repository.payment.PaymentRootRepository;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.domain.service.payment.PaymentService;
import com.xk.order.domain.support.OrderSequenceEnum;
import com.xk.order.enums.payment.PayDetailStatusEnum;
import com.xk.order.enums.payment.PayDirectionEnum;
import com.xk.order.enums.payment.RefundStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentRootServiceImpl implements PaymentRootService {

    private final IdentifierGenerateService identifierGenerateService;
    private final PaymentRootRepository paymentRootRepository;
    private final PaymentRootQueryRepository paymentRootQueryRepository;
    private final PaymentService paymentService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Long> generateId(OrderSequenceEnum orderSequence) {
        return Mono.just((Long) identifierGenerateService.generateIdentifier(IdentifierRoot
                .builder().identifier(orderSequence).type(IdentifierGenerateEnum.CACHE).build()));
    }

    @Override
    public Mono<Long> saveRefund(Mono<PaymentRoot> paymentRootMono) {
        return paymentRootMono.flatMap(paymentRoot -> {
            return generateId(OrderSequenceEnum.O_REFUND).flatMap(refundId -> {
                RefundEntity refundEntity = paymentRoot.getRefundEntity();
                refundEntity.setPaymentId(refundId);
                refundEntity.setDeleted(CommonStatusEnum.DISABLE);
                refundEntity.setCreateTime(new Date());
                return paymentRootRepository.insertRefund(refundEntity).thenReturn(refundId);
            });
        });
    }

    @Override
    public Mono<Void> savePaymentDetail(Mono<PaymentRoot> paymentRootMono) {
        return paymentRootMono.flatMap(paymentRoot -> {
            return generateId(OrderSequenceEnum.O_PAYMENT_DETAIL).flatMap(detailId -> {
                PaymentDetailEntity paymentDetailEntity = paymentRoot.getPaymentDetailEntity();
                paymentDetailEntity.setPaymentDetailId(detailId);
                paymentDetailEntity.setDeleted(CommonStatusEnum.DISABLE);
                paymentDetailEntity.setCreateTime(new Date());
                return paymentRootRepository.insertPaymentDetail(paymentDetailEntity);
            });
        });
    }

    // @Override
    // public Mono<Boolean> refund(Mono<PaymentRoot> paymentRootMono) {
    // return paymentRootMono.flatMap(paymentRoot -> {
    // PaymentEntity payment = paymentRoot.getPaymentEntity();
    // // 查询支付单
    // return paymentRootQueryRepository.findByOrderNo(payment.getOrderNo())
    // .flatMap(paymentEntity -> {
    // // 添加流水
    // return savePaymentDetail(Mono.just(PaymentRoot.builder()
    // .paymentDetailEntity(PaymentDetailEntity.builder()
    // .orderNo(paymentEntity.getOrderNo())
    // .payNo(paymentEntity.getPayNo())
    // .payDirection(PayDirectionEnum.REFUND)
    // .status(PayDetailStatusEnum.SUCCEED)
    // .amount(paymentEntity.getAmount())
    // .remark(paymentEntity.getRemark()).build())
    // .build()))
    // // 退款
    // .then(paymentService.refundPayment(Mono
    // .just(PaymentEntity.builder().orderNo(payment.getOrderNo())
    // .payNo(payment.getPayNo()).build())));
    // });
    // });
    // }

    @Override
    public Mono<Boolean> refund(Mono<PaymentRoot> paymentRootMono) {
        return paymentRootMono.flatMap(paymentRoot -> {
            PaymentEntity payment = paymentRoot.getPaymentEntity();
            RefundEntity refundEntity = paymentRoot.getRefundEntity();
            log.info("PaymentRootService 发起退款 {},{}", payment.getOrderNo(), payment.getPayNo());
            // 查询支付单
            return paymentRootQueryRepository.findByOrderNo(payment.getOrderNo())
                    .flatMap(paymentEntity -> {
                        // 调用退款
                        return paymentService.refundPayment(Mono.just(PaymentEntity.builder()
                                .orderNo(payment.getOrderNo()).payNo(payment.getPayNo()).build()))
                                .flatMap(b -> {
                                    log.info("PaymentRootService 发起退款 {} 结果 {}",
                                            payment.getOrderNo(), b);
                                    // 创建退款单
                                    Supplier<Mono<Long>> addRefundOrder = () -> {
                                        if (Objects.nonNull(refundEntity)
                                                && Objects.nonNull(refundEntity.getPaymentId())) {
                                            return Mono.just(refundEntity.getPaymentId());
                                        }
                                        return saveRefund(Mono.just(PaymentRoot.builder()
                                                .identifier(paymentRoot.getIdentifier())
                                                .refundEntity(RefundEntity.builder()
                                                        .orderNo(paymentEntity.getOrderNo())
                                                        .userId(paymentEntity.getUserId())
                                                        .refundStatus(RefundStatusEnum.PAID)
                                                        .platformType(
                                                                paymentEntity.getPlatformType())
                                                        .payType(paymentEntity.getPayType())
                                                        .payTime(paymentEntity.getPayTime())
                                                        .amount(paymentEntity.getAmount())
                                                        .remark(paymentEntity.getRemark()).build())
                                                .build()));
                                    };
                                    // 添加流水
                                    return addRefundOrder.get().flatMap(
                                            refundId -> savePaymentDetail(Mono.just(PaymentRoot
                                                    .builder()
                                                    .identifier(paymentRoot.getIdentifier())
                                                    .paymentDetailEntity(PaymentDetailEntity
                                                            .builder().paymentId(refundId)
                                                            .orderNo(paymentEntity.getOrderNo())
                                                            .payNo(paymentEntity.getPayNo())
                                                            .payDirection(PayDirectionEnum.REFUND)
                                                            .status(b ? PayDetailStatusEnum.SUCCEED
                                                                    : PayDetailStatusEnum.FAIL)
                                                            .amount(paymentEntity.getAmount())
                                                            .remark(paymentEntity.getRemark())
                                                            .build())
                                                    .build())))
                                            // 发送退款成功事件
                                            .then(b ? sendRefundSuccessEvent(RefundSuccessEvent
                                                    .builder()
                                                    .identifier(EventRoot
                                                            .getCommonsDomainEventIdentifier(
                                                                    RefundSuccessEvent.class))
                                                    .orderNo(payment.getOrderNo())
                                                    .payNo(payment.getPayNo()).build())
                                                    : Mono.empty())
                                            // 发送退款单变更事件
                                            .then(b ? sendRefundStatusChangeEvent(paymentRoot,
                                                    RefundStatusEnum.PAID) : Mono.empty())
                                            .thenReturn(b);
                                });
                    });
        });
    }

    /**
     * 发送退款成功事件
     * 
     * @param refundSuccessEvent refundSuccessEvent
     * @return Mono<Void>
     */
    private Mono<Void> sendRefundSuccessEvent(RefundSuccessEvent refundSuccessEvent) {
        return Mono.fromCallable(() -> {
            log.info("PaymentRootService 发送退款成功事件...{},{}", refundSuccessEvent.getOrderNo(),
                    refundSuccessEvent.getPayNo());
            EventRoot event = EventRoot.builder().domainEvent(refundSuccessEvent).build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("PaymentRootService 发送退款成功事件失败：{}", refundSuccessEvent.getOrderNo());
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("PaymentRootService 发送退款成功事件失败：{}，{}", refundSuccessEvent.getOrderNo(),
                        e.getMessage());
            }
            return null;
        });
    }

    private Mono<Void> sendRefundStatusChangeEvent(PaymentRoot root,
            RefundStatusEnum refundStatusEnum) {
        RefundEntity refundEntity = root.getRefundEntity();

        return Mono.fromCallable(() -> {
            Date refundTime = new Date();
            log.info("PaymentAppService 发送退款单状态变更事件...{}，{}，{}", refundEntity.getPaymentId(),
                    refundStatusEnum, refundTime);

            EventRoot event = EventRoot.builder().domainEvent(RefundStatusChangeEvent.builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(RefundStatusChangeEvent.class))
                    .paymentId(refundEntity.getPaymentId()).refundStatus(refundStatusEnum.getCode())
                    .remark(refundEntity.getRemark()).refundTime(refundTime).build()).build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("PaymentAppService 发送退款单状态变更事件失败：{}", refundEntity.getPaymentId());
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("PaymentAppService 发送退款单状态变更事件失败：{}，{}", refundEntity.getPaymentId(),
                        e.getMessage());
            }
            return null;
        });
    }
}

package com.xk.order.domain.service.order;

import java.util.concurrent.atomic.AtomicLong;

import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface OrderItemRootService {

    /**
     * 生成条目id
     * 
     * @return Mono<Long>
     */
    Mono<Long> generateId();

    /**
     * 生成订单赠品id
     * 
     * @return Mono<Long>
     */
    AtomicLong generateGiftId(Long count);

    /**
     * 保存条目聚合根
     * 
     * @param mono mono
     * @return Mono<Void>
     */
    Mono<Void> saveRoot(Mono<OrderItemRoot> mono);

    /**
     * 根据订单编号查找订单条目
     *
     * @param identifier 订单编号标识符
     */
    Flux<OrderItemRoot> searchRootByOrderNo(OrderIdentifier identifier);

    /**
     * 根据订单编号查找订单条目
     * 
     * @param identifier 订单编号标识符
     */
    Flux<OrderItemEntity> searchEntityByOrderNo(OrderIdentifier identifier);

    /**
     * 根据商品id和赠品业务id查找赠品
     *
     * @param identifier 订单编号
     */
    Flux<OrderGiftEntity> searchGiftEntityByOrderNo(OrderIdentifier identifier);

    /**
     * 根据商品id和赠品业务id查找赠品(只查询4条)
     *
     * @param identifier 订单编号
     */
    Flux<OrderGiftEntity> searchLimitGiftEntityByOrderNo(OrderIdentifier identifier);

    /**
     * 根据商品id和赠品业务id查找赠品
     *
     * @param entity 商品id和赠品业务id
     */
    Mono<OrderGiftEntity> findGiftEntityByGoodsIdAndBusinessId(OrderGiftEntity entity);

    /**
     * 添加福盒位置缓存
     * 
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> addFortunePositionCache(OrderCacheValObj valObj);

    /**
     * 查询规格是否被锁定
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<OrderItemLockValObj> querySpecificationLocked(OrderItemLockValObj valObj);

    /**
     * 校验规格是否被锁定
     * 
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> checkSpecificationLocked(OrderItemLockValObj valObj);

    /**
     * 移除福盒规格锁定
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> removeSpecificationLocked(OrderItemLockValObj valObj);

    /**
     * 添加规格锁定
     * 
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> addSpecificationLock(OrderItemLockValObj valObj);

    /**
     * 根据订单添加已支付后记录缓存
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> addOrderPaidCache(OrderCacheValObj valObj);

    /**
     * 根据订单添加购买缓存
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> addOrderBuyCache(OrderCacheValObj valObj);

    /**
     * 根据订单移除购买缓存
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> removeOrderBuyCache(OrderCacheValObj valObj);

    /**
     * 根据订单添加购买时间和数量缓存
     *
     * @param valObj valObj
     * @return Mono<Void>
     */
    Mono<Void> addUserGoodsBuyTimeCache(OrderCacheValObj valObj);

    /**
     * 获取用户购买数量
     *
     * @param cacheValObj 商品id 用户id
     * @return 总购买数量
     */
    Mono<Long> getUserBuyCount(OrderCacheValObj cacheValObj);

    /**
     * 获取用户时间购买数量
     *
     * @param cacheValObj 商品id 用户id 限购1个间隔时长
     * @return 时间内购买数量
     */
    Mono<Long> getUserBuyTimeCount(OrderCacheValObj cacheValObj);

    /**
     * 获取用户购买金额
     *
     * @param cacheValObj 商品id 用户id
     * @return 总购买金额
     */
    Mono<Long> getUserBuyAmount(OrderCacheValObj cacheValObj);

    /**
     * 获取商品榜单指定数量的用户
     *
     * @param cacheValObj 商品id 用户id
     * @return 用户id和总购买金额
     */
    Flux<OrderCacheValObj> getUserBuyAmount(OrderCacheValObj cacheValObj, Integer count);
}

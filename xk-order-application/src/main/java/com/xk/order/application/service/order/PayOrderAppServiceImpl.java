package com.xk.order.application.service.order;

import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.order.application.action.command.order.CancelOrderCommand;
import com.xk.order.application.action.command.order.CancelPaidOrderCommand;
import com.xk.order.application.action.command.payment.CreatePaymentCommand;
import com.xk.order.application.action.query.payment.PaymentQuery;
import com.xk.order.application.commons.OrderDictEnum;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.PaymentOrderSuccessEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderPriceEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelTypeEnum;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.order.enums.payment.PayStatusEnum;
import com.xk.order.enums.payment.PaymentPayTypeEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.dto.req.order.PayOrderReq;
import com.xk.order.interfaces.dto.rsp.order.PayOrderCreateRsp;
import com.xk.order.interfaces.dto.rsp.payment.PaymentRsp;
import com.xk.order.interfaces.service.order.PayOrderAppService;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessDeviceEnum;
import com.xk.tp.interfaces.dto.pay.PlaceOrderDto;
import com.xk.tp.interfaces.dto.res.pay.PlaceOrderResDto;
import com.xk.tp.interfaces.service.pay.PayService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class PayOrderAppServiceImpl implements PayOrderAppService {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final OrderRootService orderRootService;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final PayService payService;
    private final DictObjectDomainService dictObjectDomainService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<PayOrderCreateRsp> createPayOrder(Mono<PayOrderReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(userObjectRoot -> mono.flatMap(dto -> orderRootService
                        .getRootNoLogistics(OrderIdentifierConvertor.map(dto.getOrderNo()))
                        .switchIfEmpty(Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE)))
                        .flatMap(orderRoot -> {
                            Long payAmount = orderRoot.getOrderPriceEntity().getPayAmount();
                            if (payAmount == null || payAmount < 0) {
                                log.error("系统支付金额异常.订单编号{}", dto.getOrderNo());
                                return Mono.error(new SystemWrapperThrowable(
                                        SystemErrorEnum.SYSTEM_UNKNOWN_EXCEPTION));
                            }

                            // 检查订单金额是否为0，如果是则直接返回成功
                            if (Objects.equals(orderRoot.getOrderPriceEntity().getPayAmount(),
                                    0L)) {
                                return Mono
                                        .just(EventRoot.builder().domainEvent(
                                                PaymentOrderSuccessEvent.builder().identifier(
                                                        EventRoot.getCommonsDomainEventIdentifier(
                                                                PaymentOrderSuccessEvent.class))
                                                        .orderNo(dto.getOrderNo())
                                                        .payType(dto.getPayType())
                                                        .payTime(new Date())
                                                        .payStatus(PayStatusEnum.PAID.getCode())
                                                        .build())
                                                .isTry(true).isQueue(true).build())
                                        .flatMap(eventRoot -> eventRootService
                                                .publisheByMono(eventRoot))
                                        .thenReturn(PayOrderCreateRsp.builder()
                                                .orderNo(dto.getOrderNo())
                                                .payStatus(PayStatusEnum.PAID.getCode()).build());
                            }

                            UserDataObjectEntity userDataObjectEntity =
                                    userObjectRoot.getUserDataObjectEntity();
                            Long userId = userDataObjectEntity.getUserId();
                            String nickname = userDataObjectEntity.getNickname();

                            // 定义处理步骤的函数
                            Mono<PaymentRsp> queryPayment =
                                    queryDispatcher.executeQuery(
                                            Mono.just(PaymentQuery.builder()
                                                    .orderNo(dto.getOrderNo()).build()),
                                            PaymentQuery.class, PaymentRsp.class);

                            Function<PaymentRsp, Mono<Boolean>> validate = paymentRsp -> {
                                if (!PayStatusEnum.PENDING.equals(paymentRsp.getPayStatus())) {
                                    return Mono.error(new XkOrderApplicationException(
                                            XkOrderApplicationErrorEnum.PAY_ALREADY_PAID));
                                }
                                return Mono.just(true);
                            };

                            Function<OrderRoot, Mono<PlaceOrderResDto>> createPay =
                                    root -> payService.createPay(
                                            buildPlaceOrderDto(dto, root, userObjectRoot));

                            Function<PlaceOrderResDto, Mono<PayOrderCreateRsp>> buildResponse =
                                    res -> Mono.just(PayOrderCreateRsp.builder()
                                            .orderNo(dto.getOrderNo())
                                            .payStatus(PayStatusEnum.PENDING.getCode())
                                            .formData(res.getFormData()).url(res.getUrl()).build());

                            Mono<Boolean> createPayment =
                                    commandDispatcher.executeCommand(Mono.just(dto),
                                            CreatePaymentCommand.class, command -> {
                                                command.setOrderNo(dto.getOrderNo());
                                                command.setUsername(nickname);
                                                command.setMobile(userDataObjectEntity.getMobile());
                                                command.setPlatformType(
                                                        userDataObjectEntity.getLastPlatformType());
                                                command.setPayStatus(PayStatusEnum.PENDING);
                                                command.setAmount(orderRoot.getOrderPriceEntity()
                                                        .getPayAmount().toString());
                                                command.buildCreate(userId);
                                                return command;
                                            }).thenReturn(true);
                            // 组合函数并执行流程
                            return queryPayment.flatMap(validate).switchIfEmpty(createPayment)
                                    .then(Mono.just(orderRoot)).flatMap(createPay)
                                    .flatMap(buildResponse);
                        })));
    }

    @BusiCode
    @Override
    public Mono<PayOrderCreateRsp> createPayOrderTest(Mono<PayOrderReq> mono) {
        return mono.flatMap(dto -> orderRootService
                .getRootNoLogistics(OrderIdentifier.builder().orderNo(dto.getOrderNo()).build())
                .map(orderRoot -> EventRoot.builder().domainEvent(PaymentOrderSuccessEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(PaymentOrderSuccessEvent.class))
                        .orderNo(dto.getOrderNo()).payType(dto.getPayType()).payTime(new Date())
                        .payStatus(PayStatusEnum.PAID.getCode())
                        .paymentId(RandomStringUtils.secureStrong().next(15, false, true))
                        .payNo(RandomStringUtils.secureStrong().next(15, true, true)).build())
                        .isTry(true).build())
                .flatMap(eventRoot -> eventRootService.publisheByMono(eventRoot))
                .thenReturn(PayOrderCreateRsp.builder().orderNo(dto.getOrderNo()).build()));
    }

    @BusiCode
    @Override
    public Mono<Void> updateClosePayOrder(Mono<OrderNoRequireReq> mono) {
        // 发送事件
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Mono<OrderRoot> rootNoLogistics = orderRootService
                    .getRootNoLogistics(OrderIdentifier.builder().orderNo(dto.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
            Function<OrderRoot, Mono<OrderRoot>> checkOrder = root -> {
                if (!Objects.equals(root.getOrderEntity().getUserId(), userId)) {
                    return Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                }

                if (!Objects.equals(OrderStatusEnum.WAIT_PAID,
                        root.getOrderEntity().getOrderStatus())) {
                    return Mono.error(new XkOrderApplicationException(
                            XkOrderApplicationErrorEnum.ORDER_CANCEL_STATUS_ERROR));
                }

                return Mono.just(root);
            };

            Function<OrderRoot, Mono<Void>> doExecute =
                    root -> commandDispatcher.executeCommand(
                            Mono.just(CancelOrderCommand.builder().orderNo(dto.getOrderNo())
                                    .orderCancelType(OrderCancelTypeEnum.USER_CANCEL)
                                    .updateId(userId).updateTime(new Date()).build()),
                            CancelOrderCommand.class);
            return rootNoLogistics.flatMap(checkOrder).flatMap(doExecute);
        }));
    }

    @BusiCode
    @Override
    public Mono<Void> updateClosePaidOrder(Mono<OrderNoRequireReq> mono) {
        // 发送事件
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Mono<OrderRoot> rootNoLogistics = orderRootService.getRootNoLogistics(
                    OrderIdentifier.builder().orderNo(dto.getOrderNo()).build());

            CancelPaidOrderCommand command = CancelPaidOrderCommand.builder()
                    .orderNo(dto.getOrderNo()).orderCancelType(OrderCancelTypeEnum.USER_CANCEL)
                    .updateId(userId).updateTime(new Date()).build();

            Function<OrderRoot, Mono<Void>> doExecute = root -> commandDispatcher
                    .executeCommand(Mono.just(command), CancelPaidOrderCommand.class);
            return rootNoLogistics
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                    .flatMap(v -> v.checkNoneMerchantRefundPaidStatus().thenReturn(v))
                    .flatMap(doExecute);
        }));
    }

    private Mono<PlaceOrderDto> buildPlaceOrderDto(PayOrderReq req, OrderRoot root,
            UserObjectRoot userObjectRoot) {
        PaymentPayTypeEnum paymentPayTypeEnum = PaymentPayTypeEnum.getByCode(req.getPayType());
        AccessChannelTypeEnum typeEnum = switch (paymentPayTypeEnum) {
            case ALIPAY -> AccessChannelTypeEnum.aliPay;
            case WECHAT_PAY -> AccessChannelTypeEnum.weixin;
            default -> null;
        };
        OrderPriceEntity orderPriceEntity = root.getOrderPriceEntity();
        PlatformTypeEnum lastPlatformType =
                userObjectRoot.getUserDataObjectEntity().getLastPlatformType();
        String device = switch (lastPlatformType) {
            case PC_USER_WEB, PC_COMPANY_OMS -> AccessDeviceEnum.pc.getCode();
            case ANDROID_USER, IOS_USER, HARMONYOS_USER -> AccessDeviceEnum.app.getCode();
            default -> null;
        };
        assert typeEnum != null;
        return Mono.justOrEmpty(typeEnum)
                .switchIfEmpty(
                        Mono.error(new SystemWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE)))
                .then(Mono.zip(
                        dictObjectDomainService
                                .getSystemConfigToInt(OrderDictEnum.ORDER_EXPIRE_TIME),
                        dictObjectDomainService
                                .getSystemConfigValue(OrderDictEnum.PAY_ORDER_GOODS_NAME)))
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        SystemErrorEnum.VALIDATE_FAILURE, "订单配置异常")))
                .map(tuple2 -> PlaceOrderDto.builder().orderNo(req.getOrderNo())
                        .orderTime(new Date()).businessType(BusinessTypeEnum.XING_KA.getValue())
                        .channelType(typeEnum.getValue()).amount(orderPriceEntity.getPayAmount())
                        .description(tuple2.getT2())
                        .userId(String
                                .valueOf(userObjectRoot.getUserDataObjectEntity().getUserId()))
                        .clientIp(req.getClientIp()).platformType(lastPlatformType.getValue())
                        .device("app").expireTime(tuple2.getT1()).build())
                .doOnSuccess(dto -> dto.setSessionId(req.getSessionId()));
    }
}

package com.xk.order.application.handler.query.order;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.object.goods.CardGoodsValueObject;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.order.application.action.query.order.MerchantOrderAppDetailQuery;
import com.xk.order.application.dto.order.OrderDetailAppDto;
import com.xk.order.application.dto.order.OrderLogisticsOrderAppDto;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.entity.OrderLogisticsOrderEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderAppDetailRsp;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderAppGiftRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MerchantOrderAppDetailQueryHandler
        implements IActionQueryHandler<MerchantOrderAppDetailQuery, MerchantOrderAppDetailRsp> {

    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<MerchantOrderAppDetailRsp> execute(Mono<MerchantOrderAppDetailQuery> mono) {
        return mono.flatMap(query -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderDetailAppDto>> getDetail =
                    root -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
                        if (!Objects.equals(root.getOrderEntity().getUserId(), userId)) {
                            return Mono.error(new SystemWrapperThrowable(
                                    SystemErrorEnum.UNSUPPORTED_OPERATION));
                        }
                        OrderDetailAppDto appDto =
                                converter.convert(root.getOrderEntity(), OrderDetailAppDto.class);
                        converter.convert(root.getOrderPriceEntity(), appDto);
                        converter.convert(root.getOrderAddressEntity(), appDto);
                        converter.convert(root.getOrderPayEntity(), appDto);
                        converter.convert(root.getOrderRefundEntity(), appDto);
                        if (CollectionUtils.isNotEmpty(root.getOrderLogisticsOrderEntityList())) {
                            List<OrderLogisticsOrderEntity> entities =
                                    root.getOrderLogisticsOrderEntityList();
                            appDto.setLogisticsInfoDtoList(
                                    converter.convert(entities, OrderLogisticsOrderAppDto.class));
                            OrderLogisticsOrderEntity first = entities.getFirst();
                            if (Objects.equals(first.getLogisticsOrderStatus(),
                                    LogisticsOrderStatusEnum.RECEIVED)) {
                                appDto.setSuccessTime(first.getUpdateTime());
                            }
                            appDto.setLogisticsOrderId(first.getLogisticsOrderId());
                            if (first.getLogisticsOrderStatus() != null) {
                                appDto.setLogisticsOrderStatus(
                                        first.getLogisticsOrderStatus().getCode());
                            }
                            appDto.setLogisticsCorpName(first.getLogisticsCorpName());
                            appDto.setLogisticsNo(first.getLogisticsNo());
                        }
                        appDto.setCreateTime(
                                root.getOrderEntity().getCreateValObj().getCreateTime());

                        return Mono.just(appDto);
                    });

            Function<OrderDetailAppDto, Mono<OrderDetailAppDto>> getGoodsInfo =
                    appDto -> orderItemRootService
                            .searchEntityByOrderNo(
                                    OrderIdentifier.builder().orderNo(appDto.getOrderNo()).build())
                            .collectList().map(v -> {
                                OrderItemEntity first = v.getFirst();
                                appDto.setGoodsId(first.getGoodsId());
                                appDto.setGoodsName(first.getGoodsName());
                                appDto.setUnitPrice(first.getUnitPrice());
                                return appDto;
                            });

            Function<OrderDetailAppDto, Mono<OrderDetailAppDto>> getGoodsImage =
                    appDto -> selectorRootService.getGoodsObject(appDto.getGoodsId())
                            .flatMap(goodsObjectRoot -> {
                                if (!Objects.equals(goodsObjectRoot.getGoodsInfo().getGoodsType(),
                                        GoodsTypeEnum.MERCHANT_PRODUCT.name())) {
                                    return Mono.error(new SystemWrapperThrowable(
                                            SystemErrorEnum.VALIDATE_FAILURE));
                                }
                                appDto.setPlanDownTime(
                                        goodsObjectRoot.getGoodsInfo().getOffShelfTime());
                                appDto.setGoodsImages(goodsObjectRoot.getResList().stream()
                                        .filter(v -> BusinessResTypeEnum.PRODUCT_PICTURE.name()
                                                .equals(v.getResType()))
                                        .findFirst().map(GoodsResValueObject::getResAddr)
                                        .orElse(null));
                                CardGoodsValueObject cardGoods =
                                        goodsObjectRoot.getGoodsInfo().getCardGoods();
                                appDto.setProductType(ProductTypeEnum
                                        .valueOf(cardGoods.getCardGoodsType()).getCode());
                                appDto.setRandomType(cardGoods.getRandomType());
                                appDto.setRemainRandomStatus(cardGoods.getRemainRandomStatus());
                                appDto.setCollectibleCardName(cardGoods.getCollectionCardName());
                                return Mono.just(appDto);
                            }).defaultIfEmpty(appDto);

            Function<OrderDetailAppDto, Mono<OrderDetailAppDto>> getCorpInfo =
                    appDto -> selectorRootService.getCorpObject(appDto.getCorpId())
                            .doOnSuccess(v -> {
                                appDto.setCorpName(v.getCorpInfoObjectEntity().getCorpName());
                                appDto.setCorpLogo(v.getCorpInfoObjectEntity().getCorpLogo());
                            }).thenReturn(appDto);

            Function<MerchantOrderAppDetailRsp, Mono<MerchantOrderAppDetailRsp>> getGiftInfo =
                    rsp -> orderItemRootService
                            .searchLimitGiftEntityByOrderNo(
                                    OrderIdentifier.builder().orderNo(rsp.getOrderNo()).build())
                            .collectList().map(list -> {
                                rsp.setMerchantOrderAppGiftRspList(list.stream()
                                        .map(v -> MerchantOrderAppGiftRsp.builder()
                                                .giftBusinessName(v.getGiftBusinessName())
                                                .giftPrizeStatus(v.getGiftPrizeStatus().getCode())
                                                .build())
                                        .toList());
                                return rsp;
                            });

            return getOrderRoot.flatMap(getDetail).flatMap(getGoodsInfo).flatMap(getGoodsImage)
                    .flatMap(getCorpInfo)
                    .map(v -> converter.convert(v, MerchantOrderAppDetailRsp.class))
                    .flatMap(getGiftInfo);
        });
    }
}

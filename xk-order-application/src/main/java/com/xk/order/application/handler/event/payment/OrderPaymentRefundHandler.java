package com.xk.order.application.handler.event.payment;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.payment.OrderPaymentRefundEvent;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.domain.model.risk.RiskControlRoot;
import com.xk.order.domain.model.risk.entity.RefundRiskControlEntity;
import com.xk.order.domain.repository.order.OrderRootQueryRepository;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.domain.service.risk.RiskControlRootService;
import com.xk.order.enums.payment.RefundStatusEnum;
import com.xk.order.enums.payment.RefundTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaymentRefundHandler extends AbstractEventVerticle<OrderPaymentRefundEvent> {

    private final LockRootService lockRootService;
    private final PaymentRootService paymentRootService;
    private final OrderRootQueryRepository orderRootQueryRepository;
    private final RiskControlRootService riskControlRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<OrderPaymentRefundEvent> event) {
        return event.flatMap(orderPaymentRefund -> {
            try {
                lockRootService.acquireTransactionObjectLock(ZookeeperLockObject.LOCKS_PAY_CALLBACK,
                        orderPaymentRefund.getOrderNo() + "_" + orderPaymentRefund.getPayNo());
            } catch (Throwable e) {
                return Mono.error(e);
            }
            if (!orderPaymentRefund.isAutoRefund()) {
                log.info("OrderPaymentRefundHandler 不进行自动退款 {}", orderPaymentRefund.getOrderNo());
                return Mono.empty();
            }
            log.info("OrderPaymentRefundHandler 开始进行退款 {}", JSON.toJSONString(orderPaymentRefund));
            return getUserId(orderPaymentRefund.getOrderNo()).flatMap(userId -> {
                return Mono
                        .zip(riskControlRootService
                                .checkPayRefundLimit(Mono.just(RiskControlRoot.builder()
                                        .refundRiskControlEntity(RefundRiskControlEntity.builder()
                                                .userId(userId).build())
                                        .build())),
                                riskControlRootService.checkPayAmountLimit(Mono.just(RiskControlRoot
                                        .builder()
                                        .refundRiskControlEntity(RefundRiskControlEntity.builder()
                                                .amount(orderPaymentRefund.getAmount()).build())
                                        .build())))
                        .flatMap(tuple2 -> {
                            Boolean b1 = tuple2.getT1();
                            Boolean b2 = tuple2.getT2();
                            RefundTypeEnum refundTypeEnum = null;
                            RefundStatusEnum refundStatusEnum = null;
                            Date refundTime = null;
                            if (!b1) {
                                refundTypeEnum = RefundTypeEnum.REPEATEDLY;
                                refundStatusEnum = RefundStatusEnum.PENDING;
                            } else if (!b2) {
                                refundTypeEnum = RefundTypeEnum.WHOLESALE;
                                refundStatusEnum = RefundStatusEnum.PENDING;
                            } else {
                                refundStatusEnum = RefundStatusEnum.PAID;
                                refundTime = new Date();
                            }
                            log.info("OrderPaymentRefundHandler 风控校验结果：{}，{}，{}", refundTypeEnum,
                                    refundStatusEnum, refundTime);

                            OrderPaymentRefundEvent.OrderPaymentRefundEventBuilder refundEventBuilder =
                                    OrderPaymentRefundEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    OrderPaymentRefundEvent.class))
                                            .orderNo(orderPaymentRefund.getOrderNo())
                                            .payNo(orderPaymentRefund.getPayNo())
                                            .refundType(Objects.nonNull(refundTypeEnum)
                                                    ? refundTypeEnum.getCode()
                                                    : null)
                                            .refundStatus(refundStatusEnum.getCode())
                                            .platformType(orderPaymentRefund.getPlatformType())
                                            .payType(orderPaymentRefund.getPayType())
                                            .payTime(orderPaymentRefund.getPayTime())
                                            .refundTime(refundTime)
                                            .amount(orderPaymentRefund.getAmount())
                                            .remark(orderPaymentRefund.getRemark())
                                            .paymentId(orderPaymentRefund.getPaymentId())
                                            .autoRefund(false);

                            if (RefundStatusEnum.PAID.equals(refundStatusEnum)) {// 风控通过
                                // 直接退款记录退款单和流水
                                log.info("OrderPaymentRefundHandler 进入退款流程 未触发风控：{}",
                                        orderPaymentRefund.getOrderNo());
                                return paymentRootService.refund(Mono.just(PaymentRoot.builder()
                                        .identifier(
                                                PaymentIdentifier.builder().paymentId(-1L).build())
                                        .paymentEntity(PaymentEntity.builder()
                                                .orderNo(orderPaymentRefund.getOrderNo())
                                                .payNo(orderPaymentRefund.getPayNo()).build())
                                        .refundEntity(RefundEntity.builder()
                                                .paymentId(orderPaymentRefund.getPaymentId())
                                                .build())
                                        .build())).flatMap(ret -> {
                                            log.info("OrderPaymentRefundHandler 直接退款结果：{}，{}",
                                                    orderPaymentRefund.getOrderNo(), ret);
                                            if (ret) {
                                                // 退款成功
                                                return sendPaymentRefundEvent(
                                                        refundEventBuilder.build());
                                            } else {
                                                // 退款失败
                                                return sendPaymentRefundEvent(refundEventBuilder
                                                        .refundType(
                                                                RefundTypeEnum.FAIL.getCode())
                                                        .refundStatus(
                                                                RefundStatusEnum.PENDING.getCode())
                                                        .build());
                                            }
                                        });
                            } else {
                                // 风控未通过
                                log.info("OrderPaymentRefundHandler 进入退款流程 触发风控：{}",
                                        orderPaymentRefund.getOrderNo());
                                return sendPaymentRefundEvent(refundEventBuilder.build());
                            }
                        });
            });
        });
    }

    private Mono<Long> getUserId(String orderNo) {
        return orderRootQueryRepository
                .getRootNoLogistics(OrderIdentifier.builder().orderNo(orderNo).build())
                .flatMap(orderRoot -> {
                    return Mono.just(orderRoot.getOrderEntity().getUserId());
                });
    }

    private Mono<Void> sendPaymentRefundEvent(OrderPaymentRefundEvent refundEvent) {
        return Mono.fromCallable(() -> {
            log.info("OrderPaymentRefundHandler 发送退款事件...{},{}", refundEvent.getOrderNo(),
                    JSON.toJSONString(refundEvent));
            EventRoot event = EventRoot.builder().domainEvent(refundEvent).build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("OrderPaymentRefundHandler 发送退款事件失败：{}", refundEvent.getOrderNo());
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("OrderPaymentRefundHandler 发送退款事件失败：{}，{}", refundEvent.getOrderNo(),
                        e.getMessage());
            }
            return null;
        });
    }
}

package com.xk.order.application.handler.event.order;

import java.util.Date;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.order.CancelOrderCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderCancelEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 订单取消事件处理器 负责处理订单取消事件，包括更新订单状态和恢复库存
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelEventHandler extends AbstractEventVerticle<OrderCancelEvent> {


    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    /**
     * 处理订单取消事件 流程：获取锁 -> 获取订单 -> 检查订单状态 -> 更新订单状态 -> 恢复库存
     *
     * @param mono 包含订单取消事件的Mono
     * @return 处理结果的Mono
     */
    @Override
    public Mono<Void> handle(Mono<OrderCancelEvent> mono) {
        return mono.flatMap(event -> {
            Mono<Void> doExecute = commandDispatcher.executeCommand(
                    Mono.just(CancelOrderCommand.builder()
                            .orderCancelType(event.getOrderCancelType()).orderNo(event.getOrderNo())
                            .updateId(event.getUpdateId()).updateTime(new Date()).build()),
                    CancelOrderCommand.class);

            Function<Throwable, Mono<Void>> onError = e -> {
                if (event.getRetryCount() > 10) {
                    log.error("订单{}取消尝试超过最大次数", event.getOrderNo());
                    return Mono.error(new XkOrderApplicationException(
                            XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR));
                }
                log.error("订单取消处理失败: {}", event.getOrderNo(), e);
                Integer retryCount = event.getRetryCount() + 1;
                OrderCancelEvent orderCancelEvent = OrderCancelEvent.builder()
                        .identifier(
                                EventRoot.getCommonsDomainEventIdentifier(OrderCancelEvent.class))
                        .retryCount(retryCount).orderNo(event.getOrderNo()).build();
                return eventRootService.publisheByMono(
                        EventRoot.builder().domainEvent(orderCancelEvent).isQueue(true).build())
                        .then(Mono.error(e));
            };

            return doExecute.onErrorResume(onError);
        });
    }
}

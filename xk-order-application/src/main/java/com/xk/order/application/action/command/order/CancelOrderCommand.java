package com.xk.order.application.action.command.order;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.enums.order.OrderCancelTypeEnum;

import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CancelOrderCommand extends AbstractActionCommand {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单取消类型
     */
    private OrderCancelTypeEnum orderCancelType;

    /**
     * 更新id
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;
}

package com.xk.order.application.handler.event.order;

import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.util.StringUtils;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.action.command.payment.RefundCommand;
import com.xk.order.domain.event.order.OrderNotSoldOutEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderRefundStatusEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.interfaces.dto.req.UpdateUsedStatusReqDto;
import com.xk.promotion.interfaces.service.coupon.CouponInnerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderNotSoldOutEventHandler extends AbstractEventVerticle<OrderNotSoldOutEvent> {

    private final OrderRootService orderRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final CouponInnerService couponInnerService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderNotSoldOutEvent> mono) {
        return mono.flatMap(event -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRootNoLogistics(OrderIdentifierConvertor.map(event.getOrderNo()));

            Function<OrderRoot, Mono<Void>> executeCommand = root -> {
                RefundCommand command = new RefundCommand();
                command.setOrderNo(root.getIdentifier().getOrderNo());
                command.setAmount(root.getOrderPriceEntity().getPayAmount());
                command.setPayType(root.getOrderPayEntity().getPayType());
                command.setPayTime(new Date());
                command.setPlatformType(root.getOrderEntity().getPlatformType());
                command.setRemark("订单未售罄，自动退款");
                command.setPayNo(root.getOrderPayEntity().getPayNo());
                command.setUserId(root.getOrderEntity().getUserId());
                return commandDispatcher.executeCommand(Mono.just(command), RefundCommand.class);
            };

            // 零元购或者手动调用支付的订单不发送到退款方
            Function<OrderRoot, Mono<OrderRoot>> updateRefundStatus = root -> {
                if (!Objects.equals(root.getOrderPriceEntity().getPayAmount(), 0L)
                        && StringUtils.isNotBlank(root.getOrderPayEntity().getPayNo())) {
                    return Mono.just(root);
                }
                return commandDispatcher.executeCommand(Mono.just(new UpdateOrderCommand()),
                        UpdateOrderCommand.class, command -> {
                            Date date = new Date();
                            command.setOrderNo(root.getIdentifier().getOrderNo());
                            command.setRefundStatus(OrderRefundStatusEnum.REFUNDED.getCode());
                            command.setOrderStatusTime(date);
                            command.setUpdateId(-1L);
                            command.setUpdateTime(date);
                            return command;
                        }).then(Mono.empty());
            };

            Function<OrderRoot, Mono<OrderRoot>> returnCoupon = root -> {
                Long couponUserId = root.getOrderPriceEntity().getCouponUserId();
                if (couponUserId == null) {
                    return Mono.just(root);
                }
                UpdateUsedStatusReqDto reqDto = UpdateUsedStatusReqDto.builder()
                        .usedStatus(UsedStatusEnum.NOT_USED.getCode()).couponUserId(couponUserId)
                        .build();
                reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                return couponInnerService.updateUsedStatus(Mono.just(reqDto)).thenReturn(root);
            };

            return getOrderRoot.flatMap(updateRefundStatus).flatMap(returnCoupon)
                    .flatMap(executeCommand);
        });
    }
}

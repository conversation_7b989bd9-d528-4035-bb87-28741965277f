package com.xk.order.application.handler.command.order;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.order.CreateOrderGiftCommand;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.repository.order.OrderItemRootRepository;
import com.xk.order.infrastructure.convertor.order.OrderItemIdentifierConvertor;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateOrderGiftCommandHandler
        implements IActionCommandHandler<CreateOrderGiftCommand, Void> {

    private final OrderItemRootRepository orderItemRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateOrderGiftCommand> mono) {
        return execute(mono, data -> {
            List<OrderGiftEntity> convert =
                    converter.convert(data.getOrderGiftDtoList(), OrderGiftEntity.class);
            return OrderItemRoot.builder()
                    .identifier(OrderItemIdentifierConvertor
                            .map(data.getOrderGiftDtoList().getFirst().getOrderItemId()))
                    .orderGiftEntityList(convert).build();
        }, orderItemRootRepository::save);
    }
}

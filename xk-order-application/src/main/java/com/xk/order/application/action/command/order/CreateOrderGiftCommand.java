package com.xk.order.application.action.command.order;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.infrastructure.convertor.order.OrderGiftBusinessTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderGiftPrizeStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.infrastructure.convertor.order.OrderItemIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrderGiftCommand extends AbstractActionCommand {

    /**
     * 订单赠品列表
     */
    private List<OrderGiftDto> orderGiftDtoList;


    @Data
    @Builder
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @AutoMappers({@AutoMapper(target = OrderGiftEntity.class,
            uses = {OrderIdentifierConvertor.class, OrderItemIdentifierConvertor.class,
                    OrderGiftPrizeStatusEnumConvertor.class,
                    OrderGiftBusinessTypeEnumConvertor.class, CommonStatusEnumConvertor.class},
            reverseConvertGenerate = false)})
    public static class OrderGiftDto implements Serializable {
        /**
         * 订单赠品ID
         */
        private Long orderGiftId;

        /**
         * 订单ID（业务主键）
         */
        private Long orderId;

        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 订单条目ID
         */
        private Long orderItemId;

        /**
         * 商品id
         */
        private Long goodsId;

        /**
         * 商户id
         */
        private Long corpId;

        /**
         * 规格ID
         */
        private Long specificationId;

        /**
         * 赠品中奖状态 1-待公布 2-已完成 3-获赠卡
         */
        private Integer giftPrizeStatus;

        /**
         * 赠品组id
         */
        private Long businessGroupId;

        /**
         * 队伍名称
         */
        private String categoryName;

        /**
         * 赠品业务类型
         */
        private Integer giftBusinessType;

        /**
         * 赠品业务id
         */
        private Long giftBusinessId;

        /**
         * 赠品业务名称
         */
        private String giftBusinessName;

        /**
         * 赠品组地址
         */
        private String giftGroupAddr;

        /**
         * 赠品展示地址
         */
        private String giftShowAddr;

        /**
         * 分法id
         */
        private Long distributionId;

        /**
         * 赠品地址
         */
        private String giftAddr;

        /**
         * 特效
         */
        private String color;

        /**
         * 限编
         */
        private String limitEdition;

        /**
         * 创建人ID
         */
        @AutoMappings({@AutoMapping(targetClass = OrderGiftEntity.class,
                target = "createValObj.createId")})
        private Long createId;

        /**
         * 创建时间
         */
        @AutoMappings({@AutoMapping(targetClass = OrderGiftEntity.class,
                target = "createValObj.createTime")})
        private Date createTime;

        /**
         * 逻辑删除：0-未删除 1-已删除
         */
        private Integer deleted;

        public void buildCreate(Long createId) {
            this.createId = createId;
            this.createTime = new Date();
            this.deleted = 0;
        }
    }
}

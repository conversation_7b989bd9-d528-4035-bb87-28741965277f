package com.xk.order.application.handler.event.order;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderDelayCancelEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.interfaces.dto.req.UpdateUsedStatusReqDto;
import com.xk.promotion.interfaces.service.coupon.CouponInnerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderDelayCancelEventHandler extends AbstractEventVerticle<OrderDelayCancelEvent> {

    private final CouponInnerService couponInnerService;
    private final LockRootService lockRootService;
    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderDelayCancelEvent> mono) {
        return mono.flatMap(event -> {
            // 定义获取锁的函数
            Mono<Boolean> getLock = lockRootService.acquireTransactionObjectLockMono(
                    ZookeeperLockObject.LOCKS_PAY_CALLBACK, event.getOrderNo());
            return getLock.flatMap(lock -> {
                // 如果获取锁失败，记录错误并返回异常
                if (Boolean.FALSE.equals(lock)) {
                    log.error("获取LOCKS_PAY_CALLBACK锁失败,订单编号{}", event.getOrderNo());
                    return Mono.error(new XkOrderApplicationException(
                            XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR));
                }
                log.info("获取LOCKS_PAY_CALLBACK锁成功,订单编号{}", event.getOrderNo());

                // 根据订单号获取订单信息
                Mono<OrderRoot> getRoot = orderRootService
                        .getRoot(OrderIdentifier.builder().orderNo(event.getOrderNo()).build());

                // 检查订单状态是否为已取消,已取消状态的才退还优惠券和首购
                Function<OrderRoot, Mono<OrderRoot>> checkOrderStatus = root -> {
                    if (!root.getOrderEntity().getOrderStatus().equals(OrderStatusEnum.CANCEL)) {
                        log.info("订单编号{}不是已取消状态,不进行取消操作", event.getOrderNo());
                        return Mono.empty();
                    }
                    return Mono.just(root);
                };

                Function<OrderRoot, Mono<OrderRoot>> removeOrderBuyCache =
                        root -> orderItemRootService.searchEntityByOrderNo(root.getIdentifier())
                                .take(1)
                                .flatMap(entity -> orderItemRootService
                                        .removeOrderBuyCache(OrderCacheValObj.builder()
                                                .orderNo(root.getIdentifier().getOrderNo())
                                                .goodsId(entity.getGoodsId())
                                                .userId(root.getOrderEntity().getUserId())
                                                .buyCount(entity.getBuyCount())
                                                .amount(entity.getItemPayAmount()).build()))
                                .then().thenReturn(root);

                Function<OrderRoot, Mono<Void>> refundCoupon = root -> {
                    if (root.getOrderPriceEntity().getCouponUserId() == null) {
                        return Mono.empty();
                    }
                    return couponInnerService.updateUsedStatus(Mono.just(UpdateUsedStatusReqDto
                            .builder().couponUserId(root.getOrderPriceEntity().getCouponUserId())
                            .usedStatus(UsedStatusEnum.NOT_USED.getCode()).build())).then();
                };

                Function<Throwable, Mono<Void>> onError = e -> {
                    if (event.getRetryCount() > 10) {
                        log.error("订单{}取消尝试超过最大次数", event.getOrderNo());
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR));
                    }
                    log.error("订单取消处理失败: {}", event.getOrderNo(), e);
                    Integer retryCount = event.getRetryCount() + 1;
                    OrderDelayCancelEvent orderCancelEvent = OrderDelayCancelEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(OrderDelayCancelEvent.class))
                            .retryCount(retryCount).orderNo(event.getOrderNo()).build();
                    return eventRootService.publisheByMono(
                            EventRoot.builder().domainEvent(orderCancelEvent).isQueue(true).build())
                            .then(Mono.error(e));
                };

                return getRoot.flatMap(checkOrderStatus).flatMap(removeOrderBuyCache)
                        .flatMap(refundCoupon).onErrorResume(onError);
            });
        });
    }
}

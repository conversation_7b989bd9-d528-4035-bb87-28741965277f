package com.xk.order.application.service.order;

import static com.xk.order.enums.order.OrderTypeEnum.MERCHANT_PRODUCT;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.object.goods.CardGoodsValueObject;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.model.object.goods.SpecificationValueObject;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.acct.interfaces.dto.req.user.UserFollowCorpReqDto;
import com.xk.acct.interfaces.dto.rsp.follow.UserIsFollowCorpRspDto;
import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.application.commons.CommonUtil;
import com.xk.domain.commons.user.UserConfigTypeEnum;
import com.xk.domain.model.stock.valobj.StockRemainValObj;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.enums.common.BlockTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.merchant.MerchantProductStatusEnum;
import com.xk.goods.enums.merchant.ProductRandomTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.interfaces.dto.req.goods.QueryPriceReqDto;
import com.xk.goods.interfaces.dto.req.goods.SpecificationIdBatchReqDto;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionIdReq;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionItemReq;
import com.xk.goods.interfaces.dto.res.goods.PayPriceResDto;
import com.xk.goods.interfaces.dto.res.goods.SpecDetailResDto;
import com.xk.goods.interfaces.dto.res.random.RandomDistributionItemRes;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.goods.interfaces.query.goods.MerchantProductQueryService;
import com.xk.goods.interfaces.service.random.RandomDistributionService;
import com.xk.infrastructure.cache.dao.corp.CorpBlockDao;
import com.xk.infrastructure.cache.dao.merchant.FortunePositionDao;
import com.xk.infrastructure.cache.key.corp.CorpBlockKey;
import com.xk.infrastructure.cache.key.merchant.FortunePositionKey;
import com.xk.order.application.action.command.order.CreateOrderCommand;
import com.xk.order.application.action.command.order.CreateOrderItemCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.dto.order.MerchantOrderPriceAppDto;
import com.xk.order.application.dto.order.MerchantOrderPriceDetailAppDto;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.FortuneLockStatusEnum;
import com.xk.order.enums.order.OrderCancelDurationEnum;
import com.xk.order.interfaces.dto.req.order.*;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderPriceRsp;
import com.xk.order.interfaces.dto.rsp.order.MerchantRandomOrderRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderCreateRsp;
import com.xk.order.interfaces.service.order.MerchantOrderAppService;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.interfaces.dto.req.UpdateUsedStatusReqDto;
import com.xk.promotion.interfaces.service.coupon.CouponInnerService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantOrderAppServiceImpl implements MerchantOrderAppService {

    private final GoodsSearchQueryService goodsSearchQueryService;
    private final MerchantProductQueryService merchantProductQueryService;
    private final OrderItemRootService orderItemRootService;
    private final LockRootService lockRootService;
    private final CorpBlockDao corpBlockDao;
    private final StockRootService stockRootService;
    private final OrderRootService orderRootService;
    private final UserQueryService userQueryService;
    private final UserFollowCorpQueryService userFollowCorpQueryService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final SelectorRootService selectorRootService;
    private final RandomDistributionService randomDistributionService;
    private final FortunePositionDao fortunePositionDao;
    private final CouponInnerService couponInnerService;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<MerchantOrderPriceRsp> createCalculatePrice(Mono<MerchantOrderPriceReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(
                        dto -> doCheckRisk(userObject, dto).flatMap(tuple -> doGetMerchantPrice(
                                userObject.getUserDataObjectEntity().getUserId(), dto, tuple))))
                .map(appDto -> converter.convert(appDto, MerchantOrderPriceRsp.class));
    }

    @BusiCode
    @Override
    public Mono<MerchantOrderPriceRsp> createFortuneCalculatePrice(
            Mono<MerchantFortuneOrderPriceReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(dto -> selectorRootService
                        .getGoodsObject(dto.getGoodsId()).flatMap(goodsObjectRoot -> {
                            MerchantOrderPriceReq req = new MerchantOrderPriceReq();
                            req.setSessionId(dto.getSessionId());
                            req.setCouponUserId(dto.getCouponUserId());
                            req.setCouponStatus(dto.getCouponStatus());
                            req.setDetailDtoList(goodsObjectRoot.getSpecificationList().stream()
                                    .limit(1).map(spec -> {
                                        MerchantOrderPriceDetailReq detailReq =
                                                new MerchantOrderPriceDetailReq();
                                        detailReq.setSpecificationId(spec.getSpecificationId());
                                        detailReq.setBuyCount(dto.getOrderTotalBuyCount());
                                        return detailReq;
                                    }).toList());
                            return doCheckRisk(userObject, req).flatMap(tuple -> doGetMerchantPrice(
                                    userObject.getUserDataObjectEntity().getUserId(), req, tuple,
                                    true));
                        })).map(appDto -> converter.convert(appDto, MerchantOrderPriceRsp.class)));
    }

    @BusiCode
    @Override
    public Mono<MerchantRandomOrderRsp> createRemainRandomPrice(
            Mono<MerchantOrderRemainRandomPriceReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(dto -> selectorRootService
                        .getGoodsObject(dto.getGoodsId()).flatMap(goodsObjectRoot -> {
                            MerchantOrderPriceReq req = new MerchantOrderPriceReq();
                            req.setSessionId(dto.getSessionId());
                            req.setCouponUserId(dto.getCouponUserId());
                            req.setCouponStatus(dto.getCouponStatus());
                            req.setDetailDtoList(goodsObjectRoot.getSpecificationList().stream()
                                    .limit(1).map(spec -> {
                                        MerchantOrderPriceDetailReq detailReq =
                                                new MerchantOrderPriceDetailReq();
                                        detailReq.setSpecificationId(spec.getSpecificationId());
                                        detailReq.setBuyCount(dto.getBuyCount());
                                        return detailReq;
                                    }).toList());
                            return doCheckRisk(userObject, req).flatMap(tuple -> doGetMerchantPrice(
                                    userObject.getUserDataObjectEntity().getUserId(), req, tuple,
                                    true));
                        })))
                .map(appDto -> converter.convert(appDto, MerchantRandomOrderRsp.class));
    }

    @BusiCode
    @Override
    public Mono<Void> updateFortuneLock(Mono<MerchantFortunePriceReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Function<List<Long>, Mono<Void>> doAddSpec = positions -> Flux.fromIterable(positions)
                    .flatMap(position -> orderItemRootService
                            .addSpecificationLock(OrderItemLockValObj.builder().userId(userId)
                                    .lockStatus(FortuneLockStatusEnum.LOCKED)
                                    .position(Math.toIntExact(position)).goodsId(dto.getGoodsId())
                                    .build()))
                    .then();

            return checkFortuneLock(userId, false, dto).flatMap(doAddSpec);
        }));
    }

    @BusiCode
    @Override
    public Mono<Void> updateFortuneUnlock(Mono<MerchantFortuneUnlockReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono
                .flatMap(dto -> acquireDistributedLock(dto.getGoodsId()).flatMap(lock -> {
                    List<Long> positions = dto.getSpecificationIdList();
                    Mono<Void> checkFortuneUnlock = Flux.fromIterable(positions)
                            .flatMap(position -> Mono.justOrEmpty(fortunePositionDao
                                    .getValue(FortunePositionKey.builder().goodsId(dto.getGoodsId())
                                            .itemPosition(Math.toIntExact(position))
                                            .build()) != null ? null : position))
                            .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                    XkOrderApplicationErrorEnum.FORTUNE_UNLOCK_ERROR)))
                            .flatMap(position -> orderItemRootService
                                    .checkSpecificationLocked(OrderItemLockValObj.builder()
                                            .userId(userId).goodsId(dto.getGoodsId())
                                            .position(Math.toIntExact(position)).holdLock(true)
                                            .build()))
                            .then();
                    Mono<Void> doRemoveSpec = Flux.fromIterable(positions)
                            .flatMap(position -> orderItemRootService
                                    .removeSpecificationLocked(OrderItemLockValObj.builder()
                                            .lockStatus(FortuneLockStatusEnum.SELECTABLE)
                                            .userId(userId).position(Math.toIntExact(position))
                                            .goodsId(dto.getGoodsId()).build()))
                            .then();

                    return checkFortuneUnlock.then(doRemoveSpec);
                })));
    }

    @BusiCode
    @Override
    public Mono<OrderCreateRsp> createFortune(Mono<MerchantFortuneCreateReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    Long userId = userObject.getUserDataObjectEntity().getUserId();
                    PlatformTypeEnum platformType =
                            userObject.getUserDataObjectEntity().getLastPlatformType();

                    return checkFortuneLock(userId, true, dto).then(buildFortuneOrderPriceReq(dto))
                            .flatMap(req -> doCheckRisk(userObject, req)
                                    .flatMap(tuple -> doGetMerchantPrice(userId, req, tuple, true)))
                            .flatMap(priceDto -> processFortuneRandomDistribution(dto, priceDto)
                                    .flatMap(items -> createFortuneOrder(dto, priceDto, items,
                                            userId, platformType)));
                }));
    }

    /**
     * 构建福盒订单价格请求
     */
    private Mono<MerchantOrderPriceReq> buildFortuneOrderPriceReq(MerchantFortuneCreateReq dto) {
        return selectorRootService.getGoodsObject(dto.getGoodsId()).map(goodsObjectRoot -> {
            MerchantOrderPriceReq req = new MerchantOrderPriceReq();
            req.setSessionId(dto.getSessionId());
            req.setCouponUserId(dto.getCouponUserId());
            req.setCouponStatus(dto.getCouponStatus());
            req.setDetailDtoList(goodsObjectRoot.getSpecificationList().stream()
                    .limit(dto.getDetailDtoList().size()).map(spec -> {
                        MerchantOrderPriceDetailReq detailReq = new MerchantOrderPriceDetailReq();
                        detailReq.setSpecificationId(spec.getSpecificationId());
                        detailReq.setBuyCount(1);
                        return detailReq;
                    }).toList());
            return req;
        });
    }

    /**
     * 处理福盒随机分配
     */
    private Mono<List<RandomDistributionItemRes>> processFortuneRandomDistribution(
            MerchantFortuneCreateReq dto, MerchantOrderPriceAppDto priceDto) {
        RandomDistributionItemReq build = RandomDistributionItemReq.builder()
                .buyCount(dto.getOrderTotalBuyCount())
                .distributionId(priceDto.getDetailDtoList().getFirst().getDistributionId()).build();
        build.setSessionId(dto.getSessionId());

        return randomDistributionService.updateDistributionItem(Mono.just(build))
                .doOnNext(items -> updatePriceDtoWithRandomItems(dto, priceDto, items));
    }

    /**
     * 更新价格DTO中的随机物品信息
     */
    private void updatePriceDtoWithRandomItems(MerchantFortuneCreateReq dto,
            MerchantOrderPriceAppDto priceDto, List<RandomDistributionItemRes> items) {
        for (int i = 0; i < dto.getDetailDtoList().size(); i++) {
            Long position = dto.getDetailDtoList().get(i).getSpecificationId();
            MerchantOrderPriceDetailAppDto detailAppDto = priceDto.getDetailDtoList().get(i);
            RandomDistributionItemRes randomDistributionItemRes = items.get(i);

            detailAppDto.setSpecificationId(Long.valueOf(randomDistributionItemRes.getItemId()));
            detailAppDto.setItemPosition(Math.toIntExact(position));
            detailAppDto.setRandomItemId(randomDistributionItemRes.getId());
        }
    }

    /**
     * 创建福盒订单
     */
    private Mono<OrderCreateRsp> createFortuneOrder(MerchantFortuneCreateReq dto,
            MerchantOrderPriceAppDto priceDto, List<RandomDistributionItemRes> items, Long userId,
            PlatformTypeEnum platformType) {

        CreateOrderCommand orderCmd =
                converter.convert(priceDto, converter.convert(dto, CreateOrderCommand.class));
        Date createDate = new Date();

        Function<CreateOrderCommand, Mono<CreateOrderCommand>> createOrderWithUserInfo =
                buildCreateOrderFunction(userId, platformType, dto.getOrderTotalBuyCount(),
                        createDate);

        return orderCmd.processAddress(dto.getUserAddressId(), userQueryService)
                .flatMap(cmd -> generateOrderIdAndNoWithCoupon(cmd, priceDto.getProductType(),
                        dto.getCouponUserId()))
                .flatMap(createOrderWithUserInfo).flatMap(this::updateUseCoupon)
                .flatMapMany(cmd -> createOrderItems(cmd, priceDto)).flatMap(this::deductStock)
                .collectList().map(this::buildOrderCreateResponse)
                .onErrorResume(throwable -> handleFortuneOrderCreationError(throwable, items, dto));
    }

    @BusiCode
    @Override
    public Mono<OrderCreateRsp> createNoneFortune(Mono<MerchantOrderCreateReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    Long userId = userObject.getUserDataObjectEntity().getUserId();
                    PlatformTypeEnum lastPlatformType =
                            userObject.getUserDataObjectEntity().getLastPlatformType();

                    return doCheckRisk(userObject, dto)
                            .flatMap(tuple -> validateNoFortuneBox(tuple)
                                    .then(doGetMerchantPrice(userId, dto, tuple)))
                            .flatMap(priceDto -> createNormalOrder(dto, priceDto, userId,
                                    lastPlatformType))
                            .onErrorResume(throwable -> handleNoneFortuneOrderCreationError(
                                    throwable, dto));
                }));
    }

    private Mono<OrderCreateRsp> handleNoneFortuneOrderCreationError(Throwable throwable,
            MerchantOrderCreateReq dto) {
        log.error("创建非福盒订单失败", throwable);
        Supplier<Mono<Void>> updateCoupon = () -> {
            if (dto.getCouponUserId() == null) {
                return Mono.empty();
            }
            UpdateUsedStatusReqDto usedStatusReqDto =
                    UpdateUsedStatusReqDto.builder().couponUserId(dto.getCouponUserId())
                            .usedStatus(UsedStatusEnum.NOT_USED.getCode()).build();
            return couponInnerService.updateUsedStatus(Mono.just(usedStatusReqDto)).then();
        };

        return updateCoupon.get().then(Mono.error(throwable));
    }

    /**
     * 验证不包含福盒商品
     */
    private Mono<Void> validateNoFortuneBox(
            Tuple2<List<SpecDetailResDto>, Map<Long, GoodsObjectRoot>> tuple) {
        boolean hasFortuneBox = tuple.getT1().stream().anyMatch(
                item -> ProductTypeEnum.FORTUNE_BOX.getCode().equals(item.getProductType()));
        return hasFortuneBox
                ? Mono.error(new SystemWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE))
                : Mono.empty();
    }

    /**
     * 创建普通订单
     */
    private Mono<OrderCreateRsp> createNormalOrder(MerchantOrderCreateReq dto,
            MerchantOrderPriceAppDto priceDto, Long userId, PlatformTypeEnum lastPlatformType) {

        CreateOrderCommand orderCmd =
                converter.convert(priceDto, converter.convert(dto, CreateOrderCommand.class));
        Date createDate = new Date();

        Function<CreateOrderCommand, Mono<CreateOrderCommand>> createOrderWithUserInfo =
                buildCreateOrderFunction(userId, lastPlatformType, dto.getOrderTotalBuyCount(),
                        createDate);

        return orderCmd.processAddress(dto.getUserAddressId(), userQueryService)
                .flatMap(cmd -> generateOrderIdAndNoWithCoupon(cmd, priceDto.getProductType(),
                        dto.getCouponUserId()))
                .flatMap(createOrderWithUserInfo).flatMap(this::updateUseCoupon)
                .flatMapMany(cmd -> createOrderItems(cmd, priceDto)).flatMap(this::deductStock)
                .collectList().map(this::buildOrderCreateResponse);
    }

    @BusiCode
    @Override
    public Mono<OrderCreateRsp> createRemainRandomOrder(
            Mono<MerchantOrderRemainRandomOrderReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    Long userId = userObject.getUserDataObjectEntity().getUserId();
                    PlatformTypeEnum lastPlatformType =
                            userObject.getUserDataObjectEntity().getLastPlatformType();

                    return buildRemainRandomOrderPriceReq(dto)
                            .flatMap(req -> doCheckRisk(userObject, req)
                                    .flatMap(tuple -> validateNoFortuneBox(tuple)
                                            .then(doGetMerchantPrice(userId, req, tuple, true))))
                            .flatMap(priceDto -> processRemainRandomDistribution(dto, priceDto)
                                    .flatMap(items -> createRemainRandomOrder(dto, priceDto, items,
                                            userId, lastPlatformType)));
                }));
    }

    /**
     * 构建剩余随机订单价格请求
     */
    private Mono<MerchantOrderPriceReq> buildRemainRandomOrderPriceReq(
            MerchantOrderRemainRandomOrderReq dto) {
        return selectorRootService.getGoodsObject(dto.getGoodsId()).map(goodsObjectRoot -> {
            MerchantOrderPriceReq req = new MerchantOrderPriceReq();
            req.setSessionId(dto.getSessionId());
            req.setCouponUserId(dto.getCouponUserId());
            req.setCouponStatus(dto.getCouponStatus());
            req.setDetailDtoList(
                    goodsObjectRoot.getSpecificationList().stream().limit(1).map(spec -> {
                        MerchantOrderPriceDetailReq detailReq = new MerchantOrderPriceDetailReq();
                        detailReq.setSpecificationId(spec.getSpecificationId());
                        detailReq.setBuyCount(dto.getBuyCount());
                        return detailReq;
                    }).toList());
            return req;
        });
    }

    /**
     * 处理剩余随机分配
     */
    private Mono<List<RandomDistributionItemRes>> processRemainRandomDistribution(
            MerchantOrderRemainRandomOrderReq dto, MerchantOrderPriceAppDto priceDto) {
        RandomDistributionItemReq req =
                RandomDistributionItemReq.builder().buyCount(dto.getBuyCount())
                        .distributionId(priceDto.getRemainRandomDistributionId()).build();

        return randomDistributionService.updateDistributionItem(Mono.just(req));
    }

    /**
     * 创建剩余随机订单
     */
    private Mono<OrderCreateRsp> createRemainRandomOrder(MerchantOrderRemainRandomOrderReq dto,
            MerchantOrderPriceAppDto priceDto, List<RandomDistributionItemRes> items, Long userId,
            PlatformTypeEnum lastPlatformType) {

        return selectorRootService.getGoodsObject(dto.getGoodsId()).flatMap(goodsObject -> {
            updatePriceDtoWithRandomItemFrequency(priceDto, items, goodsObject);

            CreateOrderCommand orderCmd =
                    converter.convert(priceDto, converter.convert(dto, CreateOrderCommand.class));
            Date createDate = new Date();

            Function<CreateOrderCommand, Mono<CreateOrderCommand>> createOrderWithUserInfo =
                    buildCreateOrderFunction(userId, lastPlatformType, dto.getBuyCount(),
                            createDate);

            return orderCmd.processAddress(dto.getUserAddressId(), userQueryService)
                    .flatMap(cmd -> generateOrderIdAndNoWithCoupon(cmd, priceDto.getProductType(),
                            dto.getCouponUserId()))
                    .flatMap(createOrderWithUserInfo).flatMap(this::updateUseCoupon)
                    .flatMapMany(cmd -> createOrderItems(cmd, priceDto)).flatMap(this::deductStock)
                    .collectList().map(this::buildOrderCreateResponse);
        });
    }

    /**
     * 商家商品前置风控
     */
    private <T extends MerchantOrderPriceReq> Mono<Tuple2<List<SpecDetailResDto>, Map<Long, GoodsObjectRoot>>> doCheckRisk(
            UserObjectRoot userObject, T dto) {
        Map<Long, GoodsObjectRoot> goodsCache = new HashMap<>();
        Set<Long> corpCache = new HashSet<>();

        UserDataObjectEntity entity = userObject.getUserDataObjectEntity();
        String forbidDeal = userObject.getUserConfig(UserConfigTypeEnum.IS_FORBID_DEAL);
        // 校验实人认证
        Mono<Void> checkFace = CommonUtil.equalsAll(entity.getFaceAuthStatus(),
                entity.getBaseAuthStatus(), CommonStatusEnum.ENABLE.getCode()) ? Mono.empty()
                        : Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.FACE_NOT_PASS));

        // 校验运营拉黑
        Mono<Void> checkManagerBlock = StringUtils.isNotEmpty(forbidDeal)
                && CommonStatusEnum.ENABLE.getCode().toString().equals(forbidDeal)
                        ? Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.MANAGER_BUY_BLOCK))
                        : Mono.empty();

        // 获取规格详情
        SpecificationIdBatchReqDto specReqDto = new SpecificationIdBatchReqDto();
        specReqDto.setSessionId(dto.getSessionId());
        specReqDto.setSpecificationId(dto.getDetailDtoList().stream()
                .map(MerchantOrderPriceDetailReq::getSpecificationId).toList());

        Mono<List<SpecDetailResDto>> getSpecDetails =
                goodsSearchQueryService.getSpecificationDetail(Mono.just(specReqDto))
                        .filter(CollectionUtils::isNotEmpty).switchIfEmpty(Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

        // 校验商品购买参数
        Function<List<SpecDetailResDto>, Mono<Tuple2<List<SpecDetailResDto>, Map<Long, GoodsObjectRoot>>>> checkMerchant =
                specList -> {
                    Function<SpecDetailResDto, Mono<GoodsObjectRoot>> getMerchantDetail =
                            detail -> {
                                // 避免重复校验,影响速度
                                if (goodsCache.containsKey(detail.getGoodsId())) {
                                    return Mono.empty();
                                }
                                if (!GoodsTypeEnum.MERCHANT_PRODUCT.getCode()
                                        .equals(detail.getGoodsType())) {
                                    return Mono.error(new SystemWrapperThrowable(
                                            SystemErrorEnum.VALIDATE_FAILURE));
                                }
                                return selectorRootService.getGoodsObject(detail.getGoodsId());
                            };

                    Function<GoodsObjectRoot, Mono<GoodsObjectRoot>> checkSoldAndNotSoldOut =
                            detail -> {
                                if (MerchantProductStatusEnum.AUDITED.getCode()
                                        .equals(detail.getGoodsInfo().getStatus())) {
                                    return Mono.just(detail);
                                }
                                return Mono.error(new XkOrderApplicationException(
                                        XkOrderApplicationErrorEnum.GOODS_STATUS_ERROR));
                            };
                    // 校验商户拉黑
                    Function<GoodsObjectRoot, Mono<GoodsObjectRoot>> checkCorpBlock = detail -> {
                        Long corpId = detail.getGoodsInfo().getCardGoods().getCorpId();
                        if (!corpCache.add(corpId)) {
                            return Mono.just(detail);
                        }
                        Double value = corpBlockDao.getValue(
                                CorpBlockKey.builder().corpId(corpId).build(), entity.getUserId());
                        if (value != null) {
                            return Mono.error(new XkOrderApplicationException(
                                    XkOrderApplicationErrorEnum.CORP_BUY_BLOCK));
                        }
                        return Mono.just(detail);
                    };
                    // 校验是否关注
                    Function<GoodsObjectRoot, Mono<GoodsObjectRoot>> checkFollow = detail -> {
                        Integer followBuyStatus =
                                detail.getGoodsInfo().getCardGoods().getFollowBuyStatus();
                        Long corpId = detail.getGoodsInfo().getCardGoods().getCorpId();
                        if (!CommonStatusEnum.ENABLE.getCode().equals(followBuyStatus)) {
                            return Mono.just(detail);
                        }
                        UserFollowCorpReqDto reqDto =
                                UserFollowCorpReqDto.builder().corpInfoId(corpId).build();
                        reqDto.setSessionId(dto.getSessionId());
                        return userFollowCorpQueryService.followStatus(Mono.just(reqDto))
                                .map(UserIsFollowCorpRspDto::getIsFollow)
                                .filter(v -> CommonStatusEnum.ENABLE.getCode().equals(v))
                                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                        XkOrderApplicationErrorEnum.CORP_NEED_FOLLOW)))
                                .thenReturn(detail);
                    };

                    Function<GoodsObjectRoot, Mono<GoodsObjectRoot>> checkLimit = detail -> {
                        CardGoodsValueObject cardGoods = detail.getGoodsInfo().getCardGoods();
                        Integer limitStatus = cardGoods.getLimitStatus();
                        Integer limitAmount = cardGoods.getLimitAmount();
                        Integer limitTimeStatus = cardGoods.getLimitTimeStatus();
                        Long goodsId = detail.getGoodsInfo().getGoodsId();
                        Integer limitTimeInterval = cardGoods.getLimitTimeInterval();

                        if (!CommonStatusEnum.ENABLE.getCode().equals(limitStatus)
                                || Objects.equals(limitAmount, 0) || limitAmount == null) {
                            return Mono.just(detail);
                        }
                        // 不需要考虑限购时间
                        if (!CommonStatusEnum.ENABLE.getCode().equals(limitTimeStatus)) {
                            return orderItemRootService
                                    .getUserBuyCount(OrderCacheValObj.builder().goodsId(goodsId)
                                            .userId(entity.getUserId()).build())
                                    .filter(buyCount -> buyCount < limitAmount)
                                    .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                            XkOrderApplicationErrorEnum.LIMIT_COUNT_OVER,
                                            limitAmount)))
                                    .thenReturn(detail);
                        }
                        // 考虑限购时间
                        return orderItemRootService
                                .getUserBuyTimeCount(OrderCacheValObj.builder().goodsId(goodsId)
                                        .userId(entity.getUserId())
                                        .limitTimeInterval(limitTimeInterval).build())
                                .filter(buyCount -> buyCount < limitAmount)
                                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                        XkOrderApplicationErrorEnum.LIMIT_TIME_OVER,
                                        limitTimeInterval, limitAmount)))
                                .thenReturn(detail);
                    };


                    return Flux.fromIterable(specList).flatMap(getMerchantDetail)
                            .flatMap(checkSoldAndNotSoldOut).flatMap(checkCorpBlock)
                            .flatMap(checkFollow).flatMap(checkLimit)
                            .doOnNext(detail -> goodsCache
                                    .putIfAbsent(detail.getGoodsInfo().getGoodsId(), detail))
                            .then(Mono.just(Tuples.of(specList, goodsCache)));
                };
        return checkFace.then(checkManagerBlock).then(getSpecDetails).flatMap(checkMerchant);
    }

    /**
     * 检查福盒锁
     * 
     * @param userId userId
     * @param holdLock true校验是否本人持有锁 false只校验是否被他人持有锁
     * @param dto dto
     * @return Mono<List<Long>>
     * @param <T> T
     */
    public <T extends MerchantFortunePriceReq> Mono<List<Long>> checkFortuneLock(Long userId,
            boolean holdLock, T dto) {

        // 验证商品id数量以及商品类型
        Mono<Long> validateGoods =
                selectorRootService.getGoodsObject(dto.getGoodsId()).flatMap(goodsObjectRoot -> {
                    Mono<Long> checkGoodsType = GoodsTypeEnum.MERCHANT_PRODUCT.name()
                            .equals(goodsObjectRoot.getGoodsInfo().getGoodsType())
                                    ? Mono.just(dto.getGoodsId())
                                    : Mono.error(new SystemWrapperThrowable(
                                            SystemErrorEnum.VALIDATE_FAILURE));

                    Mono<Long> checkProductType = ProductTypeEnum.FORTUNE_BOX.name()
                            .equals(goodsObjectRoot.getGoodsInfo().getCardGoods()
                                    .getCardGoodsType()) ? Mono.just(dto.getGoodsId())
                                            : Mono.error(new SystemWrapperThrowable(
                                                    SystemErrorEnum.VALIDATE_FAILURE));
                    return checkGoodsType.then(checkProductType);
                }).switchIfEmpty(Mono
                        .error(new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

        // 检查锁获取结果
        Function<Boolean, Mono<Void>> validateLockAcquisition = lockResult -> {
            if (Boolean.FALSE.equals(lockResult)) {
                return Mono.error(
                        new XkOrderApplicationException(XkOrderApplicationErrorEnum.BUY_TIME_OUT));
            }
            return Mono.empty();
        };
        List<Long> positions = dto.getDetailDtoList().stream()
                .map(MerchantOrderPriceDetailReq::getSpecificationId).toList();

        // 检查和添加规格锁
        Mono<Void> processSpecificationLocks = Flux.fromIterable(positions)
                .flatMap(position -> Mono.justOrEmpty(fortunePositionDao
                        .getValue(FortunePositionKey.builder().goodsId(dto.getGoodsId())
                                .itemPosition(Math.toIntExact(position)).build()) != null ? null
                                        : position))
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.STOCK_NOT_ENOUGH)))
                .flatMap(position -> orderItemRootService.checkSpecificationLocked(
                        OrderItemLockValObj.builder().userId(userId).goodsId(dto.getGoodsId())
                                .position(Math.toIntExact(position)).holdLock(holdLock).build()))
                .then(stockRootService.checkRemainRealStock(
                        StringIdentifier.builder().id(dto.getGoodsId().toString()).build(),
                        StockBusinessTypeEnum.GOODS, 1))
                .then().then();

        return validateGoods.flatMap(this::acquireDistributedLock).flatMap(validateLockAcquisition)
                .then(processSpecificationLocks).thenReturn(positions);
    }

    /**
     * 构建创建订单Function
     */
    private Function<CreateOrderCommand, Mono<CreateOrderCommand>> buildCreateOrderFunction(
            Long userId, PlatformTypeEnum platformType, Integer orderTotalBuyCount,
            Date createDate) {
        return command -> commandDispatcher
                .executeCommand(Mono.just(command), CreateOrderCommand.class, cmd -> {
                    cmd.setUserId(userId);
                    cmd.setOrderType(MERCHANT_PRODUCT.getCode());
                    cmd.setOrderTotalBuyCount(orderTotalBuyCount);
                    if (platformType != null) {
                        cmd.setPlatformType(platformType.getValue());
                    }
                    cmd.buildCreate(userId, createDate);
                    return cmd;
                }).thenReturn(command);
    }

    /**
     * 构建订单创建响应
     */
    private OrderCreateRsp buildOrderCreateResponse(List<CreateOrderItemCommand> itemCmdList) {
        CreateOrderItemCommand firstItem = itemCmdList.getFirst();
        return OrderCreateRsp.builder().orderNo(firstItem.getOrderNo())
                .cancelDeadlineTime(DateUtils.addMinutes(firstItem.getCreateTime(),
                        OrderCancelDurationEnum.THREE_MINUTES.getDuration()))
                .build();
    }

    /**
     * 处理福盒订单创建错误
     */
    private Mono<OrderCreateRsp> handleFortuneOrderCreationError(Throwable throwable,
            List<RandomDistributionItemRes> items, MerchantFortuneCreateReq dto) {
        log.error("创建订单失败", throwable);
        Supplier<Mono<Void>> updateCoupon = () -> {
            if (dto.getCouponUserId() == null) {
                return Mono.empty();
            }
            UpdateUsedStatusReqDto usedStatusReqDto =
                    UpdateUsedStatusReqDto.builder().couponUserId(dto.getCouponUserId())
                            .usedStatus(UsedStatusEnum.NOT_USED.getCode()).build();
            return couponInnerService.updateUsedStatus(Mono.just(usedStatusReqDto)).then();
        };

        RandomDistributionIdReq req = RandomDistributionIdReq.builder()
                .itemIdList(items.stream().map(RandomDistributionItemRes::getId).toList())
                .distributionId(items.getFirst().getDistributionId()).build();
        return randomDistributionService.updateCallbackDistributionItem(Mono.just(req))
                .then(updateCoupon.get()).then(Mono.error(throwable));
    }

    /**
     * 更新优惠券使用状态
     * 
     * @param command command
     * @return Mono<CreateOrderCommand>
     */
    private Mono<CreateOrderCommand> updateUseCoupon(CreateOrderCommand command) {
        if (command.getCouponUserId() == null) {
            return Mono.just(command);
        }
        UpdateUsedStatusReqDto usedStatusReqDto =
                UpdateUsedStatusReqDto.builder().couponUserId(command.getCouponUserId())
                        .usedStatus(UsedStatusEnum.USED.getCode()).build();
        usedStatusReqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
        return couponInnerService.updateUsedStatus(Mono.just(usedStatusReqDto)).thenReturn(command);
    }

    /**
     * 创建订单项
     */
    private Flux<CreateOrderItemCommand> createOrderItems(CreateOrderCommand orderCmd,
            MerchantOrderPriceAppDto priceDto) {
        return Flux.fromIterable(priceDto.getDetailDtoList())
                .flatMap(detail -> orderItemRootService.generateId().flatMap(itemId -> {
                    CreateOrderItemCommand itemCmd =
                            converter.convert(detail, CreateOrderItemCommand.class);

                    // 设置订单项属性
                    itemCmd.setOrderItemId(itemId);
                    itemCmd.setItemPosition(detail.getItemPosition());
                    itemCmd.setOrderId(orderCmd.getOrderId());
                    itemCmd.setOrderNo(orderCmd.getOrderNo());
                    itemCmd.setOrderType(MERCHANT_PRODUCT.getCode());
                    itemCmd.setItemPayAmount(detail.getSpecPayAmount());
                    itemCmd.setItemTotalAmount(detail.getSpecTotalAmount());
                    itemCmd.setDistributionId(detail.getDistributionId());
                    itemCmd.setProductType(priceDto.getProductType().getCode());
                    if (priceDto.getRandomType() != null) {
                        itemCmd.setRandomType(priceDto.getRandomType().getCode());
                    }
                    if (priceDto.getRemainRandomStatus() != null) {
                        itemCmd.setRemainRandomStatus(priceDto.getRemainRandomStatus().getCode());
                    }
                    itemCmd.setRandomItemId(detail.getRandomItemId());
                    itemCmd.buildCreate(orderCmd.getCreateId(), orderCmd.getCreateTime());

                    // 创建订单项
                    Mono<Void> createOrderItem = commandDispatcher
                            .executeCommand(Mono.just(itemCmd), CreateOrderItemCommand.class);

                    // 构建订单缓存对象
                    OrderCacheValObj cacheValObj = buildOrderCacheValObj(orderCmd, detail);

                    OrderItemLockValObj orderItemLockValObj =
                            OrderItemLockValObj.builder().userId(itemCmd.getUserId())
                                    .lockStatus(FortuneLockStatusEnum.BUY_LOCKED)
                                    .goodsId(itemCmd.getGoodsId())
                                    .position(itemCmd.getItemPosition()).holdLock(true).build();

                    Mono<Void> updateFortuneBuyLock =
                            orderItemRootService.addSpecificationLock(orderItemLockValObj);

                    // 添加到限购缓存
                    Mono<Void> addBuyCountCache =
                            orderItemRootService.addOrderBuyCache(cacheValObj);

                    // 添加到限购时间缓存
                    Mono<Void> addBuyTimeCache =
                            CommonStatusEnum.ENABLE.getCode().equals(detail.getLimitTimeStatus())
                                    ? orderItemRootService.addUserGoodsBuyTimeCache(cacheValObj)
                                    : Mono.empty();

                    return createOrderItem.then(updateFortuneBuyLock).then(addBuyCountCache)
                            .then(addBuyTimeCache).thenReturn(itemCmd);
                }));
    }

    /**
     * 根据随机物品频率更新价格DTO
     */
    private void updatePriceDtoWithRandomItemFrequency(MerchantOrderPriceAppDto priceDto,
            List<RandomDistributionItemRes> items, GoodsObjectRoot goodsObject) {

        // 1. 统计itemId出现频率
        Map<String, List<RandomDistributionItemRes>> frequencyMap =
                items.stream().collect(Collectors.groupingBy(RandomDistributionItemRes::getItemId));

        // 2. 构建规格映射
        Map<Long, SpecificationValueObject> specMap = goodsObject.getSpecificationList().stream()
                .collect(Collectors.toMap(SpecificationValueObject::getSpecificationId, v -> v));

        // 3. 创建新的明细列表
        List<MerchantOrderPriceDetailAppDto> detailList = frequencyMap.entrySet().stream()
                .map(entry -> buildDetailFromFrequency(priceDto, entry, specMap)).toList();

        // 4. 更新priceDto
        priceDto.setDetailDtoList(detailList);
    }

    /**
     * 根据频率构建明细
     */
    private MerchantOrderPriceDetailAppDto buildDetailFromFrequency(
            MerchantOrderPriceAppDto priceDto,
            Map.Entry<String, List<RandomDistributionItemRes>> entry,
            Map<Long, SpecificationValueObject> specMap) {

        // 使用序列化深拷贝创建新对象
        MerchantOrderPriceDetailAppDto clone =
                SerializationUtils.clone(priceDto.getDetailDtoList().getFirst());

        Long specificationId = Long.valueOf(entry.getKey());
        SpecificationValueObject specificationValueObject = specMap.get(specificationId);

        clone.setSpecificationId(specificationId);
        clone.setBuyCount(Math.toIntExact(entry.getValue().size()));
        clone.setSpecName(specificationValueObject.getSpecName());
        clone.setPriceId(specificationValueObject.getPriceId());
        clone.setStockId(specificationValueObject.getStockId());
        clone.setDistributionId(specificationValueObject.getDistributionId());
        clone.setRandomItemId(entry.getValue().getFirst().getId());

        return clone;
    }

    /**
     * 计算商家订单价格
     *
     * @param dto 商家订单价格请求DTO
     * @return 商家订单价格应用DTO
     */
    private <T extends MerchantOrderPriceReq> Mono<MerchantOrderPriceAppDto> doGetMerchantPrice(
            Long userId, T dto, Tuple2<List<SpecDetailResDto>, Map<Long, GoodsObjectRoot>> tuple,
            boolean isGoodsRandom) {
        Map<Long, MerchantOrderPriceDetailReq> reqDetailMap =
                dto.getDetailDtoList().stream().collect(Collectors.toMap(
                        MerchantOrderPriceDetailReq::getSpecificationId, v -> v, (v1, v2) -> v1));
        return dto.validate().thenMany(Flux.fromIterable(tuple.getT1()))
                .flatMap(spec -> buildPriceDetail(spec, reqDetailMap, tuple.getT2(), isGoodsRandom))
                .collect(Collectors.groupingBy(MerchantOrderPriceDetailAppDto::getGoodsId))
                .flatMap(detailMap -> calculateFinalPrice(userId, dto.getCouponUserId(),
                        dto.getOrderTotalBuyCount(), detailMap, tuple.getT2()));
    }

    private <T extends MerchantOrderPriceReq> Mono<MerchantOrderPriceAppDto> doGetMerchantPrice(
            Long userId, T dto, Tuple2<List<SpecDetailResDto>, Map<Long, GoodsObjectRoot>> tuple) {
        return doGetMerchantPrice(userId, dto, tuple, false);
    }

    /**
     * 构建价格详情
     *
     * @param spec 规格详情
     * @param reqDtoMap 请求DTO映射
     * @param merchantMap 商家详情映射
     * @param isGoodsRandom 是否剩余随机模式
     * @return 商家订单价格详情应用DTO
     */
    private Mono<MerchantOrderPriceDetailAppDto> buildPriceDetail(SpecDetailResDto spec,
            Map<Long, MerchantOrderPriceDetailReq> reqDtoMap,
            Map<Long, GoodsObjectRoot> merchantMap, boolean isGoodsRandom) {

        MerchantOrderPriceDetailReq reqDetail = reqDtoMap.get(spec.getSpecificationId());
        if (reqDetail == null) {
            return Mono.error(new XkOrderApplicationException(
                    XkOrderApplicationErrorEnum.SPECIFICATION_NOT_EXIST));
        }

        // 获取商品详情
        GoodsObjectRoot merchantDetail = merchantMap.get(spec.getGoodsId());
        if (merchantDetail == null) {
            return Mono.error(new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS));
        }

        Mono<Void> checkStock = isGoodsRandom
                ? checkStock(spec.getGoodsId(), reqDetail.getBuyCount(),
                        StockBusinessTypeEnum.GOODS)
                : checkStock(spec.getSpecificationId(), reqDetail.getBuyCount(),
                        StockBusinessTypeEnum.SPECIFICATION);

        // 检查库存
        return checkStock.then(Mono.fromCallable(() -> {
            // 计算规格价格
            BigDecimal unitPrice = BigDecimal.valueOf(spec.getAmount());
            BigDecimal buyCount = BigDecimal.valueOf(reqDetail.getBuyCount());
            BigDecimal specTotalAmount = unitPrice.multiply(buyCount);

            // 创建明细DTO
            CardGoodsValueObject cardGoods = merchantDetail.getGoodsInfo().getCardGoods();
            return MerchantOrderPriceDetailAppDto.builder()
                    .specificationId(spec.getSpecificationId()).goodsId(spec.getGoodsId())
                    .goodsName(spec.getGoodsName()).specName(spec.getSpecName())
                    .goodsImage(spec.getGoodsImage()).unitPrice(spec.getAmount())
                    .currencyType(spec.getCurrencyType()).stockId(spec.getStockId())
                    .priceId(spec.getPriceId()).buyCount(reqDetail.getBuyCount())
                    .specTotalAmount(specTotalAmount.longValue())
                    .planDownTime(merchantDetail.getGoodsInfo().getOffShelfTime())
                    .limitStatus(cardGoods.getLimitStatus()).limitAmount(cardGoods.getLimitAmount())
                    .limitTimeStatus(cardGoods.getLimitTimeStatus())
                    .limitTimeInterval(cardGoods.getLimitTimeInterval())
                    .blockType(Optional
                            .of(BlockTypeEnum.valueOf(merchantDetail.getGoodsInfo().getBusiType()))
                            .map(BlockTypeEnum::getCode).orElse(null))
                    .corpId(cardGoods.getCorpId()).distributionId(spec.getDistributionId()).build();
        }));
    }

    /**
     * 检查库存
     *
     * @param specificationId 规格ID
     * @param buyCount 购买数量
     * @return Mono<Void>
     */
    private Mono<Void> checkStock(Long specificationId, Integer buyCount,
            StockBusinessTypeEnum typeEnum) {
        return stockRootService
                .getRemainRealStock(typeEnum,
                        StringIdentifier.builder().id(String.valueOf(specificationId)).build())
                .map(StockRemainValObj::getRemainRealStock).defaultIfEmpty(0L).flatMap(stock -> {
                    if (stock < buyCount) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.STOCK_NOT_ENOUGH));
                    }
                    return Mono.empty();
                });
    }

    /**
     * 计算最终价格
     *
     * @param detailMap 按商品ID分组的价格详情映射
     * @param goodsCache 缓存
     * @return 商家订单价格应用DTO
     */
    private Mono<MerchantOrderPriceAppDto> calculateFinalPrice(Long userId, Long couponUserId,
            Integer buyCount, Map<Long, List<MerchantOrderPriceDetailAppDto>> detailMap,
            Map<Long, GoodsObjectRoot> goodsCache) {

        // 计算应付金额
        BigDecimal needAmount = detailMap.values().stream().flatMap(List::stream)
                .map(detail -> BigDecimal.valueOf(detail.getSpecTotalAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 处理每个商品的价格计算
        return Flux.fromIterable(detailMap.entrySet())
                .flatMap(entry -> calculateGoodsPrice(userId, couponUserId, buyCount,
                        entry.getKey(), entry.getValue()))
                .flatMap(Flux::fromIterable) // 将 List<MerchantOrderPriceDetailAppDto> 展平
                .collectList().flatMap(allDetails -> {
                    // 重新计算总支付金额
                    BigDecimal totalPayAmount = allDetails.stream()
                            .map(detail -> BigDecimal.valueOf(detail.getSpecPayAmount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 汇总各种优惠金额
                    Long totalFirstBuyDiscountAmount = allDetails.stream()
                            .mapToLong(detail -> detail.getSpecFirstBuyDiscountAmount() != null
                                    ? detail.getSpecFirstBuyDiscountAmount()
                                    : 0L)
                            .sum();

                    Long totalCouponAmount = allDetails.stream()
                            .mapToLong(detail -> detail.getSpecCouponAmount() != null
                                    ? detail.getSpecCouponAmount()
                                    : 0L)
                            .sum();

                    Long totalDiscountAmount = allDetails.stream()
                            .mapToLong(detail -> detail.getSpecDiscountAmount() != null
                                    ? detail.getSpecDiscountAmount()
                                    : 0L)
                            .sum();
                    GoodsObjectRoot goodsObjectRoot =
                            goodsCache.get(allDetails.getFirst().getGoodsId());
                    MerchantOrderPriceAppDto appDto = MerchantOrderPriceAppDto.builder()
                            .needAmount(needAmount.longValue())
                            .otherDiscountAmount(needAmount.subtract(totalPayAmount).longValue())
                            .payAmount(totalPayAmount.longValue())
                            .firstBuyDiscountAmount(totalFirstBuyDiscountAmount)
                            .couponAmount(totalCouponAmount).discountAmount(totalDiscountAmount)
                            .detailDtoList(allDetails)
                            .blockType(allDetails.getFirst().getBlockType())
                            .corpId(allDetails.getFirst().getCorpId())
                            .remainRandomDistributionId(goodsObjectRoot.getGoodsInfo()
                                    .getCardGoods().getRemainRandomDistributionId())
                            .build();
                    Long price = goodsObjectRoot.getGoodsInfo().getCardGoods()
                            .getCollectiveCardUnitPrice();
                    if (price == null || price == 0) {
                        return Mono.error(new SystemWrapperThrowable(
                                SystemErrorEnum.SYSTEM_UNKNOWN_EXCEPTION));
                    } else {
                        appDto.setShipTotalCount(BigDecimal.valueOf(appDto.getNeedAmount())
                                .divide(BigDecimal.valueOf(price), 0, RoundingMode.UP).intValue());
                    }
                    appDto.setTotalAmount(price * appDto.getShipTotalCount());
                    appDto.setCorpDiscountAmount(appDto.getTotalAmount() - appDto.getNeedAmount());
                    appDto.setProductType(ProductTypeEnum.valueOf(
                            goodsObjectRoot.getGoodsInfo().getCardGoods().getCardGoodsType()));
                    appDto.setRandomType(ProductRandomTypeEnum.getByCode(
                            goodsObjectRoot.getGoodsInfo().getCardGoods().getRandomType()));
                    appDto.setRemainRandomStatus(CommonStatusEnum.getEnum(
                            goodsObjectRoot.getGoodsInfo().getCardGoods().getRemainRandomStatus()));

                    return Mono.just(appDto);
                });
    }

    /**
     * 计算单个商品的价格
     *
     * @param goodsId 商品ID
     * @param details 商品下的规格详情列表
     * @return 处理后的规格详情列表
     */
    private Mono<List<MerchantOrderPriceDetailAppDto>> calculateGoodsPrice(Long userId,
            Long couponUserId, Integer buyCount, Long goodsId,
            List<MerchantOrderPriceDetailAppDto> details) {

        // 计算商品应付金额
        BigDecimal goodsNeedAmount =
                details.stream().map(detail -> BigDecimal.valueOf(detail.getSpecTotalAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 查询商品的实际支付价格
        QueryPriceReqDto reqDto = QueryPriceReqDto.builder().goodsId(goodsId)
                .amount(goodsNeedAmount.longValue()).build();
        reqDto.setUserId(userId);
        reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
        reqDto.setCouponUserId(couponUserId);
        reqDto.setBuyCount(buyCount);

        return merchantProductQueryService.queryPayPrice(Mono.just(reqDto)).flatMap(payPrice -> {
            // 只买了一个的情况，同时用了优惠券并且有首购优惠或者满减要报错
            boolean isFirstDiscount =
                    CommonStatusEnum.ENABLE.getCode().equals(payPrice.getFirstBuyDiscountStatus());
            if (details.size() == 1 && details.getFirst().getBuyCount() == 1
                    && CommonStatusEnum.ENABLE.getCode().equals(payPrice.getCouponStatus())
                    && isFirstDiscount) {
                return Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.FIRST_BUY_AND_COUPON));
            }
            return Mono.just(allocateDiscountToSpecsOptimized(details, goodsNeedAmount, payPrice));
        });
    }

    /**
     * 首购优惠只分配给第一个规格的第一个商品，满减和优惠券分配给其他商品
     *
     * @param details 规格详情列表
     * @param totalAmount 总金额
     * @param payPrice 支付价格信息
     * @return 分配优惠后的规格详情列表
     */
    private List<MerchantOrderPriceDetailAppDto> allocateDiscountToSpecsOptimized(
            List<MerchantOrderPriceDetailAppDto> details, BigDecimal totalAmount,
            PayPriceResDto payPrice) {

        if (details.isEmpty() || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return details;
        }

        // 获取各种优惠金额
        BigDecimal firstBuyDiscountAmount = BigDecimal.valueOf(
                payPrice.getFirstBuyDiscountAmount() != null ? payPrice.getFirstBuyDiscountAmount()
                        : 0L);
        BigDecimal discountAmount = BigDecimal
                .valueOf(payPrice.getDiscountAmount() != null ? payPrice.getDiscountAmount() : 0L);
        BigDecimal couponAmount = BigDecimal
                .valueOf(payPrice.getCouponAmount() != null ? payPrice.getCouponAmount() : 0L);
        BigDecimal payAmount = BigDecimal.valueOf(payPrice.getPayAmount());

        // 计算非首购优惠的总金额（满减 + 优惠券）
        BigDecimal otherDiscountAmount = discountAmount.add(couponAmount);

        // 初始化所有规格的优惠字段
        for (MerchantOrderPriceDetailAppDto detail : details) {
            detail.setSpecFirstBuyDiscountAmount(0L);
            detail.setSpecDiscountAmount(0L);
            detail.setSpecCouponAmount(0L);
        }

        MerchantOrderPriceDetailAppDto firstDetail = details.getFirst();
        boolean firstItemParticipatedInFirstBuy = false;

        // 第一个规格的第一个商品：分配首购优惠
        if (firstBuyDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 按购买数量分配首购优惠，只给第一个商品
            BigDecimal firstItemDiscountAmount =
                    firstBuyDiscountAmount.min(BigDecimal.valueOf(firstDetail.getUnitPrice()));
            firstDetail.setSpecFirstBuyDiscountAmount(firstItemDiscountAmount.longValue());
            firstItemParticipatedInFirstBuy = true;
        }

        // 其他商品：按比例分配满减和优惠券优惠
        if (otherDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 计算参与满减和优惠券分配的商品总金额
            BigDecimal participatingItemsTotalAmount = calculateParticipatingItemsTotalAmount(
                    details, firstItemParticipatedInFirstBuy);

            if (participatingItemsTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                allocateOtherDiscounts(details, participatingItemsTotalAmount, discountAmount,
                        couponAmount, firstItemParticipatedInFirstBuy);
            }
        }

        // 计算每个规格的最终支付金额
        calculateSpecPayAmounts(details, payAmount);

        return details;
    }

    /**
     * 计算参与满减和优惠券分配的商品总金额
     *
     * @param details 规格详情列表
     * @param firstItemParticipatedInFirstBuy 第一个商品是否参与了首购优惠
     * @return 参与满减和优惠券分配的商品总金额
     */
    private BigDecimal calculateParticipatingItemsTotalAmount(
            List<MerchantOrderPriceDetailAppDto> details, boolean firstItemParticipatedInFirstBuy) {
        if (details.isEmpty()) {
            return BigDecimal.ZERO;
        }

        MerchantOrderPriceDetailAppDto firstDetail = details.getFirst();
        BigDecimal participatingAmount = BigDecimal.ZERO;

        if (firstItemParticipatedInFirstBuy) {
            // 如果第一个商品参与了首购优惠，则只计算第一个规格的剩余商品金额
            BigDecimal firstItemAmount = BigDecimal.valueOf(firstDetail.getUnitPrice());
            BigDecimal firstSpecRemainingAmount =
                    BigDecimal.valueOf(firstDetail.getSpecTotalAmount()).subtract(firstItemAmount);
            participatingAmount = participatingAmount.add(firstSpecRemainingAmount);
        } else {
            // 如果第一个商品没有参与首购优惠，则计算第一个规格的全部金额
            participatingAmount =
                    participatingAmount.add(BigDecimal.valueOf(firstDetail.getSpecTotalAmount()));
        }

        // 其他规格的总金额
        BigDecimal otherSpecsAmount = details.stream().skip(1)
                .map(detail -> BigDecimal.valueOf(detail.getSpecTotalAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return participatingAmount.add(otherSpecsAmount);
    }

    /**
     * 分配满减和优惠券优惠到参与分配的商品
     *
     * @param details 规格详情列表
     * @param participatingItemsTotalAmount 参与分配的商品总金额
     * @param discountAmount 满减金额
     * @param couponAmount 优惠券金额
     * @param firstItemParticipatedInFirstBuy 第一个商品是否参与了首购优惠
     */
    private void allocateOtherDiscounts(List<MerchantOrderPriceDetailAppDto> details,
            BigDecimal participatingItemsTotalAmount, BigDecimal discountAmount,
            BigDecimal couponAmount, boolean firstItemParticipatedInFirstBuy) {

        if (details.isEmpty() || participatingItemsTotalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        BigDecimal allocatedDiscountAmount = BigDecimal.ZERO;
        BigDecimal allocatedCouponAmount = BigDecimal.ZERO;

        // 第一个规格：根据是否参与首购优惠决定分配策略
        MerchantOrderPriceDetailAppDto firstDetail = details.getFirst();
        BigDecimal firstSpecParticipatingAmount;

        if (firstItemParticipatedInFirstBuy) {
            // 如果第一个商品参与了首购优惠，则只处理第一个规格的剩余商品
            BigDecimal firstItemAmount = BigDecimal.valueOf(firstDetail.getUnitPrice());
            firstSpecParticipatingAmount =
                    BigDecimal.valueOf(firstDetail.getSpecTotalAmount()).subtract(firstItemAmount);
        } else {
            // 如果第一个商品没有参与首购优惠，则处理第一个规格的全部商品
            firstSpecParticipatingAmount = BigDecimal.valueOf(firstDetail.getSpecTotalAmount());
        }

        if (firstSpecParticipatingAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 计算第一个规格参与分配商品的比例
            BigDecimal ratio = firstSpecParticipatingAmount.divide(participatingItemsTotalAmount,
                    10, RoundingMode.HALF_UP);

            // 分配满减优惠到第一个规格的参与商品
            BigDecimal specDiscountAmount =
                    discountAmount.multiply(ratio).setScale(0, RoundingMode.HALF_UP);
            firstDetail.setSpecDiscountAmount(
                    firstDetail.getSpecDiscountAmount() + specDiscountAmount.longValue());
            allocatedDiscountAmount = allocatedDiscountAmount.add(specDiscountAmount);

            // 分配优惠券优惠到第一个规格的参与商品
            BigDecimal specCouponAmount =
                    couponAmount.multiply(ratio).setScale(0, RoundingMode.HALF_UP);
            firstDetail.setSpecCouponAmount(
                    firstDetail.getSpecCouponAmount() + specCouponAmount.longValue());
            allocatedCouponAmount = allocatedCouponAmount.add(specCouponAmount);
        }

        // 处理其他规格（除最后一个）
        for (int i = 1; i < details.size() - 1; i++) {
            MerchantOrderPriceDetailAppDto detail = details.get(i);
            BigDecimal specTotalAmount = BigDecimal.valueOf(detail.getSpecTotalAmount());

            // 计算比例
            BigDecimal ratio =
                    specTotalAmount.divide(participatingItemsTotalAmount, 10, RoundingMode.HALF_UP);

            // 分配满减优惠
            BigDecimal specDiscountAmount =
                    discountAmount.multiply(ratio).setScale(0, RoundingMode.HALF_UP);
            detail.setSpecDiscountAmount(specDiscountAmount.longValue());
            allocatedDiscountAmount = allocatedDiscountAmount.add(specDiscountAmount);

            // 分配优惠券优惠
            BigDecimal specCouponAmount =
                    couponAmount.multiply(ratio).setScale(0, RoundingMode.HALF_UP);
            detail.setSpecCouponAmount(specCouponAmount.longValue());
            allocatedCouponAmount = allocatedCouponAmount.add(specCouponAmount);
        }

        // 处理最后一个规格，确保总和正确
        if (details.size() > 1) {
            MerchantOrderPriceDetailAppDto lastDetail = details.getLast();
            BigDecimal remainingDiscountAmount = discountAmount.subtract(allocatedDiscountAmount);
            BigDecimal remainingCouponAmount = couponAmount.subtract(allocatedCouponAmount);

            lastDetail.setSpecDiscountAmount(remainingDiscountAmount.longValue());
            lastDetail.setSpecCouponAmount(remainingCouponAmount.longValue());
        }
    }

    /**
     * 计算每个规格的最终支付金额
     *
     * @param details 规格详情列表
     * @param payAmount 总支付金额
     */
    private void calculateSpecPayAmounts(List<MerchantOrderPriceDetailAppDto> details,
            BigDecimal payAmount) {

        BigDecimal allocatedPayAmount = BigDecimal.ZERO;

        // 处理前n-1个规格
        for (int i = 0; i < details.size() - 1; i++) {
            MerchantOrderPriceDetailAppDto detail = details.get(i);
            BigDecimal specTotalAmount = BigDecimal.valueOf(detail.getSpecTotalAmount());

            // 计算该规格的总优惠金额
            BigDecimal specTotalDiscountAmount =
                    BigDecimal.valueOf(detail.getSpecFirstBuyDiscountAmount()
                            + detail.getSpecDiscountAmount() + detail.getSpecCouponAmount());

            // 计算支付金额
            BigDecimal specPayAmount = specTotalAmount.subtract(specTotalDiscountAmount);
            detail.setSpecPayAmount(specPayAmount.longValue());
            allocatedPayAmount = allocatedPayAmount.add(specPayAmount);
        }

        // 处理最后一个规格，确保总和正确
        if (!details.isEmpty()) {
            MerchantOrderPriceDetailAppDto lastDetail = details.getLast();
            BigDecimal lastSpecPayAmount = payAmount.subtract(allocatedPayAmount);
            lastDetail.setSpecPayAmount(lastSpecPayAmount.longValue());
        }
    }

    /**
     * 构建订单缓存值对象
     */
    private OrderCacheValObj buildOrderCacheValObj(CreateOrderCommand orderCmd,
            MerchantOrderPriceDetailAppDto detail) {
        return OrderCacheValObj.builder().orderNo(orderCmd.getOrderNo())
                .goodsId(detail.getGoodsId()).userId(orderCmd.getUserId())
                .buyCount(detail.getBuyCount()).amount(detail.getSpecPayAmount())
                .anonymousStatus(orderCmd.getAnonymousStatus()).build();
    }

    /**
     * 库存扣减
     */
    private Mono<CreateOrderItemCommand> deductStock(CreateOrderItemCommand command) {
        return stockRootService
                .deductionStock(
                        StringIdentifier.builder().id(command.getSpecificationId().toString())
                                .build(),
                        command.getBuyCount(), StockBusinessTypeEnum.SPECIFICATION)
                .filter(Boolean::booleanValue)
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.STOCK_NOT_ENOUGH)))
                .thenReturn(command);
    }

    /**
     * 获取分布式锁
     */
    private Mono<Boolean> acquireDistributedLock(Long goodsId) {
        return lockRootService
                .acquireTransactionObjectLockMono(ZookeeperLockObject.LOCKS_PAY_CALLBACK, goodsId);
    }

    /**
     * 生成订单ID和订单号
     */
    private Mono<CreateOrderCommand> generateOrderIdAndNoWithCoupon(CreateOrderCommand cmd,
            ProductTypeEnum productType, Long couponUserId) {
        return Mono
                .zip(orderRootService.generateId(),
                        orderRootService.generateOrderNo(MERCHANT_PRODUCT, productType))
                .map(tuple -> {
                    cmd.setOrderId(tuple.getT1());
                    cmd.setOrderNo(tuple.getT2());
                    cmd.setCouponUserId(couponUserId);
                    return cmd;
                });
    }
}

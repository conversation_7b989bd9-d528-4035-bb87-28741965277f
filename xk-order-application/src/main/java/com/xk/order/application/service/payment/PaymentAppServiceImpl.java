package com.xk.order.application.service.payment;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.order.application.action.query.payment.PaymentQuery;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.payment.OrderPaymentRefundEvent;
import com.xk.order.domain.event.payment.RefundStatusChangeEvent;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.enums.payment.RefundStatusEnum;
import com.xk.order.interfaces.dto.req.payment.RefundReq;
import com.xk.order.interfaces.dto.rsp.payment.PaymentRsp;
import com.xk.order.interfaces.service.payment.PaymentAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentAppServiceImpl implements PaymentAppService {

    private final PaymentRootService paymentRootService;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<Void> createRefund(Mono<RefundReq> mono) {
        return mono.flatMap(refundReq -> {
            log.info("PaymentAppService 执行退款1：{}", JSON.toJSONString(refundReq));
            // 查询支付单
            return queryDispatcher.executeQuery(
                    Mono.just(PaymentQuery.builder().orderNo(refundReq.getOrderNo()).build()),
                    PaymentQuery.class, PaymentRsp.class).flatMap(paymentRsp -> {
                        log.info("PaymentAppService 执行退款2：{}", JSON.toJSONString(paymentRsp));
                        if (refundReq.getOperateType() == 1) {
                            // 立即退款
                            return paymentRootService
                                    .refund(Mono.just(PaymentRoot.builder()
                                            .identifier(PaymentIdentifier.builder().paymentId(-1L)
                                                    .build())
                                            .paymentEntity(PaymentEntity.builder()
                                                    .orderNo(refundReq.getOrderNo())
                                                    .payNo(refundReq.getPayNo()).build())
                                            .refundEntity(RefundEntity.builder()
                                                    .paymentId(refundReq.getPaymentId()).remark(refundReq.getRemark()).build())
                                            .build()))
                                    .flatMap(b -> {
                                        log.info("PaymentAppService 执行退款3：{}", b);
                                        if (b) {
                                            // 退款成功发送事件
                                            /*return sendRefundStatusChangeEvent(refundReq,
                                                    RefundStatusEnum.PAID, new Date());*/
                                            return Mono.empty();
                                        } else {
                                            // 退款失败
                                            return Mono.error(new XkOrderApplicationException(
                                                    XkOrderApplicationErrorEnum.APPLICATION_ERROR));
                                        }
                                    });
                        } else {
                            // 拒绝退款
                            return sendRefundStatusChangeEvent(refundReq, RefundStatusEnum.REFUSE,
                                    null);
                        }
                    });
        });
    }

    private Mono<Void> sendRefundStatusChangeEvent(RefundReq refundReq,
            RefundStatusEnum refundStatusEnum, Date refundTime) {
        return Mono.fromCallable(() -> {
            log.info("PaymentAppService 发送退款单状态变更事件...{}，{}，{}", refundReq.getPaymentId(), refundStatusEnum,
                    refundTime);

            EventRoot event = EventRoot.builder().domainEvent(RefundStatusChangeEvent.builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(RefundStatusChangeEvent.class))
                    .paymentId(refundReq.getPaymentId()).refundStatus(refundStatusEnum.getCode())
                    .remark(refundReq.getRemark()).refundTime(refundTime).build()).build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("PaymentAppService 发送退款单状态变更事件失败：{}", refundReq.getPaymentId());
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("PaymentAppService 发送退款单状态变更事件失败：{}，{}", refundReq.getPaymentId(), e.getMessage());
            }
            return null;
        });
    }
}

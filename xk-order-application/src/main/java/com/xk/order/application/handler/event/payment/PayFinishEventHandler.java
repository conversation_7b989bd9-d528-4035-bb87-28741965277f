package com.xk.order.application.handler.event.payment;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.xk.order.application.action.query.order.OrderSearchByIdDetailQuery;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.domain.model.risk.RiskControlRoot;
import com.xk.order.domain.model.risk.entity.RefundRiskControlEntity;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.domain.service.risk.RiskControlRootService;
import com.xk.order.infrastructure.cache.dao.payment.PaymentRepetitionDao;
import com.xk.order.infrastructure.cache.key.payment.PaymentRepetitionKey;
import com.xk.order.interfaces.dto.rsp.order.OrderSearchByIdDetailRsp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.event.pay.PayFinishEvent;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.order.application.action.command.payment.CreatePaymentDetailCommand;
import com.xk.order.application.action.command.payment.UpdatePaymentCommand;
import com.xk.order.application.action.query.payment.PaymentQuery;
import com.xk.order.domain.event.order.PaymentOrderSuccessEvent;
import com.xk.order.domain.event.payment.OrderPaymentRefundEvent;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.payment.valobj.RiskControlValObj;
import com.xk.order.domain.repository.order.OrderRootQueryRepository;
import com.xk.order.enums.payment.*;
import com.xk.order.interfaces.dto.rsp.payment.PaymentRsp;
import com.xk.tp.enums.access.AccessChannelTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class PayFinishEventHandler extends AbstractEventVerticle<PayFinishEvent> {

    private final LockRootService lockRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final OrderRootQueryRepository orderRootQueryRepository;
    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;
    private EventRootService eventRootService;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final RiskControlRootService riskControlRootService;
    private final PaymentRootService paymentRootService;
    private final PaymentRepetitionDao paymentRepetitionDao;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<PayFinishEvent> event) {

        return event.flatMap(payFinishEvent -> {
            try {
                lockRootService.acquireTransactionObjectLock(ZookeeperLockObject.LOCKS_PAY_CALLBACK,
                        payFinishEvent.getPayNo() + "_" + payFinishEvent.getOrderNo());
            } catch (Throwable e) {
                return Mono.error(e);
            }
            log.info("PayFinishEventHandler 接收支付成功事件：{},{}", payFinishEvent.getOrderNo(),
                    JSON.toJSONString(payFinishEvent));
            // 支付失败
            if (payFinishEvent.getStatus() == 2) {
                log.error("PayFinishEventHandler 订单：{}，支付失败", payFinishEvent.getOrderNo());
                return Mono.empty();
            }
            // 去重
            PaymentRepetitionKey repetitionKey = PaymentRepetitionKey.builder()
                    .orderNo(payFinishEvent.getOrderNo()).payNo(payFinishEvent.getPayNo()).build();
            boolean exists = paymentRepetitionDao.exists(repetitionKey);
            if (exists) {
                log.error("PayFinishEventHandler 订单：{}，支付单：{}，重复", payFinishEvent.getOrderNo(),
                        payFinishEvent.getPayNo());
                return Mono.empty();
            } else {
                paymentRepetitionDao.addValue(repetitionKey, 1);
            }
            // 支付类型
            PaymentPayTypeEnum paymentPayTypeEnum =
                    switch (AccessChannelTypeEnum.getEnumByValue(payFinishEvent.getChannelType())) {
                        case weixin -> PaymentPayTypeEnum.WECHAT_PAY;
                        case aliPay -> PaymentPayTypeEnum.ALIPAY;
                        default -> null;
                    };
            // 查询支付单
            return queryDispatcher
                    .executeQuery(Mono.just(payFinishEvent), PaymentQuery.class, (s, t) -> {
                        return PaymentQuery.builder().orderNo(s.getOrderNo()).build();
                    }, PaymentRsp.class).filter(paymentRsp -> {
                        return PayStatusEnum.PENDING.equals(paymentRsp.getPayStatus())
                                || (PayStatusEnum.PAID.equals(paymentRsp.getPayStatus())
                                        && !payFinishEvent.getPayNo()
                                                .equals(paymentRsp.getPayNo()));
                    }).flatMap(paymentRsp -> {
                        // 正常流程
                        if (PayStatusEnum.PENDING.equals(paymentRsp.getPayStatus())) {
                            log.info("PayFinishEventHandler 进入正常流程：{}",
                                    payFinishEvent.getOrderNo());
                            // 修改支付单
                            return commandDispatcher.executeCommand(Mono.just(paymentRsp),
                                    UpdatePaymentCommand.class, (s, t) -> {
                                        return UpdatePaymentCommand.builder()
                                                .orderNo(s.getOrderNo())
                                                .payNo(payFinishEvent.getPayNo())
                                                .payType(paymentPayTypeEnum)
                                                .payTime(payFinishEvent.getPayTime())
                                                .updateTime(new Date())
                                                .payStatus(PayStatusEnum.PAID).build();
                                    })
                                    // 添加流水
                                    .then(commandDispatcher.executeCommand(
                                            Mono.just(payFinishEvent),
                                            CreatePaymentDetailCommand.class, (s, t) -> {
                                                return CreatePaymentDetailCommand.builder()
                                                        .paymentId(paymentRsp.getPaymentId())
                                                        .orderNo(s.getOrderNo()).payNo(s.getPayNo())
                                                        .payDirection(PayDirectionEnum.PAYMENT)
                                                        .remark(s.getRemark())
                                                        .status(PayDetailStatusEnum
                                                                .getByCode(s.getStatus()))
                                                        .amount(s.getAmount())
                                                        .createTime(new Date()).build();
                                            }))
                                    // 发送事件
                                    .then(sendPaymentSuccessEvent(payFinishEvent, paymentRsp,
                                            paymentPayTypeEnum));
                        } else if (PayStatusEnum.PAID.equals(paymentRsp.getPayStatus())) {
                            // 退款流程
                            return getUserId(payFinishEvent.getOrderNo()).flatMap(userId -> {
                                log.info("PayFinishEventHandler 进入退款流程：{},{}",
                                        payFinishEvent.getOrderNo(), userId);
                                // 风控
                                return riskControlRootService
                                        .checkPayRefundLimit(Mono.just(RiskControlRoot.builder()
                                                .refundRiskControlEntity(RefundRiskControlEntity
                                                        .builder().userId(userId).build())
                                                .build()))
                                        .flatMap(b1 -> {
                                            return riskControlRootService
                                                    .checkPayAmountLimit(Mono.just(RiskControlRoot
                                                            .builder()
                                                            .refundRiskControlEntity(
                                                                    RefundRiskControlEntity
                                                                            .builder()
                                                                            .amount(payFinishEvent
                                                                                    .getAmount())
                                                                            .build())
                                                            .build()))
                                                    .flatMap(b2 -> {
                                                        RefundTypeEnum refundTypeEnum = null;
                                                        RefundStatusEnum refundStatusEnum = null;
                                                        Date refundTime = null;
                                                        if (!b1) {
                                                            refundTypeEnum =
                                                                    RefundTypeEnum.REPEATEDLY;
                                                            refundStatusEnum =
                                                                    RefundStatusEnum.PENDING;
                                                        } else if (!b2) {
                                                            refundTypeEnum =
                                                                    RefundTypeEnum.WHOLESALE;
                                                            refundStatusEnum =
                                                                    RefundStatusEnum.PENDING;
                                                        } else {
                                                            // refundTypeEnum = RefundTypeEnum.FAIL;
                                                            refundStatusEnum =
                                                                    RefundStatusEnum.PAID;
                                                            refundTime = new Date();
                                                        }
                                                        log.info(
                                                                "PayFinishEventHandler 风控校验结果：{}，{}，{}",
                                                                refundTypeEnum, refundStatusEnum,
                                                                refundTime);
                                                        PaymentRoot refundPaymentRoot = PaymentRoot
                                                                .builder()
                                                                .identifier(PaymentIdentifier
                                                                        .builder()
                                                                        .paymentId(paymentRsp
                                                                                .getPaymentId())
                                                                        .build())
                                                                .refundEntity(RefundEntity.builder()
                                                                        .orderNo(payFinishEvent
                                                                                .getOrderNo())
                                                                        .userId(userId)
                                                                        .refundStatus(
                                                                                refundStatusEnum)
                                                                        .platformType(paymentRsp
                                                                                .getPlatformType())
                                                                        .payType(paymentPayTypeEnum)
                                                                        .payTime(payFinishEvent
                                                                                .getPayTime())
                                                                        .amount(payFinishEvent
                                                                                .getAmount())
                                                                        .remark(payFinishEvent
                                                                                .getRemark())
                                                                        .build())
                                                                .build();

                                                        OrderPaymentRefundEvent.OrderPaymentRefundEventBuilder refundEventBuilder =
                                                                OrderPaymentRefundEvent.builder()
                                                                        .identifier(EventRoot
                                                                                .getCommonsDomainEventIdentifier(
                                                                                        OrderPaymentRefundEvent.class))
                                                                        .orderNo(payFinishEvent
                                                                                .getOrderNo())
                                                                        .payNo(payFinishEvent
                                                                                .getPayNo())
                                                                        .refundType(Objects.nonNull(
                                                                                refundTypeEnum)
                                                                                        ? refundTypeEnum
                                                                                                .getCode()
                                                                                        : null)
                                                                        .refundStatus(
                                                                                refundStatusEnum
                                                                                        .getCode())
                                                                        .platformType(paymentRsp
                                                                                .getPlatformType()
                                                                                .getValue())
                                                                        .payType(paymentPayTypeEnum
                                                                                .getCode())
                                                                        .payTime(payFinishEvent
                                                                                .getPayTime())
                                                                        .refundTime(refundTime)
                                                                        .amount(payFinishEvent
                                                                                .getAmount())
                                                                        .remark(payFinishEvent
                                                                                .getRemark())
                                                                        .autoRefund(false);

                                                        if (RefundStatusEnum.PAID
                                                                .equals(refundStatusEnum)) {// 风控通过
                                                            // 直接退款记录退款单和流水
                                                            log.info(
                                                                    "PayFinishEventHandler 进入退款流程 未触发风控：{}",
                                                                    payFinishEvent.getOrderNo());
                                                            // 添加退款单
                                                            return paymentRootService
                                                                    .saveRefund(Mono.just(
                                                                            refundPaymentRoot))
                                                                    // 退款
                                                                    .flatMap(refundId -> {
                                                                        refundEventBuilder
                                                                                .paymentId(
                                                                                        refundId);
                                                                        return paymentRootService
                                                                                .refund(Mono.just(
                                                                                        PaymentRoot
                                                                                                .builder()
                                                                                                .identifier(
                                                                                                        PaymentIdentifier
                                                                                                                .builder()
                                                                                                                .paymentId(
                                                                                                                        paymentRsp
                                                                                                                                .getPaymentId())
                                                                                                                .build())
                                                                                                .paymentEntity(
                                                                                                        PaymentEntity
                                                                                                                .builder()
                                                                                                                .orderNo(
                                                                                                                        payFinishEvent
                                                                                                                                .getOrderNo())
                                                                                                                .payNo(payFinishEvent
                                                                                                                        .getPayNo())
                                                                                                                .build())
                                                                                                .refundEntity(
                                                                                                        RefundEntity
                                                                                                                .builder()
                                                                                                                .paymentId(
                                                                                                                        refundId)
                                                                                                                .build())
                                                                                                .build()));
                                                                    }).flatMap(ret -> {
                                                                        log.info(
                                                                                "PayFinishEventHandler 直接退款结果：{}，{}",
                                                                                payFinishEvent
                                                                                        .getOrderNo(),
                                                                                ret);
                                                                        if (ret) {
                                                                            // 退款成功
                                                                            return sendPaymentRefundEvent(
                                                                                    refundEventBuilder
                                                                                            .build());
                                                                        } else {
                                                                            // 退款失败
                                                                            return sendPaymentRefundEvent(
                                                                                    refundEventBuilder
                                                                                            .refundType(
                                                                                                    RefundTypeEnum.FAIL
                                                                                                            .getCode())
                                                                                            .refundStatus(
                                                                                                    RefundStatusEnum.PENDING
                                                                                                            .getCode())
                                                                                            .build());
                                                                        }
                                                                    });
                                                        } else {// 风控未通过
                                                            log.info(
                                                                    "PayFinishEventHandler 进入退款流程 触发风控：{}",
                                                                    payFinishEvent.getOrderNo());
                                                            // 添加退款单
                                                            return paymentRootService
                                                                    .saveRefund(Mono.just(
                                                                            refundPaymentRoot))
                                                                    // 发送事件
                                                                    .flatMap(refundId -> {
                                                                        return sendPaymentRefundEvent(
                                                                                refundEventBuilder
                                                                                        .paymentId(
                                                                                                refundId)
                                                                                        .build());
                                                                    });
                                                        }
                                                    });
                                        });
                            });
                        } else {
                            return Mono.empty();
                        }
                    });
        });
    }

    private Mono<Void> sendPaymentSuccessEvent(PayFinishEvent payFinishEvent, PaymentRsp paymentRsp,
            PaymentPayTypeEnum paymentPayTypeEnum) {
        return Mono.fromCallable(() -> {

            Integer status = switch (payFinishEvent.getStatus()) {
                case 1 -> PayStatusEnum.PAID.getCode();
                case 2 -> PayStatusEnum.FAILED.getCode();
                default -> null;
            };

            EventRoot event = EventRoot.builder().domainEvent(PaymentOrderSuccessEvent.builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(PaymentOrderSuccessEvent.class))
                    .payNo(payFinishEvent.getPayNo()).orderNo(payFinishEvent.getOrderNo())
                    .payStatus(status).payType(paymentPayTypeEnum.getCode())
                    .paymentId(paymentRsp.getPaymentId().toString())
                    .payTime(payFinishEvent.getPayTime()).build()).build();
            log.info("PayFinishEventHandler 发送支付成功事件...{},{}", payFinishEvent.getOrderNo(),
                    JSON.toJSONString(event));
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("PayFinishEventHandler 发送支付成功事件失败：{}", payFinishEvent.getOrderNo());
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("PayFinishEventHandler 发送支付成功事件失败：{}，{}", payFinishEvent.getOrderNo(),
                        e.getMessage());
            }
            return null;
        });
    }

    private Mono<Void> sendPaymentRefundEvent(OrderPaymentRefundEvent refundEvent) {
        return Mono.fromCallable(() -> {
            log.info("PayFinishEventHandler 发送退款事件...{},{}", refundEvent.getOrderNo(),
                    JSON.toJSONString(refundEvent));
            EventRoot event = EventRoot.builder().domainEvent(refundEvent).build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("PayFinishEventHandler 发送退款事件失败：{}", refundEvent.getOrderNo());
                }
            } catch (ExceptionWrapperThrowable e) {
                log.error("PayFinishEventHandler 发送退款事件失败：{}，{}", refundEvent.getOrderNo(),
                        e.getMessage());
            }
            return null;
        });
    }

    private Mono<RiskControlValObj> getRiskConfig() {
        return businessConfigRootQueryRepository
                .findList(BusinessConfigEntity.builder().businessType(1).groupType("basic")
                        .groupId("0").keys(List.of("pay_amount_limit", "pay_refund_limit")).build())
                .collectList().flatMap(businessConfigEntities -> {
                    RiskControlValObj.RiskControlValObjBuilder builder =
                            RiskControlValObj.builder();
                    businessConfigEntities.forEach(businessConfigEntity -> {
                        if ("pay_amount_limit".equals(businessConfigEntity.getKey())) {
                            builder.payAmountLimit(Integer.valueOf(businessConfigEntity.getVal()));
                        }
                        if ("pay_refund_limit".equals(businessConfigEntity.getKey())) {
                            builder.payRefundLimit(Integer.valueOf(businessConfigEntity.getVal()));
                        }
                    });
                    return Mono.just(builder.build());
                });
    }

    private Mono<Long> getUserId(String orderNo) {
        return orderRootQueryRepository
                .getRootNoLogistics(OrderIdentifier.builder().orderNo(orderNo).build())
                .flatMap(orderRoot -> {
                    return Mono.just(orderRoot.getOrderEntity().getUserId());
                });
    }

    private Mono<OrderSearchByIdDetailRsp> getOrderInfo(String orderNo) {
        return actionQueryDispatcher.executeQuery(
                Mono.just(OrderSearchByIdDetailQuery.builder().orderNo(orderNo).build()),
                OrderSearchByIdDetailQuery.class, OrderSearchByIdDetailRsp.class);
    }
}



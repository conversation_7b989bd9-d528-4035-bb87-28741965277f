package com.xk.search.domain.event.order;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_SEARCH, domainName = DomainNameEnum.SEARCH)
public class OrderUpdateCheckEvent extends AbstractCommonsDomainEvent implements Serializable {

    private final String orderNo;
    private final Integer orderType;

    @Builder
    public OrderUpdateCheckEvent(@NonNull Long identifier, Map<String, Object> context, String orderNo, Integer orderType) {
        super(identifier, context);
        this.orderNo = orderNo;
        this.orderType = orderType;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}

package com.xk.order.server;

import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.domain.service.payment.PaymentRootService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Mono;

@SpringBootTest(classes = XkOrderServer.class)
class XkOrderServerTests {

    @Autowired
    private PaymentRootService paymentRootService;

    @Test
    void contextLoads() {
        paymentRootService.refund(Mono.just(PaymentRoot.builder()
                        .identifier(PaymentIdentifier.builder().paymentId(-1L).build())
                        .paymentEntity(PaymentEntity.builder()
                                .orderNo("MAL250804151430480300000000100")
                                .payNo("0149default250804151432P605ac13669300000")
                                .build())
                .build())).block();
    }
}

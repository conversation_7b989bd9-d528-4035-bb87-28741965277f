package com.xk.goods.infrastructure.data.persistence.serial;

import java.util.List;

import com.myco.framework.cache.annotations.GroupStrategy;
import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.goods.infrastructure.data.po.serial.GSerialTeam;

/**
 * <AUTHOR>
 * @description 针对表【g_serial_team(卡密球队表)】的数据库操作Mapper
 * @createDate 2025-07-23 14:01:30
 * @Entity com.xk.goods.infrastructure.data.po.serial.GSerialTeam
 */
@Repository
@Table("g_serial_team")
public interface GSerialTeamMapper {

    int deleteByPrimaryKey(GSerialTeam record);

    int insert(GSerialTeam record);

    int insertSelective(GSerialTeam record);

    GSerialTeam selectByPrimaryKey(GSerialTeam record);

    int updateByPrimaryKeySelective(GSerialTeam record);

    int updateByPrimaryKey(GSerialTeam record);

    // @GroupStrategy("serialGroupId")
    List<GSerialTeam> selectBySerialGroupId(GSerialTeam record);

    List<GSerialTeam> searchTeamPager(Pagination pagination);

    List<GSerialTeam> searchTeamByTeamMemberId(GSerialTeam record);

    /**
     * Batch insert team items
     * @param records List of records to insert
     * @return Number of inserted records
     */
    int batchInsert(List<GSerialTeam> records);

    /**
     * Batch update team items
     * @param records List of records to update
     * @return Number of updated records
     */
    int batchUpdate(List<GSerialTeam> records);
}

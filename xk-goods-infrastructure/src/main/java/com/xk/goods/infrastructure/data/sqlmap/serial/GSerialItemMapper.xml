<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.goods.infrastructure.data.persistence.serial.GSerialItemMapper">

    <resultMap id="BaseResultMap" type="com.xk.goods.infrastructure.data.po.serial.GSerialItem">
            <id property="serialItemId" column="serial_item_id" jdbcType="BIGINT"/>
            <result property="serialItemType" column="serial_item_type" jdbcType="INTEGER"/>
            <result property="serialGroupId" column="serial_group_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        serial_item_id,serial_item_type,serial_group_id,
        status,update_id,create_id,
        update_time,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialItem" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from g_serial_item
        where  serial_item_id = #{serialItemId,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialItem">
        delete from g_serial_item
        where  serial_item_id = #{serialItemId,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="serial_item_id" keyProperty="serialItemId" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialItem" useGeneratedKeys="true">
        insert into g_serial_item
        ( serial_item_id,serial_item_type,serial_group_id
        ,status,update_id,create_id
        ,update_time,create_time)
        values (#{serialItemId,jdbcType=BIGINT},#{serialItemType,jdbcType=INTEGER},#{serialGroupId,jdbcType=BIGINT}
        ,#{status,jdbcType=TINYINT},#{updateId,jdbcType=BIGINT},#{createId,jdbcType=BIGINT}
        ,#{updateTime,jdbcType=TIMESTAMP},#{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="serial_item_id" keyProperty="serialItemId" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialItem" useGeneratedKeys="true">
        insert into g_serial_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="serialItemId != null">serial_item_id,</if>
                <if test="serialItemType != null">serial_item_type,</if>
                <if test="serialGroupId != null">serial_group_id,</if>
                <if test="status != null">status,</if>
                <if test="updateId != null">update_id,</if>
                <if test="createId != null">create_id,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="serialItemId != null">#{serialItemId,jdbcType=BIGINT},</if>
                <if test="serialItemType != null">#{serialItemType,jdbcType=INTEGER},</if>
                <if test="serialGroupId != null">#{serialGroupId,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
                <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialItem">
        update g_serial_item
        <set>
                <if test="serialItemType != null">
                    serial_item_type = #{serialItemType,jdbcType=INTEGER},
                </if>
                <if test="serialGroupId != null">
                    serial_group_id = #{serialGroupId,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="updateId != null">
                    update_id = #{updateId,jdbcType=BIGINT},
                </if>
                <if test="createId != null">
                    create_id = #{createId,jdbcType=BIGINT},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   serial_item_id = #{serialItemId,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialItem">
        update g_serial_item
        set
            serial_item_type =  #{serialItemType,jdbcType=INTEGER},
            serial_group_id =  #{serialGroupId,jdbcType=BIGINT},
            status =  #{status,jdbcType=TINYINT},
            update_id =  #{updateId,jdbcType=BIGINT},
            create_id =  #{createId,jdbcType=BIGINT},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_time =  #{createTime,jdbcType=TIMESTAMP}
        where   serial_item_id = #{serialItemId,jdbcType=BIGINT}
    </update>

    <!-- Batch insert for serial items -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO g_serial_item (
            serial_item_id, serial_item_type, serial_group_id, status,
            update_id, create_id, update_time, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.serialItemId,jdbcType=BIGINT},
                #{item.serialItemType,jdbcType=INTEGER},
                #{item.serialGroupId,jdbcType=BIGINT},
                #{item.status,jdbcType=TINYINT},
                #{item.updateId,jdbcType=BIGINT},
                #{item.createId,jdbcType=BIGINT},
                #{item.updateTime,jdbcType=TIMESTAMP},
                #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- Batch update for serial items -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE g_serial_item
            <set>
                <if test="item.serialItemType != null">serial_item_type = #{item.serialItemType,jdbcType=INTEGER},</if>
                <if test="item.serialGroupId != null">serial_group_id = #{item.serialGroupId,jdbcType=BIGINT},</if>
                <if test="item.status != null">status = #{item.status,jdbcType=TINYINT},</if>
                <if test="item.updateId != null">update_id = #{item.updateId,jdbcType=BIGINT},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime,jdbcType=TIMESTAMP}</if>
            </set>
            WHERE serial_item_id = #{item.serialItemId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>

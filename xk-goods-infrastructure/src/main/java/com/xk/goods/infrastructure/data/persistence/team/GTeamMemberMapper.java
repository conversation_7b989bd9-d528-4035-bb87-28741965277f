package com.xk.goods.infrastructure.data.persistence.team;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.goods.infrastructure.data.po.team.GTeamMember;

/**
* <AUTHOR>
* @description 针对表【g_team_member(队员表)】的数据库操作Mapper
* @createDate 2025-04-09 19:05:08
* @Entity com.xk.goods.infrastructure.data.po.team.GTeamMember
*/
@Repository
@Table("g_team_member")
public interface GTeamMemberMapper {

    int deleteByPrimaryKey(GTeamMember id);

    int insert(GTeamMember record);

    int insertSelective(GTeamMember record);

    GTeamMember selectByPrimaryKey(GTeamMember id);

    int updateByPrimaryKeySelective(GTeamMember record);

    int updateByPrimaryKey(GTeamMember record);

    List<GTeamMember> selectByPage(Pagination pagination);

    List<GTeamMember> selectInCondition(Pagination pagination);

    List<GTeamMember> selectAll();
}

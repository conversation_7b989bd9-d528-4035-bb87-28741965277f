package com.xk.goods.infrastructure.data.persistence.business;


import java.util.List;

import com.myco.framework.cache.annotations.GroupStrategy;
import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.goods.infrastructure.data.po.business.GBusinessRes;

/**
 * <AUTHOR>
 * @description 针对表【g_business_res(业务资源映射表)】的数据库操作Mapper
 * @createDate 2025-05-30 10:14:35
 * @Entity com.xk.goods.infrastructure.data.po.business.GBusinessRes
 */
@Repository
@Table("g_business_res")
public interface GBusinessResMapper {

    int deleteByPrimaryKey(GBusinessRes record);

    int insert(GBusinessRes record);

    int insertSelective(GBusinessRes record);

    GBusinessRes selectByPrimaryKey(GBusinessRes record);

    int updateByPrimaryKeySelective(GBusinessRes record);

    int updateByPrimaryKey(GBusinessRes record);

    @GroupStrategy("businessGroup")
    List<GBusinessRes> selectByBusinessGroup(GBusinessRes record);

    List<GBusinessRes> searchByBusinessRes(GBusinessRes record);

    /**
     * Batch query business resources by multiple business IDs
     * @param businessIds List of business IDs
     * @param businessGroupType Business group type
     * @return List of business resources
     */
    List<GBusinessRes> batchSelectByBusinessIds(List<Long> businessIds, Integer businessGroupType);
}

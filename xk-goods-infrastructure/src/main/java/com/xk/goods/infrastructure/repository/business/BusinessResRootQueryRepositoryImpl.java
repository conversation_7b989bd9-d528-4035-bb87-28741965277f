package com.xk.goods.infrastructure.repository.business;

import java.util.Comparator;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.infrastructure.data.persistence.business.GBusinessResMapper;
import com.xk.goods.infrastructure.data.po.business.GBusinessRes;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;

@Repository
@RequiredArgsConstructor
public class BusinessResRootQueryRepositoryImpl implements BusinessResRootQueryRepository {

    private final Converter converter;
    private final GBusinessResMapper gBusinessResMapper;

    @Override
    public Flux<BusinessResEntity> searchByBusinessGroup(BusinessResEntity entity) {
        return find(() -> {
            List<GBusinessRes> gBusinessRes = gBusinessResMapper
                    .selectByBusinessGroup(converter.convert(entity, GBusinessRes.class));
            gBusinessRes.sort(Comparator.comparing(GBusinessRes::getSort));
            return gBusinessRes;
        }, BusinessResEntity.class, converter::convert);
    }

    @Override
    public Flux<BusinessResEntity> searchByBusinessRes(BusinessResEntity entity) {
        return find(
                () -> gBusinessResMapper
                        .searchByBusinessRes(converter.convert(entity, GBusinessRes.class)),
                BusinessResEntity.class, converter::convert);
    }

    @Override
    public Flux<BusinessResEntity> batchSearchByBusinessIds(List<Long> businessIds, BusinessGroupTypeEnum businessGroupType) {
        if (businessIds == null || businessIds.isEmpty()) {
            return Flux.empty();
        }

        return find(() -> {
            List<GBusinessRes> gBusinessRes = gBusinessResMapper
                    .batchSelectByBusinessIds(businessIds, businessGroupType.getCode());
            gBusinessRes.sort(Comparator.comparing(GBusinessRes::getBusinessId)
                    .thenComparing(GBusinessRes::getSort));
            return gBusinessRes;
        }, BusinessResEntity.class, converter::convert);
    }
}

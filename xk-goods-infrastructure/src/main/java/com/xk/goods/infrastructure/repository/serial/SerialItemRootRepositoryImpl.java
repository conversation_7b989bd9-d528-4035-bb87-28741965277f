package com.xk.goods.infrastructure.repository.serial;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.goods.domain.model.serialitem.SerialItemRoot;
import com.xk.goods.domain.repository.serial.SerialItemRootRepository;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialItemMapper;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialOriginalItemMapper;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialSpecialItemMapper;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialTeamMapper;
import com.xk.goods.infrastructure.data.po.serial.GSerialItem;
import com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem;
import com.xk.goods.infrastructure.data.po.serial.GSerialSpecialItem;
import com.xk.goods.infrastructure.data.po.serial.GSerialTeam;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class SerialItemRootRepositoryImpl implements SerialItemRootRepository {

    private final GSerialOriginalItemMapper serialOriginalItemMapper;

    private final GSerialSpecialItemMapper serialSpecialItemMapper;

    private final GSerialTeamMapper serialTeamMapper;

    private final GSerialItemMapper serialItemMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(SerialItemRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getSerialItemEntity())
                        .flatMap(entity -> save(entity, GSerialItem.class, converter::convert,
                                serialItemMapper::insertSelective)))
                .then(Mono.justOrEmpty(root.getSerialOriginalItemEntity()))
                .flatMap(entity -> save(entity, GSerialOriginalItem.class, converter::convert,
                        serialOriginalItemMapper::insertSelective))
                .then(Mono.justOrEmpty(root.getSerialSpecialItemEntity()))
                .flatMap(entity -> save(entity, GSerialSpecialItem.class, converter::convert,
                        serialSpecialItemMapper::insertSelective))
                .then(Mono.justOrEmpty(root.getSerialTeamItemEntity()))
                .flatMap(entity -> save(entity, GSerialTeam.class, converter::convert,
                        serialTeamMapper::insertSelective))
                .then();
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(SerialItemRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(SerialItemRoot root) {
        return Mono.justOrEmpty(root).flatMap(domain -> Mono
                .justOrEmpty(domain.getSerialSpecialItemEntity())
                .flatMap(entity -> update(entity, GSerialSpecialItem.class, converter::convert,
                        serialSpecialItemMapper::updateByPrimaryKeySelective)))
                .then(Mono.justOrEmpty(root.getSerialOriginalItemEntity()))
                .flatMap(entity -> save(entity, GSerialOriginalItem.class, converter::convert,
                        serialOriginalItemMapper::updateByPrimaryKeySelective))
                .then(Mono.justOrEmpty(root.getSerialTeamItemEntity()))
                .flatMap(entity -> save(entity, GSerialTeam.class, converter::convert,
                        serialTeamMapper::updateByPrimaryKeySelective))
                .then();
    }

    @Override
    public Mono<Void> remove(SerialItemRoot root) {
        return null;
    }


    @Override
    public Mono<Void> removeBySerialGroupId(Long serialGroupId) {
        return Mono.just(serialOriginalItemMapper.deleteBySerialGroupId(serialGroupId)).then();
    }

    @Override
    public Mono<Void> batchSave(List<SerialItemRoot> roots) {
        if (roots == null || roots.isEmpty()) {
            return Mono.empty();
        }

        return Mono.fromCallable(() -> {
            // Separate entities for batch operations
            List<GSerialItem> serialItems = new ArrayList<>();
            List<GSerialOriginalItem> originalItems = new ArrayList<>();
            List<GSerialSpecialItem> specialItems = new ArrayList<>();
            List<GSerialTeam> teamItems = new ArrayList<>();

            // Convert domain objects to PO objects
            for (SerialItemRoot root : roots) {
                if (root.getSerialItemEntity() != null) {
                    serialItems.add(converter.convert(root.getSerialItemEntity(), GSerialItem.class));
                }
                if (root.getSerialOriginalItemEntity() != null) {
                    originalItems.add(converter.convert(root.getSerialOriginalItemEntity(), GSerialOriginalItem.class));
                }
                if (root.getSerialSpecialItemEntity() != null) {
                    specialItems.add(converter.convert(root.getSerialSpecialItemEntity(), GSerialSpecialItem.class));
                }
                if (root.getSerialTeamItemEntity() != null) {
                    teamItems.add(converter.convert(root.getSerialTeamItemEntity(), GSerialTeam.class));
                }
            }

            // Batch insert operations
            if (!serialItems.isEmpty()) {
                serialItemMapper.batchInsert(serialItems);
            }
            if (!originalItems.isEmpty()) {
                serialOriginalItemMapper.batchInsert(originalItems);
            }
            if (!specialItems.isEmpty()) {
                serialSpecialItemMapper.batchInsert(specialItems);
            }
            if (!teamItems.isEmpty()) {
                serialTeamMapper.batchInsert(teamItems);
            }

            return null;
        }).then();
    }
}

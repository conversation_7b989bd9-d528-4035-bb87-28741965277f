<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.goods.infrastructure.data.persistence.business.GBusinessResMapper">

    <resultMap id="BaseResultMap" type="com.xk.goods.infrastructure.data.po.business.GBusinessRes">
        <id property="businessResType" column="business_res_type" jdbcType="INTEGER"/>
        <id property="businessId" column="business_id" jdbcType="BIGINT"/>
        <id property="resId" column="res_id" jdbcType="INTEGER"/>
        <id property="businessGroupType" column="business_group_type" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        business_res_type
        ,business_id,res_id,
        business_group_type,sort
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.business.GBusinessRes"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_business_res
        where business_res_type = #{businessResType,jdbcType=INTEGER} AND business_id = #{businessId,jdbcType=BIGINT}
        AND res_id = #{resId,jdbcType=INTEGER}
    </select>
    <select id="selectByBusinessGroup" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_business_res
        where business_group_type = #{businessGroupType,jdbcType=INTEGER} AND business_id =
        #{businessId,jdbcType=BIGINT}
        order by sort
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.business.GBusinessRes">
        delete
        from g_business_res
        where business_res_type = #{businessResType,jdbcType=INTEGER}
          AND business_id = #{businessId,jdbcType=BIGINT}
          AND res_id = #{resId,jdbcType=INTEGER}
          AND business_group_type = #{businessGroupType,jdbcType=INTEGER}
    </delete>
    <insert id="insert">
        insert into g_business_res
        ( business_res_type, business_id, res_id
        , business_group_type, sort)
        values ( #{businessResType,jdbcType=INTEGER}, #{businessId,jdbcType=BIGINT}, #{resId,jdbcType=INTEGER}
               , #{businessGroupType,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective">
        insert into g_business_res
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessResType != null">business_res_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="resId != null">res_id,</if>
            <if test="businessGroupType != null">business_group_type,</if>
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessResType != null">#{businessResType,jdbcType=INTEGER},</if>
            <if test="businessId != null">#{businessId,jdbcType=BIGINT},</if>
            <if test="resId != null">#{resId,jdbcType=INTEGER},</if>
            <if test="businessGroupType != null">#{businessGroupType,jdbcType=INTEGER},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.goods.infrastructure.data.po.business.GBusinessRes">
        update g_business_res
        <set>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
        </set>
        where business_res_type = #{businessResType,jdbcType=INTEGER} AND business_id = #{businessId,jdbcType=BIGINT}
        AND res_id = #{resId,jdbcType=INTEGER} AND business_group_type = #{businessGroupType,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.business.GBusinessRes">
        update g_business_res
        set sort = #{sort,jdbcType=INTEGER}
        where business_res_type = #{businessResType,jdbcType=INTEGER}
          AND business_id = #{businessId,jdbcType=BIGINT}
          AND res_id = #{resId,jdbcType=INTEGER}
          AND business_group_type = #{businessGroupType,jdbcType=INTEGER}
    </update>

    <select id="searchByBusinessRes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_business_res
        <where>
            <if test="businessId != null">
                AND business_id = #{businessId}
            </if>
            <if test="businessResType != null">
                AND business_res_type = #{businessResType}
            </if>
        </where>
    </select>

    <!-- Batch select business resources by multiple business IDs -->
    <select id="batchSelectByBusinessIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM g_business_res
        WHERE business_group_type = #{businessGroupType,jdbcType=INTEGER}
        <if test="businessIds != null and businessIds.size() > 0">
            AND business_id IN
            <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                #{businessId,jdbcType=BIGINT}
            </foreach>
        </if>
        ORDER BY business_id, sort
    </select>
</mapper>

package com.xk.goods.infrastructure.adapter.activity;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.commons.MoneyHelper;
import com.xk.goods.domain.model.activity.GoodsActivityRoot;
import com.xk.goods.domain.model.activity.entity.DiscountEntity;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityPayValObj;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityShowValObj;
import com.xk.goods.domain.service.activity.ActivityAdapterService;
import com.xk.goods.enums.activity.ActivityTypeEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service(DiscountAdapterService.BEAN_NAME)
@RequiredArgsConstructor
public class DiscountAdapterService implements ActivityAdapterService {

    public static final String BEAN_NAME = "discountAdapterService";

    @Override
    public ActivityTypeEnum getActivityType() {
        return ActivityTypeEnum.DISCOUNT;
    }

    @Override
    public Mono<GoodsActivityShowValObj> calcShowAmount(GoodsActivityRoot root,
            GoodsActivityShowValObj goodsActivityShowValObj) {
        return Mono.just(goodsActivityShowValObj);
    }

    @Override
    public Mono<GoodsActivityPayValObj> calcPayAmount(GoodsActivityRoot root,
            GoodsActivityPayValObj goodsActivityPayValObj) {
        List<DiscountEntity> discountEntityList = root.getDiscountEntityList();
        if (CollectionUtils.isEmpty(discountEntityList)) {
            return Mono.just(goodsActivityPayValObj);
        }
        discountEntityList.sort(Comparator.comparing(DiscountEntity::getDiscountAmount).reversed());
        Long payAmount = goodsActivityPayValObj.getPayAmount();
        DiscountEntity discountEntity = discountEntityList.stream().filter(discount -> {
            if (CommonStatusEnum.ENABLE.equals(goodsActivityPayValObj.getFirstBuyDiscountStatus())
                    && goodsActivityPayValObj.getBuyCount() != null) {
                BigDecimal unitPrice = MoneyHelper.divide(goodsActivityPayValObj.getTotalAmount(),
                        (long) goodsActivityPayValObj.getBuyCount(), 0);
                return (payAmount + goodsActivityPayValObj.getFirstBuyDiscountAmount()
                        - unitPrice.longValue()) >= discount.getThresholdAmount();
            }
            return payAmount >= discount.getThresholdAmount();
        }).findFirst().orElse(null);

        if (discountEntity == null) {
            return Mono.just(goodsActivityPayValObj);
        }
        goodsActivityPayValObj.setPayAmount(payAmount - discountEntity.getDiscountAmount());
        goodsActivityPayValObj.setDiscountAmount(discountEntity.getDiscountAmount());
        goodsActivityPayValObj.setDiscountStatus(CommonStatusEnum.ENABLE);
        return Mono.just(goodsActivityPayValObj);
    }
}

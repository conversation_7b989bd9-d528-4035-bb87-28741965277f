<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.goods.infrastructure.data.persistence.team.GTeamMemberMapper">

    <resultMap id="BaseResultMap" type="com.xk.goods.infrastructure.data.po.team.GTeamMember">
        <id property="teamMemberId" column="team_member_id" jdbcType="BIGINT"/>
        <result property="memberType" column="member_type" jdbcType="INTEGER"/>
        <result property="memberCnName" column="member_cn_name" jdbcType="VARCHAR"/>
        <result property="memberEnName" column="member_en_name" jdbcType="VARCHAR"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        team_member_id
        ,member_type,member_cn_name,
        member_en_name,create_id,create_time,
        update_time,update_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_team_member
        where team_member_id = #{teamMemberId,jdbcType=BIGINT}
    </select>
    <select id="selectByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_team_member
        <where>
            <if test="memberType != null">
                and member_type = #{memberType}
            </if>
            <trim prefix="AND (" suffix=")" prefixOverrides="OR |AND ">
                <if test="memberCnName != null and memberCnName != ''">
                    member_cn_name LIKE CONCAT('%', #{memberCnName,jdbcType=VARCHAR}, '%')
                </if>
                <if test="memberEnName != null and memberEnName != ''">
                    OR member_en_name LIKE CONCAT('%', #{memberEnName,jdbcType=VARCHAR}, '%')
                </if>
            </trim>
        </where>
        order by team_member_id desc
    </select>
    <select id="selectInCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_team_member
        <where>
            <if test="memberType != null">
                and member_type = #{memberType}
            </if>
            <trim prefix="AND (" suffix=")" prefixOverrides="OR |AND ">
                <if test="memberCnName != null and memberCnName != ''">
                    member_cn_name = "${memberCnName}"
                </if>
                <if test="memberEnName != null and memberEnName != ''">
                    OR member_en_name = "${memberEnName}"
                </if>
            </trim>
        </where>
        order by team_member_id desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from g_team_member
        where team_member_id = #{teamMemberId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="team_member_id" keyProperty="teamMemberId"
            parameterType="com.xk.goods.infrastructure.data.po.team.GTeamMember" useGeneratedKeys="true">
        insert into g_team_member
        ( team_member_id, member_type, member_cn_name
        , member_en_name, create_id, create_time
        , update_time, update_id)
        values ( #{teamMemberId,jdbcType=BIGINT}, #{memberType,jdbcType=INTEGER}, #{memberCnName,jdbcType=VARCHAR}
               , #{memberEnName,jdbcType=VARCHAR}, #{createId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}
               , #{updateTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="team_member_id" keyProperty="teamMemberId"
            parameterType="com.xk.goods.infrastructure.data.po.team.GTeamMember" useGeneratedKeys="true">
        insert into g_team_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamMemberId != null">team_member_id,</if>
            <if test="memberType != null">member_type,</if>
            <if test="memberCnName != null">member_cn_name,</if>
            <if test="memberEnName != null">member_en_name,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateId != null">update_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamMemberId != null">#{teamMemberId,jdbcType=BIGINT},</if>
            <if test="memberType != null">#{memberType,jdbcType=INTEGER},</if>
            <if test="memberCnName != null">#{memberCnName,jdbcType=VARCHAR},</if>
            <if test="memberEnName != null">#{memberEnName,jdbcType=VARCHAR},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.goods.infrastructure.data.po.team.GTeamMember">
        update g_team_member
        <set>
            <if test="memberType != null">
                member_type = #{memberType,jdbcType=INTEGER},
            </if>
            <if test="memberCnName != null">
                member_cn_name = #{memberCnName,jdbcType=VARCHAR},
            </if>
            <if test="memberEnName != null">
                member_en_name = #{memberEnName,jdbcType=VARCHAR},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
        </set>
        where team_member_id = #{teamMemberId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.team.GTeamMember">
        update g_team_member
        set member_type    = #{memberType,jdbcType=INTEGER},
            member_cn_name = #{memberCnName,jdbcType=VARCHAR},
            member_en_name = #{memberEnName,jdbcType=VARCHAR},
            create_id      = #{createId,jdbcType=BIGINT},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_time    = #{updateTime,jdbcType=TIMESTAMP},
            update_id      = #{updateId,jdbcType=BIGINT}
        where team_member_id = #{teamMemberId,jdbcType=BIGINT}
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_team_member
    </select>
</mapper>

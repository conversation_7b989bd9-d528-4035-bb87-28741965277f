package com.xk.goods.infrastructure.data.persistence.serial;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.goods.infrastructure.data.po.serial.GSerialItem;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【g_serial_item(卡密条目表)】的数据库操作Mapper
* @createDate 2025-07-23 11:01:46
* @Entity com.xk.goods.infrastructure.data.po.serial.GSerialItem
*/
@Repository
@Table("g_serial_item")
public interface GSerialItemMapper {

    int deleteByPrimaryKey(GSerialItem  record);

    int insert(GSerialItem record);

    int insertSelective(GSerialItem record);

    GSerialItem selectByPrimaryKey(GSerialItem  record);

    int updateByPrimaryKeySelective(GSerialItem record);

    int updateByPrimaryKey(GSerialItem record);

    /**
     * Batch insert serial items
     * @param records List of records to insert
     * @return Number of inserted records
     */
    int batchInsert(List<GSerialItem> records);

    /**
     * Batch update serial items
     * @param records List of records to update
     * @return Number of updated records
     */
    int batchUpdate(List<GSerialItem> records);
}

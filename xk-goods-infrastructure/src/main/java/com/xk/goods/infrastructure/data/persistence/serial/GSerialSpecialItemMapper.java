package com.xk.goods.infrastructure.data.persistence.serial;

import java.util.List;

import com.myco.framework.cache.annotations.GroupStrategy;
import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.infrastructure.data.po.serial.GSerialSpecialItem;

/**
 * <AUTHOR>
 * @description 针对表【g_serial_special_item(自定义卡密组条目表)】的数据库操作Mapper
 * @createDate 2025-07-29 17:41:29
 * @Entity com.xk.goods.infrastructure.data.po.serial.GSerialSpecialItem
 */
@Repository
@Table("g_serial_special_item")
public interface GSerialSpecialItemMapper {

    int deleteByPrimaryKey(GSerialSpecialItem record);

    int insert(GSerialSpecialItem record);

    int insertSelective(GSerialSpecialItem record);

    GSerialSpecialItem selectByPrimaryKey(GSerialSpecialItem record);

    int updateByPrimaryKeySelective(GSerialSpecialItem record);

    int updateByPrimaryKey(GSerialSpecialItem record);

    List<GSerialSpecialItem> searchSpecialItem(Pagination pagination);

    List<GSerialSpecialItem> searchSpecialItemByCondition(SerialSpecialItemEntity entity);

    int countSpecialItem(Long serialGroupId);

    @GroupStrategy(value = "serialGroupId")
    List<GSerialSpecialItem> selectBySerialGroupId(GSerialSpecialItem record);

    /**
     * Batch insert special items
     * @param records List of records to insert
     * @return Number of inserted records
     */
    int batchInsert(List<GSerialSpecialItem> records);

    /**
     * Batch update special items
     * @param records List of records to update
     * @return Number of updated records
     */
    int batchUpdate(List<GSerialSpecialItem> records);
}

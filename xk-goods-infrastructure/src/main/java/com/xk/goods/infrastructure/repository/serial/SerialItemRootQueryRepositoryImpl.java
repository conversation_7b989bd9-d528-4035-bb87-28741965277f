package com.xk.goods.infrastructure.repository.serial;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.domain.model.serial.entity.SerialTeamItemEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.model.serialitem.SerialItemRoot;
import com.xk.goods.domain.model.serialitem.entity.SerialItemEntity;
import com.xk.goods.domain.model.serialitem.id.SerialItemIdentifier;
import com.xk.goods.domain.model.team.id.TeamMemberIdentifier;
import com.xk.goods.domain.repository.serial.SerialItemRootQueryRepository;
import com.xk.goods.enums.serial.SerialItemTypeEnum;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialItemMapper;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialOriginalItemMapper;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialSpecialItemMapper;
import com.xk.goods.infrastructure.data.persistence.serial.GSerialTeamMapper;
import com.xk.goods.infrastructure.data.po.serial.GSerialItem;
import com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem;
import com.xk.goods.infrastructure.data.po.serial.GSerialSpecialItem;
import com.xk.goods.infrastructure.data.po.serial.GSerialTeam;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class SerialItemRootQueryRepositoryImpl implements SerialItemRootQueryRepository {

    private final GSerialOriginalItemMapper serialOriginalItemMapper;
    private final GSerialSpecialItemMapper serialSpecialItemMapper;
    private final GSerialItemMapper gSerialItemMapper;
    private final GSerialTeamMapper serialTeamMapper;
    private final Converter converter;

    @Override
    public Flux<SerialOriginalItemEntity> searchOriginalItemByKeywords(Pagination pagination) {
        return search(pagination, serialOriginalItemMapper::searchByKeywords,
                SerialOriginalItemEntity.class, converter::convert).map(v -> {
                    String memberName =
                            StringUtils.join(v.getMemberCnName(), "|", v.getMemberEnName());
                    String team = StringUtils.join(v.getTeamName(), "|", v.getCardTypeNo());
                    String cardType = v.getCardType();
                    v.setName(StringUtils.join(memberName, "\n", team, "\n", cardType));
                    return v;
                });
    }

    @Override
    public Flux<SerialSpecialItemEntity> searchSpecialItem(Pagination pagination) {
        return search(pagination, serialSpecialItemMapper::searchSpecialItem,
                SerialSpecialItemEntity.class, converter::convert);
    }

    @Override
    public Flux<SerialTeamItemEntity> searchTeamPager(Pagination pagination) {
        return search(pagination, serialTeamMapper::searchTeamPager, SerialTeamItemEntity.class,
                converter::convert);
    }

    @Override
    public Flux<SerialSpecialItemEntity> searchSpecialItemByCondition(
            SerialSpecialItemEntity entity) {
        return find(entity, serialSpecialItemMapper::searchSpecialItemByCondition,
                SerialSpecialItemEntity.class, converter::convert);
    }

    @Override
    public Mono<Integer> countOriginalItem(Long serialGroupId) {
        int count = serialOriginalItemMapper.countOriginalItem(serialGroupId);
        return Mono.just(count);
    }

    @Override
    public Mono<Integer> countSpecialItem(Long serialGroupId) {
        int count = serialSpecialItemMapper.countSpecialItem(serialGroupId);
        return Mono.just(count);
    }

    @Override
    public Mono<Integer> countDistinctSerialGroupTeam(Long serialGroupId) {
        int count = serialOriginalItemMapper.countDistinctSerialGroupTeam(serialGroupId);
        return Mono.just(count);
    }

    @Override
    public Flux<SerialTeamItemEntity> searchTeamBySerialGroupId(SerialGroupIdentifier identifier) {
        return find(
                () -> serialTeamMapper.selectBySerialGroupId(
                        GSerialTeam.builder().serialGroupId(identifier.getSerialGroupId()).build()),
                SerialTeamItemEntity.class, converter::convert);
    }

    @Override
    public Flux<SerialOriginalItemEntity> searchOriginalBySerialGroupId(
            SerialGroupIdentifier identifier) {
        return find(
                () -> serialOriginalItemMapper.selectBySerialGroupId(GSerialOriginalItem.builder()
                        .serialGroupId(identifier.getSerialGroupId()).build()),
                SerialOriginalItemEntity.class, converter::convert).map(v -> {
                    String memberName =
                            StringUtils.join(v.getMemberCnName(), "|", v.getMemberEnName());
                    String team = StringUtils.join(v.getTeamName(), "|", v.getCardTypeNo());
                    String cardType = v.getCardType();
                    v.setName(StringUtils.join(memberName, "\n", team, "\n", cardType));
                    return v;
                });
    }

    @Override
    public Mono<SerialItemRoot> getRoot(SerialItemIdentifier identifier) {
        return getById(identifier,
                id -> gSerialItemMapper.selectByPrimaryKey(
                        GSerialItem.builder().serialItemId(id.getSerialItemId()).build()),
                SerialItemEntity.class, converter::convert).flatMap(entity -> {
                    if (Objects.equals(entity.getSerialItemType(),
                            SerialItemTypeEnum.ORIGINAL.getCode())) {
                        return getById(identifier,
                                id -> serialOriginalItemMapper
                                        .selectByPrimaryKey(GSerialOriginalItem.builder()
                                                .serialItemId(id.getSerialItemId()).build()),
                                SerialOriginalItemEntity.class, converter::convert).map(v -> {
                                    String memberName = StringUtils.join(v.getMemberCnName(), "|",
                                            v.getMemberEnName());
                                    String team = StringUtils.join(v.getTeamName(), "|",
                                            v.getCardTypeNo());
                                    String cardType = v.getCardType();
                                    v.setName(StringUtils.join(memberName, "\n", team, "\n",
                                            cardType));
                                    return SerialItemRoot.builder().identifier(identifier)
                                            .serialItemEntity(entity).serialOriginalItemEntity(v)
                                            .build();
                                });
                    }

                    if (Objects.equals(entity.getSerialItemType(),
                            SerialItemTypeEnum.SPECIAL.getCode())) {
                        return getById(identifier,
                                id -> serialSpecialItemMapper.selectByPrimaryKey(GSerialSpecialItem
                                        .builder().serialItemId(id.getSerialItemId()).build()),
                                SerialSpecialItemEntity.class, converter::convert)
                                .map(v -> SerialItemRoot.builder().identifier(identifier)
                                        .serialItemEntity(entity).serialSpecialItemEntity(v)
                                        .build());
                    }

                    if (Objects.equals(entity.getSerialItemType(),
                            SerialItemTypeEnum.TEAM.getCode())) {
                        return getById(identifier,
                                id -> serialTeamMapper.selectByPrimaryKey(GSerialTeam.builder()
                                        .serialItemId(id.getSerialItemId()).build()),
                                SerialTeamItemEntity.class, converter::convert)
                                .map(v -> SerialItemRoot.builder().identifier(identifier)
                                        .serialItemEntity(entity).serialTeamItemEntity(v).build());
                    }
                    return Mono.empty();
                });
    }

    @Override
    public Flux<SerialOriginalItemEntity> searchOriginalByTeamMemberId(
            TeamMemberIdentifier identifier) {
        return find(identifier,
                id -> serialOriginalItemMapper.searchOriginalByTeamMemberId(GSerialOriginalItem
                        .builder().teamMemberId(identifier.getTeamMemberId()).build()),
                SerialOriginalItemEntity.class, converter::convert).map(v -> {
                    String memberName =
                            StringUtils.join(v.getMemberCnName(), "|", v.getMemberEnName());
                    String team = StringUtils.join(v.getTeamName(), "|", v.getCardTypeNo());
                    String cardType = v.getCardType();
                    v.setName(StringUtils.join(memberName, "\n", team, "\n", cardType));
                    return v;
                });
    }

    @Override
    public Flux<SerialTeamItemEntity> searchTeamByTeamMemberId(TeamMemberIdentifier identifier) {
        return find(identifier,
                id -> serialTeamMapper.searchTeamByTeamMemberId(
                        GSerialTeam.builder().teamMemberId(identifier.getTeamMemberId()).build()),
                SerialTeamItemEntity.class, converter::convert);
    }
}

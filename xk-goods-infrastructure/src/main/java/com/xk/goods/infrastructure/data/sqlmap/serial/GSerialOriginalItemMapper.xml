<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.goods.infrastructure.data.persistence.serial.GSerialOriginalItemMapper">

    <resultMap id="BaseResultMap" type="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem">
        <id property="serialItemId" column="serial_item_id" jdbcType="BIGINT"/>
        <result property="serialGroupId" column="serial_group_id" jdbcType="BIGINT"/>
        <result property="seriesCategoryId" column="series_category_id" jdbcType="BIGINT"/>
        <result property="memberCnName" column="member_cn_name" jdbcType="VARCHAR"/>
        <result property="memberEnName" column="member_en_name" jdbcType="VARCHAR"/>
        <result property="teamName" column="team_name" jdbcType="VARCHAR"/>
        <result property="cardType" column="card_type" jdbcType="VARCHAR"/>
        <result property="cardTypeNo" column="card_type_no" jdbcType="VARCHAR"/>
        <result property="limitEdition" column="limit_edition" jdbcType="VARCHAR"/>
        <result property="color" column="color" jdbcType="VARCHAR"/>
        <result property="teamMemberId" column="team_member_id" jdbcType="BIGINT"/>
        <result property="memberPicAddr" column="member_pic_addr" jdbcType="VARCHAR"/>
        <result property="memberAvatarAddr" column="member_avatar_addr" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="teamType" column="team_type" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        serial_item_id
        ,serial_group_id,series_category_id,
        member_cn_name,member_en_name,team_name,
        card_type,card_type_no,limit_edition,
        color,team_member_id,member_pic_addr,
        member_avatar_addr,deleted,update_id,
        create_id,update_time,create_time,
        team_type
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_serial_original_item
        where serial_item_id = #{serialItemId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem">
        delete
        from g_serial_original_item
        where serial_item_id = #{serialItemId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="serial_item_id" keyProperty="serialItemId"
            parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem" useGeneratedKeys="true">
        insert into g_serial_original_item
        ( serial_item_id, serial_group_id, series_category_id
        , member_cn_name, member_en_name, team_name
        , card_type, card_type_no, limit_edition
        , color, team_member_id, member_pic_addr
        , member_avatar_addr, deleted, update_id
        , create_id, update_time, create_time
        , team_type)
        values ( #{serialItemId,jdbcType=BIGINT}, #{serialGroupId,jdbcType=BIGINT}, #{seriesCategoryId,jdbcType=BIGINT}
               , #{memberCnName,jdbcType=VARCHAR}, #{memberEnName,jdbcType=VARCHAR}, #{teamName,jdbcType=VARCHAR}
               , #{cardType,jdbcType=VARCHAR}, #{cardTypeNo,jdbcType=VARCHAR}, #{limitEdition,jdbcType=VARCHAR}
               , #{color,jdbcType=VARCHAR}, #{teamMemberId,jdbcType=BIGINT}, #{memberPicAddr,jdbcType=VARCHAR}
               , #{memberAvatarAddr,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{updateId,jdbcType=BIGINT}
               , #{createId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
               , #{teamType,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="serial_item_id" keyProperty="serialItemId"
            parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem" useGeneratedKeys="true">
        insert into g_serial_original_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialItemId != null">serial_item_id,</if>
            <if test="serialGroupId != null">serial_group_id,</if>
            <if test="seriesCategoryId != null">series_category_id,</if>
            <if test="memberCnName != null">member_cn_name,</if>
            <if test="memberEnName != null">member_en_name,</if>
            <if test="teamName != null">team_name,</if>
            <if test="cardType != null">card_type,</if>
            <if test="cardTypeNo != null">card_type_no,</if>
            <if test="limitEdition != null">limit_edition,</if>
            <if test="color != null">color,</if>
            <if test="teamMemberId != null">team_member_id,</if>
            <if test="memberPicAddr != null">member_pic_addr,</if>
            <if test="memberAvatarAddr != null">member_avatar_addr,</if>
            <if test="deleted != null">deleted,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="teamType != null">team_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialItemId != null">#{serialItemId,jdbcType=BIGINT},</if>
            <if test="serialGroupId != null">#{serialGroupId,jdbcType=BIGINT},</if>
            <if test="seriesCategoryId != null">#{seriesCategoryId,jdbcType=BIGINT},</if>
            <if test="memberCnName != null">#{memberCnName,jdbcType=VARCHAR},</if>
            <if test="memberEnName != null">#{memberEnName,jdbcType=VARCHAR},</if>
            <if test="teamName != null">#{teamName,jdbcType=VARCHAR},</if>
            <if test="cardType != null">#{cardType,jdbcType=VARCHAR},</if>
            <if test="cardTypeNo != null">#{cardTypeNo,jdbcType=VARCHAR},</if>
            <if test="limitEdition != null">#{limitEdition,jdbcType=VARCHAR},</if>
            <if test="color != null">#{color,jdbcType=VARCHAR},</if>
            <if test="teamMemberId != null">#{teamMemberId,jdbcType=BIGINT},</if>
            <if test="memberPicAddr != null">#{memberPicAddr,jdbcType=VARCHAR},</if>
            <if test="memberAvatarAddr != null">#{memberAvatarAddr,jdbcType=VARCHAR},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="teamType != null">#{teamType,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem">
        update g_serial_original_item
        <set>
            <if test="serialGroupId != null">
                serial_group_id = #{serialGroupId,jdbcType=BIGINT},
            </if>
            <if test="seriesCategoryId != null">
                series_category_id = #{seriesCategoryId,jdbcType=BIGINT},
            </if>
            <if test="memberCnName != null">
                member_cn_name = #{memberCnName,jdbcType=VARCHAR},
            </if>
            <if test="memberEnName != null">
                member_en_name = #{memberEnName,jdbcType=VARCHAR},
            </if>
            <if test="teamName != null">
                team_name = #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardTypeNo != null">
                card_type_no = #{cardTypeNo,jdbcType=VARCHAR},
            </if>
            <if test="limitEdition != null">
                limit_edition = #{limitEdition,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="teamMemberId != null">
                team_member_id = #{teamMemberId,jdbcType=BIGINT},
            </if>
            <if test="memberPicAddr != null">
                member_pic_addr = #{memberPicAddr,jdbcType=VARCHAR},
            </if>
            <if test="memberAvatarAddr != null">
                member_avatar_addr = #{memberAvatarAddr,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="teamType != null">
                team_type = #{teamType,jdbcType=TINYINT},
            </if>
        </set>
        where serial_item_id = #{serialItemId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem">
        update g_serial_original_item
        set serial_group_id    = #{serialGroupId,jdbcType=BIGINT},
            series_category_id = #{seriesCategoryId,jdbcType=BIGINT},
            member_cn_name     = #{memberCnName,jdbcType=VARCHAR},
            member_en_name     = #{memberEnName,jdbcType=VARCHAR},
            team_name          = #{teamName,jdbcType=VARCHAR},
            card_type          = #{cardType,jdbcType=VARCHAR},
            card_type_no       = #{cardTypeNo,jdbcType=VARCHAR},
            limit_edition      = #{limitEdition,jdbcType=VARCHAR},
            color              = #{color,jdbcType=VARCHAR},
            team_member_id     = #{teamMemberId,jdbcType=BIGINT},
            member_pic_addr    = #{memberPicAddr,jdbcType=VARCHAR},
            member_avatar_addr = #{memberAvatarAddr,jdbcType=VARCHAR},
            deleted            = #{deleted,jdbcType=TINYINT},
            update_id          = #{updateId,jdbcType=BIGINT},
            create_id          = #{createId,jdbcType=BIGINT},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            team_type          = #{teamType,jdbcType=TINYINT}
        where serial_item_id = #{serialItemId,jdbcType=BIGINT}
    </update>
    <select id="searchByKeywords" resultMap="BaseResultMap">
        SELECT * fROM (SELECT
        serial_item_id,
        serial_group_id,
        series_category_id,
        member_cn_name,
        member_en_name,
        team_name,
        team_type,
        card_type,
        card_type_no,
        limit_edition,
        color,
        team_member_id,
        member_pic_addr,
        member_avatar_addr,
        deleted,
        update_id,
        create_id,
        update_time,
        create_time
        FROM
        g_serial_original_item
        UNION ALL
        SELECT
        serial_item_id,
        serial_group_id,
        NULL AS series_category_id,
        team_name AS member_cn_name,
        NULL AS member_en_name,
        team_name,
        team_type,
        NULL AS card_type,
        NULL AS card_type_no,
        NULL AS limit_edition,
        color,
        team_member_id,
        member_pic_addr,
        member_avatar_addr,
        NULL AS deleted,
        NULL AS update_id,
        NULL AS create_id,
        NULL AS update_time,
        create_time
        FROM
        g_serial_team)tmp
        <where>
            <if test="keywords != null and keywords.size() > 0">
                and (
                <foreach collection="keywords" item="keyword" separator=" OR ">
                    (
                    member_cn_name LIKE CONCAT('%', #{keyword}, '%')
                    OR member_en_name LIKE CONCAT('%', #{keyword}, '%')
                    OR team_name LIKE CONCAT('%', #{keyword}, '%')
                    OR card_type LIKE CONCAT('%', #{keyword}, '%')
                    OR card_type_no LIKE CONCAT('%', #{keyword}, '%')
                    OR limit_edition LIKE CONCAT('%', #{keyword}, '%')
                    )
                </foreach>
                )
            </if>
            <if test="serialGroupId != null">
                and serial_group_id = #{serialGroupId}
            </if>
            <if test="seriesCategoryId != null">
                and series_category_id = #{seriesCategoryId}
            </if>
            <if test="onlyLimitEdition != null and onlyLimitEdition == 1">
                AND `limit_edition` IS NOT NULL
                AND `limit_edition` != '无限编'
            </if>
        </where>
    </select>
    <select id="selectBySerialGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_serial_original_item
        where serial_group_id = #{serialGroupId,jdbcType=BIGINT}
    </select>
    <delete id="deleteBySerialGroupId">
        delete
        from g_serial_original_item
        where serial_group_id = #{serialGroupId}
    </delete>
    <select id="countOriginalItem" resultType="java.lang.Integer">
        SELECT count(1)
        FROM g_serial_original_item
        WHERE serial_group_id = #{serialGroupId}
    </select>
    <select id="countDistinctSerialGroupTeam" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM (SELECT DISTINCT team_name
              FROM g_serial_original_item
              WHERE serial_group_id = #{serialGroupId}) t
    </select>
    <select id="searchOriginalByTeamMemberId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_serial_original_item
        where team_member_id = #{teamMemberId,jdbcType=BIGINT}
    </select>

    <!-- Batch insert for serial original items -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO g_serial_original_item (
            serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name,
            team_name, card_type, card_type_no, limit_edition, color, team_member_id,
            member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.serialItemId,jdbcType=BIGINT},
                #{item.serialGroupId,jdbcType=BIGINT},
                #{item.seriesCategoryId,jdbcType=BIGINT},
                #{item.memberCnName,jdbcType=VARCHAR},
                #{item.memberEnName,jdbcType=VARCHAR},
                #{item.teamName,jdbcType=VARCHAR},
                #{item.cardType,jdbcType=VARCHAR},
                #{item.cardTypeNo,jdbcType=VARCHAR},
                #{item.limitEdition,jdbcType=VARCHAR},
                #{item.color,jdbcType=VARCHAR},
                #{item.teamMemberId,jdbcType=BIGINT},
                #{item.memberPicAddr,jdbcType=VARCHAR},
                #{item.memberAvatarAddr,jdbcType=VARCHAR},
                #{item.deleted,jdbcType=TINYINT},
                #{item.updateId,jdbcType=BIGINT},
                #{item.createId,jdbcType=BIGINT},
                #{item.updateTime,jdbcType=TIMESTAMP},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.teamType,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!-- Batch update for serial original items -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE g_serial_original_item
            <set>
                <if test="item.serialGroupId != null">serial_group_id = #{item.serialGroupId,jdbcType=BIGINT},</if>
                <if test="item.seriesCategoryId != null">series_category_id = #{item.seriesCategoryId,jdbcType=BIGINT},</if>
                <if test="item.memberCnName != null">member_cn_name = #{item.memberCnName,jdbcType=VARCHAR},</if>
                <if test="item.memberEnName != null">member_en_name = #{item.memberEnName,jdbcType=VARCHAR},</if>
                <if test="item.teamName != null">team_name = #{item.teamName,jdbcType=VARCHAR},</if>
                <if test="item.cardType != null">card_type = #{item.cardType,jdbcType=VARCHAR},</if>
                <if test="item.cardTypeNo != null">card_type_no = #{item.cardTypeNo,jdbcType=VARCHAR},</if>
                <if test="item.limitEdition != null">limit_edition = #{item.limitEdition,jdbcType=VARCHAR},</if>
                <if test="item.color != null">color = #{item.color,jdbcType=VARCHAR},</if>
                <if test="item.teamMemberId != null">team_member_id = #{item.teamMemberId,jdbcType=BIGINT},</if>
                <if test="item.memberPicAddr != null">member_pic_addr = #{item.memberPicAddr,jdbcType=VARCHAR},</if>
                <if test="item.memberAvatarAddr != null">member_avatar_addr = #{item.memberAvatarAddr,jdbcType=VARCHAR},</if>
                <if test="item.deleted != null">deleted = #{item.deleted,jdbcType=TINYINT},</if>
                <if test="item.updateId != null">update_id = #{item.updateId,jdbcType=BIGINT},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime,jdbcType=TIMESTAMP}</if>
            </set>
            WHERE serial_item_id = #{item.serialItemId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>

package com.xk.goods.infrastructure.repository.team;

import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.team.TeamMemberRootQueryRepository;
import com.xk.goods.infrastructure.data.persistence.team.GTeamMemberMapper;
import com.xk.goods.infrastructure.data.po.team.GTeamMember;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class TeamMemberRootQueryRepositoryImpl implements TeamMemberRootQueryRepository {

    private final GTeamMemberMapper gTeamMemberMapper;
    private final Converter converter;

    @Override
    public Flux<TeamMemberEntity> searchTeamMember(Pagination pagination) {
        return this.search(pagination, gTeamMemberMapper::selectByPage, TeamMemberEntity.class,
                converter::convert);
    }

    @Override
    public Flux<TeamMemberEntity> searchTeamMemberInCondition(Pagination pagination) {
        return this.search(pagination, gTeamMemberMapper::selectInCondition, TeamMemberEntity.class,
                converter::convert);
    }

    @Override
    public Flux<TeamMemberEntity> searchAll() {
        return this.find(gTeamMemberMapper::selectAll, TeamMemberEntity.class,
                converter::convert);
    }
}

package com.xk.acct.server.endpoints.follow;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.acct.interfaces.dto.req.follow.CorpFollowQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.SelectByUserQueryReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryInnerReqDto;
import com.xk.acct.interfaces.dto.req.follow.UserFollowCorpQueryReqDto;
import com.xk.acct.interfaces.dto.req.user.UserFollowCorpReqDto;
import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;

@Configuration(proxyBeanMethods = false)
public class UserFollowCorpQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON =
            RequestPredicates.accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> userFollowCorpQueryServiceRouter(UserFollowCorpQueryService userFollowCorpQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/acct/user/follow/corp/query"),
                RouterFunctions.route()
                        .POST("/list", ACCEPT_JSON, request -> WebFluxHandler.handler(request, UserFollowCorpQueryReqDto.class, userFollowCorpQueryService::list))
                        .POST("/inner/list", ACCEPT_JSON, request -> WebFluxHandler.handler(request, UserFollowCorpQueryInnerReqDto.class, userFollowCorpQueryService::listInner))
                        .POST("/user/corpsAll", ACCEPT_JSON, request -> WebFluxHandler.handler(request, RequireSessionDto.class, userFollowCorpQueryService::userCorpsAll))
                        .POST("/user/corps", ACCEPT_JSON, request -> WebFluxHandler.handler(request, SelectByUserQueryReqDto.class, userFollowCorpQueryService::userCorps))
                        .POST("/followNumber", ACCEPT_JSON, request -> WebFluxHandler.handler(request, CorpFollowQueryReqDto.class, userFollowCorpQueryService::followNumber))
                        .POST("/user/followStatus", ACCEPT_JSON, request -> WebFluxHandler.handler(request, UserFollowCorpReqDto.class, userFollowCorpQueryService::followStatus))
                        .build());
    }

}


